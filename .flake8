[flake8]
max-line-length = 88
exclude = 
    alembic/versions/*.py,
    migrations/,
    __pycache__,
    .git,
    .venv,
    venv,
    build,
    dist,
    seed.py,
    test_openstates_federal.py
ignore =
    E203,
    E501,
    W503,
    W293,
    E302,
    E122,
    E128,
    E127,
    E129,
    E305,
    E303,
    E226,
    E402,
    F401,
    F811,
    F841,
    F541,
    W504,
    W391,
    W291
per-file-ignores =
    scripts/*.py:E501,E302,W293,E122
    tests/*.py:E501,E302,W293,E122
