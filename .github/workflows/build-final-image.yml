name: Build Final Docker Image

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to build from'
        required: true
        default: 'hotfix/ssr-bug'

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.branch }}
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
    
    - name: Build and push Docker image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: modernaction-web-staging
        IMAGE_TAG: final-fix-amd64
      run: |
        cd apps/web
        echo "Cleaning .next directory for fresh build..."
        rm -rf .next
        echo "Building Docker image with clean cache..."
        docker build --no-cache -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        echo "Pushing image to ECR..."
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "Build and push completed successfully!"
        echo "Image: $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
