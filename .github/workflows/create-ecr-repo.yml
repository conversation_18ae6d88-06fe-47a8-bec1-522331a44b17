name: Create ECR Repository

on:
  workflow_dispatch:

env:
  AWS_REGION: us-east-1

jobs:
  create-ecr-repo:
    runs-on: ubuntu-latest
    name: Create ECR Repository
    
    steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Create ECR repository
      run: |
        # Create modernaction-api repository
        aws ecr create-repository \
          --repository-name modernaction-api \
          --image-scanning-configuration scanOnPush=true \
          --region ${{ env.AWS_REGION }} || echo "Repository may already exist"
        
        # Create modernaction-api-staging repository  
        aws ecr create-repository \
          --repository-name modernaction-api-staging \
          --image-scanning-configuration scanOnPush=true \
          --region ${{ env.AWS_REGION }} || echo "Repository may already exist"
        
        # Create modernaction-web-staging repository
        aws ecr create-repository \
          --repository-name modernaction-web-staging \
          --image-scanning-configuration scanOnPush=true \
          --region ${{ env.AWS_REGION }} || echo "Repository may already exist"
        
        echo "✅ ECR repositories created successfully"
        
        # List repositories to confirm
        aws ecr describe-repositories --region ${{ env.AWS_REGION }} --query 'repositories[].repositoryName' --output table
