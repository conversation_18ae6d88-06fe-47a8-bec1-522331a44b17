name: Deploy to A<PERSON>

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: 308755113449.dkr.ecr.us-east-1.amazonaws.com
  ECS_CLUSTER: modernaction-staging
  ENVIRONMENT: staging

jobs:
  test:
    runs-on: ubuntu-latest
    name: Run Tests
    timeout-minutes: 30
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: modernaction_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: apps/web/package-lock.json

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: 1.6.1
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Cache Poetry dependencies
      uses: actions/cache@v4
      with:
        path: apps/api/.venv
        key: venv-${{ runner.os }}-${{ hashFiles('apps/api/poetry.lock') }}

    - name: Install backend dependencies
      working-directory: apps/api
      run: |
        poetry install --no-root

    - name: Install frontend dependencies
      working-directory: apps/web
      run: |
        npm ci

    - name: Run backend tests
      working-directory: apps/api
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/modernaction_test
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: test
      run: |
        echo "Starting backend tests..."
        poetry run pytest tests/ -v --tb=short
        echo "Backend tests completed successfully"

    - name: Run frontend tests
      working-directory: apps/web
      run: |
        npm run test

  build-and-deploy:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    name: Build and Deploy
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build and push API image
      uses: docker/build-push-action@v5
      with:
        context: ./apps/api
        file: ./apps/api/Dockerfile
        platforms: linux/amd64
        push: true
        tags: |
          ${{ env.ECR_REGISTRY }}/modernaction-api-${{ env.ENVIRONMENT }}:${{ github.sha }}
          ${{ env.ECR_REGISTRY }}/modernaction-api-${{ env.ENVIRONMENT }}:latest
          ${{ env.ECR_REGISTRY }}/modernaction-api-${{ env.ENVIRONMENT }}:sprint-b-launch-candidate
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push Web image
      uses: docker/build-push-action@v5
      with:
        context: ./apps/web
        platforms: linux/amd64
        push: true
        tags: |
          ${{ env.ECR_REGISTRY }}/modernaction-web-${{ env.ENVIRONMENT }}:${{ github.sha }}
          ${{ env.ECR_REGISTRY }}/modernaction-web-${{ env.ENVIRONMENT }}:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Deploy to ECS
      run: |
        # Update API service
        aws ecs update-service \
          --cluster ${{ env.ECS_CLUSTER }} \
          --service modernaction-api-${{ env.ENVIRONMENT }} \
          --force-new-deployment
        
        # Update Web service
        aws ecs update-service \
          --cluster ${{ env.ECS_CLUSTER }} \
          --service modernaction-web-${{ env.ENVIRONMENT }} \
          --force-new-deployment

    - name: Wait for deployment
      run: |
        echo "Waiting for API service to stabilize..."
        aws ecs wait services-stable \
          --cluster ${{ env.ECS_CLUSTER }} \
          --services modernaction-api-${{ env.ENVIRONMENT }}
        
        echo "Waiting for Web service to stabilize..."
        aws ecs wait services-stable \
          --cluster ${{ env.ECS_CLUSTER }} \
          --services modernaction-web-${{ env.ENVIRONMENT }}

    - name: Health check
      run: |
        ALB_URL="http://modern-ApiSe-5h6OCm8WlBhL-318015605.us-east-1.elb.amazonaws.com"
        
        # Check API health
        API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $ALB_URL/api/v1/health)
        if [ "$API_STATUS" = "200" ]; then
          echo "✓ API health check passed"
        else
          echo "✗ API health check failed (HTTP $API_STATUS)"
          exit 1
        fi
        
        # Check web application
        WEB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $ALB_URL/campaigns)
        if [ "$WEB_STATUS" = "200" ]; then
          echo "✓ Web application health check passed"
        else
          echo "✗ Web application health check failed (HTTP $WEB_STATUS)"
          exit 1
        fi

    - name: Fix campaign action counts
      run: |
        ALB_URL="http://modern-ApiSe-5h6OCm8WlBhL-318015605.us-east-1.elb.amazonaws.com"
        echo "Fixing campaign action counts..."
        curl -X POST $ALB_URL/api/v1/campaigns/admin/recalculate-action-counts || echo "Action count fix failed - may need manual intervention"

    - name: Notify deployment success
      run: |
        echo "🚀 Deployment completed successfully!"
        echo "✅ OpenStates API integration deployed with universal zip code support"
        echo "✅ Real geocoding service active for ALL US zip codes"
        echo "Application URL: http://modern-ApiSe-5h6OCm8WlBhL-318015605.us-east-1.elb.amazonaws.com"
