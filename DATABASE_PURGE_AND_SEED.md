# Database Purge and Re-seed Guide

This guide provides step-by-step instructions for purging placeholder data and seeding the database with real federal bills using the Congress.gov API integration.

## Prerequisites

- AWS CLI configured with appropriate permissions
- API keys configured in AWS Secrets Manager (from Step 1)
- CDK deployment completed successfully
- ECS services running and healthy

## Step 2.1: Access the API Container

### Find the Running API Task

```bash
# Get the cluster name and service name
CLUSTER_NAME="modernaction-staging"
SERVICE_NAME="modernaction-api-staging"

# Get the running task ARN
TASK_ARN=$(aws ecs list-tasks \
  --cluster $CLUSTER_NAME \
  --service-name $SERVICE_NAME \
  --query "taskArns[0]" \
  --output text)

echo "Task ARN: $TASK_ARN"
```

### Enable ECS Exec (if not already enabled)

```bash
# Enable execute command on the service
aws ecs update-service \
  --cluster $CLUSTER_NAME \
  --service $SERVICE_NAME \
  --enable-execute-command

# Wait for the service to stabilize
aws ecs wait services-stable \
  --cluster $CLUSTER_NAME \
  --services $SERVICE_NAME
```

### Get Shell Access to the Container

```bash
# Get an interactive shell in the API container
aws ecs execute-command \
  --cluster $CLUSTER_NAME \
  --task $TASK_ARN \
  --container "ApiContainer" \
  --interactive \
  --command "/bin/bash"
```

## Step 2.2: Verify Environment and Connectivity

Once inside the container, run these commands to verify everything is working:

```bash
# Check that we're in the right directory
pwd
ls -la

# Verify environment variables are set
echo "Environment: $ENVIRONMENT"
echo "DB Host: $DB_HOST"
echo "Google Civic API Key set: $([ -n "$GOOGLE_CIVIC_INFO_API_KEY" ] && echo "YES" || echo "NO")"
echo "Congress.gov API Key set: $([ -n "$CONGRESS_GOV_API_KEY" ] && echo "YES" || echo "NO")"

# Test database connectivity
python -c "
import os
import psycopg2
try:
    conn = psycopg2.connect(
        host=os.environ['DB_HOST'],
        port=os.environ['DB_PORT'],
        database=os.environ['DB_NAME'],
        user=os.environ['DB_USERNAME'],
        password=os.environ['DB_PASSWORD']
    )
    print('✅ Database connection successful')
    conn.close()
except Exception as e:
    print(f'❌ Database connection failed: {e}')
"

# Test API integrations
python -c "
from app.services.google_civic_api import get_google_civic_client
from app.services.congress_gov_api import get_congress_gov_client

# Test Google Civic API
civic_client = get_google_civic_client()
civic_health = civic_client.health_check()
print(f'Google Civic API: {civic_health[\"status\"]} - {civic_health[\"message\"]}')

# Test Congress.gov API
congress_client = get_congress_gov_client()
congress_health = congress_client.health_check()
print(f'Congress.gov API: {congress_health[\"status\"]} - {congress_health[\"message\"]}')
"
```

## Step 2.3: Purge Placeholder Data

**⚠️ WARNING: This will delete ALL existing bills, campaigns, and officials data. Make sure you're in the staging environment!**

```bash
# Connect to PostgreSQL and purge data
python -c "
import os
import psycopg2

# Connect to database
conn = psycopg2.connect(
    host=os.environ['DB_HOST'],
    port=os.environ['DB_PORT'],
    database=os.environ['DB_NAME'],
    user=os.environ['DB_USERNAME'],
    password=os.environ['DB_PASSWORD']
)

cursor = conn.cursor()

print('🗑️  Purging placeholder data...')

# Disable foreign key checks temporarily
cursor.execute('SET session_replication_role = replica;')

# Truncate tables in correct order (respecting foreign keys)
tables_to_truncate = [
    'actions',
    'campaigns', 
    'bills',
    'officials'
]

for table in tables_to_truncate:
    try:
        cursor.execute(f'TRUNCATE TABLE {table} RESTART IDENTITY CASCADE;')
        print(f'✅ Truncated {table}')
    except Exception as e:
        print(f'❌ Error truncating {table}: {e}')

# Re-enable foreign key checks
cursor.execute('SET session_replication_role = DEFAULT;')

# Commit changes
conn.commit()
cursor.close()
conn.close()

print('🧹 Database purge completed!')
"
```

## Step 2.4: Seed with Real Federal Bills

Now seed the database with real, high-profile federal bills:

```bash
# Seed with specific high-profile bills
python scripts/seed_real_bills.py --bills H.R.5 H.R.8 S.1

# Alternative: Seed with recent bills
# python scripts/seed_real_bills.py --limit 5
```

Expected output should look like:
```
🚀 Starting real bill data seeding from Congress.gov API...

📋 Fetching specific bill: H.R.5
✅ Created bill: H.R.5 - Equality Act...
✅ Created campaign: Support H.R.5: Equality Act...

📋 Fetching specific bill: H.R.8  
✅ Created bill: H.R.8 - Bipartisan Background Checks Act...
✅ Created campaign: Support H.R.8: Bipartisan Background Checks Act...

📋 Fetching specific bill: S.1
✅ Created bill: S.1 - For the People Act...
✅ Created campaign: Support S.1: For the People Act...

🤖 Scheduling AI summary generation for 3 bills...
✅ SUCCESS: Seeded 3 real bills and 3 campaigns
🚀 Ready for testing with real legislative data!
```

## Step 2.5: Verify the Seeded Data

```bash
# Check that bills were created correctly
python -c "
import os
import psycopg2

conn = psycopg2.connect(
    host=os.environ['DB_HOST'],
    port=os.environ['DB_PORT'],
    database=os.environ['DB_NAME'],
    user=os.environ['DB_USERNAME'],
    password=os.environ['DB_PASSWORD']
)

cursor = conn.cursor()

# Check bills
cursor.execute('SELECT bill_number, title, status FROM bills ORDER BY created_at;')
bills = cursor.fetchall()
print(f'📋 Bills in database: {len(bills)}')
for bill in bills:
    print(f'  - {bill[0]}: {bill[1][:60]}... (Status: {bill[2]})')

# Check campaigns  
cursor.execute('SELECT title, campaign_type, status FROM campaigns ORDER BY created_at;')
campaigns = cursor.fetchall()
print(f'🎯 Campaigns in database: {len(campaigns)}')
for campaign in campaigns:
    print(f'  - {campaign[0][:60]}... (Type: {campaign[1]}, Status: {campaign[2]})')

cursor.close()
conn.close()
"
```

## Step 2.6: Test the Officials Integration

```bash
# Test the officials lookup with a real zip code
python -c "
from app.services.officials import OfficialService
from app.db.database import SessionLocal

db = SessionLocal()
service = OfficialService(db)

print('🏛️  Testing officials lookup for zip code 90210...')
officials = service.get_officials_by_zip_code('90210')

print(f'Found {len(officials)} officials:')
for official in officials:
    print(f'  - {official.name} ({official.title})')
    print(f'    Party: {official.party or \"Unknown\"}')
    print(f'    Email: {official.email or \"Not available\"}')
    print(f'    Phone: {official.phone or \"Not available\"}')
    print()

db.close()
"
```

## Step 2.7: Exit the Container

```bash
# Exit the ECS Exec session
exit
```

## Verification Checklist

After completing the seeding, verify:

- [ ] ✅ Database contains 3-5 real federal bills (not placeholder "S-3456" data)
- [ ] ✅ Each bill has accurate title, bill number, and status
- [ ] ✅ Campaigns are created and linked to real bills
- [ ] ✅ Officials lookup works for test zip codes (90210, 10001, etc.)
- [ ] ✅ Google Civic API integration is functional
- [ ] ✅ Congress.gov API integration is functional

## Troubleshooting

### Common Issues

1. **ECS Exec not working**: 
   ```bash
   # Check if execute command is enabled
   aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME \
     --query "services[0].enableExecuteCommand"
   ```

2. **API keys not available**:
   ```bash
   # Check if secrets are properly mounted
   env | grep -E "(GOOGLE_CIVIC|CONGRESS_GOV)"
   ```

3. **Database connection fails**:
   ```bash
   # Check security groups and network connectivity
   aws ec2 describe-security-groups --group-ids sg-xxxxx
   ```

4. **Seeding script fails**:
   ```bash
   # Check the logs for detailed error messages
   python scripts/seed_real_bills.py --bills H.R.5 2>&1 | tee seed.log
   ```

## Next Steps

Once the database is successfully purged and re-seeded with real data, proceed to:

**Step 3: Live Fire UAT - Complete End-to-End Test**

The staging environment now contains:
- ✅ Real federal bills from Congress.gov API
- ✅ Real campaigns linked to actual legislation  
- ✅ Functional officials lookup via Google Civic API
- ✅ Clean database with no placeholder data

You're ready for the final User Acceptance Test!
