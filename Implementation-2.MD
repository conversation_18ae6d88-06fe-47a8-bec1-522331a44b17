# Complete Developer Implementation Guide - ModernAction.io MVP

## Part 1: AI Bill Summarization System

### 1.1 Database Schema Changes

**Execute these SQL migrations first:**

```sql
-- Add new columns to bills table
ALTER TABLE bills ADD COLUMN ai_summary TEXT;
ALTER TABLE bills ADD COLUMN support_reasons JSONB;
ALTER TABLE bills ADD COLUMN oppose_reasons JSONB;
ALTER TABLE bills ADD COLUMN amend_reasons JSONB;
ALTER TABLE bills ADD COLUMN message_templates JSONB;
ALTER TABLE bills ADD COLUMN ai_tags JSONB;
ALTER TABLE bills ADD COLUMN ai_processed_at TIMESTAMP;

-- Add indexes for performance
CREATE INDEX idx_bills_ai_tags ON bills USING GIN (ai_tags);
CREATE INDEX idx_bills_support_reasons ON bills USING GIN (support_reasons);
CREATE INDEX idx_bills_oppose_reasons ON bills USING GIN (oppose_reasons);
CREATE INDEX idx_bills_amend_reasons ON bills USING GIN (amend_reasons);
```

### 1.2 Congress.gov API Integration

**Create: `services/congress_api.py`**

```python
import aiohttp
import asyncio
from bs4 import BeautifulSoup
import os
import logging

logger = logging.getLogger(__name__)

class CongressAPIService:
    def __init__(self):
        self.api_key = os.getenv('CONGRESS_API_KEY')
        self.base_url = "https://api.congress.gov/v3"
        
    async def fetch_bill_data(self, bill_number: str, session: str) -> dict:
        """
        Fetch complete bill data from Congress.gov API
        
        Args:
            bill_number: e.g., "HR5", "S1" 
            session: e.g., "118"
        
        Returns:
            Complete bill data including metadata and full text
        """
        try:
            # Step 1: Get bill metadata
            metadata = await self._fetch_bill_metadata(bill_number, session)
            
            # Step 2: Get full text
            full_text = await self._fetch_bill_text(metadata)
            
            return {
                'congress_id': metadata['congress'],
                'number': metadata['number'],
                'title': metadata['title'],
                'short_title': metadata.get('titles', [{}])[0].get('title', ''),
                'summary': metadata.get('summaries', [{}])[0].get('text', ''),
                'introduced_date': metadata.get('introducedDate'),
                'sponsors': metadata.get('sponsors', []),
                'full_text': full_text,
                'congress_url': metadata.get('url'),
                'status': metadata.get('latestAction', {}).get('text', '')
            }
            
        except Exception as e:
            logger.error(f"Failed to fetch bill {bill_number}: {e}")
            raise
    
    async def _fetch_bill_metadata(self, bill_number: str, session: str) -> dict:
        """Fetch bill metadata from Congress API"""
        bill_type = bill_number[:2].lower()  # "hr", "s", etc.
        bill_num = bill_number[2:]  # "5"
        
        url = f"{self.base_url}/bill/{session}/{bill_type}/{bill_num}"
        headers = {'X-API-Key': self.api_key}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return data['bill']
                else:
                    error_text = await response.text()
                    raise Exception(f"Congress API error {response.status}: {error_text}")
    
    async def _fetch_bill_text(self, metadata: dict) -> str:
        """Fetch the full text of the bill"""
        try:
            # Get text versions from metadata
            text_versions = metadata.get('textVersions', {}).get('url')
            if not text_versions:
                return "Full text not available"
            
            # Fetch the text versions list
            async with aiohttp.ClientSession() as session:
                async with session.get(text_versions) as response:
                    versions_data = await response.json()
                    
                    # Get the latest version
                    latest_version = versions_data['textVersions'][0]
                    text_url = latest_version['formats'][0]['url']  # Usually XML or HTML
                    
                    # Fetch the actual text
                    async with session.get(text_url) as text_response:
                        raw_text = await text_response.text()
                        
                        # Clean the text (remove XML tags, etc.)
                        return self._clean_bill_text(raw_text)
                        
        except Exception as e:
            logger.warning(f"Could not fetch bill text: {e}")
            return metadata.get('summary', 'Text not available')
    
    def _clean_bill_text(self, raw_text: str) -> str:
        """Clean and extract readable text from bill HTML/XML"""
        try:
            soup = BeautifulSoup(raw_text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception:
            # Fallback: basic text cleaning
            import re
            text = re.sub(r'<[^>]+>', '', raw_text)
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
```

### 1.3 AI Processing Service

**Create: `services/ai_service.py`**

```python
import openai
import asyncio
import json
import logging
from typing import Dict, List
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

class AIService:
    def __init__(self):
        self.client = openai.AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
    async def process_bill_complete(self, bill_text: str, bill_metadata: dict) -> dict:
        """
        Generate all AI content for a bill
        
        Returns:
            {
                'ai_summary': str,
                'support_reasons': List[str],
                'oppose_reasons': List[str], 
                'amend_reasons': List[str],
                'message_templates': dict,
                'tags': List[str]
            }
        """
        # Truncate text if too long for AI model
        if len(bill_text) > 100000:
            bill_text = bill_text[:100000] + "... [text truncated for processing]"
        
        # Run all AI tasks in parallel for efficiency
        tasks = [
            self._generate_summary(bill_text, bill_metadata),
            self._generate_support_reasons(bill_text, bill_metadata),
            self._generate_oppose_reasons(bill_text, bill_metadata),
            self._generate_amend_reasons(bill_text, bill_metadata),
            self._generate_message_templates(bill_text, bill_metadata),
            self._generate_tags(bill_text, bill_metadata)
        ]
        
        try:
            results = await asyncio.gather(*tasks)
            return {
                'ai_summary': results[0],
                'support_reasons': results[1],
                'oppose_reasons': results[2],
                'amend_reasons': results[3],
                'message_templates': results[4],
                'tags': results[5]
            }
        except Exception as e:
            logger.error(f"AI processing failed: {e}")
            # Return minimal fallback data
            return self._get_fallback_ai_data(bill_metadata)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_summary(self, bill_text: str, metadata: dict) -> str:
        """Generate plain English summary"""
        prompt = f"""
        You are a civic education expert. Summarize this bill in plain English for concerned citizens.

        BILL TITLE: {metadata['title']}
        BILL TEXT: {bill_text}

        Write a 2-3 paragraph summary that:
        - Explains what this bill actually does in simple terms
        - Mentions who it affects and how
        - Avoids political jargon
        - Remains completely neutral and factual

        Summary:
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=500,
            temperature=0.3
        )
        
        return response.choices[0].message.content.strip()
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_support_reasons(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate selectable reasons for supporting the bill"""
        prompt = f"""
        Generate specific, selectable reasons why someone would SUPPORT this bill.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Create 6-8 distinct reasons someone might support this bill. Each reason should be:
        - One clear, concise sentence (10-15 words max)
        - Specific to this bill's actual provisions
        - Something a real person would say
        - Focused on benefits/positive outcomes

        Format as a JSON array of strings:
        ["Reason 1", "Reason 2", "Reason 3", ...]

        Support Reasons:
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=400,
            temperature=0.3
        )
        
        try:
            content = response.choices[0].message.content.strip()
            # Extract JSON from response
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            reasons = json.loads(json_str)
            
            if isinstance(reasons, list) and all(isinstance(r, str) for r in reasons):
                return reasons[:8]  # Limit to 8 reasons max
            else:
                raise ValueError("Invalid format")
                
        except Exception as e:
            logger.warning(f"Failed to parse support reasons JSON: {e}")
            return [
                "This bill addresses an important issue",
                "The provisions make sense for our community",
                "This legislation is needed to solve current problems"
            ]
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_oppose_reasons(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate selectable reasons for opposing the bill"""
        prompt = f"""
        Generate specific, selectable reasons why someone would OPPOSE this bill.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Create 6-8 distinct reasons someone might oppose this bill. Each reason should be:
        - One clear, concise sentence (10-15 words max)
        - Specific to this bill's actual provisions
        - Something a real person would say
        - Focused on concerns/negative impacts

        Format as a JSON array of strings:
        ["Reason 1", "Reason 2", "Reason 3", ...]

        Opposition Reasons:
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=400,
            temperature=0.3
        )
        
        try:
            content = response.choices[0].message.content.strip()
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            reasons = json.loads(json_str)
            
            if isinstance(reasons, list) and all(isinstance(r, str) for r in reasons):
                return reasons[:8]
            else:
                raise ValueError("Invalid format")
                
        except Exception as e:
            logger.warning(f"Failed to parse oppose reasons JSON: {e}")
            return [
                "This bill may have unintended consequences",
                "The legislation could be too costly to implement",
                "There are concerns about federal overreach"
            ]
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_amend_reasons(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate selectable reasons for wanting to amend the bill"""
        prompt = f"""
        Generate specific, selectable reasons why someone would want to AMEND this bill.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Create 6-8 distinct reasons someone might want amendments to this bill. Each reason should be:
        - One clear, concise sentence (10-15 words max)
        - Specific to this bill's actual provisions
        - Something a real person would say
        - Focused on improvements/modifications needed

        Format as a JSON array of strings:
        ["Reason 1", "Reason 2", "Reason 3", ...]

        Amendment Reasons:
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=400,
            temperature=0.3
        )
        
        try:
            content = response.choices[0].message.content.strip()
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            reasons = json.loads(json_str)
            
            if isinstance(reasons, list) and all(isinstance(r, str) for r in reasons):
                return reasons[:8]
            else:
                raise ValueError("Invalid format")
                
        except Exception as e:
            logger.warning(f"Failed to parse amend reasons JSON: {e}")
            return [
                "The implementation timeline needs to be adjusted",
                "Certain provisions require more clarity",
                "Additional safeguards should be included"
            ]
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_message_templates(self, bill_text: str, metadata: dict) -> dict:
        """Generate message templates for each stance"""
        prompt = f"""
        Create email/letter templates for each stance on this bill.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Generate three professional message templates that representatives would take seriously:

        1. SUPPORT template - for someone who supports the bill
        2. OPPOSE template - for someone who opposes the bill  
        3. AMEND template - for someone who wants changes to the bill

        Each template should:
        - Be 2-3 paragraphs
        - Sound like it's from a real constituent
        - Include placeholders for {{reasons}} and {{custom_message}}
        - Be respectful but firm
        - Reference the specific bill

        Format as JSON:
        {{
          "support_template": "Template text with {{reasons}} and {{custom_message}} placeholders",
          "oppose_template": "Template text with {{reasons}} and {{custom_message}} placeholders", 
          "amend_template": "Template text with {{reasons}} and {{custom_message}} placeholders"
        }}

        Templates:
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1000,
            temperature=0.4
        )
        
        try:
            content = response.choices[0].message.content.strip()
            # Extract JSON from response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_str = content[start_idx:end_idx]
            templates = json.loads(json_str)
            
            required_keys = ['support_template', 'oppose_template', 'amend_template']
            if all(key in templates for key in required_keys):
                return templates
            else:
                raise ValueError("Missing required template keys")
                
        except Exception as e:
            logger.warning(f"Failed to parse message templates JSON: {e}")
            return {
                "support_template": "Dear Representative,\n\nI am writing to express my strong support for {bill_title}. {reasons}\n\n{custom_message}\n\nI respectfully urge you to vote in favor of this important legislation.\n\nSincerely,\n{user_name}",
                "oppose_template": "Dear Representative,\n\nI am writing to express my concerns about {bill_title}. {reasons}\n\n{custom_message}\n\nI respectfully urge you to vote against this legislation.\n\nSincerely,\n{user_name}",
                "amend_template": "Dear Representative,\n\nI am writing about {bill_title}. While I see merit in addressing this issue, I believe amendments are needed. {reasons}\n\n{custom_message}\n\nI urge you to work toward improving this legislation.\n\nSincerely,\n{user_name}"
            }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_tags(self, bill_text: str, metadata: dict) -> List[str]:
        """Generate categorization tags"""
        prompt = f"""
        Generate 3-5 relevant tags for this bill to help with categorization and search.

        BILL: {metadata['title']}
        TEXT: {bill_text}

        Return ONLY a comma-separated list of tags. Focus on:
        - Issue areas (Healthcare, Environment, Economy, etc.)
        - Bill type (Appropriations, Authorization, etc.)
        - Affected groups (Small Business, Veterans, Students, etc.)

        Tags:
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=100,
            temperature=0.3
        )
        
        content = response.choices[0].message.content.strip()
        tags = [tag.strip() for tag in content.split(',')]
        return tags[:5]  # Limit to 5 tags
    
    def _get_fallback_ai_data(self, metadata: dict) -> dict:
        """Fallback data if AI processing fails"""
        return {
            'ai_summary': f"This is {metadata['title']}. Full AI analysis is temporarily unavailable.",
            'support_reasons': [
                "This bill addresses an important issue",
                "The legislation is needed for progress"
            ],
            'oppose_reasons': [
                "This bill may have unintended consequences",
                "The costs may outweigh the benefits"
            ],
            'amend_reasons': [
                "The bill needs clearer implementation guidelines",
                "Certain provisions require modification"
            ],
            'message_templates': {
                "support_template": "I support this bill because {reasons}. {custom_message}",
                "oppose_template": "I oppose this bill because {reasons}. {custom_message}",
                "amend_template": "This bill needs changes because {reasons}. {custom_message}"
            },
            'tags': ["Legislation", "Federal"]
        }
```

### 1.4 Bill Data Service Integration

**Create: `services/bill_data_service.py`**

```python
from .congress_api import CongressAPIService
from .ai_service import AIService
from models import Bill, db
import logging

logger = logging.getLogger(__name__)

class BillDataService:
    def __init__(self):
        self.congress_api = CongressAPIService()
        self.ai_service = AIService()
    
    async def ingest_bill(self, bill_number: str, session: str) -> Bill:
        """
        Complete bill ingestion: fetch from Congress.gov, process with AI, save to DB
        
        Args:
            bill_number: e.g., "HR5"
            session: e.g., "118"
            
        Returns:
            Bill object with all AI processing complete
        """
        try:
            logger.info(f"Starting ingestion for {bill_number} from session {session}")
            
            # Step 1: Fetch raw bill data
            logger.info("Fetching bill data from Congress.gov...")
            raw_data = await self.congress_api.fetch_bill_data(bill_number, session)
            
            # Step 2: Process with AI
            logger.info("Processing bill with AI...")
            ai_data = await self.ai_service.process_bill_complete(
                raw_data['full_text'], 
                raw_data
            )
            
            # Step 3: Save to database
            logger.info("Saving to database...")
            bill = await self._save_bill_to_db(raw_data, ai_data)
            
            logger.info(f"✅ Successfully processed {bill_number}: {bill.title}")
            return bill
            
        except Exception as e:
            logger.error(f"❌ Failed to ingest {bill_number}: {e}")
            raise
    
    async def _save_bill_to_db(self, raw_data: dict, ai_data: dict) -> Bill:
        """Save processed bill data to database"""
        
        # Check if bill already exists
        existing_bill = await Bill.query.filter_by(
            congress_number=raw_data['number'],
            congress_session=raw_data['congress_id']
        ).first()
        
        if existing_bill:
            # Update existing bill with new AI data
            bill = existing_bill
            logger.info(f"Updating existing bill: {bill.title}")
        else:
            # Create new bill
            bill = Bill()
            logger.info(f"Creating new bill: {raw_data['title']}")
        
        # Set all bill data
        bill.congress_number = raw_data['number']
        bill.congress_session = raw_data['congress_id']
        bill.title = raw_data['title']
        bill.short_title = raw_data['short_title']
        bill.summary = raw_data['summary']
        bill.introduced_date = raw_data['introduced_date']
        bill.congress_url = raw_data['congress_url']
        bill.status = raw_data['status']
        bill.full_text = raw_data['full_text']
        
        # Set AI-generated content
        bill.ai_summary = ai_data['ai_summary']
        bill.support_reasons = ai_data['support_reasons']
        bill.oppose_reasons = ai_data['oppose_reasons']
        bill.amend_reasons = ai_data['amend_reasons']
        bill.message_templates = ai_data['message_templates']
        bill.ai_tags = ai_data['tags']
        bill.ai_processed_at = datetime.utcnow()
        
        # Save to database
        db.session.add(bill)
        await db.session.commit()
        
        return bill
```

### 1.5 Command Line Tool

**Create: `seed.py`**

```python
#!/usr/bin/env python3
"""
Bill seeding tool for ModernAction.io

Usage:
    python seed.py --bill HR5 --session 118
    python seed.py --bill S1 --session 118
"""

import asyncio
import click
import sys
import os
from services.bill_data_service import BillDataService

@click.command()
@click.option('--bill', required=True, help='Bill number (e.g., HR5, S1)')
@click.option('--session', required=True, help='Congressional session (e.g., 118)')
@click.option('--force', is_flag=True, help='Force re-processing if bill exists')
async def seed_bill(bill: str, session: str, force: bool):
    """
    Seed a bill into the database with AI processing
    """
    click.echo(f"🚀 Starting bill ingestion: {bill} from session {session}")
    
    try:
        service = BillDataService()
        
        # Check environment variables
        required_vars = ['CONGRESS_API_KEY', 'OPENAI_API_KEY']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            click.echo(f"❌ Missing environment variables: {', '.join(missing_vars)}")
            sys.exit(1)
        
        # Process the bill
        bill_obj = await service.ingest_bill(bill, session)
        
        # Display results
        click.echo(f"\n✅ SUCCESS: {bill} has been processed!")
        click.echo(f"📋 Title: {bill_obj.title}")
        click.echo(f"📄 AI Summary: {bill_obj.ai_summary[:150]}...")
        click.echo(f"👍 Support Reasons: {len(bill_obj.support_reasons)} generated")
        click.echo(f"👎 Oppose Reasons: {len(bill_obj.oppose_reasons)} generated")
        click.echo(f"✏️  Amend Reasons: {len(bill_obj.amend_reasons)} generated")
        click.echo(f"🏷️  Tags: {', '.join(bill_obj.ai_tags)}")
        click.echo(f"\n🎯 Bill ID: {bill_obj.id}")
        click.echo(f"🌐 Available at: /campaigns/{bill_obj.id}")
        
    except Exception as e:
        click.echo(f"❌ ERROR: {e}")
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(seed_bill())
```

### 1.6 Environment Variables Needed

Add these to your `.env` file:

```bash
# Congress.gov API (free, get from https://api.congress.gov/sign-up/)
CONGRESS_API_KEY=your_congress_api_key_here

# OpenAI API (paid, get from https://platform.openai.com/api-keys)
OPENAI_API_KEY=your_openai_api_key_here
```

## Part 2: Action Network Integration

### 2.1 Action Network Service

**Create: `services/action_network_service.py`**

```python
import aiohttp
import json
import boto3
from typing import Dict, List
import logging

logger = logging.getLogger(__name__)

class ActionNetworkService:
    def __init__(self):
        self.base_url = "https://actionnetwork.org/api/v2"
        self.api_key = self._get_api_key_from_secrets()
        
    def _get_api_key_from_secrets(self) -> str:
        """Retrieve API key from AWS Secrets Manager"""
        try:
            secrets_client = boto3.client('secretsmanager')
            response = secrets_client.get_secret_value(SecretId='action-network-api-key')
            return response['SecretString']
        except Exception as e:
            # Fallback to environment variable for development
            import os
            api_key = os.getenv('ACTION_NETWORK_API_KEY')
            if not api_key:
                raise Exception("Action Network API key not found in secrets or environment")
            return api_key
    
    async def submit_message_to_officials(
        self, 
        user_message: str, 
        user_info: dict, 
        target_officials: List[dict],
        campaign_id: str = None
    ) -> dict:
        """
        Submit a user's message to their representatives via Action Network
        
        Returns:
            {
                "success": bool,
                "action_network_id": str,
                "message": str,
                "officials_contacted": int
            }
        """
        headers = {
            'OSDI-API-Token': self.api_key,
            'Content-Type': 'application/json'
        }
        
        # Prepare the action data in OSDI format
        action_data = {
            "person": {
                "given_name": user_info.get('first_name', ''),
                "family_name": user_info.get('last_name', ''),
                "email_addresses": [
                    {"address": user_info.get('email', '')}
                ],
                "postal_addresses": [
                    {
                        "postal_code": user_info.get('zip_code', ''),
                        "address_lines": [user_info.get('address', '')],
                        "locality": user_info.get('city', ''),
                        "region": user_info.get('state', '')
                    }
                ]
            },
            "targets": self._format_targets_for_action_network(target_officials),
            "action": {
                "subject": user_info.get('subject', 'Important Legislation'),
                "message": user_message
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                # Use specific campaign if provided, otherwise generic submission
                if campaign_id:
                    url = f"{self.base_url}/campaigns/{campaign_id}/submissions"
                else:
                    url = f"{self.base_url}/messages"
                
                async with session.post(url, headers=headers, json=action_data) as response:
                    response_text = await response.text()
                    
                    if response.status in [200, 201]:
                        try:
                            result = json.loads(response_text)
                            action_id = self._extract_action_id(result)
                            
                            return {
                                "success": True,
                                "action_network_id": action_id,
                                "message": "Message successfully submitted to representatives",
                                "officials_contacted": len(target_officials)
                            }
                        except json.JSONDecodeError:
                            return {
                                "success": True,
                                "action_network_id": "unknown",
                                "message": "Message submitted successfully",
                                "officials_contacted": len(target_officials)
                            }
                    else:
                        logger.error(f"Action Network API error {response.status}: {response_text}")
                        return {
                            "success": False,
                            "error": f"Action Network API error: {response.status}",
                            "details": response_text
                        }
                        
        except Exception as e:
            logger.error(f"Failed to submit to Action Network: {e}")
            return {
                "success": False,
                "error": f"Failed to submit message: {str(e)}"
            }
    
    def _format_targets_for_action_network(self, officials: List[dict]) -> List[dict]:
        """Format officials data for Action Network API"""
        targets = []
        for official in officials:
            target = {
                "given_name": official.get('first_name', ''),
                "family_name": official.get('last_name', ''),
                "organization": official.get('chamber', ''),  # "House" or "Senate"
            }
            
            # Add OpenCivicData ID if available
            if official.get('ocd_id'):
                target['ocdid'] = official['ocd_id']
            
            targets.append(target)
        
        return targets
    
    def _extract_action_id(self, response_data: dict) -> str:
        """Extract action ID from Action Network response"""
        try:
            identifiers = response_data.get('identifiers', [])
            if identifiers:
                return identifiers[0].get('identifier', 'unknown')
            return response_data.get('id', 'unknown')
        except Exception:
            return 'unknown'
```

### 2.2 Officials Lookup Service

**Create: `services/officials_service.py`**

```python
import aiohttp
import os
from typing import List, Dict
import logging

logger = logging.getLogger(__name__)

class OfficialsService:
    def __init__(self):
        self.openstates_api_key = os.getenv('OPENSTATES_API_KEY')
        self.base_url = "https://v3.openstates.org"
        
    async def get_federal_officials_by_zip(self, zip_code: str) -> List[dict]:
        """
        Get federal representatives (House + Senate) for a ZIP code
        """
        try:
            # Get representatives from OpenStates
            officials = await self._fetch_officials_from_api(zip_code)
            
            # Format for our application
            formatted_officials = []
            for official in officials:
                formatted = self._format_official_data(official)
                if formatted:
                    formatted_officials.append(formatted)
            
            return formatted_officials
            
        except Exception as e:
            logger.error(f"Failed to get officials for ZIP {zip_code}: {e}")
            # Return empty list rather than failing
            return []
    
    async def _fetch_officials_from_api(self, zip_code: str) -> List[dict]:
        """Fetch officials data from OpenStates API"""
        headers = {
            'X-API-KEY': self.openstates_api_key,
            'Accept': 'application/json'
        }
        
        # Query for federal officials
        params = {
            'jurisdiction': 'us',  # Federal level
            'per_page': 10
        }
        
        async with aiohttp.ClientSession() as session:
            # First, get the district info for this ZIP code
            district_url = f"{self.base_url}/districts"
            district_params = {
                'zip': zip_code,
                'classification': ['congressional', 'senatorial']
            }
            
            districts = []
            async with session.get(district_url, headers=headers, params=district_params) as response:
                if response.status == 200:
                    district_data = await response.json()
                    districts = district_data.get('results', [])
            
            # Now get officials for these districts
            officials = []
            for district in districts:
                people_url = f"{self.base_url}/people"
                people_params = {
                    'jurisdiction': 'us',
                    'district': district.get('name'),
                    'per_page': 5
                }
                
                async with session.get(people_url, headers=headers, params=people_params) as response:
                    if response.status == 200:
                        people_data = await response.json()
                        officials.extend(people_data.get('results', []))
            
            return officials
    
    def _format_official_data(self, official_data: dict) -> dict:
        """Format official data for our application"""
        try:
            # Extract current role
            current_role = official_data.get('current_role', {})
            if not current_role:
                return None
            
            # Parse name
            full_name = official_data.get('name', '')
            name_parts = full_name.split(' ', 1)
            first_name = name_parts[0] if name_parts else ''
            last_name = name_parts[1] if len(name_parts) > 1 else ''
            
            # Extract party
            party_info = official_data.get('party', [])
            party = party_info[0].get('name', 'Unknown') if party_info else 'Unknown'
            
            # Extract chamber info
            chamber = current_role.get('org_classification', '').title()
            if chamber == 'Upper':
                chamber = 'Senate'
            elif chamber == 'Lower':
                chamber = 'House'
            
            # Build social media profiles
            social_media = self._extract_social_media(official_data.get('links', []))
            
            return {
                'id': official_data.get('id'),
                'first_name': first_name,
                'last_name': last_name,
                'full_name': full_name,
                'title': current_role.get('title', 'Representative'),
                'chamber': chamber,
                'party': party,
                'district': current_role.get('district'),
                'state': current_role.get('jurisdiction', {}).get('name', ''),
                'photo_url': official_data.get('image'),
                'ocd_id': self._extract_ocd_id(official_data),
                'social_media': social_media,
                'office_phone': self._extract_contact_info(official_data, 'voice'),
                'website': self._extract_contact_info(official_data, 'url')
            }
            
        except Exception as e:
            logger.warning(f"Failed to format official data: {e}")
            return None
    
    def _extract_social_media(self, links: List[dict]) -> dict:
        """Extract social media profiles from links"""
        social = {}
        for link in links:
            url = link.get('url', '').lower()
            if 'twitter.com' in url or 'x.com' in url:
                social['twitter'] = link['url']
            elif 'facebook.com' in url:
                social['facebook'] = link['url']
            elif 'instagram.com' in url:
                social['instagram'] = link['url']
            elif 'youtube.com' in url:
                social['youtube'] = link['url']
        return social
    
    def _extract_ocd_id(self, official_data: dict) -> str:
        """Extract OpenCivicData ID"""
        identifiers = official_data.get('other_identifiers', [])
        for identifier in identifiers:
            if identifier.get('scheme') == 'ocd-person':
                return identifier.get('identifier')
        return official_data.get('id', '')
    
    def _extract_contact_info(self, official_data: dict, contact_type: str) -> str:
        """Extract contact information"""
        contact_details = official_data.get('contact_details', [])
        for contact in contact_details:
            if contact.get('type') == contact_type:
                return contact.get('value', '')
        return ''
```

### 2.3 Message Personalization Service

**Create: `services/message_personalization_service.py`**

```python
import openai
from typing import List
import logging

logger = logging.getLogger(__name__)

class MessagePersonalizationService:
    def __init__(self):
        self.client = openai.AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    async def generate_personalized_message(
        self,
        bill: dict,
        user_stance: str,  # "support", "oppose", "amend"
        selected_reasons: List[str],
        custom_message: str,
        user_info: dict
    ) -> str:
        """
        Generate a personalized message using the bill's template and user's selections
        """
        try:
            # Get the appropriate template
            templates = bill.get('message_templates', {})
            template_key = f"{user_stance}_template"
            base_template = templates.get(template_key, "")
            
            if not base_template:
                # Fallback template
                base_template = self._get_fallback_template(user_stance)
            
            # Format the selected reasons into natural language
            reasons_text = self._format_reasons_for_message(selected_reasons, user_stance)
            
            # Use AI to make it sound natural and personalized
            personalized_message = await self._personalize_with_ai(
                template=base_template,
                reasons=reasons_text,
                custom_message=custom_message,
                bill_title=bill.get('title', 'this legislation'),
                user_name=f"{user_info.get('first_name', '')} {user_info.get('last_name', '')}".strip(),
                stance=user_stance
            )
            
            return personalized_message
            
        except Exception as e:
            logger.error(f"Failed to generate personalized message: {e}")
            # Return a basic template-based message as fallback
            return self._generate_fallback_message(
                bill, user_stance, selected_reasons, custom_message, user_info
            )
    
    def _format_reasons_for_message(self, selected_reasons: List[str], stance: str) -> str:
        """Convert selected reasons into natural paragraph form"""
        if not selected_reasons:
            return ""
        
        if len(selected_reasons) == 1:
            return f"Specifically, {selected_reasons[0].lower()}."
        
        # Choose connector based on stance
        if stance == "support":
            connector = "I believe this legislation is important because "
        elif stance == "oppose":
            connector = "I have concerns because "
        else:  # amend
            connector = "I think this bill needs improvements because "
        
        # Format as natural sentences
        reasons_list = [reason.lower() for reason in selected_reasons]
        if len(reasons_list) > 1:
            formatted = f"{connector}{', '.join(reasons_list[:-1])}, and {reasons_list[-1]}."
        else:
            formatted = f"{connector}{reasons_list[0]}."
        
        return formatted
    
    async def _personalize_with_ai(
        self,
        template: str,
        reasons: str,
        custom_message: str,
        bill_title: str,
        user_name: str,
        stance: str
    ) -> str:
        """Use AI to create a natural, personalized message from the template"""
        
        prompt = f"""
        Create a personalized constituent letter using this template and information:

        TEMPLATE: {template}
        BILL TITLE: {bill_title}
        USER'S STANCE: {stance}
        USER'S REASONS: {reasons}
        USER'S CUSTOM MESSAGE: {custom_message}
        USER'S NAME: {user_name}

        Generate a natural, professional letter that:
        - Sounds like it's from a real person, not a template
        - Incorporates the user's specific reasons smoothly
        - Includes their custom message naturally
        - Is respectful but clear about their position
        - Is 2-3 paragraphs maximum
        - Uses the user's name appropriately

        Letter:
        """
        
        try:
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=400,
                temperature=0.6  # Slightly higher for more natural variation
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"AI personalization failed: {e}")
            # Fallback to simple template substitution
            return template.format(
                bill_title=bill_title,
                reasons=reasons,
                custom_message=custom_message,
                user_name=user_name
            )
    
    def _get_fallback_template(self, stance: str) -> str:
        """Fallback templates if none exist in database"""
        templates = {
            "support": "Dear Representative,\n\nI am writing to express my strong support for {bill_title}. {reasons}\n\n{custom_message}\n\nI respectfully urge you to vote in favor of this important legislation.\n\nSincerely,\n{user_name}",
            "oppose": "Dear Representative,\n\nI am writing to express my concerns about {bill_title}. {reasons}\n\n{custom_message}\n\nI respectfully urge you to vote against this legislation.\n\nSincerely,\n{user_name}",
            "amend": "Dear Representative,\n\nI am writing about {bill_title}. While I see merit in addressing this issue, I believe amendments are needed. {reasons}\n\n{custom_message}\n\nI urge you to work toward improving this legislation.\n\nSincerely,\n{user_name}"
        }
        return templates.get(stance, templates["support"])
    
    def _generate_fallback_message(
        self, bill: dict, stance: str, reasons: List[str], custom_message: str, user_info: dict
    ) -> str:
        """Generate a basic message if AI fails"""
        template = self._get_fallback_template(stance)
        reasons_text = self._format_reasons_for_message(reasons, stance)
        
        return template.format(
            bill_title=bill.get('title', 'this legislation'),
            reasons=reasons_text,
            custom_message=custom_message,
            user_name=f"{user_info.get('first_name', '')} {user_info.get('last_name', '')}".strip()
        )
```

### 2.4 API Endpoints

**Add to your FastAPI app (`api/actions.py`):**

```python
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from services.officials_service import OfficialsService
from services.action_network_service import ActionNetworkService
from services.message_personalization_service import MessagePersonalizationService
from models import Bill, UserAction, db
from auth import get_current_user

router = APIRouter(prefix="/api")

class ActionRequest(BaseModel):
    bill_id: str
    stance: str  # "support", "oppose", "amend"
    selected_reasons: List[str]
    custom_message: Optional[str] = ""
    zip_code: str
    address: Optional[str] = ""
    city: Optional[str] = ""
    state: Optional[str] = ""

@router.get("/bills/{bill_id}/action-data")
async def get_bill_action_data(bill_id: str):
    """Get all data needed for the action modal"""
    bill = await Bill.query.filter_by(id=bill_id).first()
    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")
    
    return {
        "bill_id": bill.id,
        "title": bill.title,
        "ai_summary": bill.ai_summary,
        "support_reasons": bill.support_reasons or [],
        "oppose_reasons": bill.oppose_reasons or [],
        "amend_reasons": bill.amend_reasons or [],
        "tags": bill.ai_tags or []
    }

@router.post("/actions/submit")
async def submit_action(
    action_request: ActionRequest, 
    current_user = Depends(get_current_user)
):
    """Submit user action with personalized message"""
    try:
        # 1. Get the bill data
        bill = await Bill.query.filter_by(id=action_request.bill_id).first()
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")
        
        # 2. Get user's representatives
        officials_service = OfficialsService()
        officials = await officials_service.get_federal_officials_by_zip(action_request.zip_code)
        
        if not officials:
            return {
                "success": False,
                "error": "Unable to find representatives for this ZIP code. Please verify your ZIP code and try again."
            }
        
        # 3. Generate personalized message
        personalization_service = MessagePersonalizationService()
        personalized_message = await personalization_service.generate_personalized_message(
            bill={
                'title': bill.title,
                'message_templates': bill.message_templates
            },
            user_stance=action_request.stance,
            selected_reasons=action_request.selected_reasons,
            custom_message=action_request.custom_message,
            user_info={
                'first_name': current_user.first_name,
                'last_name': current_user.last_name,
                'email': current_user.email,
            }
        )
        
        # 4. Submit via Action Network
        action_network = ActionNetworkService()
        result = await action_network.submit_message_to_officials(
            user_message=personalized_message,
            user_info={
                'first_name': current_user.first_name,
                'last_name': current_user.last_name,
                'email': current_user.email,
                'zip_code': action_request.zip_code,
                'address': action_request.address,
                'city': action_request.city,
                'state': action_request.state,
                'subject': f"Regarding {bill.title}"
            },
            target_officials=officials
        )
        
        # 5. Log the action in our database
        if result['success']:
            user_action = UserAction(
                user_id=current_user.id,
                bill_id=action_request.bill_id,
                action_type='message_sent',
                stance=action_request.stance,
                selected_reasons=action_request.selected_reasons,
                custom_message=action_request.custom_message,
                final_message_content=personalized_message,
                officials_contacted=[{
                    'id': official['id'],
                    'name': official['full_name'],
                    'chamber': official['chamber'],
                    'party': official['party']
                } for official in officials],
                action_network_id=result.get('action_network_id')
            )
            
            db.session.add(user_action)
            await db.session.commit()
        
        return {
            **result,
            "personalized_message": personalized_message,
            "officials": officials
        }
        
    except Exception as e:
        logger.error(f"Action submission failed: {e}")
        return {
            "success": False,
            "error": "Failed to submit action. Please try again."
        }

@router.get("/officials/{zip_code}")
async def get_officials_by_zip(zip_code: str):
    """Get representatives for a ZIP code (for preview)"""
    officials_service = OfficialsService()
    officials = await officials_service.get_federal_officials_by_zip(zip_code)
    return {"officials": officials}
```

### 2.5 Updated Database Schema

**Execute this migration:**

```sql
-- Enhanced user actions table
CREATE TABLE user_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    bill_id UUID NOT NULL REFERENCES bills(id),
    action_type VARCHAR(50) NOT NULL DEFAULT 'message_sent',
    stance VARCHAR(20) NOT NULL, -- 'support', 'oppose', 'amend'
    selected_reasons JSONB, -- Array of reason strings user selected
    custom_message TEXT, -- User's additional personal message
    final_message_content TEXT, -- The AI-generated final message sent
    officials_contacted JSONB, -- Array of official objects contacted
    action_network_id VARCHAR(255), -- Action Network submission ID
    created_at TIMESTAMP DEFAULT NOW(),
    
    INDEX idx_user_actions_user_id (user_id),
    INDEX idx_user_actions_bill_id (bill_id),
    INDEX idx_user_actions_stance (stance),
    INDEX idx_user_actions_created_at (created_at)
);

-- Analytics for tracking reason popularity
CREATE TABLE reason_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID NOT NULL REFERENCES bills(id),
    reason_text TEXT NOT NULL,
    stance VARCHAR(20) NOT NULL,
    selection_count INTEGER DEFAULT 1,
    last_selected_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(bill_id, reason_text, stance),
    INDEX idx_reason_analytics_bill_stance (bill_id, stance)
);
```

### 2.6 Environment Variables for Action Network

Add to your `.env`:

```bash
# Action Network API (get from actionnetwork.org after signup)
ACTION_NETWORK_API_KEY=your_action_network_api_key_here

# OpenStates API (free, get from openstates.org/api/register/)
OPENSTATES_API_KEY=your_openstates_api_key_here
```

## Implementation Timeline

**Week 1: AI Bill Processing**
- Set up Congress.gov API integration
- Implement AI service with all prompt templates
- Create bill data service and seed.py tool
- Test with real bills (HR5, S1, etc.)

**Week 2: Action Network Integration** 
- Set up Action Network account and API
- Implement officials lookup service
- Build message personalization service
- Create API endpoints

**Week 3: Frontend Integration & Testing**
- Build frontend action modal components
- Implement complete user flow
- Perform "Live Fire" testing
- Launch MVP

Your devs now have everything they need to build both features!