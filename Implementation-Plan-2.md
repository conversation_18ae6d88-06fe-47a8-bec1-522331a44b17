ModernAction.io: Master Implementation Plan & Developer Handover (v5.0 - Definitive Edition)
Version: 5.0
Status: "Make It Real" Phase Initiated
Lead: Lead Technical Advisor
A Note to Our Developers: The ModernAction.io Way
This document is your guide. It is intentionally verbose and prescriptive to ensure clarity and minimize ambiguity. Your primary directive is to build with quality and document your work.
Documentation is Not Optional: After completing each step, you must update our project documentation. Explain the "why" behind your implementation choices. If you deviate from this plan for a good reason, document it.
Testing is Paramount: After each logical feature is complete, you must write and run end-to-end tests. We are using a test-driven mindset. If a test fails, do not proceed.
Part 1: Executive Summary & The "Make It Real" Master Plan
Executive Summary
We have successfully built a world-class, production-ready "factory" (the AWS infrastructure). The core API framework is stable, the deployment mechanisms are in place, and the underlying architecture is sound. This is a monumental achievement.
However, a brutally honest assessment of the application itself reveals that while the factory is perfect, the "product" is non-functional. We have a beautiful car chassis with no engine. The platform lacks the core data integrations and action logic required to fulfill its mission.
The Decision: We are officially initiating the final, multi-sprint push to build the real MVP. We will not be distracted by minor issues. Our focus is now exclusively on the critical, user-facing functionality required for launch.
The Non-Negotiable "Make It Real" Master Plan
This plan is broken into three distinct, sequential sprints. Each sprint builds upon the last. We will not proceed to the next sprint until the current one is 100% complete and validated.
Sprint A: The Data Engine (1-2 Weeks)
Goal: To build a robust, automated data pipeline that populates our platform with real, live, and comprehensive legislative information.
Task 1: Build the Authoritative Bill Ingestion Service.
Action: Create a new, dedicated service in the backend, BillDataService.
Integration: This service will use the Congress.gov API as its primary source.
Functionality: It will have a core function, ingest_bill(bill_number, congress_session), which does the following:
Calls the Congress.gov API to get the bill's official title, summary, sponsors, and latest actions.
Scrapes the full text of the bill from the official URL provided by the API.
Saves the structured, factual data to our bills table.
Task 2: Implement the AI Intelligence Layer.
Action: The ingest_bill function will also:
Pass the scraped full text to our AI Summarization service to generate a clear, "plain English" summary.
Perform three separate AI calls to a powerful language model with engineered prompts:
Prompt 1: "Based on the text of this bill, provide a neutral, bulleted list of the main arguments in favor of this legislation." (Populates reasons_for_support field).
Prompt 2: "Based on the text of this bill, provide a neutral, bulleted list of the main arguments against this legislation." (Populates reasons_for_opposition field).
Prompt 3: "Analyze the text of this bill and generate 3-5 relevant, non-partisan tags." (Populates tags field).
Task 3: Create the Administrative Seeding Tool.
Action: Create a final, polished command-line script, seed.py.
Functionality: This script will allow an administrator to easily add new, fully processed bills to the platform. Example usage: python seed.py --bill HR5 --session 118.
Acceptance Criteria for Sprint A: A developer must be able to run python seed.py --bill HR5 --session 118, and a new campaign for the "Equality Act" must appear in the database, complete with a real AI summary, reasons for support/opposition, and relevant tags.
Sprint B: The Action Engine (1 Week)
Goal: To build a fully functional, end-to-end user action loop, connecting citizens to their real representatives.
Task 1: Fix the Officials Lookup.
Action: Create the OfficialService as previously designed.
Integration: It must use the Google Civic Information API.
Functionality: The get_officials_by_zip function must have the "get from database or create via API" caching logic. This is a critical performance and cost-saving feature.
Task 2: The "Live Fire" Test and Fix.
Action: With the Officials Lookup working, a developer must perform a full, manual end-to-end test of the entire action loop.
Process: Log in -> Find a real campaign (seeded in Sprint A) -> Enter a real zip code -> Verify real officials appear -> Send a real Email and a real Tweet.
Debug and Fix: Systematically debug and fix every single blocker that arises (e.g., IAM permissions for SES, Twitter API authentication, message formatting) until this flow works perfectly and verifiably.
Acceptance Criteria for Sprint B: A developer must be able to successfully complete the entire "Live Fire" test, with a real email arriving in an inbox and a real tweet being posted to the project's Twitter account.
Sprint C: The User Experience (1-2 Weeks)
Goal: To build the final UI components that present our rich data to the user in a clear and compelling way.
Task 1: Build the Bill Detail UI.
Action: The /campaigns/[id] page must be updated to display all the new, rich data we are ingesting.
Components: Create new React components to display:
The bill's current status (with a visual timeline of the stages).
The AI-generated "plain English" summary.
A two-column layout showing "Reasons to Support" and "Reasons to Oppose."
A "What this means for you" or "Why you should care" section (this can be manually written for the MVP campaigns).
The list of tags.
Task 2: Implement the AI Personalization Flow.
Action: The ActionModal UI must be updated.
Functionality:
Add three buttons: "Support," "Oppose," and "Amend."
When a user clicks one, it sets their "stance."
The user can then type their personal feedback into a text box.
This stance and feedback are sent to our AI personalization endpoint.
The AI generates a formal message reflecting the user's stance and feedback, which then populates the final message body.
Acceptance Criteria for Sprint C: The staging site must reflect a complete, data-rich, and interactive user experience as designed. A full UAT must be performed on all new UI components and the AI personalization flow.
Part 2: Technical Documentation & Project History
This section serves as the comprehensive technical handover document, detailing the project's current state, architecture, and the journey taken to get here.
1. Architecture & Infrastructure
High-Level Architecture
Generated code
Internet → ALB → ECS Fargate Tasks → RDS PostgreSQL
                ↓
            ECR Container Registry
Use code with caution.
AWS Resources (Staging Environment)
ALB URL: http://modern-ApiSe-5h6OCm8WlBhL-318015605.us-east-1.elb.amazonaws.com
ECS Cluster: modernaction-staging
Web Service: modernaction-web-staging
API Service: modernaction-api-staging
ECR Repositories: modernaction-web-staging, modernaction-api-staging, modernaction-worker-staging
Database: AWS RDS PostgreSQL 15 (db.t3.micro) in a private subnet.
Networking: Custom VPC with public/private subnets across 2 AZs.
Background Jobs: AWS Lambda functions and SQS queues for asynchronous tasks.
Secrets: AWS Secrets Manager for all sensitive credentials.
Container Architecture
API Service (modernaction-api): Lightweight FastAPI service for synchronous web requests. Intentionally built without heavy ML libraries for performance and cost-effectiveness.
Web Frontend (modernaction-web): Next.js application served via a Fargate task, utilizing server-side rendering.
AI Worker (modernaction-worker): Heavyweight container with full ML dependencies (transformers, torch) designed for one-off, resource-intensive administrative tasks like database seeding and AI analysis.
2. Development Environment Setup
Refer to apps/api/README.md for a complete, authoritative guide.
Prerequisites
Docker Desktop, Node.js 18+, Python 3.11+, AWS CLI, Git
Local Database Setup (PostgreSQL)
Generated bash
# Use Docker PostgreSQL (NEVER SQLite for PostgreSQL projects)
docker run --name modernaction-postgres \
  -e POSTGRES_DB=modernaction \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  -d postgres:15
Use code with caution.
Bash
3. Deployment Process
Current Deployment Method: Manual, CLI-driven process. To be replaced by an automated CI/CD pipeline.
Code Push: Push changes to the main branch on GitHub.
Build Docker Images: Build cross-platform (linux/amd64) images for the changed services.
Push to ECR: Tag and push the new images to the AWS Elastic Container Registry.
Deploy Infrastructure (if needed): Run cdk deploy from the infrastructure/ directory to apply any changes.
Update ECS Service: Force a new deployment of the relevant ECS service to pull the new container image.
4. Project History & Key Decisions Log
Initial Sprints (1-4): Foundation & Scaffolding
Successfully established a monorepo, scalable FastAPI backend structure, and a modular Next.js frontend. A solid service layer and Pydantic schemas were implemented.
Sprint 5: AI Integration
Decision: A singleton pattern (@lru_cache) was implemented for loading ML models to prevent reloading heavy models on every API call, drastically improving performance.
Decision: All AI summarization for new bills was moved to an asynchronous background task to keep the user-facing API fast and responsive.
Sprint 6-10: The "Deployment Gauntlet"
Challenge: The initial test suite was found to be brittle and unreliable due to a critical dependency on SQLite, which was incompatible with PostgreSQL-specific features (ARRAY, JSONB).
Solution: A mandatory remediation phase was executed. The entire backend test suite was migrated to run against a containerized PostgreSQL database, and perfect transaction-based test isolation was implemented, resulting in a 100% green build (176/176 tests passing).
Challenge: Cross-platform Docker builds from an Apple Silicon (ARM64) laptop to a Linux (AMD64) server environment were failing due to a native binary dependency in Tailwind CSS v4 (lightningcss).
Solution: The project was professionally downgraded to the stable Tailwind CSS v3, which uses a pure JavaScript toolchain, resolving all build failures.
Challenge: The live application was crashing due to a server-side rendering bug in a Next.js component.
Solution: A "debug deployment" was used to get the real error message from CloudWatch logs. The bug was identified as a field name mismatch (target_actions vs. goal_actions) and a lack of null safety. The code was fixed, a clean build was performed, and the final image was deployed successfully.
Sprint 11: Final Functionality Push
Challenge: The core authentication flow was broken, and the database was empty.
Solution: A comprehensive Auth0 integration using the secure @auth0/nextjs-auth0 SDK was implemented. A robust seeding script was created and executed to populate the database with real campaign and official data, making the app functional for the first time. The final live UAT was performed to validate the complete user journey.