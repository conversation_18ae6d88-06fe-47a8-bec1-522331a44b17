# Live Fire Test Setup Guide

This guide provides step-by-step instructions for configuring the API keys and deploying the final configuration for the live fire UAT.

## Step 1: Obtain API Keys

### 1.1 Google Civic Information API Key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Civic Information API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Civic Information API"
   - Click "Enable"
4. Create credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "API Key"
   - Copy the API key (it will look like: `AIzaSyBvOiM9OiO-HHuuFRLu7msbVJ_3JmzQlAc`)
5. (Optional) Restrict the API key:
   - Click on the API key to edit it
   - Under "API restrictions", select "Restrict key"
   - Choose "Google Civic Information API"
   - Save

### 1.2 Congress.gov API Key

1. Go to [https://api.congress.gov/sign-up/](https://api.congress.gov/sign-up/)
2. Fill out the registration form:
   - Name: Your name
   - Email: Your email address
   - Organization: ModernAction.io
   - Intended Use: Civic engagement platform for legislative information
3. Submit the form
4. Check your email for the API key (it will look like: `abc123def456ghi789jkl012`)
5. The API key is free and has generous rate limits

## Step 2: Update AWS Secrets Manager

### 2.1 Access AWS Secrets Manager

```bash
# Make sure you're authenticated with AWS CLI
aws sts get-caller-identity

# Navigate to the infrastructure directory
cd infrastructure
```

### 2.2 Update the API Keys Secret

1. Find the API Keys secret ARN:
```bash
# Get the secret ARN for staging environment
aws secretsmanager list-secrets --query "SecretList[?contains(Name, 'ApiKeysSecret') && contains(Name, 'staging')].[Name,ARN]" --output table
```

2. Update the secret with your new API keys:
```bash
# Replace YOUR_GOOGLE_CIVIC_KEY and YOUR_CONGRESS_GOV_KEY with actual keys
aws secretsmanager update-secret \
  --secret-id "arn:aws:secretsmanager:us-east-1:ACCOUNT:secret:ModernAction-staging-ApiKeysSecret-XXXXX" \
  --secret-string '{
    "OPENSTATES_API_KEY": "",
    "GOOGLE_CIVIC_INFO_API_KEY": "YOUR_GOOGLE_CIVIC_KEY",
    "CONGRESS_GOV_API_KEY": "YOUR_CONGRESS_GOV_KEY", 
    "HUGGING_FACE_API_KEY": ""
  }'
```

### 2.3 Verify the Secret Update

```bash
# Verify the secret was updated (this will show the keys are set but not their values)
aws secretsmanager describe-secret --secret-id "arn:aws:secretsmanager:us-east-1:ACCOUNT:secret:ModernAction-staging-ApiKeysSecret-XXXXX"
```

## Step 3: Deploy Infrastructure Changes

### 3.1 Deploy CDK Changes

```bash
# Make sure you're in the infrastructure directory
cd infrastructure

# Install dependencies if needed
pip install -r requirements.txt

# Deploy the changes to staging
cdk deploy ModernAction-staging --require-approval never
```

### 3.2 Force Service Restart

The CDK deployment will automatically restart the services, but you can force a restart if needed:

```bash
# Force restart of API service to pick up new environment variables
aws ecs update-service \
  --cluster modernaction-staging \
  --service modernaction-api-staging \
  --force-new-deployment

# Wait for deployment to complete
aws ecs wait services-stable \
  --cluster modernaction-staging \
  --services modernaction-api-staging
```

## Step 4: Verify Configuration

### 4.1 Check Service Health

```bash
# Check that the API service is running with new configuration
aws ecs describe-services \
  --cluster modernaction-staging \
  --services modernaction-api-staging \
  --query "services[0].{Status:status,Running:runningCount,Desired:desiredCount}"
```

### 4.2 Test API Endpoints

```bash
# Get the load balancer URL
ALB_URL=$(aws elbv2 describe-load-balancers \
  --query "LoadBalancers[?contains(LoadBalancerName, 'modernaction-staging')].DNSName" \
  --output text)

# Test the health endpoint
curl "http://$ALB_URL/api/v1/health"

# Test the Google Civic API integration
curl "http://$ALB_URL/api/v1/officials/admin/test-google-civic?zip_code=90210"

# Test the Congress.gov API integration  
curl -X POST "http://$ALB_URL/api/v1/bills/admin/seed-real-bills" \
  -H "Content-Type: application/json" \
  -d '{"bill_numbers": ["H.R.5"], "limit": 1}'
```

## Step 5: Ready for Live Fire Test

Once all the above steps are complete and the API endpoints are responding correctly, you're ready to proceed to:

1. **Step 2: Purge and Re-seed Database**
2. **Step 3: Live Fire UAT**

## Troubleshooting

### Common Issues

1. **Secret not found**: Make sure you're using the correct secret ARN for the staging environment
2. **Permission denied**: Ensure your AWS credentials have permissions to update Secrets Manager
3. **Service not restarting**: Check CloudWatch logs for the ECS service
4. **API keys not working**: Verify the keys are correct and have proper permissions

### Useful Commands

```bash
# Check ECS service logs
aws logs tail /ecs/api --follow

# Check current secret values (without revealing them)
aws secretsmanager get-secret-value --secret-id "SECRET_ARN" --query "SecretString" --output text | jq keys

# List all secrets
aws secretsmanager list-secrets --query "SecretList[].Name"
```

## Security Notes

- Never commit API keys to version control
- Use AWS Secrets Manager for all sensitive configuration
- Rotate API keys regularly
- Monitor API usage and set up alerts for unusual activity

---

**Next Steps**: Once this configuration is complete, proceed to the database purge and re-seeding phase.
