# Live Fire UAT - Complete End-to-End Test

This is the **FINAL VALIDATION** for the ModernAction.io MVP. Every checkbox must be completed successfully for launch approval.

## Prerequisites

- [ ] ✅ Step 1 completed: API keys configured and CDK deployed
- [ ] ✅ Step 2 completed: Database purged and re-seeded with real bills
- [ ] ✅ All services healthy and running in staging environment

## UAT Environment

- **URL**: https://staging.modernaction.io
- **Environment**: staging
- **Test User**: Create a test account or use existing Auth0 test user
- **Recording**: 📹 **RECORD THE ENTIRE UAT SESSION** for documentation

## The UAT Checklist

### Phase 1: Authentication & Content Verification

#### 1.1 Login Flow
- [ ] Navigate to https://staging.modernaction.io
- [ ] Click "Login" or "Sign Up"
- [ ] Successfully authenticate via Auth0
- [ ] Redirected to dashboard/campaigns page
- [ ] User profile/avatar visible in navigation

**Expected Result**: Successful authentication with no errors

#### 1.2 Content Verification  
- [ ] Navigate to `/campaigns` page
- [ ] Verify you see **REAL campaigns** (not placeholder data):
  - [ ] "Support H.R.5: Equality Act" 
  - [ ] "Support H.R.8: Bipartisan Background Checks Act"
  - [ ] "Support S.1: For the People Act"
- [ ] Click on one campaign to view details
- [ ] Verify campaign shows **real bill information**:
  - [ ] Correct bill number (H.R.5, H.R.8, or S.1)
  - [ ] Real bill title from Congress.gov
  - [ ] Proper bill status and metadata

**Expected Result**: Real federal legislation displayed, no "S-3456" placeholder data

### Phase 2: Core Action Loop - The Critical Test

#### 2.1 Initiate Action
- [ ] On a campaign detail page, click **"Act Now"** button
- [ ] Action modal opens successfully
- [ ] Modal displays campaign information correctly

#### 2.2 Officials Lookup (CRITICAL)
- [ ] Enter zip code: **90210** (Beverly Hills, CA)
- [ ] Click "Find My Representatives" or equivalent
- [ ] Wait for officials to load
- [ ] Verify **REAL officials** are displayed:
  - [ ] Representative names are real (not placeholder)
  - [ ] Correct party affiliations (D/R)
  - [ ] Real contact information (email/phone)
  - [ ] Proper titles (Representative, Senator)

**Expected Result**: Real elected officials from Google Civic API, specific to zip code 90210

#### 2.3 Message Personalization
- [ ] Select stance: **Support** (or test other options)
- [ ] Enter personal message in text area
- [ ] Verify AI personalization works (if implemented)
- [ ] Review the generated email content
- [ ] Verify email mentions correct bill number and title

#### 2.4 Social Media Toggle
- [ ] Enable **"Post a Tweet"** toggle
- [ ] Verify Twitter option is available and functional
- [ ] Review the tweet content preview

### Phase 3: Action Execution (THE MOMENT OF TRUTH)

#### 3.1 Send the Action
- [ ] Click **"Send Message"** button
- [ ] Confirm any confirmation dialogs
- [ ] Wait for success message
- [ ] Note the timestamp: ________________

**Expected Result**: Success message indicating email sent and tweet posted

### Phase 4: Output Verification (CRITICAL VALIDATION)

#### 4.1 Email Verification
- [ ] Check the email inbox for the test user
- [ ] Verify **REAL EMAIL** was sent:
  - [ ] Email received within 2 minutes
  - [ ] Correct recipient (official's real email)
  - [ ] Correct subject line mentioning real bill
  - [ ] Email body contains real bill information
  - [ ] User's personal message included
  - [ ] Professional formatting and tone

#### 4.2 Twitter Verification  
- [ ] Check the project's Twitter account (@ModernActionIO or equivalent)
- [ ] Verify **REAL TWEET** was posted:
  - [ ] Tweet posted within 2 minutes
  - [ ] Mentions correct official's Twitter handle
  - [ ] References real bill number and title
  - [ ] Appropriate hashtags and formatting
  - [ ] Professional tone and content

#### 4.3 Database Verification
- [ ] Check that action was recorded in database:
  - [ ] New action record created
  - [ ] Linked to correct user, campaign, and officials
  - [ ] Timestamp matches action execution
  - [ ] Status shows as completed

### Phase 5: Additional Validation Tests

#### 5.1 Different Zip Code Test
- [ ] Test with different zip code: **10001** (New York, NY)
- [ ] Verify different officials are returned
- [ ] Confirm officials are appropriate for NYC area

#### 5.2 Error Handling
- [ ] Test with invalid zip code: **00000**
- [ ] Verify appropriate error message
- [ ] Test with no internet connection (if possible)
- [ ] Verify graceful degradation

#### 5.3 Performance Test
- [ ] Measure page load times
- [ ] Officials lookup completes in < 5 seconds
- [ ] Action submission completes in < 10 seconds
- [ ] No browser console errors

## Final Acceptance Criteria

### ✅ PASS Criteria (ALL must be checked)

- [ ] **Authentication**: User can log in successfully
- [ ] **Real Content**: Campaigns show real federal bills (H.R.5, H.R.8, S.1)
- [ ] **Officials Lookup**: Real representatives returned for zip codes
- [ ] **Email Delivery**: Real email sent to official's actual email address
- [ ] **Twitter Posting**: Real tweet posted to project's Twitter account
- [ ] **Data Integrity**: Actions recorded correctly in database
- [ ] **Performance**: All operations complete within acceptable timeframes
- [ ] **Error Handling**: Graceful handling of edge cases

### ❌ FAIL Criteria (ANY of these = FAIL)

- [ ] Authentication fails or redirects incorrectly
- [ ] Placeholder data (S-3456) still visible anywhere
- [ ] Officials lookup returns fake/placeholder officials
- [ ] No email sent or email contains placeholder data
- [ ] No tweet posted or tweet contains placeholder data
- [ ] Database errors or data corruption
- [ ] Performance issues (>30 second load times)
- [ ] Unhandled errors or crashes

## UAT Results

### Test Execution Details
- **Date**: ________________
- **Time**: ________________  
- **Tester**: ________________
- **Environment**: staging.modernaction.io
- **Browser**: ________________
- **Recording**: ________________

### Test Results Summary
- **Total Checkboxes**: _____ / _____
- **Pass Rate**: _____%
- **Critical Issues**: ________________
- **Minor Issues**: ________________

### Final Decision

**🎯 LAUNCH DECISION**: 

- [ ] ✅ **APPROVED FOR LAUNCH** - All critical tests passed
- [ ] ❌ **NOT APPROVED** - Critical issues found (list below)

### Issues Found (if any)
1. ________________
2. ________________
3. ________________

### Next Steps
- [ ] If APPROVED: Proceed with production deployment
- [ ] If NOT APPROVED: Address issues and re-run UAT

---

## 🚀 LAUNCH READINESS STATEMENT

*"I certify that the ModernAction.io MVP has successfully completed the Live Fire UAT. The core product loop (zip code → real officials → real email/tweet delivery) is fully functional with real data sources. The application is ready for public launch."*

**Signature**: ________________  
**Date**: ________________

---

**This completes the final validation phase. The MVP is now ready for launch! 🎉**
