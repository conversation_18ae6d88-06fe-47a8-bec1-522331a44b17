[Fast Refresh] done in NaNms
scheduler.development.js:13 [Violation] 'message' handler took 198ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 150ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 194ms
page.tsx:50 📍 Step changed to: stance
page.tsx:50 📍 Step changed to: stance
page.tsx:50 📍 Step changed to: reasons
page.tsx:140 🔄 handleNextStep called, current step: reasons
page.tsx:147 🔄 Moving from reasons → contact
page.tsx:50 📍 Step changed to: contact
page.tsx:140 🔄 handleNextStep called, current step: contact
page.tsx:151 🔄 Moving from contact → ai_generation
page.tsx:50 📍 Step changed to: ai_generation
page.tsx:299 Sending preview request: {bill_id: 'c102ced4-9f90-45fc-ad61-d408d5b51665', stance: 'amend', selected_reasons: Array(4), custom_reasons: Array(0), personal_stories: '', …}
page.tsx:300 ZIP code being sent: 19146
page.tsx:301 Form data: {stance: 'amend', selected_reasons: Array(4), custom_reasons: Array(0), personal_stories: '', custom_message: '', …}
page.tsx:306 Direct officials lookup result: {status: 'success', zip_code: '19146', senators: Array(0), representative: {…}, total_representatives: 1, …}
page.tsx:314 Preview message result: {success: true, bill: {…}, representatives: Array(1), personalized_messages: Array(1), stance: 'amend', …}
page.tsx:315 Representatives in result: [{…}]
page.tsx:316 Personalized messages: [{…}]
page.tsx:330 🔄 Setting custom_message immediately after AI generation
page.tsx:341 🔄 custom_message set during AI generation step
page.tsx:140 🔄 handleNextStep called, current step: ai_generation
page.tsx:155 🔄 Moving from ai_generation → edit_and_send
page.tsx:1308 🎯 EditAndSendStep rendered
page.tsx:1308 🎯 EditAndSendStep rendered
page.tsx:1312 🎯 EditAndSendStep mounted/updated
page.tsx:50 📍 Step changed to: edit_and_send
page.tsx:58 🚨 BLOCKING: Setting step transition flag to prevent auto-submit
page.tsx:235 🚨 BLOCKED: setValue prevented on edit_and_send step to avoid auto-submission
page.tsx:1315 🎯 EditAndSendStep cleanup
page.tsx:1312 🎯 EditAndSendStep mounted/updated
page.tsx:542 🚨 Form onSubmit event triggered SyntheticBaseEvent {_reactName: 'onSubmit', _targetInst: null, type: 'submit', nativeEvent: SubmitEvent, target: form.bg-white.rounded-2xl.shadow-sm.border.border-gray-200.overflow-hidden, …}
page.tsx:543 Event target: <form class=​"bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">​…​</form>​
page.tsx:544 Current step: edit_and_send
page.tsx:545 Event type: submit
page.tsx:546 Event nativeEvent: SubmitEvent {isTrusted: true, submitter: button.btn-primary.px-8.bg-green-600.hover:bg-green-700, type: 'submit', target: form.bg-white.rounded-2xl.shadow-sm.border.border-gray-200.overflow-hidden, currentTarget: document, …}
page.tsx:547 🚨 Event isTrusted: true
page.tsx:548 🚨 Submitter element: <button type=​"submit" class=​"btn-primary px-8 bg-green-600 hover:​bg-green-700">​📤 Send Messages​</button>​
page.tsx:549 🚨 isStepTransitioning: false
page.tsx:550 🚨 Stack trace:
page.tsx:551 console.trace
onSubmit @ page.tsx:551
executeDispatch @ react-dom-client.development.js:16921
runWithFiberInDEV @ react-dom-client.development.js:872
processDispatchQueue @ react-dom-client.development.js:16971
(anonymous) @ react-dom-client.development.js:17572
batchedUpdates$1 @ react-dom-client.development.js:3312
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17125
dispatchEvent @ react-dom-client.development.js:21308
dispatchDiscreteEvent @ react-dom-client.development.js:21276
page.tsx:367 🚨 onFormSubmit called! Current step: edit_and_send
page.tsx:368 Form submit trace
onFormSubmit @ page.tsx:368
(anonymous) @ createFormControl.ts:1255
await in (anonymous)
onSubmit @ page.tsx:570
executeDispatch @ react-dom-client.development.js:16921
runWithFiberInDEV @ react-dom-client.development.js:872
processDispatchQueue @ react-dom-client.development.js:16971
(anonymous) @ react-dom-client.development.js:17572
batchedUpdates$1 @ react-dom-client.development.js:3312
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17125
dispatchEvent @ react-dom-client.development.js:21308
dispatchDiscreteEvent @ react-dom-client.development.js:21276
page.tsx:1308 🎯 EditAndSendStep rendered
page.tsx:1308 🎯 EditAndSendStep rendered
page.tsx:63 🚨 UNBLOCKING: Allowing form submissions after render complete
page.tsx:1308 🎯 EditAndSendStep rendered
page.tsx:1308 🎯 EditAndSendStep rendered
page.tsx:1308 🎯 EditAndSendStep rendered
page.tsx:1308 🎯 EditAndSendStep rendered
page.tsx:1315 🎯 EditAndSendStep cleanup
bills:1 Access to XMLHttpRequest at 'http://localhost:8000/api/v1/bills/?limit=20' from origin 'http://localhost:3000' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.Understand this error
page.tsx:32 Failed to load bills: AxiosError {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
error @ intercept-console-error.ts:44
loadBills @ page.tsx:32
await in loadBills
BillsPage.useEffect @ page.tsx:23
react_stack_bottom_frame @ react-dom-client.development.js:23637
runWithFiberInDEV @ react-dom-client.development.js:872
commitHookEffectListMount @ react-dom-client.development.js:12295
commitHookPassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:14337
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14349
flushPassiveEffects @ react-dom-client.development.js:16288
(anonymous) @ react-dom-client.development.js:15924
performWorkUntilDeadline @ scheduler.development.js:45
<BillsPage>
exports.jsx @ react-jsx-runtime.development.js:338
ClientPageRoot @ client-page.tsx:60
react_stack_bottom_frame @ react-dom-client.development.js:23552
renderWithHooksAgain @ react-dom-client.development.js:6863
renderWithHooks @ react-dom-client.development.js:6775
updateFunctionComponent @ react-dom-client.development.js:9069
beginWork @ react-dom-client.development.js:10628
runWithFiberInDEV @ react-dom-client.development.js:872
performUnitOfWork @ react-dom-client.development.js:15677
workLoopConcurrentByScheduler @ react-dom-client.development.js:15671
renderRootConcurrent @ react-dom-client.development.js:15646
performWorkOnRoot @ react-dom-client.development.js:14940
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16766
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
initializeElement @ react-server-dom-turbopack-client.browser.development.js:1199
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2823
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1102
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1077
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1900
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2657
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2599
processBinaryChunk @ react-server-dom-turbopack-client.browser.development.js:2726
progress @ react-server-dom-turbopack-client.browser.development.js:2990
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1863
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2851
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:3213
createFromNextReadableStream @ fetch-server-response.ts:402
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:217
runAction @ app-router-instance.ts:104
dispatchAction @ app-router-instance.ts:169
dispatch @ app-router-instance.ts:215
(anonymous) @ use-action-queue.ts:45
startTransition @ react-dom-client.development.js:7938
dispatch @ use-action-queue.ts:44
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:286
(anonymous) @ app-router-instance.ts:359
exports.startTransition @ react.development.js:1129
push @ app-router-instance.ts:358
(anonymous) @ page.tsx:418
setTimeout
onFormSubmit @ page.tsx:417
await in onFormSubmit
(anonymous) @ createFormControl.ts:1255
await in (anonymous)
onSubmit @ page.tsx:570
executeDispatch @ react-dom-client.development.js:16921
runWithFiberInDEV @ react-dom-client.development.js:872
processDispatchQueue @ react-dom-client.development.js:16971
(anonymous) @ react-dom-client.development.js:17572
batchedUpdates$1 @ react-dom-client.development.js:3312
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17125
dispatchEvent @ react-dom-client.development.js:21308
dispatchDiscreteEvent @ react-dom-client.development.js:21276Understand this error
apiClient.ts:184  GET http://localhost:8000/api/v1/bills/?limit=20 net::ERR_FAILED 500 (Internal Server Error)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getBills @ apiClient.ts:184
loadBills @ page.tsx:29
BillsPage.useEffect @ page.tsx:23
react_stack_bottom_frame @ react-dom-client.development.js:23637
runWithFiberInDEV @ react-dom-client.development.js:872
commitHookEffectListMount @ react-dom-client.development.js:12295
commitHookPassiveMountEffects @ react-dom-client.development.js:12416
commitPassiveMountOnFiber @ react-dom-client.development.js:14337
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14340
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14330
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14464
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:14310
commitPassiveMountOnFiber @ react-dom-client.development.js:14349
flushPassiveEffects @ react-dom-client.development.js:16288
(anonymous) @ react-dom-client.development.js:15924
performWorkUntilDeadline @ scheduler.development.js:45
<BillsPage>
exports.jsx @ react-jsx-runtime.development.js:338
ClientPageRoot @ client-page.tsx:60
react_stack_bottom_frame @ react-dom-client.development.js:23552
renderWithHooksAgain @ react-dom-client.development.js:6863
renderWithHooks @ react-dom-client.development.js:6775
updateFunctionComponent @ react-dom-client.development.js:9069
beginWork @ react-dom-client.development.js:10628
runWithFiberInDEV @ react-dom-client.development.js:872
performUnitOfWork @ react-dom-client.development.js:15677
workLoopConcurrentByScheduler @ react-dom-client.development.js:15671
renderRootConcurrent @ react-dom-client.development.js:15646
performWorkOnRoot @ react-dom-client.development.js:14940
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16766
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
initializeElement @ react-server-dom-turbopack-client.browser.development.js:1199
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2823
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1102
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1077
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1900
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2657
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2599
processBinaryChunk @ react-server-dom-turbopack-client.browser.development.js:2726
progress @ react-server-dom-turbopack-client.browser.development.js:2990
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1863
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2851
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:3213
createFromNextReadableStream @ fetch-server-response.ts:402
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:217
runAction @ app-router-instance.ts:104
dispatchAction @ app-router-instance.ts:169
dispatch @ app-router-instance.ts:215
(anonymous) @ use-action-queue.ts:45
startTransition @ react-dom-client.development.js:7938
dispatch @ use-action-queue.ts:44
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:286
(anonymous) @ app-router-instance.ts:359
exports.startTransition @ react.development.js:1129
push @ app-router-instance.ts:358
(anonymous) @ page.tsx:418
setTimeout
onFormSubmit @ page.tsx:417
await in onFormSubmit
(anonymous) @ createFormControl.ts:1255
await in (anonymous)
onSubmit @ page.tsx:570
executeDispatch @ react-dom-client.development.js:16921
runWithFiberInDEV @ react-dom-client.development.js:872
processDispatchQueue @ react-dom-client.development.js:16971
(anonymous) @ react-dom-client.development.js:17572
batchedUpdates$1 @ react-dom-client.development.js:3312
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17125
dispatchEvent @ react-dom-client.development.js:21308
dispatchDiscreteEvent @ react-dom-client.development.js:21276Understand this error
bills:1 Access to XMLHttpRequest at 'http://localhost:8000/api/v1/bills/?limit=20' from origin 'http://localhost:3000' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.Understand this error
page.tsx:32 Failed to load bills: AxiosError {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
error @ intercept-console-error.ts:44
loadBills @ page.tsx:32
await in loadBills
BillsPage.useEffect @ page.tsx:23
react_stack_bottom_frame @ react-dom-client.development.js:23637
runWithFiberInDEV @ react-dom-client.development.js:872
commitHookEffectListMount @ react-dom-client.development.js:12295
commitHookPassiveMountEffects @ react-dom-client.development.js:12416
reconnectPassiveEffects @ react-dom-client.development.js:14513
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16516
runWithFiberInDEV @ react-dom-client.development.js:872
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16480
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16525
flushPassiveEffects @ react-dom-client.development.js:16298
(anonymous) @ react-dom-client.development.js:15924
performWorkUntilDeadline @ scheduler.development.js:45
<BillsPage>
exports.jsx @ react-jsx-runtime.development.js:338
ClientPageRoot @ client-page.tsx:60
react_stack_bottom_frame @ react-dom-client.development.js:23552
renderWithHooksAgain @ react-dom-client.development.js:6863
renderWithHooks @ react-dom-client.development.js:6775
updateFunctionComponent @ react-dom-client.development.js:9069
beginWork @ react-dom-client.development.js:10628
runWithFiberInDEV @ react-dom-client.development.js:872
performUnitOfWork @ react-dom-client.development.js:15677
workLoopConcurrentByScheduler @ react-dom-client.development.js:15671
renderRootConcurrent @ react-dom-client.development.js:15646
performWorkOnRoot @ react-dom-client.development.js:14940
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16766
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
initializeElement @ react-server-dom-turbopack-client.browser.development.js:1199
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2823
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1102
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1077
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1900
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2657
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2599
processBinaryChunk @ react-server-dom-turbopack-client.browser.development.js:2726
progress @ react-server-dom-turbopack-client.browser.development.js:2990
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1863
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2851
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:3213
createFromNextReadableStream @ fetch-server-response.ts:402
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:217
runAction @ app-router-instance.ts:104
dispatchAction @ app-router-instance.ts:169
dispatch @ app-router-instance.ts:215
(anonymous) @ use-action-queue.ts:45
startTransition @ react-dom-client.development.js:7938
dispatch @ use-action-queue.ts:44
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:286
(anonymous) @ app-router-instance.ts:359
exports.startTransition @ react.development.js:1129
push @ app-router-instance.ts:358
(anonymous) @ page.tsx:418
setTimeout
onFormSubmit @ page.tsx:417
await in onFormSubmit
(anonymous) @ createFormControl.ts:1255
await in (anonymous)
onSubmit @ page.tsx:570
executeDispatch @ react-dom-client.development.js:16921
runWithFiberInDEV @ react-dom-client.development.js:872
processDispatchQueue @ react-dom-client.development.js:16971
(anonymous) @ react-dom-client.development.js:17572
batchedUpdates$1 @ react-dom-client.development.js:3312
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17125
dispatchEvent @ react-dom-client.development.js:21308
dispatchDiscreteEvent @ react-dom-client.development.js:21276Understand this error
apiClient.ts:184  GET http://localhost:8000/api/v1/bills/?limit=20 net::ERR_FAILED 500 (Internal Server Error)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getBills @ apiClient.ts:184
loadBills @ page.tsx:29
BillsPage.useEffect @ page.tsx:23
react_stack_bottom_frame @ react-dom-client.development.js:23637
runWithFiberInDEV @ react-dom-client.development.js:872
commitHookEffectListMount @ react-dom-client.development.js:12295
commitHookPassiveMountEffects @ react-dom-client.development.js:12416
reconnectPassiveEffects @ react-dom-client.development.js:14513
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14560
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14484
reconnectPassiveEffects @ react-dom-client.development.js:14506
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16516
runWithFiberInDEV @ react-dom-client.development.js:872
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16480
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16486
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16525
flushPassiveEffects @ react-dom-client.development.js:16298
(anonymous) @ react-dom-client.development.js:15924
performWorkUntilDeadline @ scheduler.development.js:45
<BillsPage>
exports.jsx @ react-jsx-runtime.development.js:338
ClientPageRoot @ client-page.tsx:60
react_stack_bottom_frame @ react-dom-client.development.js:23552
renderWithHooksAgain @ react-dom-client.development.js:6863
renderWithHooks @ react-dom-client.development.js:6775
updateFunctionComponent @ react-dom-client.development.js:9069
beginWork @ react-dom-client.development.js:10628
runWithFiberInDEV @ react-dom-client.development.js:872
performUnitOfWork @ react-dom-client.development.js:15677
workLoopConcurrentByScheduler @ react-dom-client.development.js:15671
renderRootConcurrent @ react-dom-client.development.js:15646
performWorkOnRoot @ react-dom-client.development.js:14940
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16766
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
initializeElement @ react-server-dom-turbopack-client.browser.development.js:1199
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2823
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1102
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1077
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1900
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2657
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2599
processBinaryChunk @ react-server-dom-turbopack-client.browser.development.js:2726
progress @ react-server-dom-turbopack-client.browser.development.js:2990
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1863
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2851
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:3213
createFromNextReadableStream @ fetch-server-response.ts:402
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:217
runAction @ app-router-instance.ts:104
dispatchAction @ app-router-instance.ts:169
dispatch @ app-router-instance.ts:215
(anonymous) @ use-action-queue.ts:45
startTransition @ react-dom-client.development.js:7938
dispatch @ use-action-queue.ts:44
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:286
(anonymous) @ app-router-instance.ts:359
exports.startTransition @ react.development.js:1129
push @ app-router-instance.ts:358
(anonymous) @ page.tsx:418
setTimeout
onFormSubmit @ page.tsx:417
await in onFormSubmit
(anonymous) @ createFormControl.ts:1255
await in (anonymous)
onSubmit @ page.tsx:570
executeDispatch @ react-dom-client.development.js:16921
runWithFiberInDEV @ react-dom-client.development.js:872
processDispatchQueue @ react-dom-client.development.js:16971
(anonymous) @ react-dom-client.development.js:17572
batchedUpdates$1 @ react-dom-client.development.js:3312
dispatchEventForPluginEventSystem @ react-dom-client.development.js:17125
dispatchEvent @ react-dom-client.development.js:21308
dispatchDiscreteEvent @ react-dom-client.development.js:21276Understand this error