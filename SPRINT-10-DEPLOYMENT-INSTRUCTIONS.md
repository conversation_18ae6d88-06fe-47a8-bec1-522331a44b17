# Sprint 10 Critical Networking Fix - Deployment Instructions

## Overview
This deployment fixes the critical VPC networking issue preventing web service from communicating with API service. The fixes address both probable root causes:

1. **Security Group Misconfiguration** - Added explicit rule for web→API communication
2. **Service Discovery** - Implemented CloudMap for reliable internal DNS resolution

## Pre-Deployment Checklist

- [ ] Ensure you're in the correct AWS account and region (us-east-2)
- [ ] Verify current staging environment is accessible
- [ ] Have AWS CLI configured with appropriate permissions
- [ ] Docker is running for container builds

## Step 1: Deploy Infrastructure Changes

```bash
# Navigate to infrastructure directory
cd infrastructure

# Deploy the CDK changes (this will update security groups and add service discovery)
cdk deploy ModernActionStack-staging --require-approval never

# Verify the deployment completed successfully
# Look for outputs showing the new service discovery namespace
```

## Step 2: Build and Push Updated Web Container

```bash
# Navigate to web app directory
cd ../apps/web

# Set your AWS account ID (replace with your actual account ID)
export AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
export AWS_REGION=us-east-2

# Build the updated web container with new API client logic
docker build -t modernaction-web:latest-amd64 .

# Login to ECR
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# Tag for ECR (staging environment)
docker tag modernaction-web:latest-amd64 $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/modernaction-web-staging:latest-amd64

# Push to ECR
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/modernaction-web-staging:latest-amd64
```

## Step 3: Force ECS Service Redeployment

```bash
# Force both services to redeploy with new configuration
aws ecs update-service \
    --cluster modernaction-staging \
    --service modernaction-web-staging \
    --force-new-deployment \
    --region us-east-2

aws ecs update-service \
    --cluster modernaction-staging \
    --service modernaction-api-staging \
    --force-new-deployment \
    --region us-east-2

# Wait for deployments to complete (5-10 minutes)
aws ecs wait services-stable \
    --cluster modernaction-staging \
    --services modernaction-web-staging modernaction-api-staging \
    --region us-east-2
```

## Step 4: Verify Service Discovery

```bash
# Check that the CloudMap namespace was created
aws servicediscovery list-namespaces --region us-east-2

# Check that the API service is registered
aws servicediscovery list-services --region us-east-2
```

## Step 5: Test the Fix

### Quick Smoke Test
```bash
# Test that the staging site loads
curl -I https://staging.modernaction.io

# Test that the API is accessible via ALB
curl https://staging.modernaction.io/api/v1/health
```

### Run E2E Tests
```bash
# Navigate to test directory
cd ../../tests/e2e

# Run the full E2E test suite against staging
npm test -- --config=staging

# Target: 95%+ success rate (previously 0/36 passed)
```

### Run Load Tests
```bash
# Navigate to load test directory
cd ../load

# Run the web scenario load test
k6 run --env ENVIRONMENT=staging website-browse-scenario.js

# Target: 95%+ success rate (previously 15-23%)
```

## Expected Results

After successful deployment:

1. **E2E Tests**: Should achieve 95%+ pass rate (up from 0%)
2. **Load Tests**: Web scenarios should achieve 95%+ success rate
3. **Internal Communication**: Web container can reach API via `api.staging.local:8000`
4. **External Access**: Browser requests work via `https://staging.modernaction.io/api/v1`

## Troubleshooting

### If E2E tests still fail:
1. Check ECS service logs for connection errors
2. Verify security group rules are applied
3. Test internal DNS resolution from web container

### If service discovery doesn't work:
1. Verify CloudMap namespace exists
2. Check that API service is registered in service discovery
3. Test DNS resolution: `nslookup api.staging.local` from web container

### Emergency Rollback
If issues occur, rollback by reverting the CDK changes:
```bash
git checkout HEAD~1 infrastructure/modernaction/modernaction_stack.py
cdk deploy ModernActionStack-staging --require-approval never
```

## Success Criteria for Sprint 10 Completion

- [ ] E2E test suite: ≥95% pass rate
- [ ] Load test web scenarios: ≥95% success rate  
- [ ] Internal API communication working via service discovery
- [ ] External API access working via ALB
- [ ] No regression in API-only load test performance

Once these criteria are met, Sprint 10 is officially complete and approved for launch.
