#!/usr/bin/env python3
"""
Apply Auth0 migration directly to staging database
"""
import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def apply_migration():
    """Apply the Auth0 migration"""
    
    # Database connection parameters
    db_host = os.environ.get('DB_HOST', 'modernaction-staging.cx0ko0uwy9ma.us-east-2.rds.amazonaws.com')
    db_name = os.environ.get('DB_NAME', 'modernaction_staging')
    db_user = os.environ.get('DB_USERNAME', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'M0dernAct10n_St4g3_2025')
    db_port = os.environ.get('DB_PORT', '5432')
    
    try:
        # Connect to database
        conn = psycopg2.connect(
            host=db_host,
            database=db_name,
            user=db_user,
            password=db_password,
            port=db_port
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        print("Connected to database successfully")
        
        # Check if migration already applied
        cursor.execute("SELECT version_num FROM alembic_version WHERE version_num = '004_add_auth0_integration'")
        if cursor.fetchone():
            print("Migration 004_add_auth0_integration already applied")
            return True
        
        print("Applying Auth0 migration...")
        
        # Apply migration SQL
        migration_sql = """
        -- Add Auth0 integration columns to users table
        ALTER TABLE users ADD COLUMN auth0_user_id VARCHAR;
        ALTER TABLE users ADD COLUMN name VARCHAR;
        ALTER TABLE users ADD COLUMN picture_url VARCHAR;
        ALTER TABLE users ADD COLUMN email_verified BOOLEAN NOT NULL DEFAULT false;
        
        -- Create index on auth0_user_id for fast lookups
        CREATE UNIQUE INDEX ix_users_auth0_user_id ON users (auth0_user_id);
        
        -- Make legacy fields nullable since we're using Auth0
        ALTER TABLE users ALTER COLUMN hashed_password DROP NOT NULL;
        ALTER TABLE users ALTER COLUMN first_name DROP NOT NULL;
        ALTER TABLE users ALTER COLUMN last_name DROP NOT NULL;
        
        -- Update alembic version table
        UPDATE alembic_version SET version_num = '004_add_auth0_integration';
        """
        
        cursor.execute(migration_sql)
        print("✅ Migration applied successfully!")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error applying migration: {e}")
        return False

if __name__ == "__main__":
    success = apply_migration()
    sys.exit(0 if success else 1)
