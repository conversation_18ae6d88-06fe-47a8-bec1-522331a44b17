# Database Enhancement Plan for Action Tracking

## Overview
This plan enhances our existing database to provide comprehensive action tracking, user reasoning analysis, security, and analytics while **preserving all existing infrastructure**.

## Current Schema Analysis ✅
- **Actions Table**: Already has enhanced tracking fields, Action Network integration
- **Users Table**: Auth0 integration, basic location (zip_code)
- **Officials Table**: Complete representative information
- **Bills Table**: Bill status, chamber, type tracking
- **Campaigns Table**: Campaign management

## Enhancements to Add

### 1. User Reasoning & Personalization Tables
**New Tables to Add:**

```sql
-- Pre-defined reasoning options for each bill/stance combination
CREATE TABLE reasoning_options (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID REFERENCES bills(id) ON DELETE CASCADE,
    stance VARCHAR(20) NOT NULL CHECK (stance IN ('support', 'oppose', 'amend')),
    reason_text TEXT NOT NULL,
    reason_category VARCHAR(100), -- 'economic', 'environmental', 'rights', 'healthcare', etc.
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0, -- Track popularity
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_reasoning_bill_stance (bill_id, stance),
    INDEX idx_reasoning_category (reason_category),
    INDEX idx_reasoning_active (is_active)
);

-- User's selected reasons for each action (many-to-many)
CREATE TABLE action_reasoning (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action_id UUID REFERENCES actions(id) ON DELETE CASCADE,
    reasoning_option_id UUID REFERENCES reasoning_options(id) ON DELETE CASCADE,
    is_primary_reason BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_action_reasoning_action (action_id),
    INDEX idx_action_reasoning_option (reasoning_option_id),
    UNIQUE(action_id, reasoning_option_id)
);

-- Custom reasons pool for analysis and future AI training
CREATE TABLE custom_reasons_pool (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action_id UUID REFERENCES actions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    bill_id UUID REFERENCES bills(id) ON DELETE CASCADE,
    stance VARCHAR(20) NOT NULL CHECK (stance IN ('support', 'oppose', 'amend')),
    custom_reason TEXT NOT NULL,
    is_flagged BOOLEAN DEFAULT FALSE, -- For moderation
    embedding VECTOR(1536), -- For AI similarity analysis (requires pgvector extension)
    similarity_cluster_id UUID, -- For grouping similar reasons
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_custom_reasons_bill (bill_id),
    INDEX idx_custom_reasons_user (user_id),
    INDEX idx_custom_reasons_stance (stance),
    INDEX idx_custom_reasons_flagged (is_flagged)
);
```

### 2. Enhanced Location & Privacy Tables

```sql
-- Encrypted user location data for geographic analysis
CREATE TABLE user_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    -- Encrypted fields (AES-256)
    encrypted_address TEXT, -- Full address encrypted
    encrypted_zip_code TEXT, -- ZIP code encrypted  
    -- Unencrypted for analytics (city/state level only)
    state_code VARCHAR(2), -- State for analytics
    congressional_district VARCHAR(10), -- District for routing
    senate_class VARCHAR(10), -- Senate class for analytics
    -- Approximate coordinates (city-level only for privacy)
    latitude DECIMAL(10, 6), -- Approximate, city-level
    longitude DECIMAL(10, 6), -- Approximate, city-level
    -- Metadata
    location_source VARCHAR(50) DEFAULT 'user_input', -- 'user_input', 'ip_geolocation'
    accuracy_level VARCHAR(20) DEFAULT 'city', -- 'city', 'county', 'state'
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id),
    INDEX idx_user_locations_state (state_code),
    INDEX idx_user_locations_district (congressional_district)
);
```

### 3. Comprehensive Error Tracking

```sql
-- Detailed error logging for all action failures
CREATE TABLE action_errors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action_id UUID REFERENCES actions(id) ON DELETE CASCADE,
    error_type VARCHAR(100) NOT NULL, -- 'network_error', 'validation_error', 'api_error'
    error_code VARCHAR(50), -- HTTP status code or API error code  
    error_message TEXT NOT NULL,
    error_details JSONB, -- Full error response/stack trace
    resolution_status VARCHAR(50) DEFAULT 'unresolved', -- 'unresolved', 'resolved', 'ignored'
    retry_count INTEGER DEFAULT 0,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_action_errors_action (action_id),
    INDEX idx_action_errors_type (error_type),
    INDEX idx_action_errors_status (resolution_status),
    INDEX idx_action_errors_created (created_at)
);

-- Action Network specific tracking
CREATE TABLE action_network_submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action_id UUID REFERENCES actions(id) ON DELETE CASCADE,
    campaign_id VARCHAR(255) NOT NULL, -- Action Network campaign ID
    submission_id VARCHAR(255), -- Action Network submission ID
    form_type VARCHAR(50) NOT NULL, -- 'house', 'senate', 'unified'
    target_chamber VARCHAR(20), -- 'house', 'senate', 'both'
    embed_url TEXT,
    iframe_url TEXT,
    submission_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'submitted', 'failed'
    action_network_response JSONB, -- Full AN response
    submitted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    INDEX idx_an_submissions_action (action_id),
    INDEX idx_an_submissions_campaign (campaign_id),
    INDEX idx_an_submissions_status (submission_status)
);
```

### 4. Analytics & Aggregation Tables

```sql
-- Pre-aggregated analytics for fast frontend queries
CREATE TABLE action_analytics_daily (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID REFERENCES bills(id) ON DELETE CASCADE,
    campaign_id UUID REFERENCES campaigns(id) ON DELETE SET NULL,
    date_bucket DATE NOT NULL,
    -- Action counts
    total_actions INTEGER DEFAULT 0,
    support_count INTEGER DEFAULT 0,
    oppose_count INTEGER DEFAULT 0,
    amend_count INTEGER DEFAULT 0,
    unique_users INTEGER DEFAULT 0,
    -- Top reasons (JSONB array of {reason_id, reason_text, count})
    top_support_reasons JSONB DEFAULT '[]'::jsonb,
    top_oppose_reasons JSONB DEFAULT '[]'::jsonb,
    top_amend_reasons JSONB DEFAULT '[]'::jsonb,
    -- Geographic distribution {state_code: count}
    geographic_distribution JSONB DEFAULT '{}'::jsonb,
    -- Delivery success rates
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    error_rate DECIMAL(5,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(bill_id, campaign_id, date_bucket),
    INDEX idx_analytics_daily_bill (bill_id),
    INDEX idx_analytics_daily_date (date_bucket)
);

-- Real-time analytics summary (updated frequently)
CREATE TABLE action_analytics_realtime (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID REFERENCES bills(id) ON DELETE CASCADE,
    -- Current totals
    total_actions INTEGER DEFAULT 0,
    support_count INTEGER DEFAULT 0,
    oppose_count INTEGER DEFAULT 0,
    amend_count INTEGER DEFAULT 0,
    unique_users INTEGER DEFAULT 0,
    -- Recent activity (last 24h)
    recent_actions INTEGER DEFAULT 0,
    recent_users INTEGER DEFAULT 0,
    -- Trending reasons (updated hourly)
    trending_reasons JSONB DEFAULT '[]'::jsonb,
    -- Geographic spread
    state_distribution JSONB DEFAULT '{}'::jsonb,
    -- Metadata
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(bill_id),
    INDEX idx_analytics_realtime_updated (last_updated)
);
```

### 5. Security & Access Control Enhancements

```sql
-- Add role-based access control
ALTER TABLE users ADD COLUMN role VARCHAR(50) DEFAULT 'user' 
    CHECK (role IN ('user', 'moderator', 'admin', 'super_admin'));

-- Add privacy preferences
CREATE TABLE user_privacy_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    -- Privacy controls
    share_location_analytics BOOLEAN DEFAULT TRUE,
    share_reasoning_analytics BOOLEAN DEFAULT TRUE,
    allow_public_reasons BOOLEAN DEFAULT TRUE,
    data_retention_preference VARCHAR(50) DEFAULT 'standard', -- 'minimal', 'standard', 'extended'
    -- Communication preferences  
    allow_research_contact BOOLEAN DEFAULT FALSE,
    allow_campaign_updates BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- Enable Row Level Security
ALTER TABLE actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE action_reasoning ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_reasons_pool ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE action_errors ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY actions_user_policy ON actions
    FOR ALL TO authenticated
    USING (
        user_id = auth.uid() OR 
        EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'super_admin'))
    );

CREATE POLICY user_locations_policy ON user_locations  
    FOR ALL TO authenticated
    USING (
        user_id = auth.uid() OR
        EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'super_admin'))
    );
```

## Implementation Priority

### Phase 1: Core Tracking (High Priority)
1. ✅ reasoning_options table
2. ✅ action_reasoning table  
3. ✅ custom_reasons_pool table
4. ✅ Enhanced action tracking fields

### Phase 2: Security & Privacy (High Priority)
1. ✅ user_locations table with encryption
2. ✅ user_privacy_settings table
3. ✅ Row-level security policies
4. ✅ Data encryption implementation

### Phase 3: Error & Delivery Tracking (Medium Priority)
1. ✅ action_errors table
2. ✅ action_network_submissions table
3. ✅ Enhanced error handling

### Phase 4: Analytics Infrastructure (Medium Priority)
1. ✅ action_analytics_daily table
2. ✅ action_analytics_realtime table
3. ✅ Aggregation jobs and triggers

## Security Implementation

### Data Encryption
```python
# Location encryption service
import cryptography.fernet import Fernet
import os

class LocationEncryption:
    def __init__(self):
        self.key = os.getenv('LOCATION_ENCRYPTION_KEY')
        self.cipher = Fernet(self.key.encode())
    
    def encrypt_address(self, address: str) -> str:
        return self.cipher.encrypt(address.encode()).decode()
    
    def decrypt_address(self, encrypted_address: str) -> str:
        return self.cipher.decrypt(encrypted_address.encode()).decode()
```

### Access Control
```python
# Permission checking service
def check_action_access(user_id: str, action_id: str, db: Session) -> bool:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return False
    
    # Admins can access all actions
    if user.role in ['admin', 'super_admin']:
        return True
    
    # Users can only access their own actions
    action = db.query(Action).filter(Action.id == action_id).first()
    return action and action.user_id == user_id
```

## Analytics Queries

### Support/Opposition Breakdown
```sql
SELECT 
    stance,
    COUNT(*) as total_count,
    COUNT(DISTINCT user_id) as unique_users,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM actions 
WHERE bill_id = $1 AND status IN ('sent', 'delivered')
GROUP BY stance
ORDER BY total_count DESC;
```

### Top Reasons Analysis
```sql
SELECT 
    ro.reason_text,
    ro.reason_category,
    COUNT(*) as usage_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM action_reasoning ar
JOIN reasoning_options ro ON ar.reasoning_option_id = ro.id
JOIN actions a ON ar.action_id = a.id
WHERE a.bill_id = $1 AND a.stance = $2 AND a.status IN ('sent', 'delivered')
GROUP BY ro.id, ro.reason_text, ro.reason_category
ORDER BY usage_count DESC
LIMIT 10;
```

### Geographic Distribution (Privacy-Safe)
```sql
SELECT 
    ul.state_code,
    COUNT(*) as action_count,
    COUNT(*) FILTER (WHERE a.stance = 'support') as support_count,
    COUNT(*) FILTER (WHERE a.stance = 'oppose') as oppose_count,
    COUNT(DISTINCT a.user_id) as unique_users
FROM actions a
JOIN user_locations ul ON a.user_id = ul.user_id
JOIN user_privacy_settings ups ON a.user_id = ups.user_id
WHERE a.bill_id = $1 
    AND a.status IN ('sent', 'delivered')
    AND ups.share_location_analytics = TRUE
GROUP BY ul.state_code
ORDER BY action_count DESC;
```

## Migration Strategy

### Step 1: Create new tables without breaking existing functionality
### Step 2: Populate reasoning_options with common reasons per bill
### Step 3: Migrate existing action data to new tracking fields
### Step 4: Implement encryption for existing user locations
### Step 5: Enable RLS policies gradually
### Step 6: Build analytics aggregation jobs

## Benefits

✅ **Complete Action Tracking**: Every message, recipient, reason tracked  
✅ **User Privacy**: Encrypted addresses, granular privacy controls  
✅ **Advanced Analytics**: Real-time insights, geographic patterns, trending reasons  
✅ **Error Handling**: Comprehensive error tracking and resolution  
✅ **Security**: Row-level security, role-based access, audit trails  
✅ **Scalability**: Pre-aggregated analytics for fast queries  
✅ **Compliance**: Data retention preferences, privacy controls  

This enhancement provides the foundation for advanced civic engagement analytics while maintaining strict privacy and security standards.