# ---- Builder Stage ----
# This stage installs dependencies and creates a clean Python environment.
FROM python:3.11-slim as builder

# Install system dependencies needed for some Python packages
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Set up a non-root user
RUN useradd --create-home appuser
WORKDIR /home/<USER>

# Install poetry
RUN pip install --no-cache-dir poetry

# Copy only the files needed to install dependencies
COPY poetry.lock pyproject.toml ./

# Install dependencies into a virtual environment. This is the key step.
# This separates our app's dependencies from the system's Python.
RUN poetry config virtualenvs.in-project true && \
    poetry install --no-root --without dev

# ---- Final Production Stage ----
# This stage will be our final, small image.
FROM python:3.11-slim

# Install only runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set up the same non-root user
RUN useradd --create-home appuser
WORKDIR /home/<USER>

# Copy the virtual environment with all its dependencies from the builder stage.
# This is the magic that makes the image small.
COPY --from=builder /home/<USER>/.venv ./.venv

# Copy the application code
COPY app ./app
COPY seed.py ./seed.py
COPY alembic ./alembic
COPY alembic.ini ./alembic.ini

# Activate the virtual environment by adding it to the PATH
ENV PATH="/home/<USER>/.venv/bin:$PATH"

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random

# Set the owner of the files to the non-root user
RUN chown -R appuser:appuser ./
USER appuser

# No CMD or ENTRYPOINT needed, as this is for one-off tasks.