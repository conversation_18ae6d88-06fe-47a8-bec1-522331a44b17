"""Add bill summary versions table

Revision ID: 004_add_bill_summary_versions
Revises: ec05d8de4366
Create Date: 2025-08-05 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004_add_bill_summary_versions'
down_revision = 'ec05d8de4366'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add bill_summary_versions table for tracking summary changes over time."""
    
    # Create the bill_summary_versions table
    op.create_table('bill_summary_versions',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('bill_id', sa.String(length=36), nullable=False),
        sa.Column('version_number', sa.Integer(), nullable=False),
        sa.Column('change_reason', sa.String(), nullable=True),
        sa.Column('summary_what_does', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('summary_who_affects', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('summary_why_matters', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('summary_key_provisions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('summary_timeline', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('summary_cost_impact', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('ai_summary', sa.Text(), nullable=True),
        sa.Column('simple_summary', sa.Text(), nullable=True),
        sa.Column('ai_processed_at', sa.DateTime(), nullable=True),
        sa.Column('is_current', sa.Boolean(), nullable=False),
        sa.Column('changes_detected', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for performance
    op.create_index(op.f('ix_bill_summary_versions_id'), 'bill_summary_versions', ['id'], unique=False)
    op.create_index(op.f('ix_bill_summary_versions_bill_id'), 'bill_summary_versions', ['bill_id'], unique=False)
    op.create_index('ix_bill_summary_versions_bill_version', 'bill_summary_versions', ['bill_id', 'version_number'], unique=True)
    op.create_index('ix_bill_summary_versions_current', 'bill_summary_versions', ['bill_id', 'is_current'], unique=False)
    
    # Create GIN indexes for JSONB fields for efficient querying
    op.create_index('idx_bill_summary_versions_what_does', 'bill_summary_versions', ['summary_what_does'], postgresql_using='gin')
    op.create_index('idx_bill_summary_versions_who_affects', 'bill_summary_versions', ['summary_who_affects'], postgresql_using='gin')
    op.create_index('idx_bill_summary_versions_why_matters', 'bill_summary_versions', ['summary_why_matters'], postgresql_using='gin')
    op.create_index('idx_bill_summary_versions_key_provisions', 'bill_summary_versions', ['summary_key_provisions'], postgresql_using='gin')
    op.create_index('idx_bill_summary_versions_timeline', 'bill_summary_versions', ['summary_timeline'], postgresql_using='gin')
    op.create_index('idx_bill_summary_versions_cost_impact', 'bill_summary_versions', ['summary_cost_impact'], postgresql_using='gin')
    op.create_index('idx_bill_summary_versions_changes', 'bill_summary_versions', ['changes_detected'], postgresql_using='gin')
    
    # Create foreign key constraint
    op.create_foreign_key(
        'fk_bill_summary_versions_bill_id',
        'bill_summary_versions', 'bills',
        ['bill_id'], ['id'],
        ondelete='CASCADE'
    )


def downgrade() -> None:
    """Remove bill_summary_versions table."""
    
    # Drop foreign key constraint
    op.drop_constraint('fk_bill_summary_versions_bill_id', 'bill_summary_versions', type_='foreignkey')
    
    # Drop GIN indexes
    op.drop_index('idx_bill_summary_versions_changes', 'bill_summary_versions')
    op.drop_index('idx_bill_summary_versions_cost_impact', 'bill_summary_versions')
    op.drop_index('idx_bill_summary_versions_timeline', 'bill_summary_versions')
    op.drop_index('idx_bill_summary_versions_key_provisions', 'bill_summary_versions')
    op.drop_index('idx_bill_summary_versions_why_matters', 'bill_summary_versions')
    op.drop_index('idx_bill_summary_versions_who_affects', 'bill_summary_versions')
    op.drop_index('idx_bill_summary_versions_what_does', 'bill_summary_versions')
    
    # Drop regular indexes
    op.drop_index('ix_bill_summary_versions_current', 'bill_summary_versions')
    op.drop_index('ix_bill_summary_versions_bill_version', 'bill_summary_versions')
    op.drop_index(op.f('ix_bill_summary_versions_bill_id'), 'bill_summary_versions')
    op.drop_index(op.f('ix_bill_summary_versions_id'), 'bill_summary_versions')
    
    # Drop the table
    op.drop_table('bill_summary_versions')
