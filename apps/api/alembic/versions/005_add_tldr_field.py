"""Add TL;DR field to bills and bill_summary_versions tables

Revision ID: 005_add_tldr_field
Revises: 4ad056549353
Create Date: 2025-08-05 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '005_add_tldr_field'
down_revision = '4ad056549353'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add TL;DR field to bills and bill_summary_versions tables."""
    
    # Add tldr column to bills table
    op.add_column('bills', sa.Column('tldr', sa.Text(), nullable=True))
    
    # Add tldr column to bill_summary_versions table
    op.add_column('bill_summary_versions', sa.Column('tldr', sa.Text(), nullable=True))


def downgrade() -> None:
    """Remove TL;DR field from bills and bill_summary_versions tables."""
    
    # Remove tldr column from bill_summary_versions table
    op.drop_column('bill_summary_versions', 'tldr')
    
    # Remove tldr column from bills table
    op.drop_column('bills', 'tldr')
