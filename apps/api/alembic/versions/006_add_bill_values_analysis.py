"""Add bill values analysis and tagging system

Revision ID: 006_add_bill_values_analysis
Revises: 005_add_tldr_field
Create Date: 2025-08-05 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '006_add_bill_values_analysis'
down_revision = '005_add_tldr_field'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add bill values analysis and tagging tables."""
    
    # Create bill_values_analysis table
    op.create_table('bill_values_analysis',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('bill_id', sa.String(length=36), nullable=False),
        
        # Democracy scores
        sa.Column('democracy_threat_score', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('democracy_support_score', sa.Integer(), nullable=False, server_default='0'),
        
        # Human rights scores
        sa.Column('human_rights_threat_score', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('human_rights_support_score', sa.Integer(), nullable=False, server_default='0'),
        
        # Environmental scores
        sa.Column('environmental_threat_score', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('environmental_support_score', sa.Integer(), nullable=False, server_default='0'),
        
        # Overall assessments
        sa.Column('overall_threat_level', sa.String(length=20), nullable=True),
        sa.Column('overall_support_level', sa.String(length=20), nullable=True),
        
        # AI analysis details
        sa.Column('analysis_reasoning', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('confidence_score', sa.DECIMAL(precision=3, scale=2), nullable=True),
        
        # Moderation flags
        sa.Column('requires_human_review', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('is_flagged', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('is_blocked', sa.Boolean(), nullable=False, server_default='false'),
        
        # Human review tracking
        sa.Column('reviewed_by', sa.String(length=36), nullable=True),
        sa.Column('reviewed_at', sa.DateTime(), nullable=True),
        sa.Column('review_notes', sa.Text(), nullable=True),
        
        # Processing metadata
        sa.Column('analyzed_at', sa.DateTime(), nullable=False),
        sa.Column('ai_model_version', sa.String(length=50), nullable=True),
        
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create bill_values_tags table
    op.create_table('bill_values_tags',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('bill_id', sa.String(length=36), nullable=False),
        sa.Column('analysis_id', sa.String(length=36), nullable=False),
        
        # Tag classification
        sa.Column('tag_category', sa.String(length=50), nullable=False),
        sa.Column('tag_type', sa.String(length=20), nullable=False),
        
        # Tag content
        sa.Column('tag_name', sa.String(length=100), nullable=False),
        sa.Column('display_text', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        
        # Severity and priority
        sa.Column('severity_level', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('display_priority', sa.Integer(), nullable=False, server_default='5'),
        
        # Visual styling hints
        sa.Column('color_theme', sa.String(length=20), nullable=True),
        sa.Column('icon_name', sa.String(length=50), nullable=True),
        
        # Metadata
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for bill_values_analysis
    op.create_index(op.f('ix_bill_values_analysis_id'), 'bill_values_analysis', ['id'], unique=False)
    op.create_index('idx_bill_values_analysis_bill_id', 'bill_values_analysis', ['bill_id'], unique=False)
    op.create_index('idx_bill_values_analysis_threat_level', 'bill_values_analysis', ['overall_threat_level'], unique=False)
    op.create_index('idx_bill_values_analysis_support_level', 'bill_values_analysis', ['overall_support_level'], unique=False)
    op.create_index('idx_bill_values_analysis_flagged', 'bill_values_analysis', ['is_flagged'], unique=False)
    op.create_index('idx_bill_values_analysis_review_needed', 'bill_values_analysis', ['requires_human_review'], unique=False)
    
    # Create indexes for bill_values_tags
    op.create_index(op.f('ix_bill_values_tags_id'), 'bill_values_tags', ['id'], unique=False)
    op.create_index('idx_bill_values_tags_bill_id', 'bill_values_tags', ['bill_id'], unique=False)
    op.create_index('idx_bill_values_tags_category', 'bill_values_tags', ['tag_category'], unique=False)
    op.create_index('idx_bill_values_tags_type', 'bill_values_tags', ['tag_type'], unique=False)
    op.create_index('idx_bill_values_tags_active', 'bill_values_tags', ['is_active'], unique=False)
    op.create_index('idx_bill_values_tags_priority', 'bill_values_tags', ['display_priority'], unique=False)
    
    # Create foreign key constraints
    op.create_foreign_key(
        'fk_bill_values_analysis_bill_id',
        'bill_values_analysis', 'bills',
        ['bill_id'], ['id'],
        ondelete='CASCADE'
    )
    
    op.create_foreign_key(
        'fk_bill_values_analysis_reviewed_by',
        'bill_values_analysis', 'users',
        ['reviewed_by'], ['id'],
        ondelete='SET NULL'
    )
    
    op.create_foreign_key(
        'fk_bill_values_tags_bill_id',
        'bill_values_tags', 'bills',
        ['bill_id'], ['id'],
        ondelete='CASCADE'
    )
    
    op.create_foreign_key(
        'fk_bill_values_tags_analysis_id',
        'bill_values_tags', 'bill_values_analysis',
        ['analysis_id'], ['id'],
        ondelete='CASCADE'
    )


def downgrade() -> None:
    """Remove bill values analysis and tagging tables."""
    
    # Drop foreign key constraints
    op.drop_constraint('fk_bill_values_tags_analysis_id', 'bill_values_tags', type_='foreignkey')
    op.drop_constraint('fk_bill_values_tags_bill_id', 'bill_values_tags', type_='foreignkey')
    op.drop_constraint('fk_bill_values_analysis_reviewed_by', 'bill_values_analysis', type_='foreignkey')
    op.drop_constraint('fk_bill_values_analysis_bill_id', 'bill_values_analysis', type_='foreignkey')
    
    # Drop indexes for bill_values_tags
    op.drop_index('idx_bill_values_tags_priority', 'bill_values_tags')
    op.drop_index('idx_bill_values_tags_active', 'bill_values_tags')
    op.drop_index('idx_bill_values_tags_type', 'bill_values_tags')
    op.drop_index('idx_bill_values_tags_category', 'bill_values_tags')
    op.drop_index('idx_bill_values_tags_bill_id', 'bill_values_tags')
    op.drop_index(op.f('ix_bill_values_tags_id'), 'bill_values_tags')
    
    # Drop indexes for bill_values_analysis
    op.drop_index('idx_bill_values_analysis_review_needed', 'bill_values_analysis')
    op.drop_index('idx_bill_values_analysis_flagged', 'bill_values_analysis')
    op.drop_index('idx_bill_values_analysis_support_level', 'bill_values_analysis')
    op.drop_index('idx_bill_values_analysis_threat_level', 'bill_values_analysis')
    op.drop_index('idx_bill_values_analysis_bill_id', 'bill_values_analysis')
    op.drop_index(op.f('ix_bill_values_analysis_id'), 'bill_values_analysis')
    
    # Drop tables
    op.drop_table('bill_values_tags')
    op.drop_table('bill_values_analysis')
