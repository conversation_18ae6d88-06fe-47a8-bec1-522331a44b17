"""add_structured_summary_and_user_tracking_fields

Revision ID: 0c4090db812d
Revises: 346f02f1560f
Create Date: 2025-07-31 03:28:22.760302

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0c4090db812d'
down_revision: Union[str, Sequence[str], None] = '346f02f1560f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add structured summary fields to bills table
    op.add_column('bills', sa.Column('ai_summary_what_it_does', sa.Text(), nullable=True))
    op.add_column('bills', sa.Column('ai_summary_who_it_affects', sa.Text(), nullable=True))
    op.add_column('bills', sa.Column('ai_summary_why_it_matters', sa.Text(), nullable=True))
    op.add_column('bills', sa.Column('ai_summary_key_provisions', sa.Text(), nullable=True))

    # Add user tracking fields to actions table
    op.add_column('actions', sa.Column('selected_reasons', sa.dialects.postgresql.JSONB(), nullable=True))
    op.add_column('actions', sa.Column('custom_reasons', sa.dialects.postgresql.JSONB(), nullable=True))
    op.add_column('actions', sa.Column('original_ai_message', sa.Text(), nullable=True))
    op.add_column('actions', sa.Column('user_edited_message', sa.Text(), nullable=True))
    op.add_column('actions', sa.Column('message_was_edited', sa.Boolean(), nullable=False, server_default='false'))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove user tracking fields from actions table
    op.drop_column('actions', 'message_was_edited')
    op.drop_column('actions', 'user_edited_message')
    op.drop_column('actions', 'original_ai_message')
    op.drop_column('actions', 'custom_reasons')
    op.drop_column('actions', 'selected_reasons')

    # Remove structured summary fields from bills table
    op.drop_column('bills', 'ai_summary_key_provisions')
    op.drop_column('bills', 'ai_summary_why_it_matters')
    op.drop_column('bills', 'ai_summary_who_it_affects')
    op.drop_column('bills', 'ai_summary_what_it_does')
