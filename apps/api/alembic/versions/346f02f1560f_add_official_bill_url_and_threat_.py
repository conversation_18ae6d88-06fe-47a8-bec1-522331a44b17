"""Add official_bill_url and threat analysis fields

Revision ID: 346f02f1560f
Revises: f791c0c650e3
Create Date: 2025-07-30 15:30:31.434479

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '346f02f1560f'
down_revision: Union[str, Sequence[str], None] = 'f791c0c650e3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bills', sa.Column('environmental_threat_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('bills', sa.Column('social_rights_threat_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('bills', sa.Column('environmental_justice_threat_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('bills', sa.Column('official_bill_url', sa.String(), nullable=True))
    op.drop_index(op.f('idx_bills_ai_processed_at'), table_name='bills')
    op.drop_index(op.f('idx_bills_ai_tags'), table_name='bills', postgresql_using='gin')
    op.drop_index(op.f('idx_bills_amend_reasons'), table_name='bills', postgresql_using='gin')
    op.drop_index(op.f('idx_bills_oppose_reasons'), table_name='bills', postgresql_using='gin')
    op.drop_index(op.f('idx_bills_support_reasons'), table_name='bills', postgresql_using='gin')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('idx_bills_support_reasons'), 'bills', ['support_reasons'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bills_oppose_reasons'), 'bills', ['oppose_reasons'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bills_amend_reasons'), 'bills', ['amend_reasons'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bills_ai_tags'), 'bills', ['ai_tags'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bills_ai_processed_at'), 'bills', ['ai_processed_at'], unique=False)
    op.drop_column('bills', 'official_bill_url')
    op.drop_column('bills', 'environmental_justice_threat_analysis')
    op.drop_column('bills', 'social_rights_threat_analysis')
    op.drop_column('bills', 'environmental_threat_analysis')
    # ### end Alembic commands ###
