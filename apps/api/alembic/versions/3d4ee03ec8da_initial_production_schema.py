"""Initial production schema

Revision ID: 3d4ee03ec8da
Revises: 
Create Date: 2025-07-26 04:20:54.788461

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3d4ee03ec8da'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bills',
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('bill_number', sa.String(), nullable=False),
    sa.Column('bill_type', sa.Enum('HOUSE_BILL', 'SENATE_BILL', 'HOUSE_RESOLUTION', 'SENATE_RESOLUTION', 'JOINT_RESOLUTION', 'CONCURRENT_RESOLUTION', name='billtype'), nullable=False),
    sa.Column('status', sa.Enum('DRAFT', 'INTRODUCED', 'COMMITTEE', 'FLOOR', 'PASSED', 'SIGNED', 'VETOED', 'FAILED', name='billstatus'), nullable=False),
    sa.Column('session_year', sa.Integer(), nullable=False),
    sa.Column('chamber', sa.String(), nullable=False),
    sa.Column('state', sa.String(), nullable=False),
    sa.Column('full_text', sa.Text(), nullable=True),
    sa.Column('summary', sa.Text(), nullable=True),
    sa.Column('ai_summary', sa.Text(), nullable=True),
    sa.Column('reasons_for_support', sa.Text(), nullable=True),
    sa.Column('reasons_for_opposition', sa.Text(), nullable=True),
    sa.Column('openstates_id', sa.String(), nullable=True),
    sa.Column('congress_gov_id', sa.String(), nullable=True),
    sa.Column('source_url', sa.String(), nullable=True),
    sa.Column('text_url', sa.String(), nullable=True),
    sa.Column('introduced_date', sa.DateTime(), nullable=True),
    sa.Column('last_action_date', sa.DateTime(), nullable=True),
    sa.Column('sponsor_name', sa.String(), nullable=True),
    sa.Column('sponsor_party', sa.String(), nullable=True),
    sa.Column('sponsor_state', sa.String(), nullable=True),
    sa.Column('cosponsors', sa.Text(), nullable=True),
    sa.Column('vote_history', sa.Text(), nullable=True),
    sa.Column('is_featured', sa.Boolean(), nullable=False),
    sa.Column('priority_score', sa.Integer(), nullable=False),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('categories', sa.Text(), nullable=True),
    sa.Column('bill_metadata', sa.Text(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bills_bill_number'), 'bills', ['bill_number'], unique=False)
    op.create_index(op.f('ix_bills_congress_gov_id'), 'bills', ['congress_gov_id'], unique=True)
    op.create_index(op.f('ix_bills_id'), 'bills', ['id'], unique=False)
    op.create_index(op.f('ix_bills_openstates_id'), 'bills', ['openstates_id'], unique=True)
    op.create_index(op.f('ix_bills_title'), 'bills', ['title'], unique=False)
    op.create_table('officials',
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('party', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('website', sa.String(), nullable=True),
    sa.Column('office_address', sa.Text(), nullable=True),
    sa.Column('office_city', sa.String(), nullable=True),
    sa.Column('office_state', sa.String(), nullable=True),
    sa.Column('office_zip', sa.String(), nullable=True),
    sa.Column('level', sa.String(), nullable=False),
    sa.Column('chamber', sa.String(), nullable=True),
    sa.Column('state', sa.String(), nullable=True),
    sa.Column('district', sa.String(), nullable=True),
    sa.Column('bioguide_id', sa.String(), nullable=True),
    sa.Column('openstates_id', sa.String(), nullable=True),
    sa.Column('google_civic_id', sa.String(), nullable=True),
    sa.Column('five_calls_id', sa.String(), nullable=True),
    sa.Column('twitter_handle', sa.String(), nullable=True),
    sa.Column('facebook_url', sa.String(), nullable=True),
    sa.Column('instagram_handle', sa.String(), nullable=True),
    sa.Column('social_media', sa.Text(), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('profile_picture_url', sa.String(), nullable=True),
    sa.Column('term_start', sa.String(), nullable=True),
    sa.Column('term_end', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('voting_record', sa.Text(), nullable=True),
    sa.Column('positions', sa.Text(), nullable=True),
    sa.Column('response_rate', sa.Integer(), nullable=True),
    sa.Column('avg_response_time', sa.Integer(), nullable=True),
    sa.Column('official_metadata', sa.Text(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_officials_bioguide_id'), 'officials', ['bioguide_id'], unique=True)
    op.create_index(op.f('ix_officials_five_calls_id'), 'officials', ['five_calls_id'], unique=True)
    op.create_index(op.f('ix_officials_google_civic_id'), 'officials', ['google_civic_id'], unique=True)
    op.create_index(op.f('ix_officials_id'), 'officials', ['id'], unique=False)
    op.create_index(op.f('ix_officials_name'), 'officials', ['name'], unique=False)
    op.create_index(op.f('ix_officials_openstates_id'), 'officials', ['openstates_id'], unique=True)
    op.create_table('users',
    sa.Column('auth0_user_id', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('picture_url', sa.String(), nullable=True),
    sa.Column('email_verified', sa.Boolean(), nullable=False),
    sa.Column('hashed_password', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('zip_code', sa.String(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('email_notifications', sa.Boolean(), nullable=False),
    sa.Column('sms_notifications', sa.Boolean(), nullable=False),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('profile_picture_url', sa.String(), nullable=True),
    sa.Column('email_verification_token', sa.String(), nullable=True),
    sa.Column('email_verified_at', sa.DateTime(), nullable=True),
    sa.Column('password_reset_token', sa.String(), nullable=True),
    sa.Column('password_reset_expires_at', sa.DateTime(), nullable=True),
    sa.Column('last_login_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_auth0_user_id'), 'users', ['auth0_user_id'], unique=True)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_table('bill_status_pipeline',
    sa.Column('bill_id', sa.String(length=36), nullable=False),
    sa.Column('previous_status', sa.Enum('DRAFT', 'INTRODUCED', 'COMMITTEE', 'FLOOR', 'PASSED', 'SIGNED', 'VETOED', 'FAILED', name='billstatus'), nullable=True),
    sa.Column('current_status', sa.Enum('DRAFT', 'INTRODUCED', 'COMMITTEE', 'FLOOR', 'PASSED', 'SIGNED', 'VETOED', 'FAILED', name='billstatus'), nullable=False),
    sa.Column('status_changed_at', sa.DateTime(), nullable=False),
    sa.Column('detected_at', sa.DateTime(), nullable=False),
    sa.Column('external_data', sa.Text(), nullable=True),
    sa.Column('vote_details', sa.Text(), nullable=True),
    sa.Column('notification_sent', sa.Boolean(), nullable=False),
    sa.Column('is_significant_change', sa.Boolean(), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bill_status_pipeline_bill_id'), 'bill_status_pipeline', ['bill_id'], unique=False)
    op.create_index(op.f('ix_bill_status_pipeline_current_status'), 'bill_status_pipeline', ['current_status'], unique=False)
    op.create_index(op.f('ix_bill_status_pipeline_id'), 'bill_status_pipeline', ['id'], unique=False)
    op.create_table('campaigns',
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('short_description', sa.String(), nullable=True),
    sa.Column('campaign_type', sa.Enum('SUPPORT', 'OPPOSE', 'NEUTRAL', 'AMENDMENT', name='campaigntype'), nullable=False),
    sa.Column('status', sa.Enum('DRAFT', 'ACTIVE', 'PAUSED', 'COMPLETED', 'ARCHIVED', name='campaignstatus'), nullable=False),
    sa.Column('call_to_action', sa.Text(), nullable=False),
    sa.Column('email_template', sa.Text(), nullable=True),
    sa.Column('talking_points', sa.Text(), nullable=True),
    sa.Column('target_audience', sa.String(), nullable=True),
    sa.Column('geographic_scope', sa.Text(), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('is_featured', sa.Boolean(), nullable=False),
    sa.Column('is_public', sa.Boolean(), nullable=False),
    sa.Column('requires_verification', sa.Boolean(), nullable=False),
    sa.Column('goal_actions', sa.Integer(), nullable=True),
    sa.Column('actual_actions', sa.Integer(), nullable=False),
    sa.Column('banner_image_url', sa.String(), nullable=True),
    sa.Column('thumbnail_image_url', sa.String(), nullable=True),
    sa.Column('social_media_message', sa.Text(), nullable=True),
    sa.Column('hashtags', sa.Text(), nullable=True),
    sa.Column('campaign_metadata', sa.Text(), nullable=True),
    sa.Column('bill_id', sa.String(length=36), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_campaigns_id'), 'campaigns', ['id'], unique=False)
    op.create_index(op.f('ix_campaigns_title'), 'campaigns', ['title'], unique=False)
    op.create_table('actions',
    sa.Column('subject', sa.String(), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('action_type', sa.Enum('EMAIL', 'PHONE', 'LETTER', 'SOCIAL_MEDIA', 'PETITION', 'TWITTER', name='actiontype'), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', 'PARTIAL', name='actionstatus'), nullable=False),
    sa.Column('sent_at', sa.DateTime(), nullable=True),
    sa.Column('delivered_at', sa.DateTime(), nullable=True),
    sa.Column('response_received', sa.Boolean(), nullable=False),
    sa.Column('response_content', sa.Text(), nullable=True),
    sa.Column('response_received_at', sa.DateTime(), nullable=True),
    sa.Column('contact_email', sa.String(), nullable=True),
    sa.Column('contact_phone', sa.String(), nullable=True),
    sa.Column('contact_address', sa.Text(), nullable=True),
    sa.Column('user_name', sa.String(), nullable=False),
    sa.Column('user_email', sa.String(), nullable=False),
    sa.Column('user_address', sa.Text(), nullable=True),
    sa.Column('user_zip_code', sa.String(), nullable=True),
    sa.Column('delivery_method', sa.String(), nullable=True),
    sa.Column('delivery_id', sa.String(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=False),
    sa.Column('action_metadata', sa.Text(), nullable=True),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('campaign_id', sa.String(length=36), nullable=False),
    sa.Column('official_id', sa.String(length=36), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ),
    sa.ForeignKeyConstraint(['official_id'], ['officials.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_actions_id'), 'actions', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_actions_id'), table_name='actions')
    op.drop_table('actions')
    op.drop_index(op.f('ix_campaigns_title'), table_name='campaigns')
    op.drop_index(op.f('ix_campaigns_id'), table_name='campaigns')
    op.drop_table('campaigns')
    op.drop_index(op.f('ix_bill_status_pipeline_id'), table_name='bill_status_pipeline')
    op.drop_index(op.f('ix_bill_status_pipeline_current_status'), table_name='bill_status_pipeline')
    op.drop_index(op.f('ix_bill_status_pipeline_bill_id'), table_name='bill_status_pipeline')
    op.drop_table('bill_status_pipeline')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_auth0_user_id'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_officials_openstates_id'), table_name='officials')
    op.drop_index(op.f('ix_officials_name'), table_name='officials')
    op.drop_index(op.f('ix_officials_id'), table_name='officials')
    op.drop_index(op.f('ix_officials_google_civic_id'), table_name='officials')
    op.drop_index(op.f('ix_officials_five_calls_id'), table_name='officials')
    op.drop_index(op.f('ix_officials_bioguide_id'), table_name='officials')
    op.drop_table('officials')
    op.drop_index(op.f('ix_bills_title'), table_name='bills')
    op.drop_index(op.f('ix_bills_openstates_id'), table_name='bills')
    op.drop_index(op.f('ix_bills_id'), table_name='bills')
    op.drop_index(op.f('ix_bills_congress_gov_id'), table_name='bills')
    op.drop_index(op.f('ix_bills_bill_number'), table_name='bills')
    op.drop_table('bills')
    # ### end Alembic commands ###
