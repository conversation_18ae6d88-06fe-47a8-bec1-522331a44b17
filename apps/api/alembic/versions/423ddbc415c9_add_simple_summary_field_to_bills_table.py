"""Add simple_summary field to bills table

Revision ID: 423ddbc415c9
Revises: ec05d8de4366
Create Date: 2025-07-31 18:23:09.352257

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '423ddbc415c9'
down_revision: Union[str, Sequence[str], None] = 'ec05d8de4366'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bills', sa.Column('simple_summary', sa.Text(), nullable=True))
    op.drop_index(op.f('idx_bills_summary_cost_impact'), table_name='bills', postgresql_using='gin')
    op.drop_index(op.f('idx_bills_summary_key_provisions'), table_name='bills', postgresql_using='gin')
    op.drop_index(op.f('idx_bills_summary_timeline'), table_name='bills', postgresql_using='gin')
    op.drop_index(op.f('idx_bills_summary_what_does'), table_name='bills', postgresql_using='gin')
    op.drop_index(op.f('idx_bills_summary_who_affects'), table_name='bills', postgresql_using='gin')
    op.drop_index(op.f('idx_bills_summary_why_matters'), table_name='bills', postgresql_using='gin')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('idx_bills_summary_why_matters'), 'bills', ['summary_why_matters'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bills_summary_who_affects'), 'bills', ['summary_who_affects'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bills_summary_what_does'), 'bills', ['summary_what_does'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bills_summary_timeline'), 'bills', ['summary_timeline'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bills_summary_key_provisions'), 'bills', ['summary_key_provisions'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bills_summary_cost_impact'), 'bills', ['summary_cost_impact'], unique=False, postgresql_using='gin')
    op.drop_column('bills', 'simple_summary')
    # ### end Alembic commands ###
