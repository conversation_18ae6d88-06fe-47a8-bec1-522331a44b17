"""merge heads

Revision ID: 4ad056549353
Revises: 004_add_bill_summary_versions, fe8486ac8849
Create Date: 2025-08-04 20:06:07.790850

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4ad056549353'
down_revision: Union[str, Sequence[str], None] = ('004_add_bill_summary_versions', 'fe8486ac8849')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
