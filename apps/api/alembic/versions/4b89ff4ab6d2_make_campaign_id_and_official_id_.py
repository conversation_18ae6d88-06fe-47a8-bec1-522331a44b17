"""Make campaign_id and official_id nullable in actions table

Revision ID: 4b89ff4ab6d2
Revises: 423ddbc415c9
Create Date: 2025-08-01 01:50:58.432203

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4b89ff4ab6d2'
down_revision: Union[str, Sequence[str], None] = '423ddbc415c9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('actions', 'campaign_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=True)
    op.alter_column('actions', 'official_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('actions', 'official_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=False)
    op.alter_column('actions', 'campaign_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=False)
    # ### end Alembic commands ###
