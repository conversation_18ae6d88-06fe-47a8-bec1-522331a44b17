"""add_ai_processing_columns_to_bills

Revision ID: 955708e82798
Revises: 5d06b228025a
Create Date: 2025-07-30 01:18:36.777720

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '955708e82798'
down_revision: Union[str, Sequence[str], None] = '5d06b228025a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add new AI processing columns to bills table
    op.add_column('bills', sa.Column('support_reasons', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('bills', sa.Column('oppose_reasons', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('bills', sa.Column('amend_reasons', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('bills', sa.Column('message_templates', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('bills', sa.Column('ai_tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('bills', sa.Column('ai_processed_at', sa.DateTime(), nullable=True))

    # Add indexes for performance
    op.create_index('idx_bills_ai_tags', 'bills', ['ai_tags'], postgresql_using='gin')
    op.create_index('idx_bills_support_reasons', 'bills', ['support_reasons'], postgresql_using='gin')
    op.create_index('idx_bills_oppose_reasons', 'bills', ['oppose_reasons'], postgresql_using='gin')
    op.create_index('idx_bills_amend_reasons', 'bills', ['amend_reasons'], postgresql_using='gin')
    op.create_index('idx_bills_ai_processed_at', 'bills', ['ai_processed_at'])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes first
    op.drop_index('idx_bills_ai_processed_at', table_name='bills')
    op.drop_index('idx_bills_amend_reasons', table_name='bills')
    op.drop_index('idx_bills_oppose_reasons', table_name='bills')
    op.drop_index('idx_bills_support_reasons', table_name='bills')
    op.drop_index('idx_bills_ai_tags', table_name='bills')

    # Drop columns
    op.drop_column('bills', 'ai_processed_at')
    op.drop_column('bills', 'ai_tags')
    op.drop_column('bills', 'message_templates')
    op.drop_column('bills', 'amend_reasons')
    op.drop_column('bills', 'oppose_reasons')
    op.drop_column('bills', 'support_reasons')
