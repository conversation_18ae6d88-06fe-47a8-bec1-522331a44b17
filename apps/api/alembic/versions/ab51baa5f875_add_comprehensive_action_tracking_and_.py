"""Add comprehensive action tracking and analytics infrastructure

Revision ID: ab51baa5f875
Revises: 4b89ff4ab6d2
Create Date: 2025-08-04 18:31:15.971657

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ab51baa5f875'
down_revision: Union[str, Sequence[str], None] = '4b89ff4ab6d2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
