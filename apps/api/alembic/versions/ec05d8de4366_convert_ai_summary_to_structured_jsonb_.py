"""convert_ai_summary_to_structured_jsonb_fields

Revision ID: ec05d8de4366
Revises: 0c4090db812d
Create Date: 2025-07-31 04:48:14.864322

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ec05d8de4366'
down_revision: Union[str, Sequence[str], None] = '0c4090db812d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Convert AI summary from blob text to structured JSONB fields."""

    # Import JSONB type
    from sqlalchemy.dialects.postgresql import JSONB

    # Drop old AI summary fields (keeping ai_summary for now as backup)
    op.drop_column('bills', 'ai_summary_what_it_does')
    op.drop_column('bills', 'ai_summary_who_it_affects')
    op.drop_column('bills', 'ai_summary_why_it_matters')
    op.drop_column('bills', 'ai_summary_key_provisions')

    # Add new structured JSONB fields as per Implementation-2.MD guide
    op.add_column('bills', sa.Column('summary_what_does', JSONB, nullable=True))
    op.add_column('bills', sa.Column('summary_who_affects', JSONB, nullable=True))
    op.add_column('bills', sa.Column('summary_why_matters', JSONB, nullable=True))
    op.add_column('bills', sa.Column('summary_key_provisions', JSONB, nullable=True))
    op.add_column('bills', sa.Column('summary_timeline', JSONB, nullable=True))
    op.add_column('bills', sa.Column('summary_cost_impact', JSONB, nullable=True))

    # Add GIN indexes for performance on JSONB fields
    op.create_index('idx_bills_summary_what_does', 'bills', ['summary_what_does'], postgresql_using='gin')
    op.create_index('idx_bills_summary_who_affects', 'bills', ['summary_who_affects'], postgresql_using='gin')
    op.create_index('idx_bills_summary_why_matters', 'bills', ['summary_why_matters'], postgresql_using='gin')
    op.create_index('idx_bills_summary_key_provisions', 'bills', ['summary_key_provisions'], postgresql_using='gin')
    op.create_index('idx_bills_summary_timeline', 'bills', ['summary_timeline'], postgresql_using='gin')
    op.create_index('idx_bills_summary_cost_impact', 'bills', ['summary_cost_impact'], postgresql_using='gin')


def downgrade() -> None:
    """Revert to old AI summary structure."""

    # Drop GIN indexes
    op.drop_index('idx_bills_summary_cost_impact', 'bills')
    op.drop_index('idx_bills_summary_timeline', 'bills')
    op.drop_index('idx_bills_summary_key_provisions', 'bills')
    op.drop_index('idx_bills_summary_why_matters', 'bills')
    op.drop_index('idx_bills_summary_who_affects', 'bills')
    op.drop_index('idx_bills_summary_what_does', 'bills')

    # Drop new JSONB fields
    op.drop_column('bills', 'summary_cost_impact')
    op.drop_column('bills', 'summary_timeline')
    op.drop_column('bills', 'summary_key_provisions')
    op.drop_column('bills', 'summary_why_matters')
    op.drop_column('bills', 'summary_who_affects')
    op.drop_column('bills', 'summary_what_does')

    # Restore old Text fields
    op.add_column('bills', sa.Column('ai_summary_what_it_does', sa.Text, nullable=True))
    op.add_column('bills', sa.Column('ai_summary_who_it_affects', sa.Text, nullable=True))
    op.add_column('bills', sa.Column('ai_summary_why_it_matters', sa.Text, nullable=True))
    op.add_column('bills', sa.Column('ai_summary_key_provisions', sa.Text, nullable=True))
