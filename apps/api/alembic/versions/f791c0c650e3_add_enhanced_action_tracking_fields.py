"""add_enhanced_action_tracking_fields

Revision ID: f791c0c650e3
Revises: 955708e82798
Create Date: 2025-07-30 01:33:34.877028

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'f791c0c650e3'
down_revision: Union[str, Sequence[str], None] = '955708e82798'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('actions', sa.Column('bill_id', sa.String(length=36), nullable=True))
    op.add_column('actions', sa.Column('position', sa.String(), nullable=True))
    op.add_column('actions', sa.Column('action_network_id', sa.String(), nullable=True))
    op.add_column('actions', sa.Column('personalized_content', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('actions', sa.Column('representative_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('actions', sa.Column('user_location', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.create_foreign_key(None, 'actions', 'bills', ['bill_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'actions', type_='foreignkey')
    op.drop_column('actions', 'user_location')
    op.drop_column('actions', 'representative_info')
    op.drop_column('actions', 'personalized_content')
    op.drop_column('actions', 'action_network_id')
    op.drop_column('actions', 'position')
    op.drop_column('actions', 'bill_id')
    # ### end Alembic commands ###
