"""Add enhanced action tracking models and relationships

Revision ID: fe8486ac8849
Revises: ab51baa5f875
Create Date: 2025-08-04 18:34:26.891510

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'fe8486ac8849'
down_revision: Union[str, Sequence[str], None] = 'ab51baa5f875'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('action_analytics_realtime',
    sa.Column('bill_id', sa.String(length=36), nullable=False),
    sa.Column('total_actions', sa.Integer(), nullable=False),
    sa.Column('support_count', sa.Integer(), nullable=False),
    sa.Column('oppose_count', sa.Integer(), nullable=False),
    sa.Column('amend_count', sa.Integer(), nullable=False),
    sa.Column('unique_users', sa.Integer(), nullable=False),
    sa.Column('recent_actions', sa.Integer(), nullable=False),
    sa.Column('recent_users', sa.Integer(), nullable=False),
    sa.Column('trending_reasons', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('state_distribution', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('last_updated', sa.DateTime(), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('bill_id')
    )
    op.create_index('idx_analytics_realtime_updated', 'action_analytics_realtime', ['last_updated'], unique=False)
    op.create_index(op.f('ix_action_analytics_realtime_id'), 'action_analytics_realtime', ['id'], unique=False)
    op.create_table('reasoning_options',
    sa.Column('bill_id', sa.String(length=36), nullable=False),
    sa.Column('stance', sa.Enum('SUPPORT', 'OPPOSE', 'AMEND', name='actionstance'), nullable=False),
    sa.Column('reason_text', sa.Text(), nullable=False),
    sa.Column('reason_category', sa.Enum('ECONOMIC', 'ENVIRONMENTAL', 'HEALTHCARE', 'EDUCATION', 'CIVIL_RIGHTS', 'NATIONAL_SECURITY', 'INFRASTRUCTURE', 'SOCIAL_JUSTICE', 'TECHNOLOGY', 'OTHER', name='reasoningcategory'), nullable=True),
    sa.Column('display_order', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('usage_count', sa.Integer(), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.CheckConstraint("stance IN ('SUPPORT', 'OPPOSE', 'AMEND')", name='check_valid_stance'),
    sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_reasoning_active', 'reasoning_options', ['is_active'], unique=False)
    op.create_index('idx_reasoning_bill_stance', 'reasoning_options', ['bill_id', 'stance'], unique=False)
    op.create_index('idx_reasoning_category', 'reasoning_options', ['reason_category'], unique=False)
    op.create_index(op.f('ix_reasoning_options_id'), 'reasoning_options', ['id'], unique=False)
    op.create_table('user_locations',
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('encrypted_address', sa.Text(), nullable=True),
    sa.Column('encrypted_zip_code', sa.Text(), nullable=True),
    sa.Column('state_code', sa.String(length=2), nullable=True),
    sa.Column('congressional_district', sa.String(length=10), nullable=True),
    sa.Column('senate_class', sa.String(length=10), nullable=True),
    sa.Column('latitude', sa.DECIMAL(precision=10, scale=6), nullable=True),
    sa.Column('longitude', sa.DECIMAL(precision=10, scale=6), nullable=True),
    sa.Column('location_source', sa.String(length=50), nullable=False),
    sa.Column('accuracy_level', sa.String(length=20), nullable=False),
    sa.Column('verified_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index('idx_user_locations_district', 'user_locations', ['congressional_district'], unique=False)
    op.create_index('idx_user_locations_state', 'user_locations', ['state_code'], unique=False)
    op.create_index(op.f('ix_user_locations_id'), 'user_locations', ['id'], unique=False)
    op.create_table('user_privacy_settings',
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('share_location_analytics', sa.Boolean(), nullable=False),
    sa.Column('share_reasoning_analytics', sa.Boolean(), nullable=False),
    sa.Column('allow_public_reasons', sa.Boolean(), nullable=False),
    sa.Column('data_retention_preference', sa.Enum('MINIMAL', 'STANDARD', 'EXTENDED', name='dataretentionpreference'), nullable=False),
    sa.Column('allow_research_contact', sa.Boolean(), nullable=False),
    sa.Column('allow_campaign_updates', sa.Boolean(), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_user_privacy_settings_id'), 'user_privacy_settings', ['id'], unique=False)
    op.create_table('action_analytics_daily',
    sa.Column('bill_id', sa.String(length=36), nullable=False),
    sa.Column('campaign_id', sa.String(length=36), nullable=True),
    sa.Column('date_bucket', sa.Date(), nullable=False),
    sa.Column('total_actions', sa.Integer(), nullable=False),
    sa.Column('support_count', sa.Integer(), nullable=False),
    sa.Column('oppose_count', sa.Integer(), nullable=False),
    sa.Column('amend_count', sa.Integer(), nullable=False),
    sa.Column('unique_users', sa.Integer(), nullable=False),
    sa.Column('top_support_reasons', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('top_oppose_reasons', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('top_amend_reasons', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('geographic_distribution', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('success_rate', sa.DECIMAL(precision=5, scale=2), nullable=False),
    sa.Column('error_rate', sa.DECIMAL(precision=5, scale=2), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['campaign_id'], ['campaigns.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('bill_id', 'campaign_id', 'date_bucket', name='uq_analytics_daily')
    )
    op.create_index('idx_analytics_daily_bill', 'action_analytics_daily', ['bill_id'], unique=False)
    op.create_index('idx_analytics_daily_date', 'action_analytics_daily', ['date_bucket'], unique=False)
    op.create_index(op.f('ix_action_analytics_daily_id'), 'action_analytics_daily', ['id'], unique=False)
    op.create_table('action_errors',
    sa.Column('action_id', sa.String(length=36), nullable=False),
    sa.Column('error_type', sa.Enum('NETWORK_ERROR', 'VALIDATION_ERROR', 'API_ERROR', 'AUTH_ERROR', 'RATE_LIMIT_ERROR', 'SERVICE_UNAVAILABLE', 'UNKNOWN_ERROR', name='errortype'), nullable=False),
    sa.Column('error_code', sa.String(length=50), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=False),
    sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('resolution_status', sa.Enum('UNRESOLVED', 'RESOLVED', 'IGNORED', 'IN_PROGRESS', name='resolutionstatus'), nullable=False),
    sa.Column('retry_count', sa.Integer(), nullable=False),
    sa.Column('resolved_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['action_id'], ['actions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_action_errors_action', 'action_errors', ['action_id'], unique=False)
    op.create_index('idx_action_errors_created', 'action_errors', ['created_at'], unique=False)
    op.create_index('idx_action_errors_status', 'action_errors', ['resolution_status'], unique=False)
    op.create_index('idx_action_errors_type', 'action_errors', ['error_type'], unique=False)
    op.create_index(op.f('ix_action_errors_id'), 'action_errors', ['id'], unique=False)
    op.create_table('action_network_submissions',
    sa.Column('action_id', sa.String(length=36), nullable=False),
    sa.Column('campaign_id', sa.String(length=255), nullable=False),
    sa.Column('submission_id', sa.String(length=255), nullable=True),
    sa.Column('form_type', sa.String(length=50), nullable=False),
    sa.Column('target_chamber', sa.String(length=20), nullable=True),
    sa.Column('embed_url', sa.Text(), nullable=True),
    sa.Column('iframe_url', sa.Text(), nullable=True),
    sa.Column('submission_status', sa.String(length=50), nullable=False),
    sa.Column('action_network_response', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('submitted_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['action_id'], ['actions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_an_submissions_action', 'action_network_submissions', ['action_id'], unique=False)
    op.create_index('idx_an_submissions_campaign', 'action_network_submissions', ['campaign_id'], unique=False)
    op.create_index('idx_an_submissions_status', 'action_network_submissions', ['submission_status'], unique=False)
    op.create_index(op.f('ix_action_network_submissions_id'), 'action_network_submissions', ['id'], unique=False)
    op.create_table('action_reasoning',
    sa.Column('action_id', sa.String(length=36), nullable=False),
    sa.Column('reasoning_option_id', sa.String(length=36), nullable=False),
    sa.Column('is_primary_reason', sa.Boolean(), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['action_id'], ['actions.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['reasoning_option_id'], ['reasoning_options.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('action_id', 'reasoning_option_id', name='uq_action_reasoning')
    )
    op.create_index('idx_action_reasoning_action', 'action_reasoning', ['action_id'], unique=False)
    op.create_index('idx_action_reasoning_option', 'action_reasoning', ['reasoning_option_id'], unique=False)
    op.create_index(op.f('ix_action_reasoning_id'), 'action_reasoning', ['id'], unique=False)
    op.create_table('custom_reasons_pool',
    sa.Column('action_id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('bill_id', sa.String(length=36), nullable=False),
    sa.Column('stance', sa.Enum('SUPPORT', 'OPPOSE', 'AMEND', name='actionstance'), nullable=False),
    sa.Column('custom_reason', sa.Text(), nullable=False),
    sa.Column('is_flagged', sa.Boolean(), nullable=False),
    sa.Column('similarity_cluster_id', sa.String(length=36), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.CheckConstraint("stance IN ('SUPPORT', 'OPPOSE', 'AMEND')", name='check_custom_valid_stance'),
    sa.ForeignKeyConstraint(['action_id'], ['actions.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_custom_reasons_bill', 'custom_reasons_pool', ['bill_id'], unique=False)
    op.create_index('idx_custom_reasons_flagged', 'custom_reasons_pool', ['is_flagged'], unique=False)
    op.create_index('idx_custom_reasons_stance', 'custom_reasons_pool', ['stance'], unique=False)
    op.create_index('idx_custom_reasons_user', 'custom_reasons_pool', ['user_id'], unique=False)
    op.create_index(op.f('ix_custom_reasons_pool_id'), 'custom_reasons_pool', ['id'], unique=False)
    # Add role column with default value, then update existing users
    op.add_column('users', sa.Column('role', sa.String(length=50), nullable=True))
    op.execute("UPDATE users SET role = 'user' WHERE role IS NULL")
    op.alter_column('users', 'role', nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'role')
    op.drop_index(op.f('ix_custom_reasons_pool_id'), table_name='custom_reasons_pool')
    op.drop_index('idx_custom_reasons_user', table_name='custom_reasons_pool')
    op.drop_index('idx_custom_reasons_stance', table_name='custom_reasons_pool')
    op.drop_index('idx_custom_reasons_flagged', table_name='custom_reasons_pool')
    op.drop_index('idx_custom_reasons_bill', table_name='custom_reasons_pool')
    op.drop_table('custom_reasons_pool')
    op.drop_index(op.f('ix_action_reasoning_id'), table_name='action_reasoning')
    op.drop_index('idx_action_reasoning_option', table_name='action_reasoning')
    op.drop_index('idx_action_reasoning_action', table_name='action_reasoning')
    op.drop_table('action_reasoning')
    op.drop_index(op.f('ix_action_network_submissions_id'), table_name='action_network_submissions')
    op.drop_index('idx_an_submissions_status', table_name='action_network_submissions')
    op.drop_index('idx_an_submissions_campaign', table_name='action_network_submissions')
    op.drop_index('idx_an_submissions_action', table_name='action_network_submissions')
    op.drop_table('action_network_submissions')
    op.drop_index(op.f('ix_action_errors_id'), table_name='action_errors')
    op.drop_index('idx_action_errors_type', table_name='action_errors')
    op.drop_index('idx_action_errors_status', table_name='action_errors')
    op.drop_index('idx_action_errors_created', table_name='action_errors')
    op.drop_index('idx_action_errors_action', table_name='action_errors')
    op.drop_table('action_errors')
    op.drop_index(op.f('ix_action_analytics_daily_id'), table_name='action_analytics_daily')
    op.drop_index('idx_analytics_daily_date', table_name='action_analytics_daily')
    op.drop_index('idx_analytics_daily_bill', table_name='action_analytics_daily')
    op.drop_table('action_analytics_daily')
    op.drop_index(op.f('ix_user_privacy_settings_id'), table_name='user_privacy_settings')
    op.drop_table('user_privacy_settings')
    op.drop_index(op.f('ix_user_locations_id'), table_name='user_locations')
    op.drop_index('idx_user_locations_state', table_name='user_locations')
    op.drop_index('idx_user_locations_district', table_name='user_locations')
    op.drop_table('user_locations')
    op.drop_index(op.f('ix_reasoning_options_id'), table_name='reasoning_options')
    op.drop_index('idx_reasoning_category', table_name='reasoning_options')
    op.drop_index('idx_reasoning_bill_stance', table_name='reasoning_options')
    op.drop_index('idx_reasoning_active', table_name='reasoning_options')
    op.drop_table('reasoning_options')
    op.drop_index(op.f('ix_action_analytics_realtime_id'), table_name='action_analytics_realtime')
    op.drop_index('idx_analytics_realtime_updated', table_name='action_analytics_realtime')
    op.drop_table('action_analytics_realtime')
    # ### end Alembic commands ###
