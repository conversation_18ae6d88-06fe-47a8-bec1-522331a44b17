# app/api/v1/api.py
from fastapi import APIRouter
from app.api.v1.endpoints import health, officials, bills, campaigns, actions, ai, admin, action_analytics, simple_analytics, values_simple
# Temporarily disable values_analysis due to import issues
# from app.api.v1.endpoints import values_analysis

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(officials.router, prefix="/officials", tags=["officials"])
api_router.include_router(bills.router, prefix="/bills", tags=["bills"])
api_router.include_router(campaigns.router, prefix="/campaigns", tags=["campaigns"])
api_router.include_router(actions.router, prefix="/actions", tags=["actions"])

# Simple analytics (KISS approach) - primary interface
api_router.include_router(simple_analytics.router, prefix="/simple", tags=["simple-analytics"])

# Advanced analytics (complex features for future)
api_router.include_router(action_analytics.router, prefix="/analytics", tags=["analytics"])

api_router.include_router(ai.router, prefix="/ai", tags=["ai"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
# Use simplified values analysis instead of the complex one
api_router.include_router(values_simple.router, prefix="/values", tags=["values-analysis"])
# Temporarily disable values_analysis router due to import issues
# api_router.include_router(values_analysis.router, prefix="/values", tags=["values-analysis"])
