# app/api/v1/endpoints/action_analytics.py
"""
Action analytics API endpoints for comprehensive civic engagement insights.

These endpoints provide privacy-safe analytics on user actions, reasoning,
and geographic distribution while maintaining strict access controls.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.action_tracking_service import ActionTrackingService
from app.core.auth import get_current_user, ensure_user_exists
from app.models.action_tracking import ActionStance, ReasoningCategory
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/bills/{bill_id}/analytics")
async def get_bill_analytics(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Get comprehensive analytics for a bill
    
    Returns privacy-safe aggregated data including:
    - Support/Opposition/Amendment counts
    - Top reasons by stance
    - Geographic distribution (state level only)
    - Activity trends over time
    """
    try:
        tracking_service = ActionTrackingService(db)
        analytics = tracking_service.get_bill_analytics(bill_id)
        
        if 'error' in analytics:
            raise HTTPException(status_code=500, detail=analytics['error'])
        
        return {
            "success": True,
            "bill_id": bill_id,
            "analytics": analytics,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get bill analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve analytics")


@router.get("/bills/{bill_id}/reasoning-options")
async def get_reasoning_options(
    bill_id: str,
    stance: ActionStance,
    db: Session = Depends(get_db)
):
    """
    Get available reasoning options for a bill and stance
    
    Returns pre-defined reasons users can select from when taking action
    """
    try:
        from app.models.action_tracking import ReasoningOption
        
        options = db.query(ReasoningOption).filter(
            ReasoningOption.bill_id == bill_id,
            ReasoningOption.stance == stance,
            ReasoningOption.is_active == True
        ).order_by(ReasoningOption.display_order).all()
        
        return {
            "success": True,
            "bill_id": bill_id,
            "stance": stance,
            "reasoning_options": [
                {
                    "id": option.id,
                    "reason_text": option.reason_text,
                    "reason_category": option.reason_category,
                    "display_order": option.display_order,
                    "usage_count": option.usage_count
                } for option in options
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to get reasoning options: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve reasoning options")


@router.post("/bills/{bill_id}/reasoning-options")
async def create_reasoning_options(
    bill_id: str,
    stance: ActionStance,
    reasons: List[Dict[str, Any]],
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create reasoning options for a bill (Admin only)
    
    Expected format for reasons:
    [
        {
            "text": "This bill will improve healthcare access",
            "category": "healthcare",
            "display_order": 1
        }
    ]
    """
    try:
        # Check if user is admin
        user = await ensure_user_exists(current_user, db)
        if user.role not in ['admin', 'super_admin']:
            raise HTTPException(status_code=403, detail="Admin access required")
        
        tracking_service = ActionTrackingService(db)
        created_options = tracking_service.create_reasoning_options_for_bill(
            bill_id=bill_id,
            stance=stance,
            reasons=reasons
        )
        
        return {
            "success": True,
            "bill_id": bill_id,
            "stance": stance,
            "created_count": len(created_options),
            "reasoning_options": [
                {
                    "id": option.id,
                    "reason_text": option.reason_text,
                    "reason_category": option.reason_category,
                    "display_order": option.display_order
                } for option in created_options
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create reasoning options: {e}")
        raise HTTPException(status_code=500, detail="Failed to create reasoning options")


@router.post("/actions/{action_id}/track-reasoning")
async def track_action_reasoning(
    action_id: str,
    reasoning_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Track user's reasoning for an action
    
    Expected format:
    {
        "selected_reasons": ["reason_id_1", "reason_id_2"],
        "custom_reasons": ["My custom reason 1", "My custom reason 2"]
    }
    """
    try:
        # Verify user owns this action or is admin
        from app.models.action import Action
        action = db.query(Action).filter(Action.id == action_id).first()
        if not action:
            raise HTTPException(status_code=404, detail="Action not found")
        
        user = await ensure_user_exists(current_user, db)
        if action.user_id != user.id and user.role not in ['admin', 'super_admin']:
            raise HTTPException(status_code=403, detail="Access denied")
        
        tracking_service = ActionTrackingService(db)
        result = tracking_service.track_action_reasoning(
            action_id=action_id,
            selected_reasons=reasoning_data.get('selected_reasons', []),
            custom_reasons=reasoning_data.get('custom_reasons', [])
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to track action reasoning: {e}")
        raise HTTPException(status_code=500, detail="Failed to track reasoning")


@router.post("/users/{user_id}/location")
async def save_user_location(
    user_id: str,
    location_data: Dict[str, str],
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Save encrypted user location data
    
    Expected format:
    {
        "address": "123 Main St, New York, NY",
        "zip_code": "10001"
    }
    """
    try:
        # Verify user can update this location
        user = await ensure_user_exists(current_user, db)
        if user.id != user_id and user.role not in ['admin', 'super_admin']:
            raise HTTPException(status_code=403, detail="Access denied")
        
        tracking_service = ActionTrackingService(db)
        location = tracking_service.save_user_location(
            user_id=user_id,
            address=location_data.get('address', ''),
            zip_code=location_data.get('zip_code', ''),
            source='user_input'
        )
        
        if not location:
            raise HTTPException(status_code=500, detail="Failed to save location")
        
        return {
            "success": True,
            "message": "Location saved with privacy protection",
            "location_id": location.id,
            "analytics_data": {
                "state_code": location.state_code,
                "congressional_district": location.congressional_district,
                "accuracy_level": location.accuracy_level
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to save user location: {e}")
        raise HTTPException(status_code=500, detail="Failed to save location")


@router.get("/analytics/geographic-distribution")
async def get_geographic_distribution(
    bill_id: Optional[str] = Query(None, description="Filter by bill ID"),
    db: Session = Depends(get_db)
):
    """
    Get privacy-safe geographic distribution of actions
    
    Returns state-level aggregated data for users who opted in to location sharing
    """
    try:
        from app.models.action_tracking import UserLocation, UserPrivacySettings
        from app.models.action import Action, ActionStatus
        from sqlalchemy import func, and_
        
        query = db.query(
            UserLocation.state_code,
            func.count(Action.id).label('total_actions'),
            func.count(Action.id).filter(Action.position == 'support').label('support_count'),
            func.count(Action.id).filter(Action.position == 'oppose').label('oppose_count'),
            func.count(func.distinct(Action.user_id)).label('unique_users')
        ).join(Action, UserLocation.user_id == Action.user_id).join(
            UserPrivacySettings, UserLocation.user_id == UserPrivacySettings.user_id
        ).filter(
            and_(
                Action.status.in_([ActionStatus.SENT, ActionStatus.DELIVERED]),
                UserPrivacySettings.share_location_analytics == True,
                UserLocation.state_code.isnot(None)
            )
        )
        
        if bill_id:
            query = query.filter(Action.bill_id == bill_id)
        
        results = query.group_by(UserLocation.state_code).order_by(
            func.count(Action.id).desc()
        ).all()
        
        return {
            "success": True,
            "geographic_distribution": [
                {
                    "state_code": row.state_code,
                    "total_actions": row.total_actions,
                    "support_count": row.support_count,
                    "oppose_count": row.oppose_count,
                    "unique_users": row.unique_users
                } for row in results
            ],
            "privacy_note": "Only includes users who opted in to location analytics",
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get geographic distribution: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve geographic data")


@router.get("/analytics/custom-reasons")
async def get_custom_reasons_analysis(
    bill_id: str,
    stance: ActionStance,
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    Get analysis of custom reasons submitted by users
    
    Returns anonymized custom reasons for content analysis and insights
    """
    try:
        from app.models.action_tracking import CustomReasonsPool, UserPrivacySettings
        from sqlalchemy import func
        
        # Only include reasons from users who allow public sharing
        custom_reasons = db.query(
            CustomReasonsPool.custom_reason,
            CustomReasonsPool.created_at
        ).join(
            UserPrivacySettings, CustomReasonsPool.user_id == UserPrivacySettings.user_id
        ).filter(
            and_(
                CustomReasonsPool.bill_id == bill_id,
                CustomReasonsPool.stance == stance,
                CustomReasonsPool.is_flagged == False,
                UserPrivacySettings.allow_public_reasons == True
            )
        ).order_by(CustomReasonsPool.created_at.desc()).limit(limit).all()
        
        return {
            "success": True,
            "bill_id": bill_id,
            "stance": stance,
            "custom_reasons": [
                {
                    "reason_text": reason.custom_reason,
                    "submitted_at": reason.created_at.isoformat()
                } for reason in custom_reasons
            ],
            "total_count": len(custom_reasons),
            "privacy_note": "Only includes reasons from users who opted in to public sharing"
        }
        
    except Exception as e:
        logger.error(f"Failed to get custom reasons analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve custom reasons")


@router.get("/health/tracking")
async def check_tracking_health(db: Session = Depends(get_db)):
    """
    Health check for action tracking infrastructure
    """
    try:
        from app.services.location_encryption import LocationEncryptionService
        
        # Check database connectivity
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        
        # Check encryption service
        encryption_service = LocationEncryptionService()
        encryption_health = encryption_service.health_check()
        
        # Check table existence
        tables = [
            'reasoning_options', 'action_reasoning', 'custom_reasons_pool',
            'user_locations', 'action_errors', 'action_network_submissions',
            'action_analytics_daily', 'action_analytics_realtime', 'user_privacy_settings'
        ]
        
        table_status = {}
        for table in tables:
            try:
                result = db.execute(text(f"SELECT COUNT(*) FROM {table}")).scalar()
                table_status[table] = f"OK ({result} records)"
            except Exception as e:
                table_status[table] = f"ERROR: {str(e)}"
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "database": "connected",
            "encryption_service": encryption_health,
            "tables": table_status
        }
        
    except Exception as e:
        logger.error(f"Tracking health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }