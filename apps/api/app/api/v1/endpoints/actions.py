# app/api/v1/endpoints/actions.py
"""
Actions API endpoints for the ModernAction platform.

This module provides REST API endpoints for managing user actions (emails, calls, etc.)
sent to government officials through campaigns.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.action import ActionService
from app.core.auth import get_current_user, ensure_user_exists
from app.schemas.action import (
    ActionCreate,
    ActionResponse,
    ActionSummary,
    ActionSearch,
    ActionStats,
    ActionWithDetails
)
from app.models.action import ActionStatus, ActionType
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=ActionResponse, status_code=202)
async def create_action(
    action_data: ActionCreate,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new action and queue it for processing.

    This endpoint creates an action record in the database and schedules
    the actual sending (email, etc.) as a background task to ensure
    fast API responses.

    Returns 202 Accepted to indicate the request has been received
    and is being processed asynchronously.

    Requires authentication - user must be logged in via Auth0.
    """
    try:
        # Ensure user exists in our database
        user = await ensure_user_exists(current_user, db)

        service = ActionService(db)

        # Set the user_id from the authenticated user
        action_data.user_id = str(user.id)

        # Create the action record
        action = service.create_action(action_data)

        # Import the background task function here to avoid circular imports
        from app.tasks import task_process_action

        # Schedule the action processing as a background task
        # This runs after the response is sent to the client
        background_tasks.add_task(
            task_process_action,
            action.id,
            db
        )

        return action

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        raise HTTPException(status_code=500, detail="Failed to create action")

@router.get("/", response_model=List[ActionSummary])
def get_actions(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    campaign_id: Optional[str] = Query(None, description="Filter by campaign ID"),
    official_id: Optional[str] = Query(None, description="Filter by official ID"),
    status: Optional[ActionStatus] = Query(None, description="Filter by action status"),
    action_type: Optional[ActionType] = Query(None, description="Filter by action type"),
    date_from: Optional[datetime] = Query(None, description="Filter actions from this date"),
    date_to: Optional[datetime] = Query(None, description="Filter actions to this date"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of actions to return"),
    offset: int = Query(0, ge=0, description="Number of actions to skip"),
    db: Session = Depends(get_db)
):
    """
    Get a list of actions with optional filtering.

    Returns a paginated list of action summaries ordered by creation date (newest first).
    """
    service = ActionService(db)

    search_params = ActionSearch(
        user_id=user_id,
        campaign_id=campaign_id,
        official_id=official_id,
        status=status,
        action_type=action_type,
        date_from=date_from,
        date_to=date_to,
        limit=limit,
        offset=offset
    )

    actions = service.get_actions(search_params)
    return actions

@router.get("/stats", response_model=ActionStats)
def get_action_stats(
    campaign_id: Optional[str] = Query(None, description="Filter stats by campaign ID"),
    db: Session = Depends(get_db)
):
    """
    Get action statistics.

    Returns statistics about actions including success rates, counts by status/type,
    and average response times.
    """
    service = ActionService(db)
    stats = service.get_action_stats(campaign_id)
    return stats

@router.get("/{action_id}", response_model=ActionWithDetails)
def get_action(
    action_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific action.

    Returns complete action information including related campaign,
    official, and bill details.
    """
    service = ActionService(db)
    action = service.get_action_with_details(action_id)

    if not action:
        raise HTTPException(status_code=404, detail="Action not found")

    return action

@router.post("/{action_id}/retry", response_model=ActionResponse)
def retry_action(
    action_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Retry a failed action.

    This endpoint allows retrying actions that failed to send.
    The action must be in a failed state and have retry attempts remaining.
    """
    service = ActionService(db)

    # Attempt to retry the action
    action = service.retry_failed_action(action_id)

    if not action:
        raise HTTPException(
            status_code=404,
            detail="Action not found or cannot be retried"
        )

    # Schedule the retry as a background task
    from app.tasks import task_send_action_email

    background_tasks.add_task(
        task_send_action_email,
        action.id,
        db
    )

    return action

@router.delete("/{action_id}")
def delete_action(
    action_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete an action.

    Permanently removes the action from the database.
    Note: This should be used carefully as it affects campaign statistics.
    """
    service = ActionService(db)
    success = service.delete_action(action_id)

    if not success:
        raise HTTPException(status_code=404, detail="Action not found")

    return {"message": "Action deleted successfully"}

@router.get("/campaign/{campaign_id}/stats", response_model=ActionStats)
def get_campaign_action_stats(
    campaign_id: str,
    db: Session = Depends(get_db)
):
    """
    Get action statistics for a specific campaign.

    Returns detailed statistics about actions taken for a particular campaign.
    """
    service = ActionService(db)
    stats = service.get_action_stats(campaign_id)
    return stats

@router.get("/user/{user_id}/actions", response_model=List[ActionSummary])
def get_user_actions(
    user_id: str,
    limit: int = Query(20, ge=1, le=100, description="Maximum number of actions to return"),
    offset: int = Query(0, ge=0, description="Number of actions to skip"),
    db: Session = Depends(get_db)
):
    """
    Get all actions taken by a specific user.

    Returns a paginated list of actions taken by the user,
    ordered by creation date (newest first).
    """
    service = ActionService(db)

    search_params = ActionSearch(
        user_id=user_id,
        limit=limit,
        offset=offset
    )

    actions = service.get_actions(search_params)
    return actions


@router.post("/bill-action", response_model=ActionResponse, status_code=202)
async def create_bill_action(
    bill_id: str,
    position: str,  # 'support', 'oppose', 'amend'
    zip_code: str,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db),
    custom_message: Optional[str] = None,
    use_action_network: bool = False
):
    """
    Create an action for a specific bill with enhanced features.

    This endpoint simplifies taking action on bills by:
    1. Looking up representatives for the user's ZIP code
    2. Optionally personalizing the message with AI
    3. Supporting Action Network delivery
    4. Using existing action processing infrastructure
    """
    try:
        # Ensure user exists in our database
        user = await ensure_user_exists(current_user, db)

        # Get the bill
        from app.models.bill import Bill
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")

        # Look up representatives for ZIP code
        from app.services.officials import OfficialService
        officials_service = OfficialService(db)
        officials = officials_service.get_officials_by_zip_code(zip_code)

        if not officials:
            raise HTTPException(status_code=400, detail=f"No representatives found for ZIP code {zip_code}")

        # Use the first representative (could be enhanced to let user choose)
        representative = officials[0]

        # Generate or use custom message
        if custom_message:
            message = custom_message
        else:
            # Use bill summary or create a basic message
            message = f"I am writing to express my {position} for {bill.bill_number}: {bill.title}."
            if bill.ai_summary:
                message += f"\n\n{bill.ai_summary}"

        # Create action using existing schema
        action_data = ActionCreate(
            campaign_id=bill_id,  # Use bill as campaign for now
            official_id=str(representative.id),
            subject=f"Regarding {bill.bill_number}: {bill.title}",
            message=message,
            action_type=ActionType.EMAIL,
            user_name=current_user.get('name', 'Constituent'),
            user_email=current_user.get('email'),
            user_zip_code=zip_code,
            action_types=["ACTION_NETWORK"] if use_action_network else ["EMAIL"],
            use_action_network=use_action_network,
            bill_id=bill_id,
            position=position
        )

        service = ActionService(db)
        action_data.user_id = str(user.id)
        action = service.create_action(action_data)

        # Schedule the action processing as a background task
        from app.tasks import task_process_action
        background_tasks.add_task(task_process_action, action.id, db)

        return action

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating bill action: {e}")
        raise HTTPException(status_code=500, detail="Failed to create bill action")

# Pydantic models for the new submit endpoint
from pydantic import BaseModel
from typing import List, Optional

class ActionSubmitRequest(BaseModel):
    bill_id: str
    stance: str  # "support", "oppose", "amend"
    selected_reasons: List[str]
    custom_reasons: Optional[List[str]] = []  # User-submitted custom reasons
    personal_stories: Optional[str] = ""  # User's personal story for AI personalization
    custom_message: Optional[str] = ""
    zip_code: str
    email: str
    address: Optional[str] = ""
    city: Optional[str] = ""
    state: Optional[str] = ""

class MessagePreviewRequest(BaseModel):
    bill_id: str
    stance: str  # "support", "oppose", "amend"
    selected_reasons: List[str]
    custom_reasons: Optional[List[str]] = []  # User-submitted custom reasons
    personal_stories: Optional[str] = ""  # User's personal story for AI personalization
    first_name: str
    last_name: str
    zip_code: str

@router.post("/preview-message")
async def preview_message(
    preview_request: MessagePreviewRequest,
    db: Session = Depends(get_db)
):
    """
    Preview personalized message before submission

    This endpoint generates the AI-personalized message and shows representatives
    without actually submitting the action. Used for message preview step.
    """
    try:
        # 1. Get bill data
        from app.services.bills import BillService
        bill_service = BillService(db)
        bill = bill_service.get_bill(preview_request.bill_id)

        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")

        # 2. Look up representatives
        from app.services.officials_service import OfficialsService
        officials_service = OfficialsService()

        representatives_result = await officials_service.lookup_representatives_by_zip(
            preview_request.zip_code
        )

        if representatives_result.get('status') != 'success':
            raise HTTPException(
                status_code=400,
                detail=f"Could not find representatives for ZIP code {preview_request.zip_code}"
            )

        # Extract representatives from the result
        representatives = []
        if representatives_result.get('senators'):
            representatives.extend(representatives_result['senators'])
        if representatives_result.get('representative'):
            representatives.append(representatives_result['representative'])

        # 3. Generate personalized message
        from app.services.message_personalization_service import MessagePersonalizationService
        personalization_service = MessagePersonalizationService()

        message_result = await personalization_service.create_personalized_messages({
            'bill': bill,
            'position': preview_request.stance,
            'user_info': {
                'first_name': preview_request.first_name,
                'last_name': preview_request.last_name,
                'email': '<EMAIL>',
                'zip_code': preview_request.zip_code
            },
            'representatives': representatives,
            'selected_reasons': preview_request.selected_reasons,
            'custom_reasons': preview_request.custom_reasons,
            'personal_stories': preview_request.personal_stories
        })

        if not message_result.get('status') == 'success':
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate personalized message: {message_result.get('message', 'Unknown error')}"
            )

        # Return preview data
        personalized_messages = message_result.get('messages', [])

        return {
            "success": True,
            "bill": {
                "id": bill.id,
                "title": bill.title,
                "bill_number": bill.bill_number
            },
            "representatives": representatives,
            "personalized_messages": personalized_messages,
            "stance": preview_request.stance,
            "selected_reasons": preview_request.selected_reasons,
            "custom_reasons": preview_request.custom_reasons or []
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Message preview failed: {e}")
        raise HTTPException(status_code=500, detail=f"Message preview failed: {str(e)}")

@router.post("/submit")
async def submit_action(
    action_request: ActionSubmitRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Submit user action with personalized message to representatives

    This endpoint implements the complete Part 2 workflow with dual delivery:
    1. Get bill data with AI analysis
    2. Look up user's representatives by ZIP code
    3. Generate personalized message using AI
    4. Submit via Action Network (primary)
    5. Send direct emails as fallback/backup
    6. Log the action in database with comprehensive tracking
    """
    try:
        # Import services
        from app.services.officials_service import OfficialsService
        from app.services.action_network_service import ActionNetworkService
        from app.services.message_personalization_service import MessagePersonalizationService
        from app.services.bills import BillService


        # 1. Get the bill data
        bill_service = BillService(db)
        bill = bill_service.get_bill(action_request.bill_id)
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")

        # 2. Get user's representatives
        officials_service = OfficialsService()
        officials_result = await officials_service.lookup_representatives_by_zip(action_request.zip_code)

        if not officials_result.get('success', False):
            return {
                "success": False,
                "error": "Unable to find representatives for this ZIP code. Please verify your ZIP code and try again.",
                "details": officials_result.get('error', 'Unknown error')
            }

        # Get all representatives first
        all_representatives = []
        senators = officials_result.get('senators', [])
        house_rep = officials_result.get('representative')
        
        if senators:
            all_representatives.extend(senators)
        if house_rep:
            all_representatives.append(house_rep)

        if not all_representatives:
            return {
                "success": False,
                "error": "No representatives found for this ZIP code"
            }

        # Determine which representatives to contact based on bill status and routing logic
        action_network = ActionNetworkService()
        routing_info = action_network.determine_target_chamber_and_form({
            'chamber': bill.chamber,
            'status': bill.status.value if bill.status else 'introduced',
            'bill_type': bill.bill_type.value if bill.bill_type else 'house_bill'
        })
        
        # Filter representatives based on routing decision
        target_chamber = routing_info['target_chamber']
        if target_chamber == 'senate':
            # Only contact senators
            representatives = [rep for rep in all_representatives if rep.get('chamber') == 'senate']
            logger.info(f"Routing to Senate: contacting {len(representatives)} senators")
        elif target_chamber == 'house':
            # Only contact house representatives  
            representatives = [rep for rep in all_representatives if rep.get('chamber') == 'house']
            logger.info(f"Routing to House: contacting {len(representatives)} house representatives")
        else:
            # Contact all representatives (both chambers)
            representatives = all_representatives
            logger.info(f"Routing to both chambers: contacting {len(representatives)} representatives")
        
        if not representatives:
            return {
                "success": False,
                "error": f"No {target_chamber} representatives found for this ZIP code based on bill status",
                "routing_info": routing_info
            }

        # 3. Generate personalized message
        personalization_service = MessagePersonalizationService()
        message_result = await personalization_service.create_personalized_messages({
            'bill': bill,
            'position': action_request.stance,
            'user_info': {
                'first_name': current_user.get('given_name', ''),
                'last_name': current_user.get('family_name', ''),
                'email': action_request.email,
                'zip_code': action_request.zip_code
            },
            'representatives': representatives,
            'custom_message': action_request.custom_message,
            'selected_reasons': action_request.selected_reasons
        })

        if not message_result.get('success', False):
            return {
                "success": False,
                "error": "Failed to generate personalized message",
                "details": message_result.get('error', 'Unknown error')
            }

        # 4. Submit via Action Network
        action_network = ActionNetworkService()

        # Use the first personalized message (they should all be similar)
        personalized_messages = message_result.get('personalized_messages', {})
        if not personalized_messages:
            return {
                "success": False,
                "error": "No personalized messages generated"
            }

        # Get the first message
        first_message = next(iter(personalized_messages.values()))

        # 4. Submit via Action Network (primary delivery method)
        action_network_success = False
        action_network_result = {}

        try:
            logger.info(f"Submitting message via Action Network for bill {bill.bill_number}")

            submission_result = await action_network.submit_message({
                'person': {
                    'first_name': current_user.get('given_name', ''),
                    'last_name': current_user.get('family_name', ''),
                    'email': action_request.email,
                    'address_line1': action_request.address,
                    'city': action_request.city,
                    'state': action_request.state,
                    'zip_code': action_request.zip_code
                },
                'targets': representatives,
                'subject': f"Regarding {bill.title}",
                'body': first_message,
                'bill_number': bill.bill_number,
                'position': action_request.stance,
                # Enhanced bill information for smart routing
                'chamber': bill.chamber,
                'status': bill.status.value if bill.status else 'introduced',
                'bill_type': bill.bill_type.value if bill.bill_type else 'house_bill'
            })

            logger.info(f"Action Network submission result: {submission_result}")

            if submission_result.get('status') == 'success':
                action_network_success = True
                action_network_result = {
                    'success': True,
                    'message': submission_result.get('message', 'Successfully submitted via Action Network'),
                    'total_targets': submission_result.get('total_targets', len(representatives)),
                    'successful_submissions': submission_result.get('successful_submissions', 0),
                    'failed_submissions': submission_result.get('failed_submissions', 0),
                    'results': submission_result.get('results', [])
                }
            else:
                action_network_result = {
                    'success': False,
                    'error': submission_result.get('message', 'Action Network submission failed'),
                    'details': submission_result.get('error', 'Unknown error')
                }

        except Exception as e:
            logger.error(f"Action Network submission failed: {e}")
            action_network_result = {
                'success': False,
                'error': f"Network error: {str(e)}"
            }

        # Calculate overall success based on Action Network results
        overall_success = action_network_success

        # 5. Save action records to database for tracking
        from app.models.action import Action, ActionStatus, ActionType
        from app.models.user import User
        from app.models.campaign import Campaign
        import uuid
        from datetime import datetime

        user_name = f"{current_user.get('given_name', '')} {current_user.get('family_name', '')}".strip()
        user_email = action_request.email

        # Get or create user
        user = db.query(User).filter(User.email == user_email).first()
        if not user:
            # Create a minimal user record for tracking
            user = User(
                id=str(uuid.uuid4()),
                auth0_user_id=current_user.get('sub', ''),
                email=user_email,
                name=user_name,
                is_active=True
            )
            db.add(user)
            db.commit()
            db.refresh(user)

        # Get or create a campaign for this bill
        campaign = db.query(Campaign).filter(Campaign.bill_id == bill.id).first()
        if not campaign:
            # Create a default campaign for this bill
            campaign = Campaign(
                id=str(uuid.uuid4()),
                title=f"Action Campaign for {bill.title}",
                description=f"Constituent actions for {bill.bill_number}",
                call_to_action=f"Contact your representatives about {bill.title}",
                bill_id=bill.id,
                status="active"
            )
            db.add(campaign)
            db.commit()
            db.refresh(campaign)

        # Create action records for each representative contacted
        action_records = []
        action_network_id = None

        # Extract Action Network ID from results
        if action_network_success and action_network_result.get('results'):
            # Get the first successful result's message_id
            for result in action_network_result['results']:
                if result.get('success') and result.get('message_id'):
                    action_network_id = result['message_id']
                    break

        for rep in representatives:
            # Determine status based on Action Network results
            rep_success = False
            error_message = None

            if action_network_success:
                # Check if this specific representative was successfully contacted
                for result in action_network_result.get('results', []):
                    if result.get('target_name') == rep.get('name') and result.get('success'):
                        rep_success = True
                        break

                if not rep_success:
                    # Find error for this specific representative
                    for result in action_network_result.get('results', []):
                        if result.get('target_name') == rep.get('name') and not result.get('success'):
                            error_message = result.get('error', 'Action Network submission failed')
                            break

            status = ActionStatus.SENT if rep_success else ActionStatus.FAILED
            delivery_method = "action_network" if rep_success else "failed"

            # Get personalized message for this representative
            rep_message = personalized_messages.get(rep.get('name', ''), first_message)

            action = Action(
                id=str(uuid.uuid4()),
                subject=f"Regarding {bill.title}",
                message=rep_message,
                action_type=ActionType.EMAIL,
                status=status,
                user_name=user_name,
                user_email=user_email,
                user_address=action_request.address,
                user_zip_code=action_request.zip_code,
                contact_email=rep.get('email'),
                contact_phone=rep.get('phone'),
                delivery_method=delivery_method,
                delivery_id=action_network_id,
                error_message=error_message,
                user_id=user.id,
                campaign_id=campaign.id,
                official_id=rep.get('id'),  # This might need to be created/looked up
                bill_id=bill.id,
                position=action_request.stance,
                action_network_id=action_network_id,
                personalized_content={
                    'original_message': rep_message,
                    'selected_reasons': action_request.selected_reasons,
                    'custom_reasons': action_request.custom_reasons or [],
                    'stance': action_request.stance
                },
                representative_info={
                    'name': rep.get('name'),
                    'title': rep.get('title'),
                    'email': rep.get('email'),
                    'phone': rep.get('phone'),
                    'level': rep.get('level'),
                    'chamber': rep.get('chamber')
                },
                user_location={
                    'zip_code': action_request.zip_code,
                    'address': action_request.address,
                    'city': action_request.city,
                    'state': action_request.state
                },
                sent_at=datetime.now() if status == ActionStatus.SENT else None
            )

            db.add(action)
            action_records.append(action)

        # Update campaign action count
        campaign.actual_actions += len(action_records)

        db.commit()

        # 6. Return comprehensive results with Action Network embed information
        return {
            "success": True,  # Always true since we're providing embed info for user to complete
            "message": "Action Network campaign ready for user completion",
            "action_network_embed": {
                "campaign_id": action_network_result.get('campaign_id'),
                "chamber": action_network_result.get('chamber'),
                "embed_url": action_network_result.get('embed_url'),
                "iframe_url": action_network_result.get('iframe_url'),
                "status": action_network_result.get('status')
            },
            "delivery_summary": {
                "method": "action_network_embed",
                "requires_user_completion": True,
                "total_targets": len(representatives),
                "message": "User must complete Action Network form to send messages"
            },
            "officials_contacted": len(representatives),
            "representatives": [rep.get('name', 'Unknown') for rep in representatives],
            "personalized_message_preview": first_message[:200] + "..." if len(first_message) > 200 else first_message,
            "database_records": {
                "actions_created": len(action_records),
                "campaign_id": str(campaign.id),
                "user_id": str(user.id)
            },
            "user_instructions": {
                "next_step": "Complete the Action Network form to send your message to representatives",
                "form_url": action_network_result.get('embed_url'),
                "note": "Your message will be delivered directly to your representatives through Action Network"
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting action: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to submit action: {str(e)}")


@router.post("/submit-dev")
async def submit_action_dev(
    action_request: ActionSubmitRequest,
    db: Session = Depends(get_db)
):
    """
    Development-only endpoint for testing action submission without authentication.

    This endpoint bypasses Auth0 authentication for frontend testing purposes.
    Should only be used in development environments.
    """
    print("=== SUBMIT-DEV ENDPOINT CALLED ===")
    logger.info("=== SUBMIT-DEV ENDPOINT CALLED ===")
    try:
        # Mock user data for development testing
        mock_user = {
            'sub': 'dev-test-user',
            'email': action_request.email,
            'given_name': 'Test',
            'family_name': 'User',
            'name': 'Test User'
        }

        # Import services
        from app.services.officials_service import OfficialsService
        from app.services.action_network_service import ActionNetworkService
        from app.services.bills import BillService

        logger.info(f"Development action submission for bill: {action_request.bill_id}")

        # 1. Get bill data
        bill_service = BillService(db)
        bill = bill_service.get_bill(action_request.bill_id)
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")

        # 2. Look up representatives
        officials_service = OfficialsService()
        logger.info(f"Looking up representatives for ZIP code: {action_request.zip_code}")
        representatives_response = await officials_service.lookup_representatives_by_zip(action_request.zip_code)
        logger.info(f"Representatives response: {representatives_response}")

        if not representatives_response or representatives_response.get('status') != 'success':
            logger.error(f"No representatives found for ZIP code {action_request.zip_code}: {representatives_response}")
            return {
                "success": False,
                "error": "No representatives found for ZIP code",
                "zip_code": action_request.zip_code
            }

        # Extract the actual representatives list from the response
        representatives = []
        if representatives_response.get('senators'):
            representatives.extend(representatives_response['senators'])
        if representatives_response.get('representative'):
            representatives.append(representatives_response['representative'])

        logger.info(f"Extracted {len(representatives)} representatives")
        for i, rep in enumerate(representatives):
            logger.info(f"Representative {i+1}: {type(rep)} - {rep.get('full_name', 'Unknown') if isinstance(rep, dict) else rep}")

        # 3. Generate personalized message
        from app.services.message_personalization_service import MessagePersonalizationService
        personalization_service = MessagePersonalizationService()
        logger.info(f"Creating personalized messages for {len(representatives)} representatives")
        message_result = await personalization_service.create_personalized_messages({
            'bill': bill,
            'position': action_request.stance,
            'user_info': {
                'first_name': 'Test',
                'last_name': 'User',
                'email': action_request.email,
                'zip_code': action_request.zip_code
            },
            'representatives': representatives,
            'selected_reasons': action_request.selected_reasons,
            'custom_reasons': action_request.custom_reasons,
            'personal_stories': action_request.personal_stories,
            'custom_message': action_request.custom_message
        })

        logger.info(f"Message result: {message_result}")

        if message_result.get('status') != 'success':
            logger.error(f"Message personalization failed: {message_result}")
            return {
                "success": False,
                "error": "Failed to generate personalized message",
                "details": message_result.get('message', 'Unknown error')
            }

        # 4. Submit via Action Network with enhanced routing and tracking
        action_network = ActionNetworkService()

        # Use the first personalized message
        personalized_messages = message_result.get('messages', [])
        if not personalized_messages:
            return {
                "success": False,
                "error": "No personalized messages generated"
            }

        # Get the first message
        first_message = personalized_messages[0] if personalized_messages else {}

        # Enhanced routing logic based on bill chamber and status
        try:
            logger.info(f"=== ENHANCED ACTION NETWORK ROUTING ===")
            logger.info(f"Bill: {bill.bill_number} ({bill.chamber}) - Status: {bill.status}")
            
            # Get smart routing information
            routing_result = await action_network.get_campaign_embed_info({
                'chamber': bill.chamber,
                'status': bill.status.value if bill.status else 'introduced',
                'bill_type': bill.bill_type.value if bill.bill_type else 'house_bill',
                'bill_number': bill.bill_number,
                'position': action_request.stance
            })
            
            logger.info(f"Routing decision: {routing_result}")
            
            # Helper functions for chamber identification
            def is_senator(rep):
                """Check if representative is a Senator based on chamber or title"""
                chamber = rep.get('chamber', '').lower()
                title = rep.get('title', '').lower()
                return chamber == 'senate' or 'senator' in title
                
            def is_house_rep(rep):
                """Check if representative is a House rep based on chamber or title"""
                chamber = rep.get('chamber', '').lower()
                title = rep.get('title', '').lower()
                return chamber == 'house' or 'representative' in title
                
            # Filter representatives based on routing decision  
            target_chamber = routing_result.get('target_chamber', bill.chamber)
            # For testing: force to "both" if we can't match chamber properly
            if target_chamber in ['senate', 'house'] and not any(
                (target_chamber == 'senate' and is_senator(rep)) or 
                (target_chamber == 'house' and is_house_rep(rep)) 
                for rep in representatives
            ):
                logger.info(f"No {target_chamber} reps found, routing to both chambers for testing")
                target_chamber = 'both'
            
            if target_chamber == 'senate':
                # Only contact senators
                filtered_reps = [rep for rep in representatives if is_senator(rep)]
                logger.info(f"Routing to Senate: {len(filtered_reps)} senators")
            elif target_chamber == 'house':
                # Only contact house representatives
                filtered_reps = [rep for rep in representatives if is_house_rep(rep)]
                logger.info(f"Routing to House: {len(filtered_reps)} house representatives")
            else:
                # Contact all representatives (both chambers)
                filtered_reps = representatives
                logger.info(f"Routing to both chambers: {len(filtered_reps)} representatives")
            
            # Error handling for no representatives found
            if not filtered_reps:
                return {
                    "success": False,
                    "error": f"No {target_chamber} representatives found for ZIP code {action_request.zip_code}",
                    "routing_info": routing_result,
                    "available_representatives": len(representatives),
                    "target_chamber": target_chamber
                }
            
            submission_result = {
                "success": True,
                "message": f"DEV: Action Network {routing_result.get('chamber', 'form').upper()} form ready",
                "action_network_id": "dev-test-" + str(hash(str(first_message)))[:8],
                "embed_url": routing_result.get('embed_url'),
                "iframe_url": routing_result.get('iframe_url'),
                "campaign_id": routing_result.get('campaign_id'),
                "target_chamber": target_chamber,
                "representatives_contacted": len(filtered_reps),
                "routing_reason": routing_result.get('routing_info', {}).get('routing_reason'),
                "mock": True,
                "routing_test": routing_result
            }
            
            # Update representatives list to filtered ones
            representatives = filtered_reps
            
        except Exception as e:
            logger.error(f"Action Network routing failed: {e}")
            submission_result = {
                "success": False,
                "error": f"Action Network routing failed: {str(e)}",
                "mock": True,
                "routing_error": str(e)
            }

        # 5. Create or get a development test user
        from app.models.user import User
        from app.models.action import Action, ActionStatus, ActionType
        from app.db.types import get_uuid_type
        import uuid

        # Get or create test user for development
        test_user = db.query(User).filter(User.email == action_request.email).first()
        if not test_user:
            test_user = User(
                id=str(uuid.uuid4()),
                auth0_user_id='dev-test-user-' + str(hash(action_request.email))[:8],
                email=action_request.email,
                name='Test User',
                is_active=True,
                email_verified=True
            )
            db.add(test_user)
            db.commit()
            db.refresh(test_user)

        # Save action to database with comprehensive Action Network tracking
        action = Action(
            id=str(uuid.uuid4()),
            subject=first_message.get('subject', f"Action on {bill.bill_number}"),
            message=first_message.get('body', ''),
            action_type=ActionType.EMAIL,
            user_name=mock_user['name'],
            user_email=mock_user['email'],
            user_zip_code=action_request.zip_code,
            user_address=action_request.address,
            campaign_id=None,  # Don't use foreign key for dev testing
            official_id=None,  # Don't use foreign key for dev testing
            user_id=test_user.id,  # Use the test user ID
            status=ActionStatus.PENDING,
            # Enhanced tracking fields
            bill_id=action_request.bill_id,
            position=action_request.stance,
            action_network_id=submission_result.get('action_network_id'),
            personalized_content=first_message,
            representative_info=representatives,
            user_location={
                "zip_code": action_request.zip_code,
                "address": action_request.address,
                "city": action_request.city,
                "state": action_request.state
            },
            # User input tracking fields
            selected_reasons=action_request.selected_reasons,
            custom_reasons=action_request.custom_reasons or [],
            original_ai_message=first_message.get('body', ''),
            user_edited_message=action_request.custom_message if action_request.custom_message else None,
            message_was_edited=bool(action_request.custom_message),
            # Basic Action Network tracking (store extra info in action_metadata)
            delivery_method="action_network_embed",
            delivery_id=submission_result.get('campaign_id'),
            action_metadata=str({
                "action_network_campaign_id": submission_result.get('campaign_id'),
                "action_network_embed_url": submission_result.get('embed_url'),
                "action_network_iframe_url": submission_result.get('iframe_url'),
                "routing_info": {
                    "target_chamber": submission_result.get('target_chamber'),
                    "routing_reason": submission_result.get('routing_reason'),
                    "form_type": submission_result.get('routing_test', {}).get('chamber'),
                    "representatives_contacted": submission_result.get('representatives_contacted', 0),
                    "bill_chamber": bill.chamber,
                    "bill_status": bill.status.value if bill.status else 'introduced'
                }
            })
        )

        db.add(action)
        db.commit()
        db.refresh(action)

        logger.info(f"Development action submitted successfully for {len(representatives)} representatives")

        # 6. Track user reasoning (simple approach)
        try:
            from app.services.simple_action_tracking import get_simple_tracking
            
            tracker = get_simple_tracking(db)
            tracker.track_user_reasoning(
                action_id=action.id,
                selected_reasons=action_request.selected_reasons or [],
                custom_reason=getattr(action_request, 'custom_reason', None)
            )
            logger.info(f"Tracked reasoning for action {action.id}")
        except Exception as e:
            # Don't fail the whole request if reasoning tracking fails
            logger.warning(f"Failed to track reasoning for action {action.id}: {e}")

        # 7. Return comprehensive results with Action Network embed information
        return {
            "success": True,
            "message": "Action Network form ready for user completion",
            "action_network_id": submission_result.get('action_network_id'),
            "action_id": action.id,
            "status": "PENDING",
            "officials_contacted": 0,  # Set to 0 because we haven't actually sent yet
            "officials_ready_to_contact": len(representatives),
            "representatives": [rep.get('name', 'Unknown') for rep in representatives],
            "personalized_message_preview": first_message,
            "development_mode": True,
            "requires_user_confirmation": True,
            # Action Network embed information for frontend
            "action_network": {
                "embed_url": submission_result.get('embed_url'),
                "iframe_url": submission_result.get('iframe_url'),
                "campaign_id": submission_result.get('campaign_id'),
                "target_chamber": submission_result.get('target_chamber'),
                "form_type": submission_result.get('routing_test', {}).get('chamber', 'house'),
                "routing_reason": submission_result.get('routing_reason')
            },
            # Routing information for debugging
            "routing": {
                "bill_chamber": bill.chamber,
                "bill_status": bill.status.value if bill.status else 'introduced',
                "target_chamber": submission_result.get('target_chamber'),
                "reason": submission_result.get('routing_reason'),
                "representatives_filtered": len(representatives)
            },
            # User instructions
            "user_instructions": {
                "next_step": f"Complete the Action Network {submission_result.get('routing_test', {}).get('chamber', 'house').title()} form",
                "form_url": submission_result.get('embed_url'),
                "note": f"Your message will be sent to {len(representatives)} {submission_result.get('target_chamber', 'house')} representatives"
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting development action: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to submit action: {str(e)}")


@router.get("/action-network/health")
async def check_action_network_health(db: Session = Depends(get_db)):
    """
    Health check endpoint for Action Network integration
    
    Returns status of Action Network service configuration and campaign availability
    """
    try:
        from app.services.action_network_service import ActionNetworkService
        
        action_network = ActionNetworkService()
        health_result = action_network.health_check()
        
        return {
            "service": "action_network",
            "timestamp": datetime.now().isoformat(),
            **health_result
        }
        
    except Exception as e:
        logger.error(f"Action Network health check failed: {e}")
        return {
            "service": "action_network",
            "status": "error",
            "message": f"Health check failed: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


@router.post("/action-network/test-routing")
async def test_action_network_routing(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Test Action Network routing logic for a specific bill
    
    Returns routing decision and available forms without creating an action
    """
    try:
        from app.services.action_network_service import ActionNetworkService
        from app.services.bills import BillService
        
        # Get bill data
        bill_service = BillService(db)
        bill = bill_service.get_bill(bill_id)
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")
        
        # Test routing logic
        action_network = ActionNetworkService()
        routing_result = await action_network.get_campaign_embed_info({
            'chamber': bill.chamber,
            'status': bill.status.value if bill.status else 'introduced',
            'bill_type': bill.bill_type.value if bill.bill_type else 'house_bill',
            'bill_number': bill.bill_number,
            'position': 'support'  # Default position for testing
        })
        
        return {
            "bill": {
                "id": bill.id,
                "number": bill.bill_number,
                "title": bill.title[:100] + "..." if len(bill.title) > 100 else bill.title,
                "chamber": bill.chamber,
                "status": bill.status.value if bill.status else 'introduced',
                "bill_type": bill.bill_type.value if bill.bill_type else 'house_bill'
            },
            "routing_test": routing_result,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Action Network routing test failed: {e}")
        raise HTTPException(status_code=500, detail=f"Routing test failed: {str(e)}")
