# app/api/v1/endpoints/bills.py
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.bills import BillService
from app.tasks import task_generate_summary_for_bill, task_regenerate_summary_for_bill
from app.schemas.bill import (
    BillResponse,
    BillCreate,
    BillUpdate,
    BillSummaryVersionResponse,
    BillStatusHistoryResponse,
    BillTimelineResponse
)
from app.models.bill import BillStatus, BillType

router = APIRouter()

@router.get("/", response_model=List[BillResponse])
def get_bills(
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get a list of bills with pagination.

    Returns a paginated list of bills ordered by creation date (newest first).
    """
    try:
        # Use a more efficient query that doesn't load all the heavy JSON fields
        from app.models.bill import Bill
        from sqlalchemy import desc

        bills = (
            db.query(Bill)
            .order_by(desc(Bill.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

        # Convert to response format without heavy JSON deserialization
        bill_responses = []
        for bill in bills:
            bill_response = BillResponse(
                id=bill.id,
                title=bill.title,
                description=bill.description,
                bill_number=bill.bill_number,
                bill_type=bill.bill_type,
                status=bill.status,
                session_year=bill.session_year,
                chamber=bill.chamber,
                state=bill.state,
                summary=bill.summary,
                ai_summary=bill.ai_summary,
                simple_summary=bill.simple_summary,
                tldr=bill.tldr,
                sponsor_name=bill.sponsor_name,
                sponsor_party=bill.sponsor_party,
                sponsor_state=bill.sponsor_state,
                is_featured=bill.is_featured,
                priority_score=bill.priority_score,
                created_at=bill.created_at,
                updated_at=bill.updated_at,
                introduced_date=bill.introduced_date,
                last_action_date=bill.last_action_date,
                openstates_id=bill.openstates_id,
                congress_gov_id=bill.congress_gov_id,
                # Set JSON fields to empty lists to avoid parsing issues
                cosponsors=[],
                vote_history=[],
                tags=[],
                categories=[],
                reasons_for_support=[],
                reasons_for_opposition=[]
            )
            bill_responses.append(bill_response)

        return bill_responses

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error fetching bills: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch bills")

@router.get("/simple", response_model=List[dict])
def get_bills_simple(
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get a simple list of bills without JSON deserialization.

    This is a lightweight endpoint that avoids JSON parsing issues.
    """
    try:
        from app.models.bill import Bill
        from sqlalchemy import desc

        bills = (
            db.query(Bill.id, Bill.title, Bill.bill_number, Bill.status,
                    Bill.chamber, Bill.ai_summary, Bill.is_featured,
                    Bill.priority_score, Bill.created_at)
            .order_by(desc(Bill.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

        return [
            {
                "id": bill.id,
                "title": bill.title,
                "bill_number": bill.bill_number,
                "status": bill.status.value if bill.status else "unknown",
                "chamber": bill.chamber,
                "ai_summary": bill.ai_summary,
                "is_featured": bill.is_featured,
                "priority_score": bill.priority_score,
                "created_at": bill.created_at.isoformat() if bill.created_at else None
            }
            for bill in bills
        ]

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error fetching simple bills: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch bills")

@router.get("/test")
def test_endpoint():
    """Simple test endpoint that doesn't touch the database"""
    return {"status": "ok", "message": "Bills API is responding"}

@router.get("/search", response_model=List[BillResponse])
def search_bills(
    query: Optional[str] = Query(None, description="Search term for title, description, summary, or bill number"),
    bill_type: Optional[BillType] = Query(None, description="Type of bill (house_bill, senate_bill, etc.)"),
    status: Optional[BillStatus] = Query(None, description="Bill status (introduced, committee, passed, etc.)"),
    session_year: Optional[int] = Query(None, description="Legislative session year"),
    chamber: Optional[str] = Query(None, description="Chamber (house, senate)"),
    state: Optional[str] = Query(None, description="State abbreviation or 'federal'"),
    sponsor_name: Optional[str] = Query(None, description="Sponsor name (partial match)"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    skip: int = Query(0, ge=0, description="Number of results to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of results to return"),
    db: Session = Depends(get_db)
):
    """
    Search bills based on various criteria.

    Supports text search across title, description, summary, and bill number.
    Results are ordered by priority score and creation date.
    """
    service = BillService(db)
    bills = service.search_bills(
        query=query,
        bill_type=bill_type,
        status=status,
        session_year=session_year,
        chamber=chamber,
        state=state,
        sponsor_name=sponsor_name,
        is_featured=is_featured,
        skip=skip,
        limit=limit
    )
    return bills

@router.get("/featured", response_model=List[BillResponse])
def get_featured_bills(
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get featured bills.

    Returns bills marked as featured, ordered by priority score.
    """
    service = BillService(db)
    bills = service.get_featured_bills(skip=skip, limit=limit)
    return bills

@router.get("/status/{status}", response_model=List[BillResponse])
def get_bills_by_status(
    status: BillStatus,
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get bills by status.

    Returns bills with the specified status (introduced, committee, passed, etc.).
    """
    service = BillService(db)
    bills = service.get_bills_by_status(status, skip=skip, limit=limit)
    return bills

@router.get("/type/{bill_type}", response_model=List[BillResponse])
def get_bills_by_type(
    bill_type: BillType,
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get bills by type.

    Returns bills of the specified type (house_bill, senate_bill, etc.).
    """
    service = BillService(db)
    bills = service.get_bills_by_type(bill_type, skip=skip, limit=limit)
    return bills

@router.get("/session/{session_year}", response_model=List[BillResponse])
def get_bills_by_session_year(
    session_year: int,
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get bills by session year.

    Returns bills from the specified legislative session year.
    """
    service = BillService(db)
    bills = service.get_bills_by_session_year(session_year, skip=skip, limit=limit)
    return bills

@router.get("/sponsor/{sponsor_name}", response_model=List[BillResponse])
def get_bills_by_sponsor(
    sponsor_name: str,
    skip: int = Query(0, ge=0, description="Number of bills to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of bills to return"),
    db: Session = Depends(get_db)
):
    """
    Get bills by sponsor name.

    Returns bills sponsored by the specified legislator (partial name match).
    """
    service = BillService(db)
    bills = service.get_bills_by_sponsor(sponsor_name, skip=skip, limit=limit)
    return bills

@router.get("/external/{id_type}/{external_id:path}", response_model=BillResponse)
def get_bill_by_external_id(
    id_type: str,
    external_id: str,
    db: Session = Depends(get_db)
):
    """
    Get a bill by external ID.

    Supported ID types:
    - openstates: Open States API ID
    - congress_gov: Congress.gov ID
    """
    valid_id_types = ["openstates", "congress_gov"]
    if id_type not in valid_id_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid ID type. Must be one of: {', '.join(valid_id_types)}"
        )

    service = BillService(db)
    bill = service.get_bill_by_external_id(external_id, id_type)

    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    return bill

@router.get("/{bill_id}", response_model=BillResponse)
def get_bill(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific bill.
    Simplified version to avoid performance issues.
    """
    try:
        from app.models.bill import Bill

        # Simple query without any relationships
        bill = db.query(Bill).filter(Bill.id == bill_id).first()

        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")

        # Use the service to handle JSON deserialization
        service = BillService(db)
        service._deserialize_json_fields(bill)

        return bill

    except Exception as e:
        import traceback
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error getting bill {bill_id}: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/", response_model=BillResponse, status_code=201)
def create_bill(
    bill_data: BillCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Create a new bill record with asynchronous AI summarization.

    This endpoint creates a bill and schedules AI summary generation in the background
    to ensure fast API responses. The AI summary will be available shortly after
    the bill is created.

    This endpoint is typically used for data ingestion from external APIs
    or manual data entry by administrators.
    """
    service = BillService(db, background_tasks)

    # Check if bill already exists by external ID
    if hasattr(bill_data, 'openstates_id') and bill_data.openstates_id:
        existing = service.get_bill_by_external_id(bill_data.openstates_id, "openstates")
        if existing:
            raise HTTPException(status_code=400, detail="Bill with this openstates_id already exists")

    if hasattr(bill_data, 'congress_gov_id') and bill_data.congress_gov_id:
        existing = service.get_bill_by_external_id(bill_data.congress_gov_id, "congress_gov")
        if existing:
            raise HTTPException(status_code=400, detail="Bill with this congress_gov_id already exists")

    # Create the bill
    bill = service.create_bill(bill_data)

    # Schedule AI summary generation as a background task
    # This runs after the response is sent to the client
    background_tasks.add_task(
        task_generate_summary_for_bill,
        bill.id,
        db
    )

    return bill

@router.put("/{bill_id}", response_model=BillResponse)
def update_bill(
    bill_id: str,
    bill_data: BillUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an existing bill.

    Updates only the provided fields, leaving others unchanged.
    """
    service = BillService(db)
    bill = service.update_bill(bill_id, bill_data)

    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    return bill

@router.delete("/{bill_id}")
def delete_bill(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete a bill.

    Permanently removes the bill from the database.
    """
    service = BillService(db)
    success = service.delete_bill(bill_id)

    if not success:
        raise HTTPException(status_code=404, detail="Bill not found")

    return {"message": "Bill deleted successfully"}

@router.get("/stats/count")
def get_bills_count(db: Session = Depends(get_db)):
    """
    Get total count of bills in the database.

    Returns the total number of bills for statistics purposes.
    """
    service = BillService(db)
    count = service.get_bills_count()
    return {"total_bills": count}

@router.post("/{bill_id}/regenerate-summary")
def regenerate_ai_summary(
    bill_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Regenerate AI summary for an existing bill.

    This endpoint schedules regeneration of the AI summary for a specific bill.
    Useful when the AI model has been updated or the bill text has changed.
    """
    service = BillService(db)

    # Check if bill exists
    bill = service.get_bill(bill_id)
    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    # Schedule AI summary regeneration as a background task
    background_tasks.add_task(
        task_regenerate_summary_for_bill,
        bill.id,
        db
    )

    return {"message": "AI summary regeneration scheduled", "bill_id": bill_id}

@router.get("/{bill_id}/ai-summary-status")
def get_ai_summary_status(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Get the AI summary status for a specific bill.

    Returns information about whether the bill has an AI summary
    and when it was last updated.
    """
    service = BillService(db)

    # Check if bill exists
    bill = service.get_bill(bill_id)
    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    has_ai_summary = bool(bill.ai_summary and bill.ai_summary.strip())
    has_full_text = bool(bill.full_text and bill.full_text.strip())

    return {
        "bill_id": bill_id,
        "has_ai_summary": has_ai_summary,
        "has_full_text": has_full_text,
        "ai_summary_length": len(bill.ai_summary) if bill.ai_summary else 0,
        "full_text_length": len(bill.full_text) if bill.full_text else 0,
        "ai_summary_preview": bill.ai_summary[:100] if bill.ai_summary else None,
        "can_generate_summary": has_full_text and not has_ai_summary,
        "last_updated": bill.updated_at.isoformat() if bill.updated_at else None
    }

@router.post("/admin/seed-real-bills")
def seed_real_bills(
    bill_numbers: Optional[List[str]] = None,
    limit: int = 5,
    db: Session = Depends(get_db)
):
    """
    Admin endpoint to seed the database with real bill data from Congress.gov API.

    This fetches real, current bills from the Congress.gov API,
    downloads their full text, and creates campaigns for them.

    Args:
        bill_numbers: Optional list of specific bill numbers to fetch (e.g., ["H.R.5", "S.1234"])
        limit: Maximum number of recent bills to fetch if no specific bills provided (default: 5)

    Returns:
        dict: Summary of seeding results
    """
    try:
        from app.services.congress_gov_api import get_congress_gov_client
        from app.models.campaign import CampaignType

        # Initialize services
        congress_client = get_congress_gov_client()

        if not congress_client.enabled:
            return {
                "error": "Congress.gov API is not configured. Please set CONGRESS_GOV_API_KEY.",
                "success": False
            }

        created_bills = []
        created_campaigns = []
        errors = []

        if bill_numbers:
            # Fetch specific bills with FULL AI enhancement
            for bill_number in bill_numbers:
                try:
                    # Parse bill number
                    parsed = congress_client.parse_bill_number(bill_number)

                    # Fetch bill data
                    bill_data = congress_client.get_bill_by_number(
                        congress=parsed['congress'],
                        bill_type=parsed['bill_type'],
                        bill_number=parsed['number']
                    )

                    if bill_data:
                        # Import the creation function
                        import sys
                        import os
                        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'scripts'))
                        from seed_real_bills import create_bill_from_congress_gov_data, create_campaign_for_bill

                        # Step 1: Create basic bill record
                        bill = create_bill_from_congress_gov_data(bill_data, db)
                        if bill:
                            created_bills.append(bill.bill_number)

                            # Step 1.5: Fetch full text for AI processing
                            try:
                                # Parse bill info for text fetching
                                bill_number = bill_data.get('number', '')
                                bill_type = bill_data.get('type', '').lower()
                                congress_num = bill_data.get('congress', 118)

                                if bill_number and bill_type:
                                    # Fetch full text using Congress.gov API
                                    import asyncio
                                    full_text = asyncio.run(congress_client.get_bill_full_text(congress_num, bill_type, int(bill_number)))

                                    if full_text and len(full_text.strip()) > 100:
                                        bill.full_text = full_text
                                        db.commit()
                                        print(f"  ✅ Fetched {len(full_text)} characters of bill text")
                                    else:
                                        # Use title and summary as fallback for AI processing
                                        mock_text = f"Title: {bill.title}\n\nSummary: {bill.summary or 'No summary available'}"
                                        bill.full_text = mock_text
                                        db.commit()
                                        print(f"  ⚠️ Using mock text for AI processing ({len(mock_text)} characters)")

                            except Exception as text_error:
                                errors.append(f"Text fetching failed for bill {bill.bill_number}: {str(text_error)}")
                                # Set minimal text for AI processing
                                mock_text = f"Title: {bill.title}\n\nSummary: {bill.summary or 'No summary available'}"
                                bill.full_text = mock_text
                                db.commit()

                            # Step 2: Add COMPLETE AI enhancements using OpenAI-based pipeline
                            try:
                                # Use the comprehensive OpenAI-based AI service
                                from app.services.ai_service import AIService
                                ai_service = AIService()

                                if ai_service.enabled:
                                    # Run complete AI processing with OpenAI GPT-4
                                    import asyncio
                                    ai_results = asyncio.run(ai_service.process_bill_complete(
                                        bill.full_text,
                                        {
                                            'title': bill.title,
                                            'summary': bill.summary or '',
                                            'bill_number': bill.bill_number
                                        }
                                    ))

                                    # Update bill with comprehensive AI analysis
                                    bill.ai_summary = ai_results.get('ai_summary', '')
                                    bill.support_reasons = ai_results.get('support_reasons', [])
                                    bill.oppose_reasons = ai_results.get('oppose_reasons', [])
                                    bill.amend_reasons = ai_results.get('amend_reasons', [])
                                    bill.message_templates = ai_results.get('message_templates', {})
                                    bill.ai_tags = ai_results.get('tags', [])

                                    # Set structured summary fields
                                    structured_summary = ai_results.get('structured_summary', {})
                                    bill.summary_what_does = structured_summary.get('what_does', '')
                                    bill.summary_who_affects = structured_summary.get('who_affects', '')
                                    bill.summary_why_matters = structured_summary.get('why_matters', '')
                                    bill.summary_key_provisions = structured_summary.get('key_provisions', [])

                                    # Mark as AI processed
                                    from datetime import datetime
                                    bill.ai_processed_at = datetime.utcnow()

                                    db.commit()

                                    # Step 3: Run values analysis (social/environmental/democratic justice scoring)
                                    try:
                                        from app.services.bill_values_analysis_service import BillValuesAnalysisService
                                        values_service = BillValuesAnalysisService(db)
                                        asyncio.run(values_service.analyze_bill_values(bill))
                                        print(f"  ✅ Values analysis completed for bill {bill.bill_number}")
                                    except Exception as values_error:
                                        print(f"  ❌ Values analysis failed for bill {bill.bill_number}: {str(values_error)}")
                                        errors.append(f"Values analysis failed for bill {bill.bill_number}: {str(values_error)}")

                                else:
                                    errors.append(f"OpenAI API not configured - AI processing skipped for bill {bill.bill_number}")

                            except Exception as ai_error:
                                errors.append(f"AI processing failed for bill {bill.bill_number}: {str(ai_error)}")

                            # Step 3: Create a campaign for the bill
                            campaign = create_campaign_for_bill(bill, CampaignType.SUPPORT, db)
                            if campaign:
                                created_campaigns.append(campaign.title)
                    else:
                        errors.append(f"Could not fetch data for bill {bill_number}")

                except Exception as e:
                    errors.append(f"Error processing bill {bill_number}: {str(e)}")

        else:
            # Fetch recent bills
            try:
                recent_bills = congress_client.get_recent_bills(
                    congress=118,
                    limit=limit
                )

                # Import the creation function
                import sys
                import os
                sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'scripts'))
                from seed_real_bills import create_bill_from_congress_gov_data, create_campaign_for_bill

                # Process each bill with FULL AI enhancement pipeline
                for bill_data in recent_bills:
                    try:
                        # Step 1: Create basic bill record
                        bill = create_bill_from_congress_gov_data(bill_data, db)
                        if bill:
                            created_bills.append(bill.bill_number)

                            # Step 1.5: Fetch full text for AI processing
                            try:
                                # Extract bill info for text fetching
                                bill_number = bill_data.get('number', '')
                                bill_type = bill_data.get('type', '').lower()
                                congress_num = bill_data.get('congress', 118)

                                if bill_number and bill_type:
                                    # Fetch full text using Congress.gov API
                                    import asyncio
                                    full_text = asyncio.run(congress_client.get_bill_full_text(congress_num, bill_type, int(bill_number)))

                                    if full_text and len(full_text.strip()) > 100:
                                        bill.full_text = full_text
                                        db.commit()
                                        print(f"  ✅ Fetched {len(full_text)} characters of bill text")
                                    else:
                                        # Use title and summary as fallback for AI processing
                                        mock_text = f"Title: {bill.title}\n\nSummary: {bill.summary or 'No summary available'}"
                                        bill.full_text = mock_text
                                        db.commit()
                                        print(f"  ⚠️ Using mock text for AI processing ({len(mock_text)} characters)")

                            except Exception as text_error:
                                errors.append(f"Text fetching failed for bill {bill.bill_number}: {str(text_error)}")
                                # Set minimal text for AI processing
                                mock_text = f"Title: {bill.title}\n\nSummary: {bill.summary or 'No summary available'}"
                                bill.full_text = mock_text
                                db.commit()

                            # Step 2: Add COMPLETE AI enhancements using OpenAI-based pipeline
                            try:
                                # Use the comprehensive OpenAI-based AI service
                                from app.services.ai_service import AIService
                                ai_service = AIService()

                                if ai_service.enabled:
                                    # Run complete AI processing with OpenAI GPT-4
                                    import asyncio
                                    ai_results = asyncio.run(ai_service.process_bill_complete(
                                        bill.full_text,
                                        {
                                            'title': bill.title,
                                            'summary': bill.summary or '',
                                            'bill_number': bill.bill_number
                                        }
                                    ))

                                    # Update bill with comprehensive AI analysis
                                    bill.ai_summary = ai_results.get('ai_summary', '')
                                    bill.support_reasons = ai_results.get('support_reasons', [])
                                    bill.oppose_reasons = ai_results.get('oppose_reasons', [])
                                    bill.amend_reasons = ai_results.get('amend_reasons', [])
                                    bill.message_templates = ai_results.get('message_templates', {})
                                    bill.ai_tags = ai_results.get('tags', [])

                                    # Set structured summary fields
                                    structured_summary = ai_results.get('structured_summary', {})
                                    bill.summary_what_does = structured_summary.get('what_does', '')
                                    bill.summary_who_affects = structured_summary.get('who_affects', '')
                                    bill.summary_why_matters = structured_summary.get('why_matters', '')
                                    bill.summary_key_provisions = structured_summary.get('key_provisions', [])

                                    # Mark as AI processed
                                    from datetime import datetime
                                    bill.ai_processed_at = datetime.utcnow()

                                    db.commit()

                                    # Step 3: Run values analysis (social/environmental/democratic justice scoring)
                                    try:
                                        from app.services.bill_values_analysis_service import BillValuesAnalysisService
                                        values_service = BillValuesAnalysisService(db)
                                        asyncio.run(values_service.analyze_bill_values(bill))
                                        print(f"  ✅ Values analysis completed for bill {bill.bill_number}")
                                    except Exception as values_error:
                                        print(f"  ❌ Values analysis failed for bill {bill.bill_number}: {str(values_error)}")
                                        errors.append(f"Values analysis failed for bill {bill.bill_number}: {str(values_error)}")

                                else:
                                    errors.append(f"OpenAI API not configured - AI processing skipped for bill {bill.bill_number}")

                            except Exception as ai_error:
                                errors.append(f"AI processing failed for bill {bill.bill_number}: {str(ai_error)}")

                            # Step 3: Create a campaign for the bill
                            campaign = create_campaign_for_bill(bill, CampaignType.SUPPORT, db)
                            if campaign:
                                created_campaigns.append(campaign.title)
                        else:
                            errors.append(f"Could not create bill from data")

                    except Exception as e:
                        errors.append(f"Error processing bill: {str(e)}")

            except Exception as e:
                errors.append(f"Error fetching recent bills: {str(e)}")

        return {
            "success": True,
            "created_bills": created_bills,
            "created_campaigns": created_campaigns,
            "errors": errors,
            "total_bills_created": len(created_bills),
            "total_campaigns_created": len(created_campaigns)
        }

    except Exception as e:
        db.rollback()
        return {
            "error": f"Failed to seed real bills: {str(e)}",
            "success": False
        }

@router.get("/{bill_id}/action-data")
def get_bill_action_data(bill_id: str, db: Session = Depends(get_db)):
    """
    Get all data needed for the action modal

    Returns bill information including AI analysis for user action interface
    """
    service = BillService(db)
    bill = service.get_bill(bill_id)

    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    return {
        "bill_id": bill.id,
        "title": bill.title,
        "bill_number": bill.bill_number,
        "ai_summary": bill.ai_summary,
        "support_reasons": bill.support_reasons or [],
        "oppose_reasons": bill.oppose_reasons or [],
        "amend_reasons": bill.amend_reasons or [],
        "ai_tags": bill.ai_tags or [],
        "message_templates": bill.message_templates or {}
    }


@router.get("/{bill_id}/summary-versions", response_model=List[BillSummaryVersionResponse])
def get_bill_summary_versions(
    bill_id: str,
    include_content: bool = Query(False, description="Include full summary content in response"),
    db: Session = Depends(get_db)
):
    """
    Get summary version history for a bill.

    Returns a list of all summary versions for the specified bill,
    ordered by version number (newest first).
    """
    from app.services.bill_summary_version_service import BillSummaryVersionService

    service = BillService(db)
    bill = service.get_bill(bill_id)

    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    version_service = BillSummaryVersionService(db)
    timeline = version_service.get_summary_timeline(bill_id)

    # Convert to response format
    versions = []
    for entry in timeline:
        version_data = {
            "version": entry["version"],
            "date": entry["date"],
            "reason": entry["reason"],
            "is_current": entry["is_current"],
            "changes": entry["changes"],
            "has_structured_data": entry["has_structured_data"]
        }

        # Include content if requested
        if include_content:
            version_obj = version_service.get_version_by_number(bill_id, entry["version"])
            if version_obj:
                version_data.update({
                    "summary_what_does": version_obj.summary_what_does,
                    "summary_who_affects": version_obj.summary_who_affects,
                    "summary_why_matters": version_obj.summary_why_matters,
                    "summary_key_provisions": version_obj.summary_key_provisions,
                    "summary_timeline": version_obj.summary_timeline,
                    "summary_cost_impact": version_obj.summary_cost_impact,
                    "ai_summary": version_obj.ai_summary,
                    "simple_summary": version_obj.simple_summary,
                    "tldr": version_obj.tldr
                })

        versions.append(version_data)

    return versions


@router.get("/{bill_id}/status-history", response_model=List[BillStatusHistoryResponse])
def get_bill_status_history(bill_id: str, db: Session = Depends(get_db)):
    """
    Get status change history for a bill.

    Returns a list of all status changes for the specified bill,
    ordered by date (newest first).
    """
    service = BillService(db)
    bill = service.get_bill(bill_id)

    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    # Get status history from the bill's relationship
    status_history = []
    for status_record in bill.status_history:
        history_entry = {
            "status": status_record.current_status,
            "date": status_record.status_changed_at,
            "notes": status_record.notes,
            "is_significant_change": status_record.is_significant_change,
            "vote_details": status_record.vote_details
        }
        status_history.append(history_entry)

    return status_history


@router.get("/{bill_id}/timeline", response_model=BillTimelineResponse)
def get_bill_timeline(
    bill_id: str,
    include_summary_content: bool = Query(False, description="Include full summary content in response"),
    db: Session = Depends(get_db)
):
    """
    Get complete timeline for a bill including both status changes and summary versions.

    Returns combined timeline data for comprehensive bill tracking.
    """
    service = BillService(db)
    bill = service.get_bill(bill_id)

    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    # Get status history
    status_history = []
    for status_record in bill.status_history:
        history_entry = {
            "status": status_record.current_status,
            "date": status_record.status_changed_at,
            "notes": status_record.notes,
            "is_significant_change": status_record.is_significant_change,
            "vote_details": status_record.vote_details
        }
        status_history.append(history_entry)

    # Get summary versions
    from app.services.bill_summary_version_service import BillSummaryVersionService
    version_service = BillSummaryVersionService(db)
    timeline = version_service.get_summary_timeline(bill_id)

    summary_versions = []
    for entry in timeline:
        version_data = {
            "version": entry["version"],
            "date": entry["date"],
            "reason": entry["reason"],
            "is_current": entry["is_current"],
            "changes": entry["changes"],
            "has_structured_data": entry["has_structured_data"]
        }

        if include_summary_content:
            version_obj = version_service.get_version_by_number(bill_id, entry["version"])
            if version_obj:
                version_data.update({
                    "summary_what_does": version_obj.summary_what_does,
                    "summary_who_affects": version_obj.summary_who_affects,
                    "summary_why_matters": version_obj.summary_why_matters,
                    "summary_key_provisions": version_obj.summary_key_provisions,
                    "summary_timeline": version_obj.summary_timeline,
                    "summary_cost_impact": version_obj.summary_cost_impact,
                    "ai_summary": version_obj.ai_summary,
                    "simple_summary": version_obj.simple_summary,
                    "tldr": version_obj.tldr
                })

        summary_versions.append(version_data)

    return {
        "bill_id": bill_id,
        "status_history": status_history,
        "summary_versions": summary_versions
    }


@router.post("/{bill_id}/track-history")
def track_bill_history(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Manually trigger history tracking for a specific bill.

    Args:
        bill_id: ID of the bill to track
        db: Database session

    Returns:
        History tracking results
    """
    # Get the bill
    from app.models.bill import Bill
    bill = db.query(Bill).filter(Bill.id == bill_id).first()
    if not bill:
        raise HTTPException(status_code=404, detail="Bill not found")

    # Track history changes
    from app.services.bill_history_tracker import BillHistoryTracker
    tracker = BillHistoryTracker(db)
    results = tracker.track_bill_changes(bill)

    # Commit changes
    db.commit()

    return {
        "success": True,
        "bill_id": bill_id,
        "bill_number": bill.bill_number,
        "tracking_results": results
    }


@router.post("/admin/track-all-history")
def track_all_bills_history(
    limit: int = Query(50, description="Maximum number of bills to check"),
    db: Session = Depends(get_db)
):
    """
    Track history changes for all active bills.

    Args:
        limit: Maximum number of bills to check
        db: Database session

    Returns:
        Summary of tracking results
    """
    from app.services.bill_history_tracker import BillHistoryTracker
    tracker = BillHistoryTracker(db)
    results = tracker.track_all_active_bills(limit=limit)

    return {
        "success": True,
        "summary": results
    }
