# app/api/v1/endpoints/campaigns.py
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.campaigns import CampaignService
from app.schemas.campaign import (
    CampaignResponse,
    CampaignCreate,
    CampaignUpdate
)
from app.models.campaign import CampaignStatus, CampaignType

router = APIRouter()

@router.get("/", response_model=List[CampaignResponse])
def get_campaigns(
    skip: int = Query(0, ge=0, description="Number of campaigns to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of campaigns to return"),
    db: Session = Depends(get_db)
):
    """
    Get a list of campaigns with pagination.

    Returns campaigns ordered by creation date (newest first).
    """
    service = CampaignService(db)
    campaigns = service.get_campaigns(skip=skip, limit=limit)
    return campaigns

@router.get("/search", response_model=List[CampaignResponse])
def search_campaigns(
    query: Optional[str] = Query(None, description="Search term for title and description"),
    campaign_type: Optional[CampaignType] = Query(None, description="Filter by campaign type"),
    status: Optional[CampaignStatus] = Query(None, description="Filter by campaign status"),
    bill_id: Optional[str] = Query(None, description="Filter by bill ID"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    is_public: Optional[bool] = Query(None, description="Filter by public status"),
    skip: int = Query(0, ge=0, description="Number of campaigns to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of campaigns to return"),
    db: Session = Depends(get_db)
):
    """
    Search campaigns based on various criteria.

    Supports text search in title and description, and filtering by various attributes.
    """
    service = CampaignService(db)
    campaigns = service.search_campaigns(
        query=query,
        campaign_type=campaign_type,
        status=status,
        bill_id=bill_id,
        is_featured=is_featured,
        is_public=is_public,
        skip=skip,
        limit=limit
    )
    return campaigns

@router.get("/featured", response_model=List[CampaignResponse])
def get_featured_campaigns(
    skip: int = Query(0, ge=0, description="Number of campaigns to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of campaigns to return"),
    db: Session = Depends(get_db)
):
    """
    Get featured campaigns.

    Returns campaigns marked as featured, ordered by creation date.
    """
    service = CampaignService(db)
    campaigns = service.get_featured_campaigns(skip=skip, limit=limit)
    return campaigns

@router.get("/active", response_model=List[CampaignResponse])
def get_active_campaigns(
    skip: int = Query(0, ge=0, description="Number of campaigns to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of campaigns to return"),
    db: Session = Depends(get_db)
):
    """
    Get active campaigns.

    Returns campaigns with status 'active', ordered by creation date.
    """
    service = CampaignService(db)
    campaigns = service.get_active_campaigns(skip=skip, limit=limit)
    return campaigns

@router.get("/status/{status}", response_model=List[CampaignResponse])
def get_campaigns_by_status(
    status: CampaignStatus,
    skip: int = Query(0, ge=0, description="Number of campaigns to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of campaigns to return"),
    db: Session = Depends(get_db)
):
    """
    Get campaigns by status.

    Returns campaigns with the specified status, ordered by creation date.
    """
    service = CampaignService(db)
    campaigns = service.get_campaigns_by_status(status, skip=skip, limit=limit)
    return campaigns

@router.get("/bill/{bill_id}", response_model=List[CampaignResponse])
def get_campaigns_by_bill(
    bill_id: str,
    skip: int = Query(0, ge=0, description="Number of campaigns to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of campaigns to return"),
    db: Session = Depends(get_db)
):
    """
    Get campaigns for a specific bill.

    Returns all campaigns associated with the given bill ID.
    """
    service = CampaignService(db)
    campaigns = service.get_campaigns_by_bill(bill_id, skip=skip, limit=limit)
    return campaigns

@router.get("/{campaign_id}", response_model=CampaignResponse)
def get_campaign(
    campaign_id: str,
    db: Session = Depends(get_db)
):
    """
    Get a single campaign by ID.

    Returns the campaign with its associated bill information.
    """
    service = CampaignService(db)
    campaign = service.get_campaign(campaign_id)

    if not campaign:
        raise HTTPException(status_code=404, detail="Campaign not found")

    return campaign

@router.post("/", response_model=CampaignResponse)
def create_campaign(
    campaign_data: CampaignCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new campaign.

    Creates a campaign associated with a specific bill.
    """
    service = CampaignService(db)

    # Verify that the bill exists
    from app.services.bills import BillService
    bill_service = BillService(db)
    bill = bill_service.get_bill(campaign_data.bill_id)
    if not bill:
        raise HTTPException(status_code=400, detail="Bill not found")

    campaign = service.create_campaign(campaign_data)
    return campaign

@router.put("/{campaign_id}", response_model=CampaignResponse)
def update_campaign(
    campaign_id: str,
    campaign_data: CampaignUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an existing campaign.

    Updates only the provided fields, leaving others unchanged.
    """
    service = CampaignService(db)
    campaign = service.update_campaign(campaign_id, campaign_data)

    if not campaign:
        raise HTTPException(status_code=404, detail="Campaign not found")

    return campaign

@router.delete("/{campaign_id}")
def delete_campaign(
    campaign_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete a campaign.

    Permanently removes the campaign and all associated data.
    """
    service = CampaignService(db)
    success = service.delete_campaign(campaign_id)

    if not success:
        raise HTTPException(status_code=404, detail="Campaign not found")

    return {"message": "Campaign deleted successfully"}

@router.post("/admin/recalculate-action-counts")
def recalculate_action_counts(db: Session = Depends(get_db)):
    """
    Admin endpoint to recalculate action counts for all campaigns.

    This fixes data inconsistency issues where campaigns show incorrect action counts.
    Should be used when campaigns display "0 of 0 actions" despite having real actions.

    Returns:
        dict: Number of campaigns that were updated
    """
    service = CampaignService(db)
    updated_count = service.recalculate_action_counts()
    return {
        "message": f"Recalculated action counts for {updated_count} campaigns",
        "updated_campaigns": updated_count
    }

@router.get("/count", response_model=int)
def get_campaigns_count(db: Session = Depends(get_db)):
    """
    Get the total count of campaigns.

    Returns the total number of campaigns in the database.
    """
    service = CampaignService(db)
    return service.get_campaigns_count()

@router.post("/admin/seed-live-data")
def seed_live_data(db: Session = Depends(get_db)):
    """
    Admin endpoint to seed the database with live campaign data.

    This creates 5 real campaigns based on current legislation for the MVP launch.
    Should be run once after deployment to populate the platform with content.

    Returns:
        dict: Summary of seeding results
    """
    import sys
    import os
    from app.models.campaign import Campaign

    # Import the campaign data from the seed script
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'scripts'))

    try:
        from seed_live_data import LAUNCH_CAMPAIGNS, create_bill_and_campaign

        created_campaigns = []
        skipped_campaigns = []

        for campaign_data in LAUNCH_CAMPAIGNS:
            # Check if campaign already exists
            existing_campaign = db.query(Campaign).filter(
                Campaign.title == campaign_data["title"]
            ).first()

            if existing_campaign:
                skipped_campaigns.append(campaign_data["title"])
                continue

            try:
                campaign = create_bill_and_campaign(campaign_data, db)
                created_campaigns.append(campaign.title)

            except Exception as e:
                db.rollback()
                return {
                    "error": f"Failed to create campaign: {str(e)}",
                    "created_campaigns": created_campaigns,
                    "skipped_campaigns": skipped_campaigns
                }

        # Commit all changes
        db.commit()

        return {
            "message": f"Successfully seeded {len(created_campaigns)} campaigns",
            "created_campaigns": created_campaigns,
            "skipped_campaigns": skipped_campaigns,
            "total_campaigns": len(created_campaigns) + len(skipped_campaigns)
        }

    except ImportError as e:
        return {"error": f"Failed to import seed data: {str(e)}"}
    except Exception as e:
        db.rollback()
        return {"error": f"Seeding failed: {str(e)}"}
