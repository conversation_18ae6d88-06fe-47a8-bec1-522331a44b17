"""
Authentication service for Auth0 JWT validation.
"""
import os
import jwt
import requests
from typing import Optional, Dict, Any
from fastapi import HTTPEx<PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)

# Auth0 configuration
AUTH0_DOMAIN = os.getenv("AUTH0_DOMAIN")
AUTH0_AUDIENCE = os.getenv("AUTH0_AUDIENCE")
AUTH0_ALGORITHMS = ["RS256"]

security = HTTPBearer()


class AuthError(Exception):
    """Custom exception for authentication errors."""
    def __init__(self, error: str, status_code: int):
        self.error = error
        self.status_code = status_code


@lru_cache()
def get_auth0_public_key():
    """
    Fetch Auth0 public key for JWT verification.
    Cached to avoid repeated API calls.
    """
    if not AUTH0_DOMAIN:
        raise AuthError("AUTH0_DOMAIN not configured", 500)

    try:
        jwks_url = f"https://{AUTH0_DOMAIN}/.well-known/jwks.json"
        response = requests.get(jwks_url, timeout=10)
        response.raise_for_status()
        jwks = response.json()

        # Get the first key (Auth0 typically uses one key)
        if not jwks.get("keys"):
            raise AuthError("No keys found in JWKS", 500)

        return jwks["keys"][0]
    except requests.RequestException as e:
        logger.error(f"Failed to fetch Auth0 JWKS: {e}")
        raise AuthError("Failed to fetch authentication keys", 500)


def verify_jwt_token(token: str) -> Dict[str, Any]:
    """
    Verify and decode JWT token from Auth0.

    Args:
        token: JWT token string

    Returns:
        Decoded token payload containing user information

    Raises:
        AuthError: If token is invalid or verification fails
    """
    if not AUTH0_DOMAIN or not AUTH0_AUDIENCE:
        raise AuthError("Auth0 configuration missing", 500)

    try:
        # Get the public key
        jwks_key = get_auth0_public_key()

        # Construct the public key for verification
        public_key = jwt.algorithms.RSAAlgorithm.from_jwk(jwks_key)

        # Verify and decode the token
        payload = jwt.decode(
            token,
            public_key,
            algorithms=AUTH0_ALGORITHMS,
            audience=AUTH0_AUDIENCE,
            issuer=f"https://{AUTH0_DOMAIN}/"
        )

        return payload

    except jwt.ExpiredSignatureError:
        raise AuthError("Token has expired", 401)
    except jwt.InvalidTokenError as e:
        logger.error(f"Invalid token: {e}")
        raise AuthError("Invalid token", 401)
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        raise AuthError("Token verification failed", 401)


def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    FastAPI dependency to get current authenticated user.

    Args:
        credentials: HTTP Bearer token from request header

    Returns:
        User information from decoded JWT

    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Extract token from "Bearer <token>"
        token = credentials.credentials

        # Verify and decode the token
        payload = verify_jwt_token(token)

        # Extract user information
        user_info = {
            "user_id": payload.get("sub"),  # Auth0 user ID
            "email": payload.get("email"),
            "email_verified": payload.get("email_verified", False),
            "name": payload.get("name"),
            "picture": payload.get("picture"),
            "auth0_payload": payload  # Full payload for debugging
        }

        if not user_info["user_id"]:
            raise AuthError("User ID not found in token", 401)

        return user_info

    except AuthError as e:
        raise HTTPException(
            status_code=e.status_code,
            detail=e.error
        )
    except Exception as e:
        logger.error(f"Authentication failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )


def get_optional_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[Dict[str, Any]]:
    """
    FastAPI dependency to get current user if authenticated, None otherwise.
    Useful for endpoints that work with or without authentication.

    Args:
        credentials: Optional HTTP Bearer token from request header

    Returns:
        User information if authenticated, None otherwise
    """
    if not credentials:
        return None

    try:
        return get_current_user(credentials)
    except HTTPException:
        return None


# User database operations
async def ensure_user_exists(user_info: Dict[str, Any], db_session) -> Dict[str, Any]:
    """
    Ensure user exists in our database, create if not exists.
    This will be called when a user first authenticates.

    Args:
        user_info: User information from JWT token
        db_session: Database session

    Returns:
        User record from database
    """
    from app.models.user import User  # Import here to avoid circular imports

    auth0_user_id = user_info["user_id"]

    # Check if user already exists
    existing_user = db_session.query(User).filter(User.auth0_user_id == auth0_user_id).first()

    if existing_user:
        # Update user info if changed
        if existing_user.email != user_info.get("email"):
            existing_user.email = user_info.get("email")
        if existing_user.name != user_info.get("name"):
            existing_user.name = user_info.get("name")
        if existing_user.picture_url != user_info.get("picture"):
            existing_user.picture_url = user_info.get("picture")

        db_session.commit()
        return existing_user

    # Create new user
    new_user = User(
        auth0_user_id=auth0_user_id,
        email=user_info.get("email"),
        name=user_info.get("name"),
        picture_url=user_info.get("picture"),
        email_verified=user_info.get("email_verified", False)
    )

    db_session.add(new_user)
    db_session.commit()
    db_session.refresh(new_user)

    logger.info(f"Created new user: {new_user.auth0_user_id}")
    return new_user
