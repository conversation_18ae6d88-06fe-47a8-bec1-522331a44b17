# app/core/config.py
from pydantic_settings import BaseSettings
from typing import List, Optional

class Settings(BaseSettings):
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "ModernAction API"
    VERSION: str = "1.0.0"

    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True

    # Database
    DATABASE_URL: str = "sqlite:///./modernaction.db"
    DB_HOST: Optional[str] = None
    DB_PORT: Optional[str] = None
    DB_NAME: Optional[str] = None
    DB_USERNAME: Optional[str] = None
    DB_PASSWORD: Optional[str] = None

    # Security
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    JWT_SECRET: str = "your-jwt-secret-here-change-in-production"
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRATION_MINUTES: int = 30

    # Auth0 Configuration
    AUTH0_DOMAIN: Optional[str] = None
    AUTH0_AUDIENCE: Optional[str] = None

    # CORS
    CORS_ORIGINS: str = "http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003"

    # External API Keys
    OPENSTATES_API_KEY: Optional[str] = None
    GOOGLE_CIVIC_INFO_API_KEY: Optional[str] = None
    PROPUBLICA_CONGRESS_API_KEY: Optional[str] = None
    CONGRESS_GOV_API_KEY: Optional[str] = None
    HUGGING_FACE_API_KEY: Optional[str] = None
    OPENAI_API_KEY: Optional[str] = None
    ACTION_NETWORK_API_KEY: Optional[str] = None
    ACTION_NETWORK_SENATE_CAMPAIGN_ID: Optional[str] = None
    ACTION_NETWORK_HOUSE_CAMPAIGN_ID: Optional[str] = None

    # AWS Configuration
    AWS_REGION: str = "us-east-1"
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None

    # SES Configuration
    AWS_SES_FROM_EMAIL: str = "<EMAIL>"
    AWS_SES_FROM_NAME: str = "ModernAction"

    # Twitter Configuration
    TWITTER_API_KEY: Optional[str] = None
    TWITTER_API_SECRET: Optional[str] = None
    TWITTER_ACCESS_TOKEN: Optional[str] = None
    TWITTER_ACCESS_TOKEN_SECRET: Optional[str] = None

    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"

    # Logging
    LOG_LEVEL: str = "INFO"

    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = 100

    # Model Configuration
    MODEL_CACHE_SIZE: int = 1
    SUMMARIZATION_MODEL: str = "t5-small"
    TEXT_GENERATION_MODEL: str = "t5-small"

    class Config:
        case_sensitive = True
        env_file = ".env"
        env_file_encoding = "utf-8"

    @property
    def cors_origins_list(self) -> List[str]:
        """Convert CORS origins string to list"""
        if not self.CORS_ORIGINS:
            return []
        return [origin.strip() for origin in self.CORS_ORIGINS.split(',')]

    @property
    def database_url(self) -> str:
        """Get database URL, constructing from components if available"""
        # If individual DB components are provided, construct the URL
        if (self.DB_HOST and self.DB_PORT and self.DB_NAME and
            self.DB_USERNAME and self.DB_PASSWORD):
            return f"postgresql://{self.DB_USERNAME}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

        # Otherwise return the default DATABASE_URL
        return self.DATABASE_URL

def get_settings() -> Settings:
    """Get application settings"""
    return Settings()

settings = get_settings()
