# app/db/types.py
from sqlalchemy import Text, String

def get_json_type():
    """Get the appropriate JSON type based on the database URL"""
    # Always use Text for now to ensure SQLite compatibility during testing
    return Text
    # if settings.DATABASE_URL.startswith("postgresql"):
    #     return JSONB
    # else:
    #     # For SQLite and other databases, use JSON or Text
    #     return Text  # Use Text for SQLite compatibility

def get_uuid_type():
    """Get the appropriate UUID type based on the database URL"""
    # Always use String for now to ensure SQLite compatibility during testing
    return String(36)
    # if settings.DATABASE_URL.startswith("postgresql"):
    #     return UUID(as_uuid=True)
    # else:
    #     # For SQLite, use String
    #     return String(36)
