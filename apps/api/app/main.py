# app/main.py
import os
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.v1.api import api_router
from app.core.config import settings

# Load environment variables from .env file
load_dotenv()

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="ModernAction.io API - Empowering civic engagement through technology"
)

# Ensure DATABASE_URL is properly constructed from environment variables
@app.on_event("startup")
async def startup_event():
    """Ensure database configuration is properly set up"""
    # Force construction of DATABASE_URL from individual components if available
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    db_username = os.getenv("DB_USERNAME")
    db_password = os.getenv("DB_PASSWORD")

    if all([db_host, db_port, db_name, db_username, db_password]):
        database_url = f"postgresql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}"
        os.environ["DATABASE_URL"] = database_url
        print(f"DATABASE_URL constructed: postgresql://{db_username}:***@{db_host}:{db_port}/{db_name}")
    else:
        print(f"Using default DATABASE_URL: {os.getenv('DATABASE_URL', 'Not set')}")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)
