# app/models/action.py
from sqlalchemy import Column, String, Text, DateTime, Boolean, ForeignKey, Enum as SQL<PERSON><PERSON>, Integer
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.db.types import get_json_type, get_uuid_type
import enum

class ActionStatus(str, enum.Enum):
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    BOUNCED = "bounced"
    PARTIAL = "partial"  # Some action types succeeded, others failed

class ActionType(str, enum.Enum):
    EMAIL = "email"
    PHONE = "phone"
    LETTER = "letter"
    SOCIAL_MEDIA = "social_media"
    PETITION = "petition"
    TWITTER = "twitter"

class Action(Base):
    __tablename__ = "actions"

    # Action content
    subject = Column(String, nullable=False)
    message = Column(Text, nullable=False)

    # Action type and delivery
    action_type = Column(SQLEnum(ActionType), nullable=False, default=ActionType.EMAIL)
    status = Column(SQLEnum(ActionStatus), nullable=False, default=ActionStatus.PENDING)

    # Delivery information
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)

    # Response tracking
    response_received = Column(Boolean, default=False, nullable=False)
    response_content = Column(Text, nullable=True)
    response_received_at = Column(DateTime, nullable=True)

    # Contact information used
    contact_email = Column(String, nullable=True)
    contact_phone = Column(String, nullable=True)
    contact_address = Column(Text, nullable=True)

    # User information (for personalization)
    user_name = Column(String, nullable=False)
    user_email = Column(String, nullable=False)
    user_address = Column(Text, nullable=True)
    user_zip_code = Column(String, nullable=True)

    # Delivery details
    delivery_method = Column(String, nullable=True)  # "ses", "smtp", "api", etc.
    delivery_id = Column(String, nullable=True)      # External delivery ID

    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)

    # Metadata
    action_metadata = Column(Text, nullable=True)

    # Enhanced tracking fields (Implementation-2.MD)
    bill_id = Column(get_uuid_type(), ForeignKey("bills.id"), nullable=True)  # Link to specific bill
    position = Column(String, nullable=True)  # 'support', 'oppose', 'amend'
    action_network_id = Column(String, nullable=True)  # Action Network message ID
    personalized_content = Column(postgresql.JSONB, nullable=True)  # AI-generated personalized message
    representative_info = Column(postgresql.JSONB, nullable=True)  # Representative details
    user_location = Column(postgresql.JSONB, nullable=True)  # User's address/ZIP for context

    # User input tracking fields
    selected_reasons = Column(postgresql.JSONB, nullable=True)  # Array of selected pre-generated reasons
    custom_reasons = Column(postgresql.JSONB, nullable=True)  # Array of user-submitted custom reasons
    original_ai_message = Column(Text, nullable=True)  # Original AI-generated message
    user_edited_message = Column(Text, nullable=True)  # User's edited version of the message
    message_was_edited = Column(Boolean, default=False, nullable=False)  # Whether user edited the message

    # Foreign keys
    user_id = Column(get_uuid_type(), ForeignKey("users.id"), nullable=False)
    campaign_id = Column(get_uuid_type(), ForeignKey("campaigns.id"), nullable=True)  # Nullable for development testing
    official_id = Column(get_uuid_type(), ForeignKey("officials.id"), nullable=True)  # Nullable for development testing

    # Relationships
    user = relationship("User", back_populates="actions")
    campaign = relationship("Campaign", back_populates="actions")
    official = relationship("Official", back_populates="actions")
    bill = relationship("Bill", back_populates="actions")
    
    # Enhanced tracking relationships
    action_reasoning = relationship("ActionReasoning", back_populates="action", cascade="all, delete-orphan")
    errors = relationship("ActionError", back_populates="action", cascade="all, delete-orphan")
    action_network_submission = relationship("ActionNetworkSubmission", back_populates="action", uselist=False, cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Action(user='{self.user_name}', official='{self.official_id}', status='{self.status}')>"

    @property
    def is_successful(self) -> bool:
        return self.status in [ActionStatus.SENT, ActionStatus.DELIVERED]

    @property
    def is_failed(self) -> bool:
        return self.status in [ActionStatus.FAILED, ActionStatus.BOUNCED]

    @property
    def can_retry(self) -> bool:
        return self.is_failed and self.retry_count < 3
