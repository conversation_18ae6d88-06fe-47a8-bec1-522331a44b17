# app/models/action_tracking.py
"""
Enhanced action tracking models for comprehensive civic engagement analytics.
These models provide detailed tracking of user actions, reasoning, and outcomes
while maintaining privacy and security.
"""

from sqlalchemy import (
    Column, String, Text, DateTime, <PERSON><PERSON>an, Integer, Foreign<PERSON>ey, 
    Enum as S<PERSON><PERSON><PERSON>, DECIMAL, Date, CheckConstraint, Index, UniqueConstraint
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.db.types import get_json_type, get_uuid_type
import enum
from datetime import datetime


class ReasoningCategory(str, enum.Enum):
    """Categories for organizing reasoning options"""
    ECONOMIC = "economic"
    ENVIRONMENTAL = "environmental"
    HEALTHCARE = "healthcare"
    EDUCATION = "education"
    CIVIL_RIGHTS = "civil_rights"
    NATIONAL_SECURITY = "national_security"
    INFRASTRUCTURE = "infrastructure"
    SOCIAL_JUSTICE = "social_justice"
    TECHNOLOGY = "technology"
    OTHER = "other"


class ActionStance(str, enum.Enum):
    """User's stance on a bill or issue"""
    SUPPORT = "support"
    OPPOSE = "oppose"
    AMEND = "amend"


class ErrorType(str, enum.Enum):
    """Types of errors that can occur during action processing"""
    NETWORK_ERROR = "network_error"
    VALIDATION_ERROR = "validation_error"
    API_ERROR = "api_error"
    AUTH_ERROR = "auth_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    SERVICE_UNAVAILABLE = "service_unavailable"
    UNKNOWN_ERROR = "unknown_error"


class ResolutionStatus(str, enum.Enum):
    """Status of error resolution"""
    UNRESOLVED = "unresolved"
    RESOLVED = "resolved"
    IGNORED = "ignored"
    IN_PROGRESS = "in_progress"


class UserRole(str, enum.Enum):
    """User roles for access control"""
    USER = "user"
    MODERATOR = "moderator"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


class DataRetentionPreference(str, enum.Enum):
    """Data retention preferences"""
    MINIMAL = "minimal"     # 1 year
    STANDARD = "standard"   # 3 years
    EXTENDED = "extended"   # 7 years


class ReasoningOption(Base):
    """Pre-defined reasoning options for user selection"""
    __tablename__ = "reasoning_options"

    bill_id = Column(get_uuid_type(), ForeignKey("bills.id", ondelete="CASCADE"), nullable=False)
    stance = Column(SQLEnum(ActionStance), nullable=False)
    reason_text = Column(Text, nullable=False)
    reason_category = Column(SQLEnum(ReasoningCategory), nullable=True)
    display_order = Column(Integer, default=0, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    usage_count = Column(Integer, default=0, nullable=False)

    # Relationships
    bill = relationship("Bill", back_populates="reasoning_options")
    action_reasoning = relationship("ActionReasoning", back_populates="reasoning_option")

    # Indexes
    __table_args__ = (
        Index('idx_reasoning_bill_stance', 'bill_id', 'stance'),
        Index('idx_reasoning_category', 'reason_category'),
        Index('idx_reasoning_active', 'is_active'),
        CheckConstraint(stance.in_(['support', 'oppose', 'amend']), name='check_valid_stance'),
    )

    def __repr__(self):
        return f"<ReasoningOption(bill='{self.bill_id}', stance='{self.stance}', text='{self.reason_text[:50]}...')>"


class ActionReasoning(Base):
    """Links actions to their selected reasoning options"""
    __tablename__ = "action_reasoning"

    action_id = Column(get_uuid_type(), ForeignKey("actions.id", ondelete="CASCADE"), nullable=False)
    reasoning_option_id = Column(get_uuid_type(), ForeignKey("reasoning_options.id", ondelete="CASCADE"), nullable=False)
    is_primary_reason = Column(Boolean, default=False, nullable=False)

    # Relationships
    action = relationship("Action", back_populates="action_reasoning")
    reasoning_option = relationship("ReasoningOption", back_populates="action_reasoning")

    # Constraints
    __table_args__ = (
        Index('idx_action_reasoning_action', 'action_id'),
        Index('idx_action_reasoning_option', 'reasoning_option_id'),
        UniqueConstraint('action_id', 'reasoning_option_id', name='uq_action_reasoning'),
    )

    def __repr__(self):
        return f"<ActionReasoning(action='{self.action_id}', reasoning='{self.reasoning_option_id}')>"


class CustomReasonsPool(Base):
    """Pool of user-submitted custom reasons for analysis"""
    __tablename__ = "custom_reasons_pool"

    action_id = Column(get_uuid_type(), ForeignKey("actions.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(get_uuid_type(), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    bill_id = Column(get_uuid_type(), ForeignKey("bills.id", ondelete="CASCADE"), nullable=False)
    stance = Column(SQLEnum(ActionStance), nullable=False)
    custom_reason = Column(Text, nullable=False)
    is_flagged = Column(Boolean, default=False, nullable=False)
    # Note: embedding column for AI analysis can be added later with pgvector extension
    similarity_cluster_id = Column(get_uuid_type(), nullable=True)

    # Relationships
    action = relationship("Action")
    user = relationship("User")
    bill = relationship("Bill")

    # Indexes
    __table_args__ = (
        Index('idx_custom_reasons_bill', 'bill_id'),
        Index('idx_custom_reasons_user', 'user_id'),
        Index('idx_custom_reasons_stance', 'stance'),
        Index('idx_custom_reasons_flagged', 'is_flagged'),
        CheckConstraint(stance.in_(['support', 'oppose', 'amend']), name='check_custom_valid_stance'),
    )

    def __repr__(self):
        return f"<CustomReasonsPool(user='{self.user_id}', bill='{self.bill_id}', stance='{self.stance}')>"


class UserLocation(Base):
    """Encrypted user location data for privacy-safe geographic analysis"""
    __tablename__ = "user_locations"

    user_id = Column(get_uuid_type(), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True)
    # Encrypted fields (will be encrypted at application level)
    encrypted_address = Column(Text, nullable=True)
    encrypted_zip_code = Column(Text, nullable=True)
    # Unencrypted for analytics (aggregate level only)
    state_code = Column(String(2), nullable=True)
    congressional_district = Column(String(10), nullable=True)
    senate_class = Column(String(10), nullable=True)
    # Approximate coordinates (city-level only for privacy)
    latitude = Column(DECIMAL(10, 6), nullable=True)
    longitude = Column(DECIMAL(10, 6), nullable=True)
    # Metadata
    location_source = Column(String(50), default='user_input', nullable=False)
    accuracy_level = Column(String(20), default='city', nullable=False)
    verified_at = Column(DateTime, nullable=True)

    # Relationships
    user = relationship("User", back_populates="location")

    # Indexes
    __table_args__ = (
        Index('idx_user_locations_state', 'state_code'),
        Index('idx_user_locations_district', 'congressional_district'),
    )

    def __repr__(self):
        return f"<UserLocation(user='{self.user_id}', state='{self.state_code}')>"


class ActionError(Base):
    """Detailed error tracking for failed actions"""
    __tablename__ = "action_errors"

    action_id = Column(get_uuid_type(), ForeignKey("actions.id", ondelete="CASCADE"), nullable=False)
    error_type = Column(SQLEnum(ErrorType), nullable=False)
    error_code = Column(String(50), nullable=True)  # HTTP status or API error code
    error_message = Column(Text, nullable=False)
    error_details = Column(postgresql.JSONB, nullable=True)  # Full error response/stack
    resolution_status = Column(SQLEnum(ResolutionStatus), default=ResolutionStatus.UNRESOLVED, nullable=False)
    retry_count = Column(Integer, default=0, nullable=False)
    resolved_at = Column(DateTime, nullable=True)

    # Relationships
    action = relationship("Action", back_populates="errors")

    # Indexes
    __table_args__ = (
        Index('idx_action_errors_action', 'action_id'),
        Index('idx_action_errors_type', 'error_type'),
        Index('idx_action_errors_status', 'resolution_status'),
        Index('idx_action_errors_created', 'created_at'),
    )

    def __repr__(self):
        return f"<ActionError(action='{self.action_id}', type='{self.error_type}', status='{self.resolution_status}')>"


class ActionNetworkSubmission(Base):
    """Tracking for Action Network form submissions"""
    __tablename__ = "action_network_submissions"

    action_id = Column(get_uuid_type(), ForeignKey("actions.id", ondelete="CASCADE"), nullable=False)
    campaign_id = Column(String(255), nullable=False)  # Action Network campaign ID
    submission_id = Column(String(255), nullable=True)  # Action Network submission ID
    form_type = Column(String(50), nullable=False)  # 'house', 'senate', 'unified'
    target_chamber = Column(String(20), nullable=True)  # 'house', 'senate', 'both'
    embed_url = Column(Text, nullable=True)
    iframe_url = Column(Text, nullable=True)
    submission_status = Column(String(50), default='pending', nullable=False)
    action_network_response = Column(postgresql.JSONB, nullable=True)
    submitted_at = Column(DateTime, nullable=True)

    # Relationships
    action = relationship("Action", back_populates="action_network_submission")

    # Indexes
    __table_args__ = (
        Index('idx_an_submissions_action', 'action_id'),
        Index('idx_an_submissions_campaign', 'campaign_id'),
        Index('idx_an_submissions_status', 'submission_status'),
    )

    def __repr__(self):
        return f"<ActionNetworkSubmission(action='{self.action_id}', campaign='{self.campaign_id}')>"


class ActionAnalyticsDaily(Base):
    """Pre-aggregated daily analytics for fast queries"""
    __tablename__ = "action_analytics_daily"

    bill_id = Column(get_uuid_type(), ForeignKey("bills.id", ondelete="CASCADE"), nullable=False)
    campaign_id = Column(get_uuid_type(), ForeignKey("campaigns.id", ondelete="SET NULL"), nullable=True)
    date_bucket = Column(Date, nullable=False)
    # Action counts
    total_actions = Column(Integer, default=0, nullable=False)
    support_count = Column(Integer, default=0, nullable=False)
    oppose_count = Column(Integer, default=0, nullable=False)
    amend_count = Column(Integer, default=0, nullable=False)
    unique_users = Column(Integer, default=0, nullable=False)
    # Top reasons (JSONB array of {reason_id, reason_text, count})
    top_support_reasons = Column(postgresql.JSONB, default=lambda: [], nullable=False)
    top_oppose_reasons = Column(postgresql.JSONB, default=lambda: [], nullable=False)
    top_amend_reasons = Column(postgresql.JSONB, default=lambda: [], nullable=False)
    # Geographic distribution {state_code: count}
    geographic_distribution = Column(postgresql.JSONB, default=lambda: {}, nullable=False)
    # Success rates
    success_rate = Column(DECIMAL(5, 2), default=0.00, nullable=False)
    error_rate = Column(DECIMAL(5, 2), default=0.00, nullable=False)

    # Relationships
    bill = relationship("Bill")
    campaign = relationship("Campaign")

    # Constraints
    __table_args__ = (
        UniqueConstraint('bill_id', 'campaign_id', 'date_bucket', name='uq_analytics_daily'),
        Index('idx_analytics_daily_bill', 'bill_id'),
        Index('idx_analytics_daily_date', 'date_bucket'),
    )

    def __repr__(self):
        return f"<ActionAnalyticsDaily(bill='{self.bill_id}', date='{self.date_bucket}')>"


class ActionAnalyticsRealtime(Base):
    """Real-time analytics summary (updated frequently)"""
    __tablename__ = "action_analytics_realtime"

    bill_id = Column(get_uuid_type(), ForeignKey("bills.id", ondelete="CASCADE"), nullable=False, unique=True)
    # Current totals
    total_actions = Column(Integer, default=0, nullable=False)
    support_count = Column(Integer, default=0, nullable=False)
    oppose_count = Column(Integer, default=0, nullable=False)
    amend_count = Column(Integer, default=0, nullable=False)
    unique_users = Column(Integer, default=0, nullable=False)
    # Recent activity (last 24h)
    recent_actions = Column(Integer, default=0, nullable=False)
    recent_users = Column(Integer, default=0, nullable=False)
    # Trending data
    trending_reasons = Column(postgresql.JSONB, default=lambda: [], nullable=False)
    state_distribution = Column(postgresql.JSONB, default=lambda: {}, nullable=False)
    last_updated = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    bill = relationship("Bill")

    # Indexes
    __table_args__ = (
        Index('idx_analytics_realtime_updated', 'last_updated'),
    )

    def __repr__(self):
        return f"<ActionAnalyticsRealtime(bill='{self.bill_id}', updated='{self.last_updated}')>"


class UserPrivacySettings(Base):
    """User privacy preferences and data retention settings"""
    __tablename__ = "user_privacy_settings"

    user_id = Column(get_uuid_type(), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True)
    # Privacy controls
    share_location_analytics = Column(Boolean, default=True, nullable=False)
    share_reasoning_analytics = Column(Boolean, default=True, nullable=False)
    allow_public_reasons = Column(Boolean, default=True, nullable=False)
    data_retention_preference = Column(SQLEnum(DataRetentionPreference), default=DataRetentionPreference.STANDARD, nullable=False)
    # Communication preferences
    allow_research_contact = Column(Boolean, default=False, nullable=False)
    allow_campaign_updates = Column(Boolean, default=True, nullable=False)

    # Relationships
    user = relationship("User", back_populates="privacy_settings")

    def __repr__(self):
        return f"<UserPrivacySettings(user='{self.user_id}', retention='{self.data_retention_preference}')>"