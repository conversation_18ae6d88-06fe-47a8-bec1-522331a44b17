# app/models/campaign.py
from sqlalchemy import Column, String, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ger, Foreign<PERSON>ey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.db.types import get_json_type, get_uuid_type
import enum

class CampaignStatus(str, enum.Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"

class CampaignType(str, enum.Enum):
    SUPPORT = "support"
    OPPOSE = "oppose"
    NEUTRAL = "neutral"
    AMENDMENT = "amendment"

class Campaign(Base):
    __tablename__ = "campaigns"

    # Basic campaign information
    title = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)
    short_description = Column(String, nullable=True)

    # Campaign configuration
    campaign_type = Column(SQLEnum(CampaignType), nullable=False, default=CampaignType.SUPPORT)
    status = Column(SQLEnum(CampaignStatus), nullable=False, default=CampaignStatus.DRAFT)

    # Campaign content
    call_to_action = Column(Text, nullable=False)
    email_template = Column(Text, nullable=True)
    talking_points = Column(Text, nullable=True)  # Array of talking points

    # Campaign targeting
    target_audience = Column(String, nullable=True)  # e.g., "constituents", "all_users"
    geographic_scope = Column(Text, nullable=True)  # Array of states/districts

    # Campaign timing
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)

    # Campaign settings
    is_featured = Column(Boolean, default=False, nullable=False)
    is_public = Column(Boolean, default=True, nullable=False)
    requires_verification = Column(Boolean, default=False, nullable=False)

    # Engagement metrics
    goal_actions = Column(Integer, nullable=True)
    actual_actions = Column(Integer, default=0, nullable=False)

    # Campaign assets
    banner_image_url = Column(String, nullable=True)
    thumbnail_image_url = Column(String, nullable=True)

    # Social media
    social_media_message = Column(Text, nullable=True)
    hashtags = Column(Text, nullable=True)  # Array of hashtags

    # Campaign metadata
    campaign_metadata = Column(Text, nullable=True)

    # Foreign keys
    bill_id = Column(get_uuid_type(), ForeignKey("bills.id"), nullable=False)

    # Relationships
    bill = relationship("Bill", back_populates="campaigns")
    actions = relationship("Action", back_populates="campaign", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Campaign(title='{self.title}', type='{self.campaign_type}')>"

    @property
    def completion_percentage(self) -> float:
        if not self.goal_actions or self.goal_actions == 0:
            return 0.0
        return min(100.0, (self.actual_actions / self.goal_actions) * 100)

    @property
    def is_active(self) -> bool:
        return self.status == CampaignStatus.ACTIVE

    @property
    def is_expired(self) -> bool:
        if not self.end_date:
            return False
        from datetime import datetime
        return datetime.utcnow() > self.end_date
