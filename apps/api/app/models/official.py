# app/models/official.py
from sqlalchemy import Column, String, Text, Bo<PERSON>an, Integer
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.db.types import get_json_type

class Official(Base):
    __tablename__ = "officials"

    # Basic official information
    name = Column(String, nullable=False, index=True)
    title = Column(String, nullable=False)
    party = Column(String, nullable=True)

    # Contact information
    email = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    website = Column(String, nullable=True)

    # Address information
    office_address = Column(Text, nullable=True)
    office_city = Column(String, nullable=True)
    office_state = Column(String, nullable=True)
    office_zip = Column(String, nullable=True)

    # Government structure - using String instead of Enum to avoid data integrity issues
    level = Column(String, nullable=False)  # 'federal', 'state', 'local'
    chamber = Column(String, nullable=True)  # 'house', 'senate', 'executive', 'judicial', 'other'

    # Geographic representation
    state = Column(String, nullable=True)  # State abbreviation
    district = Column(String, nullable=True)  # District number or name

    # External identifiers
    bioguide_id = Column(String, nullable=True, unique=True, index=True)
    openstates_id = Column(String, nullable=True, unique=True, index=True)
    google_civic_id = Column(String, nullable=True, unique=True, index=True)
    five_calls_id = Column(String, nullable=True, unique=True, index=True)

    # Social media - individual fields for backward compatibility
    twitter_handle = Column(String, nullable=True)  # Twitter username without @
    facebook_url = Column(String, nullable=True)
    instagram_handle = Column(String, nullable=True)

    # Rich social media data - JSONB column for comprehensive social media information
    social_media = Column(Text, nullable=True)

    # Official profile
    bio = Column(Text, nullable=True)
    profile_picture_url = Column(String, nullable=True)

    # Term information
    term_start = Column(String, nullable=True)  # Year as string
    term_end = Column(String, nullable=True)    # Year as string

    # Status
    is_active = Column(Boolean, default=True, nullable=False)

    # Voting record and positions
    voting_record = Column(Text, nullable=True)  # Array of vote records
    positions = Column(Text, nullable=True)      # Array of position statements

    # Engagement metrics
    response_rate = Column(Integer, nullable=True)  # Response rate percentage
    avg_response_time = Column(Integer, nullable=True)  # Response time in hours

    # Metadata
    official_metadata = Column(Text, nullable=True)

    # Relationships
    actions = relationship("Action", back_populates="official", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Official(name='{self.name}', title='{self.title}', state='{self.state}')>"

    @property
    def full_title(self) -> str:
        if self.state:
            return f"{self.title} from {self.state}"
        return self.title

    @property
    def display_name(self) -> str:
        if self.party:
            return f"{self.name} ({self.party})"
        return self.name

    @property
    def contact_preference(self) -> str:
        """Return the preferred contact method"""
        if self.email:
            return "email"
        elif self.phone:
            return "phone"
        elif self.website:
            return "website"
        return "none"
