# app/models/user.py
from sqlalchemy import Column, String, Boolean, DateTime, Text
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class User(Base):
    __tablename__ = "users"

    # Auth0 integration
    auth0_user_id = Column(String, unique=True, index=True, nullable=False)  # Auth0 user ID (sub claim)

    # Basic user information
    email = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=True)  # Full name from Auth0
    picture_url = Column(String, nullable=True)  # Profile picture from Auth0
    email_verified = Column(Boolean, default=False, nullable=False)  # From Auth0

    # Legacy fields (kept for backward compatibility, now optional)
    hashed_password = Column(String, nullable=True)  # Not used with Auth0
    first_name = Column(String, nullable=True)  # Derived from name if needed
    last_name = Column(String, nullable=True)   # Derived from name if needed

    # User preferences and settings
    zip_code = Column(String, nullable=True)
    phone_number = Column(String, nullable=True)

    # User status and permissions
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)

    # User engagement preferences
    email_notifications = Column(Boolean, default=True, nullable=False)
    sms_notifications = Column(Boolean, default=False, nullable=False)

    # User profile information
    bio = Column(Text, nullable=True)
    profile_picture_url = Column(String, nullable=True)

    # Email verification
    email_verification_token = Column(String, nullable=True)
    email_verified_at = Column(DateTime, nullable=True)

    # Password reset
    password_reset_token = Column(String, nullable=True)
    password_reset_expires_at = Column(DateTime, nullable=True)

    # Last login tracking
    last_login_at = Column(DateTime, nullable=True)

    # Enhanced user role for access control
    role = Column(String(50), default='user', nullable=False)

    # Relationships
    actions = relationship("Action", back_populates="user", cascade="all, delete-orphan")
    location = relationship("UserLocation", back_populates="user", uselist=False, cascade="all, delete-orphan")
    privacy_settings = relationship("UserPrivacySettings", back_populates="user", uselist=False, cascade="all, delete-orphan")

    def __repr__(self):
        return f"<User(email='{self.email}', name='{self.name}', auth0_id='{self.auth0_user_id}')>"

    @property
    def full_name(self) -> str:
        """Return full name, preferring Auth0 name over legacy first/last name."""
        if self.name:
            return self.name
        elif self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.email.split('@')[0]  # Fallback to email username
