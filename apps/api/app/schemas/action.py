# app/schemas/action.py
from pydantic import BaseModel, EmailStr, ConfigDict
from typing import Optional, Dict, Any, List
from datetime import datetime
from app.models.action import ActionStatus, ActionType

class ActionBase(BaseModel):
    """Base action schema with common fields"""
    subject: str
    message: str
    action_type: ActionType = ActionType.EMAIL
    user_name: str
    user_email: EmailStr
    user_address: Optional[str] = None
    user_zip_code: Optional[str] = None

class ActionCreate(ActionBase):
    """Schema for creating a new action"""
    campaign_id: str
    official_id: str
    action_types: List[str] = ["EMAIL"]  # List of action types: EMAIL, TWITTER, etc.
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None
    contact_address: Optional[str] = None
    # Action Network integration fields
    use_action_network: bool = False
    bill_id: Optional[str] = None
    position: Optional[str] = None  # 'support', 'oppose', 'amend'
    personalize_message: bool = True

class ActionUpdate(BaseModel):
    """Schema for updating action information"""
    status: Optional[ActionStatus] = None
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    response_received: Optional[bool] = None
    response_content: Optional[str] = None
    response_received_at: Optional[datetime] = None
    delivery_method: Optional[str] = None
    delivery_id: Optional[str] = None
    error_message: Optional[str] = None

class ActionResponse(ActionBase):
    """Schema for action API responses"""
    id: str
    campaign_id: str
    official_id: str
    user_id: str
    status: ActionStatus
    created_at: datetime
    updated_at: datetime
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    response_received: bool
    response_content: Optional[str] = None
    response_received_at: Optional[datetime] = None
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None
    contact_address: Optional[str] = None
    delivery_method: Optional[str] = None
    delivery_id: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int

    model_config = ConfigDict(from_attributes=True)

class Action(ActionResponse):
    """Complete action schema for internal use"""
    action_metadata: Optional[Dict[str, Any]] = None

class ActionSummary(BaseModel):
    """Lightweight action summary for lists"""
    id: str
    subject: str
    action_type: ActionType
    status: ActionStatus
    campaign_id: str
    official_name: str
    created_at: datetime
    sent_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class ActionSearch(BaseModel):
    """Schema for action search parameters"""
    user_id: Optional[str] = None
    campaign_id: Optional[str] = None
    official_id: Optional[str] = None
    status: Optional[ActionStatus] = None
    action_type: Optional[ActionType] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    limit: int = 20
    offset: int = 0

class ActionStats(BaseModel):
    """Action statistics schema"""
    total_actions: int
    successful_actions: int
    pending_actions: int
    failed_actions: int
    success_rate: float
    average_response_time: Optional[float] = None
    by_type: Dict[str, int]
    by_status: Dict[str, int]

class ActionWithDetails(ActionResponse):
    """Action with related campaign and official details"""
    campaign_title: str
    official_name: str
    official_title: str
    bill_title: str
    bill_number: str

    model_config = ConfigDict(from_attributes=True)
