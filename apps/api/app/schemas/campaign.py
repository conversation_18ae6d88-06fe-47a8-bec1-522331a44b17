# app/schemas/campaign.py
from pydantic import BaseModel, ConfigDict, HttpUrl, field_serializer
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models.campaign import CampaignStatus, CampaignType
from app.schemas.bill import BillResponse

class CampaignBase(BaseModel):
    """Base campaign schema with common fields"""
    title: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    campaign_type: CampaignType = CampaignType.SUPPORT
    status: CampaignStatus = CampaignStatus.DRAFT
    call_to_action: str
    email_template: Optional[str] = None
    target_audience: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_featured: bool = False
    is_public: bool = True
    requires_verification: bool = False
    goal_actions: Optional[int] = None
    banner_image_url: Optional[HttpUrl] = None
    thumbnail_image_url: Optional[HttpUrl] = None
    social_media_message: Optional[str] = None

    @field_serializer('banner_image_url', 'thumbnail_image_url')
    def serialize_urls(self, value):
        return str(value) if value else None

class CampaignCreate(CampaignBase):
    """Schema for creating a new campaign"""
    bill_id: str
    talking_points: Optional[List[str]] = None
    geographic_scope: Optional[List[str]] = None
    hashtags: Optional[List[str]] = None

class CampaignUpdate(BaseModel):
    """Schema for updating campaign information"""
    title: Optional[str] = None
    description: Optional[str] = None
    short_description: Optional[str] = None
    status: Optional[CampaignStatus] = None
    call_to_action: Optional[str] = None
    email_template: Optional[str] = None
    target_audience: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_featured: Optional[bool] = None
    is_public: Optional[bool] = None
    requires_verification: Optional[bool] = None
    goal_actions: Optional[int] = None
    banner_image_url: Optional[HttpUrl] = None
    thumbnail_image_url: Optional[HttpUrl] = None
    social_media_message: Optional[str] = None
    talking_points: Optional[List[str]] = None
    geographic_scope: Optional[List[str]] = None
    hashtags: Optional[List[str]] = None

    @field_serializer('banner_image_url', 'thumbnail_image_url')
    def serialize_urls(self, value):
        return str(value) if value else None

class CampaignResponse(CampaignBase):
    """Schema for campaign API responses"""
    id: str
    bill_id: str
    bill: BillResponse  # Nested Bill object for rich responses
    actual_actions: int
    created_at: datetime
    updated_at: datetime
    talking_points: Optional[List[str]] = None
    geographic_scope: Optional[List[str]] = None
    hashtags: Optional[List[str]] = None
    completion_percentage: float

    model_config = ConfigDict(from_attributes=True)

class Campaign(CampaignResponse):
    """Complete campaign schema for internal use"""
    campaign_metadata: Optional[Dict[str, Any]] = None

class CampaignSummary(BaseModel):
    """Lightweight campaign summary for lists"""
    id: str
    title: str
    campaign_type: CampaignType
    status: CampaignStatus
    actual_actions: int
    goal_actions: Optional[int] = None
    completion_percentage: float
    is_featured: bool
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class CampaignSearch(BaseModel):
    """Schema for campaign search parameters"""
    query: Optional[str] = None
    campaign_type: Optional[CampaignType] = None
    status: Optional[CampaignStatus] = None
    bill_id: Optional[str] = None
    is_featured: Optional[bool] = None
    is_public: Optional[bool] = None
    is_active: Optional[bool] = None
    limit: int = 20
    offset: int = 0

class CampaignStats(BaseModel):
    """Campaign statistics schema"""
    total_campaigns: int
    active_campaigns: int
    total_actions: int
    average_completion: float
    top_campaigns: List[CampaignSummary]

class CampaignWithBill(CampaignResponse):
    """Campaign with related bill information"""
    bill_title: str
    bill_number: str
    bill_chamber: str
    bill_status: str

    model_config = ConfigDict(from_attributes=True)
