# app/schemas/user.py
from pydantic import BaseModel, EmailStr, ConfigDict
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    """Base user schema with common fields"""
    email: EmailStr
    first_name: str
    last_name: str
    zip_code: Optional[str] = None
    phone_number: Optional[str] = None
    email_notifications: bool = True
    sms_notifications: bool = False
    bio: Optional[str] = None
    profile_picture_url: Optional[str] = None

class UserCreate(UserBase):
    """Schema for creating a new user"""
    password: str

class UserUpdate(BaseModel):
    """Schema for updating user information"""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    zip_code: Optional[str] = None
    phone_number: Optional[str] = None
    email_notifications: Optional[bool] = None
    sms_notifications: Optional[bool] = None
    bio: Optional[str] = None
    profile_picture_url: Optional[str] = None

class UserResponse(UserBase):
    """Schema for user API responses"""
    id: str
    is_active: bool
    is_verified: bool
    is_superuser: bool
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class User(UserResponse):
    """Complete user schema for internal use"""
    pass

class UserAuth(BaseModel):
    """Schema for user authentication"""
    email: EmailStr
    password: str

class UserToken(BaseModel):
    """Schema for authentication tokens"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int

class UserProfile(BaseModel):
    """Public user profile schema"""
    first_name: str
    last_name: str
    bio: Optional[str] = None
    profile_picture_url: Optional[str] = None
    actions_taken: int = 0
    campaigns_joined: int = 0

    model_config = ConfigDict(from_attributes=True)
