# app/services/action_tracking_service.py
"""
Comprehensive action tracking service for civic engagement analytics.

This service handles all aspects of action tracking including:
- User reasoning and personalization
- Error logging and resolution
- Action Network submission tracking  
- Privacy-safe analytics aggregation
"""

import logging
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from datetime import datetime, date, timedelta
import uuid

from app.models.action_tracking import (
    ReasoningOption, ActionReasoning, CustomReasonsPool,
    UserLocation, ActionError, ActionNetworkSubmission,
    ActionAnalyticsDaily, ActionAnalyticsRealtime, UserPrivacySettings,
    ActionStance, ErrorType, ResolutionStatus
)
from app.models.action import Action, ActionStatus
from app.models.user import User
from app.models.bill import Bill
from app.services.location_encryption import LocationEncryptionService

logger = logging.getLogger(__name__)


class ActionTrackingService:
    """Service for comprehensive action tracking and analytics"""

    def __init__(self, db: Session):
        self.db = db
        self.location_service = LocationEncryptionService()

    def create_reasoning_options_for_bill(self, bill_id: str, stance: ActionStance, reasons: List[Dict[str, Any]]) -> List[ReasoningOption]:
        """
        Create pre-defined reasoning options for a bill/stance combination
        
        Args:
            bill_id: Bill ID
            stance: User stance (support, oppose, amend)
            reasons: List of reason dicts with text, category, display_order
            
        Returns:
            List of created ReasoningOption objects
        """
        try:
            created_options = []
            
            for reason_data in reasons:
                reasoning_option = ReasoningOption(
                    id=str(uuid.uuid4()),
                    bill_id=bill_id,
                    stance=stance,
                    reason_text=reason_data['text'],
                    reason_category=reason_data.get('category'),
                    display_order=reason_data.get('display_order', 0),
                    is_active=reason_data.get('is_active', True)
                )
                
                self.db.add(reasoning_option)
                created_options.append(reasoning_option)
            
            self.db.commit()
            logger.info(f"Created {len(created_options)} reasoning options for bill {bill_id}, stance {stance}")
            return created_options
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create reasoning options: {e}")
            raise

    def track_action_reasoning(self, action_id: str, selected_reasons: List[str], custom_reasons: List[str]) -> Dict[str, Any]:
        """
        Track user's reasoning for an action
        
        Args:
            action_id: Action ID
            selected_reasons: List of reasoning option IDs
            custom_reasons: List of custom reason texts
            
        Returns:
            Dict with tracking results
        """
        try:
            # Track selected pre-defined reasons
            for i, reason_id in enumerate(selected_reasons):
                action_reasoning = ActionReasoning(
                    id=str(uuid.uuid4()),
                    action_id=action_id,
                    reasoning_option_id=reason_id,
                    is_primary_reason=(i == 0)  # First reason is primary
                )
                self.db.add(action_reasoning)
                
                # Increment usage count for the reasoning option
                reasoning_option = self.db.query(ReasoningOption).filter(
                    ReasoningOption.id == reason_id
                ).first()
                if reasoning_option:
                    reasoning_option.usage_count += 1

            # Track custom reasons
            action = self.db.query(Action).filter(Action.id == action_id).first()
            if not action:
                raise ValueError(f"Action {action_id} not found")

            for custom_reason in custom_reasons:
                if custom_reason.strip():  # Only track non-empty reasons
                    custom_reason_record = CustomReasonsPool(
                        id=str(uuid.uuid4()),
                        action_id=action_id,
                        user_id=action.user_id,
                        bill_id=action.bill_id,
                        stance=ActionStance(action.position) if action.position else ActionStance.SUPPORT,
                        custom_reason=custom_reason.strip()
                    )
                    self.db.add(custom_reason_record)

            self.db.commit()
            
            return {
                'success': True,
                'selected_reasons_tracked': len(selected_reasons),
                'custom_reasons_tracked': len([r for r in custom_reasons if r.strip()]),
                'message': 'Action reasoning tracked successfully'
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to track action reasoning: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def save_user_location(self, user_id: str, address: str, zip_code: str, source: str = 'user_input') -> Optional[UserLocation]:
        """
        Save encrypted user location data with privacy-safe analytics extraction
        
        Args:
            user_id: User ID
            address: Full address
            zip_code: ZIP code
            source: Source of location data
            
        Returns:
            UserLocation object or None if failed
        """
        try:
            # Encrypt sensitive data
            encrypted_address = self.location_service.encrypt_address(address)
            encrypted_zip = self.location_service.encrypt_zip_code(zip_code)
            
            # Extract analytics-safe data
            analytics_data = self.location_service.extract_analytics_data(address, zip_code)
            
            # Check if user location already exists
            existing_location = self.db.query(UserLocation).filter(
                UserLocation.user_id == user_id
            ).first()
            
            if existing_location:
                # Update existing location
                existing_location.encrypted_address = encrypted_address
                existing_location.encrypted_zip_code = encrypted_zip
                existing_location.state_code = analytics_data.get('state_code')
                existing_location.congressional_district = analytics_data.get('congressional_district')
                existing_location.latitude = analytics_data.get('latitude')
                existing_location.longitude = analytics_data.get('longitude')
                existing_location.location_source = source
                existing_location.accuracy_level = analytics_data.get('accuracy_level', 'city')
                existing_location.updated_at = datetime.utcnow()
                
                user_location = existing_location
            else:
                # Create new location record
                user_location = UserLocation(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    encrypted_address=encrypted_address,
                    encrypted_zip_code=encrypted_zip,
                    state_code=analytics_data.get('state_code'),
                    congressional_district=analytics_data.get('congressional_district'),
                    latitude=analytics_data.get('latitude'),
                    longitude=analytics_data.get('longitude'),
                    location_source=source,
                    accuracy_level=analytics_data.get('accuracy_level', 'city')
                )
                self.db.add(user_location)
            
            self.db.commit()
            logger.info(f"Saved encrypted location for user {user_id}")
            return user_location
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to save user location: {e}")
            return None

    def log_action_error(self, action_id: str, error_type: ErrorType, error_message: str, 
                        error_details: Dict[str, Any] = None, error_code: str = None) -> ActionError:
        """
        Log an error for an action
        
        Args:
            action_id: Action ID
            error_type: Type of error
            error_message: Error message
            error_details: Additional error details
            error_code: Error code (HTTP status, API error code, etc.)
            
        Returns:
            ActionError object
        """
        try:
            action_error = ActionError(
                id=str(uuid.uuid4()),
                action_id=action_id,
                error_type=error_type,
                error_code=error_code,
                error_message=error_message,
                error_details=error_details or {},
                resolution_status=ResolutionStatus.UNRESOLVED
            )
            
            self.db.add(action_error)
            
            # Update action status to failed
            action = self.db.query(Action).filter(Action.id == action_id).first()
            if action:
                action.status = ActionStatus.FAILED
                action.error_message = error_message
                action.retry_count += 1
            
            self.db.commit()
            logger.error(f"Logged error for action {action_id}: {error_message}")
            return action_error
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to log action error: {e}")
            raise

    def track_action_network_submission(self, action_id: str, campaign_id: str, form_type: str,
                                      target_chamber: str, embed_url: str = None, 
                                      iframe_url: str = None) -> ActionNetworkSubmission:
        """
        Track Action Network form submission
        
        Args:
            action_id: Action ID
            campaign_id: Action Network campaign ID
            form_type: Form type (house, senate, unified)
            target_chamber: Target chamber (house, senate, both)
            embed_url: Embed URL
            iframe_url: Iframe URL
            
        Returns:
            ActionNetworkSubmission object
        """
        try:
            submission = ActionNetworkSubmission(
                id=str(uuid.uuid4()),
                action_id=action_id,
                campaign_id=campaign_id,
                form_type=form_type,
                target_chamber=target_chamber,
                embed_url=embed_url,
                iframe_url=iframe_url,
                submission_status='pending'
            )
            
            self.db.add(submission)
            self.db.commit()
            
            logger.info(f"Tracked Action Network submission for action {action_id}")
            return submission
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to track Action Network submission: {e}")
            raise

    def get_bill_analytics(self, bill_id: str) -> Dict[str, Any]:
        """
        Get comprehensive analytics for a bill
        
        Args:
            bill_id: Bill ID
            
        Returns:
            Dict with analytics data
        """
        try:
            # Get real-time analytics
            realtime = self.db.query(ActionAnalyticsRealtime).filter(
                ActionAnalyticsRealtime.bill_id == bill_id
            ).first()
            
            # Get recent daily analytics (last 30 days)
            thirty_days_ago = date.today() - timedelta(days=30)
            daily_analytics = self.db.query(ActionAnalyticsDaily).filter(
                and_(
                    ActionAnalyticsDaily.bill_id == bill_id,
                    ActionAnalyticsDaily.date_bucket >= thirty_days_ago
                )
            ).order_by(ActionAnalyticsDaily.date_bucket.desc()).all()
            
            # Get top reasons by stance
            top_reasons = self._get_top_reasons_for_bill(bill_id)
            
            # Get geographic distribution (privacy-safe)
            geographic_data = self._get_geographic_distribution(bill_id)
            
            return {
                'bill_id': bill_id,
                'realtime_stats': {
                    'total_actions': realtime.total_actions if realtime else 0,
                    'support_count': realtime.support_count if realtime else 0,
                    'oppose_count': realtime.oppose_count if realtime else 0,
                    'amend_count': realtime.amend_count if realtime else 0,
                    'unique_users': realtime.unique_users if realtime else 0,
                    'recent_actions': realtime.recent_actions if realtime else 0,
                    'last_updated': realtime.last_updated if realtime else None
                },
                'daily_trend': [
                    {
                        'date': day.date_bucket,
                        'total_actions': day.total_actions,
                        'support_count': day.support_count,
                        'oppose_count': day.oppose_count,
                        'amend_count': day.amend_count,
                        'unique_users': day.unique_users,
                        'success_rate': float(day.success_rate)
                    } for day in daily_analytics
                ],
                'top_reasons': top_reasons,
                'geographic_distribution': geographic_data
            }
            
        except Exception as e:
            logger.error(f"Failed to get bill analytics: {e}")
            return {'error': str(e)}

    def _get_top_reasons_for_bill(self, bill_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """Get top reasons by stance for a bill"""
        top_reasons = {}
        
        for stance in ['support', 'oppose', 'amend']:
            # Get top pre-defined reasons
            predefined_reasons = self.db.query(
                ReasoningOption.reason_text,
                ReasoningOption.reason_category,
                func.count(ActionReasoning.id).label('usage_count')
            ).join(ActionReasoning).join(Action).filter(
                and_(
                    Action.bill_id == bill_id,
                    Action.position == stance,
                    Action.status.in_([ActionStatus.SENT, ActionStatus.DELIVERED])
                )
            ).group_by(
                ReasoningOption.id, ReasoningOption.reason_text, ReasoningOption.reason_category
            ).order_by(func.count(ActionReasoning.id).desc()).limit(5).all()
            
            top_reasons[stance] = [
                {
                    'reason_text': reason.reason_text,
                    'category': reason.reason_category,
                    'usage_count': reason.usage_count,
                    'type': 'predefined'
                } for reason in predefined_reasons
            ]
        
        return top_reasons

    def _get_geographic_distribution(self, bill_id: str) -> Dict[str, Any]:
        """Get privacy-safe geographic distribution for a bill"""
        try:
            # Only include users who opted in to location analytics
            geo_data = self.db.query(
                UserLocation.state_code,
                func.count(Action.id).label('action_count'),
                func.count(Action.id).filter(Action.position == 'support').label('support_count'),
                func.count(Action.id).filter(Action.position == 'oppose').label('oppose_count'),
                func.count(func.distinct(Action.user_id)).label('unique_users')
            ).join(Action, UserLocation.user_id == Action.user_id).join(
                UserPrivacySettings, UserLocation.user_id == UserPrivacySettings.user_id
            ).filter(
                and_(
                    Action.bill_id == bill_id,
                    Action.status.in_([ActionStatus.SENT, ActionStatus.DELIVERED]),
                    UserPrivacySettings.share_location_analytics == True,
                    UserLocation.state_code.isnot(None)
                )
            ).group_by(UserLocation.state_code).order_by(
                func.count(Action.id).desc()
            ).all()
            
            return {
                'by_state': [
                    {
                        'state_code': row.state_code,
                        'action_count': row.action_count,
                        'support_count': row.support_count,
                        'oppose_count': row.oppose_count,
                        'unique_users': row.unique_users
                    } for row in geo_data
                ]
            }
            
        except Exception as e:
            logger.error(f"Failed to get geographic distribution: {e}")
            return {'by_state': []}

    def update_realtime_analytics(self, bill_id: str) -> ActionAnalyticsRealtime:
        """Update real-time analytics for a bill"""
        try:
            # Get current stats
            stats = self.db.query(
                func.count(Action.id).label('total_actions'),
                func.count(Action.id).filter(Action.position == 'support').label('support_count'),
                func.count(Action.id).filter(Action.position == 'oppose').label('oppose_count'),
                func.count(Action.id).filter(Action.position == 'amend').label('amend_count'),
                func.count(func.distinct(Action.user_id)).label('unique_users')
            ).filter(
                and_(
                    Action.bill_id == bill_id,
                    Action.status.in_([ActionStatus.SENT, ActionStatus.DELIVERED])
                )
            ).first()
            
            # Get recent activity (last 24 hours)
            yesterday = datetime.utcnow() - timedelta(hours=24)
            recent_stats = self.db.query(
                func.count(Action.id).label('recent_actions'),
                func.count(func.distinct(Action.user_id)).label('recent_users')
            ).filter(
                and_(
                    Action.bill_id == bill_id,
                    Action.status.in_([ActionStatus.SENT, ActionStatus.DELIVERED]),
                    Action.created_at >= yesterday
                )
            ).first()
            
            # Update or create real-time analytics
            realtime = self.db.query(ActionAnalyticsRealtime).filter(
                ActionAnalyticsRealtime.bill_id == bill_id
            ).first()
            
            if realtime:
                realtime.total_actions = stats.total_actions or 0
                realtime.support_count = stats.support_count or 0
                realtime.oppose_count = stats.oppose_count or 0
                realtime.amend_count = stats.amend_count or 0
                realtime.unique_users = stats.unique_users or 0
                realtime.recent_actions = recent_stats.recent_actions or 0
                realtime.recent_users = recent_stats.recent_users or 0
                realtime.last_updated = datetime.utcnow()
            else:
                realtime = ActionAnalyticsRealtime(
                    id=str(uuid.uuid4()),
                    bill_id=bill_id,
                    total_actions=stats.total_actions or 0,
                    support_count=stats.support_count or 0,
                    oppose_count=stats.oppose_count or 0,
                    amend_count=stats.amend_count or 0,
                    unique_users=stats.unique_users or 0,
                    recent_actions=recent_stats.recent_actions or 0,
                    recent_users=recent_stats.recent_users or 0
                )
                self.db.add(realtime)
            
            self.db.commit()
            return realtime
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update realtime analytics: {e}")
            raise