# app/services/ai.py
"""
AI service for bill summarization using Hugging Face transformers.

This service provides bill text summarization capabilities using the t5-small model
optimized for performance with singleton pattern to avoid repeated model loading.
"""

import logging
import time
from functools import lru_cache
from typing import Dict, Any
from app.core.config import settings

# Optional import for AI features - gracefully handle missing dependency
try:
    from transformers import pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    logging.warning("transformers library not available - AI features will be disabled")
    pipeline = None
    TRANSFORMERS_AVAILABLE = False

logger = logging.getLogger(__name__)

@lru_cache(maxsize=None)
def get_summarizer():
    """
    Get the summarization pipeline as a singleton.

    This function will only run once due to @lru_cache, ensuring the model
    is loaded only once for the entire application lifecycle.

    Returns:
        pipeline: Hugging Face summarization pipeline
    """
    if not TRANSFORMERS_AVAILABLE:
        logger.warning("Transformers library not available - cannot load summarization model")
        return None
        
    logger.info("Loading summarization model...")
    try:
        # Use the model specified in settings, fallback to t5-small
        model_name = getattr(settings, 'SUMMARIZATION_MODEL', 't5-small')

        summarizer = pipeline(
            "summarization",
            model=model_name,
            tokenizer=model_name,
            framework="pt",  # Use PyTorch
            device=-1  # Use CPU (-1), GPU would be 0
        )

        logger.info(f"Successfully loaded summarization model: {model_name}")
        return summarizer

    except Exception as e:
        logger.error(f"Failed to load summarization model: {e}")
        raise RuntimeError(f"Could not initialize summarization model: {e}")


@lru_cache(maxsize=None)
def get_text_generator():
    """
    Get the text generation pipeline as a singleton.

    This function will only run once due to @lru_cache, ensuring the model
    is loaded only once for the entire application lifecycle.

    Returns:
        pipeline: Hugging Face text generation pipeline
    """
    if not TRANSFORMERS_AVAILABLE:
        logger.warning("Transformers library not available - cannot load text generation model")
        return None
        
    logger.info("Loading text generation model...")
    try:
        # Use a model suitable for text generation
        # t5-small can be used for text-to-text generation
        model_name = getattr(settings, 'TEXT_GENERATION_MODEL', 't5-small')

        generator = pipeline(
            "text2text-generation",
            model=model_name,
            tokenizer=model_name,
            framework="pt",  # Use PyTorch
            device=-1  # Use CPU (-1), GPU would be 0
        )

        logger.info(f"Successfully loaded text generation model: {model_name}")
        return generator

    except Exception as e:
        logger.error(f"Failed to load text generation model: {e}")
        raise RuntimeError(f"Could not initialize text generation model: {e}")

def summarize_text(text: str, max_length: int = 150, min_length: int = 30) -> str:
    """
    Summarize long text into a shorter, more digestible version.

    This function takes complex bill text and converts it into plain English
    summaries that citizens can easily understand.

    Args:
        text (str): The original bill text to summarize
        max_length (int): Maximum length of the summary (default: 150)
        min_length (int): Minimum length of the summary (default: 30)

    Returns:
        str: A plain English summary of the bill

    Raises:
        ValueError: If text is empty or too short
        RuntimeError: If summarization fails
    """
    if not text or not text.strip():
        raise ValueError("Text to summarize cannot be empty")

    # Clean and prepare the text
    text = text.strip()

    # Handle very short texts
    if len(text) < min_length:
        logger.warning(f"Text too short for summarization: {len(text)} characters")
        return text

    # Handle very long texts by truncating (t5-small has token limits)
    MAX_INPUT_LENGTH = 1024  # Conservative limit for t5-small
    if len(text) > MAX_INPUT_LENGTH:
        logger.info(f"Truncating text from {len(text)} to {MAX_INPUT_LENGTH} characters")
        text = text[:MAX_INPUT_LENGTH]

    try:
        # Get the summarizer instance
        summarizer = get_summarizer()
        
        if summarizer is None:
            # Return a fallback message when transformers is not available
            logger.warning("AI summarization not available - returning original text excerpt")
            return text[:max_length] + "..." if len(text) > max_length else text

        # Generate summary
        logger.info("Generating summary...")
        summary_result = summarizer(
            text,
            max_length=max_length,
            min_length=min_length,
            do_sample=False,  # Use greedy decoding for consistent results
            truncation=True
        )

        # Extract the summary text
        summary = summary_result[0]['summary_text']

        logger.info(f"Generated summary: {len(summary)} characters")
        return summary.strip()

    except Exception as e:
        logger.error(f"Summarization failed: {e}")
        raise RuntimeError(f"Failed to generate summary: {e}")

def summarize_bill(bill_text: str, title: str = "") -> str:
    """
    Specialized function for summarizing bill text with context.

    This function is optimized for legislative text and provides
    citizen-friendly summaries with proper context.

    Args:
        bill_text (str): The full text of the bill
        title (str): Optional bill title for context

    Returns:
        str: A citizen-friendly summary of the bill
    """
    if not bill_text:
        return "No bill text available for summarization."

    # Prepare the text with context if title is provided
    if title:
        # Add title context to help the model understand what it's summarizing
        contextualized_text = f"Bill Title: {title}\n\nBill Text: {bill_text}"
    else:
        contextualized_text = bill_text

    try:
        # Use slightly longer summaries for bills since they're complex
        summary = summarize_text(
            contextualized_text,
            max_length=200,  # Longer for complex legislative text
            min_length=50
        )

        # Post-process the summary to ensure it's citizen-friendly
        # Remove any remaining legal jargon indicators
        summary = summary.replace("SEC.", "Section")
        summary = summary.replace("SECTION", "Section")

        return summary

    except Exception as e:
        logger.error(f"Bill summarization failed: {e}")
        return f"Summary generation failed: {str(e)}"

def get_model_info() -> dict:
    """
    Get information about the loaded model for debugging and monitoring.

    Returns:
        dict: Model information including name, status, and capabilities
    """
    try:
        get_summarizer()  # This will raise exception if model fails to load
        model_name = getattr(settings, 'SUMMARIZATION_MODEL', 't5-small')

        return {
            "model_name": model_name,
            "status": "loaded",
            "framework": "transformers",
            "device": "cpu",
            "task": "summarization",
            "max_input_length": 1024,
            "default_max_length": 150,
            "default_min_length": 30
        }
    except Exception as e:
        return {
            "model_name": "unknown",
            "status": "failed",
            "error": str(e)
        }

def personalize_message(raw_text: str, context: str, tone: str = "professional") -> Dict[str, Any]:
    """
    Personalize a user's raw message using AI to make it more persuasive and well-structured.

    This function takes a user's personal story or concern and transforms it into
    a more effective advocacy message while preserving the personal touch.

    Args:
        raw_text (str): The user's original message or personal story
        context (str): Context about the campaign or issue (e.g., bill title, campaign goal)
        tone (str): Desired tone for the message (professional, passionate, formal, personal, urgent)

    Returns:
        Dict[str, Any]: Dictionary containing personalized message and metadata

    Raises:
        ValueError: If raw_text or context is empty
        RuntimeError: If personalization fails
    """
    start_time = time.time()

    if not raw_text or not raw_text.strip():
        raise ValueError("Raw text cannot be empty")

    if not context or not context.strip():
        raise ValueError("Context cannot be empty")

    # Clean inputs
    raw_text = raw_text.strip()
    context = context.strip()

    try:
        # Create a structured prompt for the text generation model
        prompt = _create_personalization_prompt(raw_text, context, tone)

        # Get the text generator
        generator = get_text_generator()
        
        if generator is None:
            # Return fallback when transformers is not available
            logger.warning("AI personalization not available - returning formatted original text")
            personalized_text = f"Re: {context}\n\n{raw_text}\n\nThank you for your consideration."
        else:
            # Generate the personalized message
            logger.info("Generating personalized message...")
            result = generator(
                prompt,
                max_length=300,  # Allow for longer personalized messages
                min_length=50,
                do_sample=True,  # Allow some creativity
                temperature=0.7,  # Balanced creativity
                truncation=True
            )

            # Extract and clean the generated text
            personalized_text = result[0]['generated_text'].strip()

            # Post-process the generated text
            personalized_text = _post_process_personalized_message(personalized_text)

        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds

        logger.info(f"Generated personalized message: {len(personalized_text)} characters in {processing_time:.1f}ms")

        return {
            "personalized_message": personalized_text,
            "original_length": len(raw_text),
            "personalized_length": len(personalized_text),
            "processing_time_ms": processing_time
        }

    except Exception as e:
        logger.error(f"Message personalization failed: {e}")
        raise RuntimeError(f"Failed to personalize message: {e}")


def _create_personalization_prompt(raw_text: str, context: str, tone: str) -> str:
    """
    Create a structured prompt for message personalization.

    Args:
        raw_text (str): User's original message
        context (str): Campaign/issue context
        tone (str): Desired tone

    Returns:
        str: Formatted prompt for the AI model
    """
    tone_instructions = {
        "professional": "Write in a professional, respectful tone suitable for official correspondence.",
        "passionate": "Write with passion and urgency while remaining respectful and constructive.",
        "formal": "Write in a formal, diplomatic tone appropriate for government officials.",
        "personal": "Write in a personal, heartfelt tone that emphasizes individual impact.",
        "urgent": "Write with a sense of urgency and importance while remaining professional."
    }

    tone_instruction = tone_instructions.get(tone, tone_instructions["professional"])

    prompt = f"""Transform the following personal message into a persuasive advocacy letter.

Context: {context}

Personal Message: {raw_text}

Instructions: {tone_instruction} Make the message more structured and persuasive while preserving the personal elements. Include a clear call to action.

Transformed Message:"""

    return prompt


def _post_process_personalized_message(text: str) -> str:
    """
    Post-process the AI-generated message to ensure quality.

    Args:
        text (str): Raw AI-generated text

    Returns:
        str: Cleaned and formatted message
    """
    # Remove any prompt artifacts
    text = text.replace("Transformed Message:", "").strip()

    # Ensure proper sentence structure
    if not text.endswith(('.', '!', '?')):
        text += '.'

    # Capitalize first letter
    if text and text[0].islower():
        text = text[0].upper() + text[1:]

    return text


# Health check function for the AI service
def health_check() -> dict:
    """
    Perform a health check on the AI service.

    Returns:
        dict: Health status information
    """
    if not TRANSFORMERS_AVAILABLE:
        return {
            "status": "limited",
            "model_loaded": False,
            "capabilities": ["fallback_summarization", "fallback_personalization"],
            "message": "AI features running in fallback mode - transformers library not available",
            "model_info": {"status": "transformers_unavailable"}
        }
        
    try:
        # Test summarization
        test_text = "This is a test of the summarization service to ensure it is working correctly."
        summary = summarize_text(test_text, max_length=50, min_length=10)

        # Test text generation
        test_personalization = personalize_message(
            "I am concerned about this issue",
            "Environmental Protection Act",
            "professional"
        )

        return {
            "status": "healthy",
            "model_loaded": True,
            "capabilities": ["summarization", "text_generation", "message_personalization"],
            "test_summary_length": len(summary),
            "test_personalization_length": test_personalization["personalized_length"],
            "model_info": get_model_info()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "model_loaded": False,
            "error": str(e),
            "capabilities": [],
            "model_info": get_model_info()
        }
