# app/services/bill_history_tracker.py
"""
Bill History Tracking Service

This service monitors bills for changes and updates their history records.
It fetches data from Congress.gov to track:
- Status changes (introduced -> committee -> floor -> signed/vetoed)
- Amendment additions
- Vote results
- Text versions
- Timeline updates
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.models.bill import Bill, BillStatus, BillStatusPipeline, BillSummaryVersion
from app.services.congress_gov_api import CongressGovAPI
from app.services.bill_status_update import BillStatusUpdateService
from app.services.bill_summary_version_service import BillSummaryVersionService

logger = logging.getLogger(__name__)


class BillHistoryTracker:
    """Service for tracking bill history and changes over time."""
    
    def __init__(self, db: Session):
        self.db = db
        self.congress_api = CongressGovAPI()
        self.status_service = BillStatusUpdateService(db)
        self.version_service = BillSummaryVersionService(db)
    
    def track_bill_changes(self, bill: Bill) -> Dict[str, Any]:
        """
        Track all changes for a single bill.
        
        Args:
            bill: Bill to track
            
        Returns:
            Dict with tracking results
        """
        results = {
            'bill_id': bill.id,
            'bill_number': bill.bill_number,
            'status_changes': [],
            'amendments': [],
            'votes': [],
            'text_versions': [],
            'errors': []
        }
        
        try:
            # 1. Check for status changes
            status_change = self._check_status_changes(bill)
            if status_change:
                results['status_changes'].append(status_change)
            
            # 2. Check for new amendments
            amendments = self._check_amendments(bill)
            results['amendments'].extend(amendments)
            
            # 3. Check for vote results
            votes = self._check_vote_results(bill)
            results['votes'].extend(votes)
            
            # 4. Check for text version changes
            text_versions = self._check_text_versions(bill)
            results['text_versions'].extend(text_versions)
            
            # 5. Update last checked timestamp
            bill.last_history_check = datetime.utcnow()
            
            logger.info(f"Completed history tracking for bill {bill.bill_number}")
            
        except Exception as e:
            error_msg = f"Failed to track changes for bill {bill.bill_number}: {str(e)}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def _check_status_changes(self, bill: Bill) -> Optional[Dict[str, Any]]:
        """Check for bill status changes."""
        try:
            # Get current bill data from Congress.gov
            bill_data = self._fetch_current_bill_data(bill)
            if not bill_data:
                return None
            
            # Extract current status from API
            current_api_status = self._extract_status_from_api_data(bill_data)
            if not current_api_status:
                return None
            
            # Compare with our current status
            if current_api_status != bill.status:
                # Status has changed - create history record
                status_record = self.status_service.create_status_change_record(
                    bill=bill,
                    new_status=current_api_status,
                    status_changed_at=datetime.utcnow(),
                    external_data=str(bill_data),
                    is_significant=True
                )
                
                return {
                    'previous_status': bill.status.value,
                    'new_status': current_api_status.value,
                    'changed_at': status_record.status_changed_at.isoformat(),
                    'is_significant': True
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to check status changes for bill {bill.id}: {e}")
            return None
    
    def _check_amendments(self, bill: Bill) -> List[Dict[str, Any]]:
        """Check for new amendments to the bill."""
        amendments = []
        
        try:
            # Get amendments from Congress.gov
            if hasattr(bill, 'congress_number') and hasattr(bill, 'bill_type_code'):
                amendments_data = self.congress_api.get_bill_amendments(
                    congress=getattr(bill, 'congress_number', 118),
                    bill_type=getattr(bill, 'bill_type_code', 'hr'),
                    bill_number=bill.bill_number
                )
                
                for amendment in amendments_data:
                    amendments.append({
                        'amendment_number': amendment.get('number'),
                        'purpose': amendment.get('purpose'),
                        'sponsor': amendment.get('sponsor', {}).get('name'),
                        'submitted_date': amendment.get('submittedDate'),
                        'status': amendment.get('latestAction', {}).get('text')
                    })
            
        except Exception as e:
            logger.error(f"Failed to check amendments for bill {bill.id}: {e}")
        
        return amendments
    
    def _check_vote_results(self, bill: Bill) -> List[Dict[str, Any]]:
        """Check for new vote results."""
        votes = []
        
        try:
            # Get vote data from Congress.gov
            if hasattr(bill, 'congress_number') and hasattr(bill, 'bill_type_code'):
                # This would require implementing vote tracking in congress_gov_api
                # For now, return empty list
                pass
            
        except Exception as e:
            logger.error(f"Failed to check votes for bill {bill.id}: {e}")
        
        return votes
    
    def _check_text_versions(self, bill: Bill) -> List[Dict[str, Any]]:
        """Check for new text versions of the bill."""
        versions = []
        
        try:
            # Get text versions from Congress.gov
            if hasattr(bill, 'congress_number') and hasattr(bill, 'bill_type_code'):
                # This would require implementing text version tracking
                # For now, return empty list
                pass
            
        except Exception as e:
            logger.error(f"Failed to check text versions for bill {bill.id}: {e}")
        
        return versions
    
    def _fetch_current_bill_data(self, bill: Bill) -> Optional[Dict[str, Any]]:
        """Fetch current bill data from Congress.gov."""
        try:
            # Extract congress and bill info from bill number
            # This is a simplified version - would need more robust parsing
            congress = getattr(bill, 'congress_number', 118)
            bill_type = getattr(bill, 'bill_type_code', 'hr')
            
            return self.congress_api.get_bill_details(
                congress=congress,
                bill_type=bill_type,
                bill_number=bill.bill_number
            )
            
        except Exception as e:
            logger.error(f"Failed to fetch current bill data for {bill.bill_number}: {e}")
            return None
    
    def _extract_status_from_api_data(self, bill_data: Dict[str, Any]) -> Optional[BillStatus]:
        """Extract bill status from Congress.gov API data."""
        try:
            # Map Congress.gov status to our BillStatus enum
            latest_action = bill_data.get('latestAction', {})
            action_text = latest_action.get('text', '').lower()
            
            # Simple status mapping - would need more comprehensive mapping
            if 'introduced' in action_text:
                return BillStatus.INTRODUCED
            elif 'committee' in action_text or 'referred' in action_text:
                return BillStatus.IN_COMMITTEE
            elif 'passed' in action_text and 'house' in action_text:
                return BillStatus.PASSED_HOUSE
            elif 'passed' in action_text and 'senate' in action_text:
                return BillStatus.PASSED_SENATE
            elif 'signed' in action_text or 'became law' in action_text:
                return BillStatus.SIGNED
            elif 'vetoed' in action_text:
                return BillStatus.VETOED
            
            # Default to current status if we can't determine
            return None
            
        except Exception as e:
            logger.error(f"Failed to extract status from API data: {e}")
            return None
    
    def track_all_active_bills(self, limit: int = 100) -> Dict[str, Any]:
        """
        Track changes for all active bills.
        
        Args:
            limit: Maximum number of bills to check
            
        Returns:
            Summary of tracking results
        """
        results = {
            'total_checked': 0,
            'bills_with_changes': 0,
            'total_status_changes': 0,
            'total_amendments': 0,
            'total_votes': 0,
            'errors': []
        }
        
        try:
            # Get active bills that haven't been checked recently
            cutoff_time = datetime.utcnow() - timedelta(hours=6)  # Check every 6 hours
            
            active_bills = (
                self.db.query(Bill)
                .filter(
                    Bill.status.in_([
                        BillStatus.INTRODUCED,
                        BillStatus.IN_COMMITTEE,
                        BillStatus.PASSED_HOUSE,
                        BillStatus.PASSED_SENATE
                    ])
                )
                .filter(
                    (Bill.last_history_check.is_(None)) |
                    (Bill.last_history_check < cutoff_time)
                )
                .limit(limit)
                .all()
            )
            
            logger.info(f"Tracking changes for {len(active_bills)} active bills")
            
            for bill in active_bills:
                bill_results = self.track_bill_changes(bill)
                results['total_checked'] += 1
                
                if (bill_results['status_changes'] or 
                    bill_results['amendments'] or 
                    bill_results['votes'] or 
                    bill_results['text_versions']):
                    results['bills_with_changes'] += 1
                
                results['total_status_changes'] += len(bill_results['status_changes'])
                results['total_amendments'] += len(bill_results['amendments'])
                results['total_votes'] += len(bill_results['votes'])
                results['errors'].extend(bill_results['errors'])
            
            # Commit all changes
            self.db.commit()
            
            logger.info(f"Completed tracking for {results['total_checked']} bills")
            
        except Exception as e:
            error_msg = f"Failed to track all active bills: {str(e)}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
            self.db.rollback()
        
        return results
