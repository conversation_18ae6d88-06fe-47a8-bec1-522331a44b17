# app/services/bill_summary_version_service.py
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from app.models.bill import Bill, BillSummaryVersion
import logging

logger = logging.getLogger(__name__)


class BillSummaryVersionService:
    """Service for managing bill summary versions and tracking changes."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_summary_version(
        self,
        bill: Bill,
        change_reason: str = "manual_update",
        **summary_fields
    ) -> BillSummaryVersion:
        """
        Create a new summary version for a bill.
        
        Args:
            bill: The bill to create a version for
            change_reason: Reason for the summary change
            **summary_fields: Summary field values to store
            
        Returns:
            The created BillSummaryVersion
        """
        # Get the next version number
        latest_version = self.db.query(BillSummaryVersion)\
            .filter(BillSummaryVersion.bill_id == bill.id)\
            .order_by(desc(BillSummaryVersion.version_number))\
            .first()
        
        next_version = (latest_version.version_number + 1) if latest_version else 1
        
        # Mark all existing versions as not current
        self.db.query(BillSummaryVersion)\
            .filter(BillSummaryVersion.bill_id == bill.id)\
            .update({"is_current": False})
        
        # Detect changes from previous version
        changes_detected = self._detect_changes(latest_version, summary_fields) if latest_version else None
        
        # Create new version
        new_version = BillSummaryVersion(
            bill_id=bill.id,
            version_number=next_version,
            change_reason=change_reason,
            is_current=True,
            ai_processed_at=datetime.utcnow(),
            changes_detected=changes_detected,
            **summary_fields
        )
        
        self.db.add(new_version)
        self.db.flush()  # Get the ID without committing
        
        logger.info(f"Created summary version {next_version} for bill {bill.id} (reason: {change_reason})")
        return new_version
    
    def get_current_version(self, bill_id: str) -> Optional[BillSummaryVersion]:
        """Get the current summary version for a bill."""
        return self.db.query(BillSummaryVersion)\
            .filter(and_(
                BillSummaryVersion.bill_id == bill_id,
                BillSummaryVersion.is_current == True
            ))\
            .first()
    
    def get_version_history(self, bill_id: str) -> List[BillSummaryVersion]:
        """Get all summary versions for a bill, ordered by version number (newest first)."""
        return self.db.query(BillSummaryVersion)\
            .filter(BillSummaryVersion.bill_id == bill_id)\
            .order_by(desc(BillSummaryVersion.version_number))\
            .all()
    
    def get_version_by_number(self, bill_id: str, version_number: int) -> Optional[BillSummaryVersion]:
        """Get a specific version by number."""
        return self.db.query(BillSummaryVersion)\
            .filter(and_(
                BillSummaryVersion.bill_id == bill_id,
                BillSummaryVersion.version_number == version_number
            ))\
            .first()
    
    def update_bill_summary_with_versioning(
        self,
        bill: Bill,
        change_reason: str = "ai_update",
        **summary_updates
    ) -> BillSummaryVersion:
        """
        Update a bill's summary fields and create a new version.
        
        Args:
            bill: The bill to update
            change_reason: Reason for the update
            **summary_updates: Fields to update
            
        Returns:
            The new summary version
        """
        # Update the bill's current summary fields
        for field, value in summary_updates.items():
            if hasattr(bill, field):
                setattr(bill, field, value)
        
        # Create a new version record
        new_version = self.create_summary_version(
            bill=bill,
            change_reason=change_reason,
            **summary_updates
        )
        
        return new_version
    
    def _detect_changes(
        self,
        previous_version: Optional[BillSummaryVersion],
        new_fields: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Detect what changed between versions.
        
        Args:
            previous_version: The previous version to compare against
            new_fields: The new field values
            
        Returns:
            Dictionary describing the changes
        """
        if not previous_version:
            return {"type": "initial_version", "fields_added": list(new_fields.keys())}
        
        changes = {
            "type": "update",
            "fields_changed": [],
            "fields_added": [],
            "fields_removed": []
        }
        
        # Check for changes in each field
        summary_fields = [
            'summary_what_does', 'summary_who_affects', 'summary_why_matters',
            'summary_key_provisions', 'summary_timeline', 'summary_cost_impact',
            'ai_summary', 'simple_summary', 'tldr'
        ]
        
        for field in summary_fields:
            old_value = getattr(previous_version, field, None)
            new_value = new_fields.get(field)
            
            if old_value != new_value:
                if old_value is None and new_value is not None:
                    changes["fields_added"].append(field)
                elif old_value is not None and new_value is None:
                    changes["fields_removed"].append(field)
                elif old_value != new_value:
                    changes["fields_changed"].append(field)
        
        return changes
    
    def get_summary_timeline(self, bill_id: str) -> List[Dict[str, Any]]:
        """
        Get a timeline of summary changes for display in UI.
        
        Returns:
            List of timeline entries with version info and changes
        """
        versions = self.get_version_history(bill_id)
        
        timeline = []
        for version in versions:
            entry = {
                "version": version.version_number,
                "date": version.created_at,
                "reason": version.change_reason,
                "is_current": version.is_current,
                "changes": version.changes_detected or {},
                "has_structured_data": version.has_structured_summary
            }
            timeline.append(entry)
        
        return timeline
