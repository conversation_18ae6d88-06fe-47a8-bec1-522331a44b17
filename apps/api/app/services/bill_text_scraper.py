# app/services/bill_text_scraper.py
"""
Bill text scraping service for extracting full text from legislative sources.

This service handles downloading and parsing bill text from various sources
including Congress.gov and other legislative websites.
"""

import requests
import logging
from typing import Optional
from bs4 import BeautifulSoup
import re

logger = logging.getLogger(__name__)


class BillTextScraper:
    """Service for scraping bill full text from legislative websites"""

    def __init__(self):
        """Initialize the bill text scraper"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'ModernAction.io Bill Text Scraper (<EMAIL>)'
        })
        logger.info("Bill text scraper initialized")

    def fetch_bill_text(self, text_url: str) -> Optional[str]:
        """
        Fetch the full text of a bill from a given URL.

        Args:
            text_url: URL to the bill's full text page

        Returns:
            String containing the bill's full text or None if extraction fails
        """
        if not text_url:
            logger.warning("No text URL provided")
            return None

        try:
            logger.info(f"Fetching bill text from: {text_url}")

            # Determine the source and use appropriate extraction method
            if 'congress.gov' in text_url:
                return self._extract_congress_gov_text(text_url)
            else:
                # Generic text extraction for other sources
                return self._extract_generic_text(text_url)

        except Exception as e:
            logger.error(f"Failed to fetch bill text from {text_url}: {e}")
            return None

    def _extract_congress_gov_text(self, url: str) -> Optional[str]:
        """
        Extract bill text from Congress.gov pages.

        Args:
            url: Congress.gov URL

        Returns:
            Extracted bill text or None if extraction fails
        """
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Congress.gov typically has bill text in specific containers
            # Try multiple selectors to find the bill text
            text_selectors = [
                '.bill-text-container',
                '.generated-html-container',
                '#billTextContainer',
                '.bill-text',
                'pre.bill-text',
                '.legis-body'
            ]

            bill_text = None
            for selector in text_selectors:
                text_element = soup.select_one(selector)
                if text_element:
                    bill_text = text_element.get_text(strip=True)
                    break

            # If no specific container found, try to find the main content
            if not bill_text:
                # Look for the main content area
                main_content = soup.select_one('main') or soup.select_one('#main-content')
                if main_content:
                    # Remove navigation, headers, footers
                    for unwanted in main_content.select('nav, header, footer, .breadcrumb, .sidebar'):
                        unwanted.decompose()

                    bill_text = main_content.get_text(strip=True)

            if bill_text:
                # Clean up the text
                bill_text = self._clean_bill_text(bill_text)
                logger.info(f"Successfully extracted {len(bill_text)} characters of bill text")
                return bill_text
            else:
                logger.warning("Could not find bill text in Congress.gov page")
                return None

        except Exception as e:
            logger.error(f"Failed to extract text from Congress.gov: {e}")
            return None

    def _extract_generic_text(self, url: str) -> Optional[str]:
        """
        Generic text extraction for non-Congress.gov sources.

        Args:
            url: URL to extract text from

        Returns:
            Extracted text or None if extraction fails
        """
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Get text from the body
            body = soup.find('body')
            if body:
                text = body.get_text()
                text = self._clean_bill_text(text)
                logger.info(f"Successfully extracted {len(text)} characters of text")
                return text
            else:
                logger.warning("Could not find body element in page")
                return None

        except Exception as e:
            logger.error(f"Failed to extract generic text: {e}")
            return None

    def _clean_bill_text(self, text: str) -> str:
        """
        Clean and normalize bill text.

        Args:
            text: Raw text to clean

        Returns:
            Cleaned text
        """
        if not text:
            return ""

        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove excessive newlines
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)

        # Strip leading/trailing whitespace
        text = text.strip()

        return text

    def get_bill_summary_from_text(self, bill_text: str, max_length: int = 500) -> str:
        """
        Extract a summary from the bill text by finding key sections.

        Args:
            bill_text: Full text of the bill
            max_length: Maximum length of the summary

        Returns:
            Summary text
        """
        if not bill_text:
            return ""

        # Look for common bill summary patterns
        summary_patterns = [
            r'(?i)(?:short title|brief summary|summary)[\s\S]*?(?=\n\n|\n[A-Z])',
            r'(?i)(?:section 1|sec\. 1)[\s\S]*?(?=\n\n|\nsection|\nsec\.)',
            r'(?i)(?:be it enacted)[\s\S]*?(?=\n\n|\nsection|\nsec\.)'
        ]

        for pattern in summary_patterns:
            match = re.search(pattern, bill_text)
            if match:
                summary = match.group(0).strip()
                if len(summary) <= max_length:
                    return summary
                else:
                    # Truncate to max_length at word boundary
                    words = summary.split()
                    truncated = ""
                    for word in words:
                        if len(truncated + " " + word) <= max_length - 3:
                            truncated += " " + word if truncated else word
                        else:
                            break
                    return truncated + "..."

        # If no specific summary found, return first paragraph
        paragraphs = bill_text.split('\n\n')
        if paragraphs:
            first_para = paragraphs[0].strip()
            if len(first_para) <= max_length:
                return first_para
            else:
                # Truncate first paragraph
                words = first_para.split()
                truncated = ""
                for word in words:
                    if len(truncated + " " + word) <= max_length - 3:
                        truncated += " " + word if truncated else word
                    else:
                        break
                return truncated + "..."

        return ""

    def validate_url(self, url: str) -> bool:
        """
        Validate if a URL is accessible and contains text content.

        Args:
            url: URL to validate

        Returns:
            True if URL is valid and accessible, False otherwise
        """
        try:
            response = self.session.head(url, timeout=10)
            return response.status_code == 200
        except Exception:
            return False


# Convenience function for easy import
def get_bill_text_scraper() -> BillTextScraper:
    """Get a bill text scraper instance"""
    return BillTextScraper()
