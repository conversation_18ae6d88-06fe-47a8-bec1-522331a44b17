# app/services/campaigns.py
import json
from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, func
from app.models.campaign import Campaign, CampaignStatus, CampaignType
from app.schemas.campaign import CampaignCreate, CampaignUpdate


class CampaignService:
    """Service layer for campaign operations"""

    def __init__(self, db: Session):
        self.db = db

    def _deserialize_json_fields(self, campaign: Campaign) -> None:
        """Convert JSON string fields back to Python objects for API responses"""
        if campaign.talking_points and isinstance(campaign.talking_points, str):
            try:
                campaign.talking_points = json.loads(campaign.talking_points)
            except (json.J<PERSON>NDecodeError, TypeError):
                campaign.talking_points = []

        if campaign.geographic_scope and isinstance(campaign.geographic_scope, str):
            try:
                campaign.geographic_scope = json.loads(campaign.geographic_scope)
            except (json.JSONDecodeError, TypeError):
                campaign.geographic_scope = []

        if campaign.hashtags and isinstance(campaign.hashtags, str):
            try:
                campaign.hashtags = json.loads(campaign.hashtags)
            except (json.JSONDecodeError, TypeError):
                campaign.hashtags = []

    def _deserialize_json_fields_list(self, campaigns: List[Campaign]) -> None:
        """Convert JSON string fields back to Python objects for a list of campaigns"""
        for campaign in campaigns:
            self._deserialize_json_fields(campaign)

    def get_campaign(self, campaign_id: str) -> Optional[Campaign]:
        """Get a single campaign by ID with bill relationship loaded"""
        campaign = (
            self.db.query(Campaign)
            .options(joinedload(Campaign.bill))
            .filter(Campaign.id == campaign_id)
            .first()
        )
        if campaign:
            self._deserialize_json_fields(campaign)
        return campaign

    def get_campaigns(self, skip: int = 0, limit: int = 20) -> List[Campaign]:
        """Get a list of campaigns with pagination and bill relationships loaded"""
        campaigns = (
            self.db.query(Campaign)
            .options(joinedload(Campaign.bill))
            .order_by(desc(Campaign.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(campaigns)
        return campaigns

    def create_campaign(self, campaign_data: CampaignCreate) -> Campaign:
        """Create a new campaign"""
        # Convert lists to JSON strings for database storage
        campaign_dict = campaign_data.model_dump()

        # Handle JSON fields
        if campaign_dict.get('talking_points'):
            campaign_dict['talking_points'] = json.dumps(campaign_dict['talking_points'])
        if campaign_dict.get('geographic_scope'):
            campaign_dict['geographic_scope'] = json.dumps(campaign_dict['geographic_scope'])
        if campaign_dict.get('hashtags'):
            campaign_dict['hashtags'] = json.dumps(campaign_dict['hashtags'])

        campaign = Campaign(**campaign_dict)
        self.db.add(campaign)
        self.db.commit()
        self.db.refresh(campaign)

        # Load the bill relationship and convert JSON strings back to lists for API response
        campaign = (
            self.db.query(Campaign)
            .options(joinedload(Campaign.bill))
            .filter(Campaign.id == campaign.id)
            .first()
        )
        self._deserialize_json_fields(campaign)
        return campaign

    def update_campaign(self, campaign_id: str, campaign_data: CampaignUpdate) -> Optional[Campaign]:
        """Update an existing campaign"""
        campaign = self.db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not campaign:
            return None

        # Get only the fields that were provided (not None)
        update_data = campaign_data.model_dump(exclude_unset=True)

        # Handle JSON fields
        if 'talking_points' in update_data and update_data['talking_points'] is not None:
            update_data['talking_points'] = json.dumps(update_data['talking_points'])
        if 'geographic_scope' in update_data and update_data['geographic_scope'] is not None:
            update_data['geographic_scope'] = json.dumps(update_data['geographic_scope'])
        if 'hashtags' in update_data and update_data['hashtags'] is not None:
            update_data['hashtags'] = json.dumps(update_data['hashtags'])

        # Update the campaign with provided data
        for field, value in update_data.items():
            setattr(campaign, field, value)

        self.db.commit()
        self.db.refresh(campaign)

        # Load the bill relationship and convert JSON strings back to lists for API response
        campaign = (
            self.db.query(Campaign)
            .options(joinedload(Campaign.bill))
            .filter(Campaign.id == campaign.id)
            .first()
        )
        self._deserialize_json_fields(campaign)
        return campaign

    def delete_campaign(self, campaign_id: str) -> bool:
        """Delete a campaign"""
        campaign = self.get_campaign(campaign_id)
        if not campaign:
            return False

        self.db.delete(campaign)
        self.db.commit()
        return True

    def search_campaigns(
        self,
        query: Optional[str] = None,
        campaign_type: Optional[CampaignType] = None,
        status: Optional[CampaignStatus] = None,
        bill_id: Optional[str] = None,
        is_featured: Optional[bool] = None,
        is_public: Optional[bool] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[Campaign]:
        """Search campaigns based on various criteria"""
        query_builder = self.db.query(Campaign).options(joinedload(Campaign.bill))

        # Text search in title and description
        if query:
            search_term = f"%{query.lower()}%"
            query_builder = query_builder.filter(
                func.lower(Campaign.title).contains(search_term) |
                func.lower(Campaign.description).contains(search_term)
            )

        # Filter by campaign type
        if campaign_type:
            query_builder = query_builder.filter(Campaign.campaign_type == campaign_type)

        # Filter by status
        if status:
            query_builder = query_builder.filter(Campaign.status == status)

        # Filter by bill ID
        if bill_id:
            query_builder = query_builder.filter(Campaign.bill_id == bill_id)

        # Filter by featured status
        if is_featured is not None:
            query_builder = query_builder.filter(Campaign.is_featured == is_featured)

        # Filter by public status
        if is_public is not None:
            query_builder = query_builder.filter(Campaign.is_public == is_public)

        campaigns = (
            query_builder
            .order_by(desc(Campaign.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(campaigns)
        return campaigns

    def get_campaigns_by_status(self, status: CampaignStatus, skip: int = 0, limit: int = 20) -> List[Campaign]:
        """Get campaigns by status"""
        campaigns = (
            self.db.query(Campaign)
            .options(joinedload(Campaign.bill))
            .filter(Campaign.status == status)
            .order_by(desc(Campaign.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(campaigns)
        return campaigns

    def get_campaigns_by_bill(self, bill_id: str, skip: int = 0, limit: int = 20) -> List[Campaign]:
        """Get campaigns for a specific bill"""
        campaigns = (
            self.db.query(Campaign)
            .options(joinedload(Campaign.bill))
            .filter(Campaign.bill_id == bill_id)
            .order_by(desc(Campaign.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(campaigns)
        return campaigns

    def get_featured_campaigns(self, skip: int = 0, limit: int = 20) -> List[Campaign]:
        """Get featured campaigns"""
        campaigns = (
            self.db.query(Campaign)
            .options(joinedload(Campaign.bill))
            .filter(Campaign.is_featured == True)  # noqa: E712
            .order_by(desc(Campaign.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(campaigns)
        return campaigns

    def get_campaigns_count(self) -> int:
        """Get total count of campaigns"""
        return self.db.query(Campaign).count()

    def recalculate_action_counts(self) -> int:
        """
        Recalculate action counts for all campaigns.

        This method fixes data inconsistency by updating the actual_actions
        field for each campaign based on the real count of actions.

        Returns:
            int: Number of campaigns that were updated
        """
        from app.models.action import Action
        from sqlalchemy import func

        campaigns = self.db.query(Campaign).all()
        updated_count = 0

        for campaign in campaigns:
            # Count actual actions for this campaign
            actual_count = self.db.query(func.count(Action.id)).filter(
                Action.campaign_id == campaign.id
            ).scalar()

            # Update if different
            if campaign.actual_actions != actual_count:
                campaign.actual_actions = actual_count
                updated_count += 1

        self.db.commit()
        return updated_count

    def recalculate_single_campaign_actions(self, campaign_id: str) -> bool:
        """
        Recalculate action count for a single campaign.

        Args:
            campaign_id: ID of the campaign to update

        Returns:
            bool: True if campaign was found and updated, False otherwise
        """
        from app.models.action import Action
        from sqlalchemy import func

        campaign = self.get_campaign(campaign_id)
        if not campaign:
            return False

        # Count actual actions for this campaign
        actual_count = self.db.query(func.count(Action.id)).filter(
            Action.campaign_id == campaign.id
        ).scalar()

        # Update the count
        campaign.actual_actions = actual_count
        self.db.commit()
        return True

    def get_active_campaigns(self, skip: int = 0, limit: int = 20) -> List[Campaign]:
        """Get active campaigns"""
        campaigns = (
            self.db.query(Campaign)
            .options(joinedload(Campaign.bill))
            .filter(Campaign.status == CampaignStatus.ACTIVE)
            .order_by(desc(Campaign.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(campaigns)
        return campaigns
