# app/services/congress_api.py
"""
Enhanced Congress.gov API service for AI bill processing.

This service extends the existing congress_gov_api.py with async support
and enhanced bill data fetching for AI processing workflows.
"""

import aiohttp
import asyncio
from bs4 import BeautifulSoup
import os
import logging
from typing import Dict, Any, Optional
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class CongressAPIService:
    """Enhanced Congress.gov API service with async support for AI processing"""
    
    def __init__(self):
        self.api_key = settings.CONGRESS_GOV_API_KEY
        self.base_url = "https://api.congress.gov/v3"
        
        if not self.api_key:
            logger.warning("CONGRESS_GOV_API_KEY not configured. Congress.gov API features will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info("Enhanced Congress.gov API service initialized successfully")
        
    async def fetch_bill_data(self, bill_number: str, session: str) -> Dict[str, Any]:
        """
        Fetch complete bill data from Congress.gov API
        
        Args:
            bill_number: e.g., "HR5", "S1" 
            session: e.g., "118"
        
        Returns:
            Complete bill data including metadata and full text
        """
        if not self.enabled:
            raise Exception("Congress.gov API is not enabled. Please configure CONGRESS_GOV_API_KEY.")
            
        try:
            logger.info(f"Fetching bill data for {bill_number} from session {session}")
            
            # Step 1: Get bill metadata
            metadata = await self._fetch_bill_metadata(bill_number, session)
            
            # Step 2: Get full text
            full_text = await self._fetch_bill_text(metadata)
            
            return {
                'congress_id': metadata['congress'],
                'number': metadata['number'],
                'title': metadata['title'],
                'short_title': metadata.get('titles', [{}])[0].get('title', ''),
                'summary': metadata.get('summaries', [{}])[0].get('text', ''),
                'introduced_date': metadata.get('introducedDate'),
                'sponsors': metadata.get('sponsors', []),
                'full_text': full_text,
                'congress_url': metadata.get('url'),
                'status': metadata.get('latestAction', {}).get('text', '')
            }
            
        except Exception as e:
            logger.error(f"Failed to fetch bill {bill_number}: {e}")
            raise
    
    async def _fetch_bill_metadata(self, bill_number: str, session: str) -> Dict[str, Any]:
        """Fetch bill metadata from Congress API"""
        bill_type = bill_number[:2].lower()  # "hr", "s", etc.
        bill_num = bill_number[2:]  # "5"
        
        url = f"{self.base_url}/bill/{session}/{bill_type}/{bill_num}"
        headers = {'X-API-Key': self.api_key}
        
        async with aiohttp.ClientSession() as session_client:
            async with session_client.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return data['bill']
                else:
                    error_text = await response.text()
                    raise Exception(f"Congress API error {response.status}: {error_text}")
    
    async def _fetch_bill_text(self, metadata: Dict[str, Any]) -> str:
        """Fetch the full text of the bill"""
        try:
            # Get text versions from metadata
            text_versions = metadata.get('textVersions', {}).get('url')
            if not text_versions:
                return "Full text not available"
            
            # Fetch the text versions list
            async with aiohttp.ClientSession() as session:
                async with session.get(text_versions) as response:
                    versions_data = await response.json()
                    
                    # Get the latest version
                    if not versions_data.get('textVersions'):
                        return metadata.get('summary', 'Text not available')
                        
                    latest_version = versions_data['textVersions'][0]
                    formats = latest_version.get('formats', [])
                    
                    if not formats:
                        return metadata.get('summary', 'Text not available')
                        
                    text_url = formats[0]['url']  # Usually XML or HTML
                    
                    # Fetch the actual text
                    async with session.get(text_url) as text_response:
                        raw_text = await text_response.text()
                        
                        # Clean the text (remove XML tags, etc.)
                        return self._clean_bill_text(raw_text)
                        
        except Exception as e:
            logger.warning(f"Could not fetch bill text: {e}")
            return metadata.get('summary', 'Text not available')
    
    def _clean_bill_text(self, raw_text: str) -> str:
        """Clean and extract readable text from bill HTML/XML"""
        try:
            soup = BeautifulSoup(raw_text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception:
            # Fallback: basic text cleaning
            import re
            text = re.sub(r'<[^>]+>', '', raw_text)
            text = re.sub(r'\s+', ' ', raw_text)
            return text.strip()


# Convenience function for easy import
def get_congress_api_service() -> CongressAPIService:
    """Get a Congress API service instance"""
    return CongressAPIService()
