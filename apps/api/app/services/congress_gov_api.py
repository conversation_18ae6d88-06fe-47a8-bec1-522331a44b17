# app/services/congress_gov_api.py
"""
Congress.gov API service for fetching real federal legislative data.

This service provides access to the official Congress.gov API to fetch
current bills, their status, full text, and other legislative information.
"""

import requests
import aiohttp
import asyncio
import re
import logging
from typing import Dict, Any, Optional, List
from bs4 import BeautifulSoup
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class CongressGovAPI:
    """Service for interacting with the Congress.gov API"""

    def __init__(self):
        """Initialize the Congress.gov API client"""
        self.base_url = "https://api.congress.gov/v3"
        self.api_key = settings.CONGRESS_GOV_API_KEY

        if not self.api_key:
            logger.warning("CONGRESS_GOV_API_KEY not configured. Congress.gov API features will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info("Congress.gov API client initialized successfully")

    def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Make a request to the Congress.gov API.

        Args:
            endpoint: API endpoint path
            params: Query parameters

        Returns:
            Dict containing API response data or None if request fails
        """
        if not self.enabled:
            logger.error("Congress.gov API is not enabled. Please configure CONGRESS_GOV_API_KEY.")
            return None

        url = f"{self.base_url}/{endpoint}"

        # Add API key to parameters
        if params is None:
            params = {}
        params['api_key'] = self.api_key
        params['format'] = 'json'

        try:
            logger.debug(f"Making request to Congress.gov API: {endpoint}")
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            logger.debug("Successfully fetched data from Congress.gov API")
            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch data from Congress.gov API: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching data from Congress.gov API: {e}")
            return None

    def get_bill_by_number(self, congress: int, bill_type: str, bill_number: int) -> Optional[Dict[str, Any]]:
        """
        Get a specific bill by its number.

        Args:
            congress: Congress number (e.g., 118 for current Congress)
            bill_type: Bill type ('hr', 's', 'hjres', 'sjres', 'hconres', 'sconres', 'hres', 'sres')
            bill_number: Bill number (e.g., 5 for H.R.5)

        Returns:
            Dict containing bill data or None if not found
        """
        endpoint = f"bill/{congress}/{bill_type}/{bill_number}"
        response = self._make_request(endpoint)

        if response and response.get('bill'):
            return response['bill']

        return None

    def get_recent_bills(self, congress: int = 118, bill_type: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent bills from Congress.

        Args:
            congress: Congress number (default: 118 for current Congress)
            bill_type: Bill type to filter by (optional)
            limit: Maximum number of bills to return

        Returns:
            List of bill dictionaries
        """
        endpoint = f"bill/{congress}"
        params = {
            'limit': min(limit, 250)  # API max is 250
        }

        if bill_type:
            params['billType'] = bill_type

        response = self._make_request(endpoint, params)

        if response and response.get('bills'):
            return response['bills']

        return []

    def get_bill_text(self, congress: int, bill_type: str, bill_number: int) -> Optional[Dict[str, Any]]:
        """
        Get bill text information.

        Args:
            congress: Congress number
            bill_type: Bill type
            bill_number: Bill number

        Returns:
            Dict containing bill text data or None if not found
        """
        endpoint = f"bill/{congress}/{bill_type}/{bill_number}/text"
        response = self._make_request(endpoint)

        if response and response.get('textVersions'):
            return response['textVersions']

        return None

    async def get_bill_full_text(self, congress: int, bill_type: str, bill_number: int) -> Optional[str]:
        """
        Get the full text content of a bill.

        Args:
            congress: Congress number
            bill_type: Bill type
            bill_number: Bill number

        Returns:
            Full text content of the bill or None if not available
        """
        try:
            # First get the text versions
            text_versions = self.get_bill_text(congress, bill_type, bill_number)

            if not text_versions or len(text_versions) == 0:
                logger.warning(f"No text versions found for {bill_type.upper()}.{bill_number}")
                return None

            # Get the latest version (first in the list)
            latest_version = text_versions[0]
            formats = latest_version.get('formats', [])

            if not formats:
                logger.warning(f"No formats available for {bill_type.upper()}.{bill_number}")
                return None

            # Prefer HTML format for easier text extraction
            html_format = None
            xml_format = None

            for fmt in formats:
                fmt_type = fmt.get('type', '').lower()
                if 'html' in fmt_type or 'formatted text' in fmt_type:
                    html_format = fmt
                    break
                elif 'xml' in fmt_type:
                    xml_format = fmt

            # Use HTML format if available, otherwise XML
            selected_format = html_format or xml_format or formats[0]
            text_url = selected_format.get('url')

            if not text_url:
                logger.warning(f"No URL found in format for {bill_type.upper()}.{bill_number}")
                return None

            logger.info(f"Fetching full text from: {text_url}")

            # Fetch the actual text content
            async with aiohttp.ClientSession() as session:
                async with session.get(text_url) as response:
                    if response.status != 200:
                        logger.error(f"HTTP {response.status} when fetching bill text")
                        return None

                    content = await response.text()

                    # Clean and extract the text
                    cleaned_text = self._clean_bill_text(content)

                    logger.info(f"Successfully fetched {len(cleaned_text)} characters of bill text")
                    return cleaned_text

        except Exception as e:
            logger.error(f"Failed to fetch full text for {bill_type.upper()}.{bill_number}: {e}")
            return None

    def _clean_bill_text(self, raw_content: str) -> str:
        """
        Clean and extract readable text from HTML/XML bill content.

        Args:
            raw_content: Raw HTML/XML content from Congress.gov

        Returns:
            Cleaned, readable bill text
        """
        try:
            # Parse HTML content
            soup = BeautifulSoup(raw_content, 'html.parser')

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Get text content
            text = soup.get_text()

            # Clean up whitespace and formatting
            lines = []
            for line in text.split('\n'):
                line = line.strip()
                if line:  # Skip empty lines
                    lines.append(line)

            # Join lines with single newlines
            cleaned_text = '\n'.join(lines)

            # Remove excessive whitespace
            cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
            cleaned_text = re.sub(r' {2,}', ' ', cleaned_text)

            return cleaned_text.strip()

        except Exception as e:
            logger.error(f"Failed to clean bill text: {e}")
            # Return raw content as fallback
            return raw_content

    def get_bill_actions(self, congress: int, bill_type: str, bill_number: int) -> Optional[List[Dict[str, Any]]]:
        """
        Get bill actions/history.

        Args:
            congress: Congress number
            bill_type: Bill type
            bill_number: Bill number

        Returns:
            List of action dictionaries or None if not found
        """
        endpoint = f"bill/{congress}/{bill_type}/{bill_number}/actions"
        response = self._make_request(endpoint)

        if response and response.get('actions'):
            return response['actions']

        return None

    def parse_bill_number(self, bill_number: str) -> Dict[str, Any]:
        """
        Parse a bill number into its components.

        Args:
            bill_number: Bill number like "H.R.5", "S.1234", etc.

        Returns:
            Dict with 'bill_type', 'number', and 'congress' keys
        """
        bill_number = bill_number.upper().replace(".", "").replace(" ", "")

        # Extract number
        import re
        match = re.search(r'(\d+)$', bill_number)
        if not match:
            raise ValueError(f"Could not extract number from bill: {bill_number}")

        number = int(match.group(1))
        prefix = bill_number[:match.start()]

        # Map prefixes to API bill types
        type_mapping = {
            'HR': 'hr',
            'S': 's',
            'HJRES': 'hjres',
            'SJRES': 'sjres',
            'HCONRES': 'hconres',
            'SCONRES': 'sconres',
            'HRES': 'hres',
            'SRES': 'sres'
        }

        bill_type = type_mapping.get(prefix)
        if not bill_type:
            raise ValueError(f"Unknown bill type prefix: {prefix}")

        return {
            'bill_type': bill_type,
            'number': number,
            'congress': 118  # Default to current Congress
        }

    def search_bills(self, query: str, congress: int = 118, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Search for bills by query.

        Args:
            query: Search query
            congress: Congress number
            limit: Maximum number of results

        Returns:
            List of bill dictionaries
        """
        endpoint = f"bill/{congress}"
        params = {
            'q': query,
            'limit': min(limit, 250)
        }

        response = self._make_request(endpoint, params)

        if response and response.get('bills'):
            return response['bills']

        return []

    def get_bill_summary(self, congress: int, bill_type: str, bill_number: int) -> Optional[str]:
        """
        Get bill summary from Congress.gov.

        Args:
            congress: Congress number
            bill_type: Bill type
            bill_number: Bill number

        Returns:
            Bill summary text or None if not available
        """
        endpoint = f"bill/{congress}/{bill_type}/{bill_number}/summaries"
        response = self._make_request(endpoint)

        if response and response.get('summaries'):
            summaries = response['summaries']
            if summaries:
                # Get the most recent summary
                latest_summary = summaries[0]
                return latest_summary.get('text', '')

        return None

    def health_check(self) -> Dict[str, Any]:
        """
        Check the health of the Congress.gov API connection.

        Returns:
            Dict containing health status information
        """
        if not self.enabled:
            return {
                "status": "disabled",
                "message": "Congress.gov API key not configured",
                "enabled": False
            }

        try:
            # Test with a simple request for recent bills
            response = self._make_request("bill/118", {"limit": 1})

            if response and response.get('bills'):
                return {
                    "status": "healthy",
                    "message": "Congress.gov API is accessible",
                    "enabled": True,
                    "test_response_count": len(response.get('bills', []))
                }
            else:
                return {
                    "status": "error",
                    "message": "Congress.gov API returned unexpected response",
                    "enabled": True,
                    "response": response
                }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Congress.gov API health check failed: {str(e)}",
                "enabled": True,
                "error": str(e)
            }


# Convenience function for easy import
def get_congress_gov_client() -> CongressGovAPI:
    """Get a Congress.gov API client instance"""
    return CongressGovAPI()
