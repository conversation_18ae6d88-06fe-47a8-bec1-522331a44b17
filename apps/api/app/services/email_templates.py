# app/services/email_templates.py
"""
Professional email templates for constituent messages to elected officials.

This module provides HTML and plain text email templates that comply with
political communication standards and best practices.
"""

from typing import Dict, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class EmailTemplateService:
    """Service for generating professional email templates for political communications."""

    @staticmethod
    def generate_official_email_html(
        official_name: str,
        official_title: str,
        user_name: str,
        user_email: str,
        user_address: Optional[str],
        user_city: Optional[str],
        user_state: Optional[str],
        user_zip_code: Optional[str],
        subject: str,
        message: str,
        bill_number: Optional[str] = None,
        position: Optional[str] = None
    ) -> str:
        """
        Generate professional HTML email template for constituent messages.
        
        Args:
            official_name: Official's full name
            official_title: Official's title (e.g., "Senator", "Representative")
            user_name: Constituent's full name
            user_email: Constituent's email address
            user_address: Constituent's street address
            user_city: Constituent's city
            user_state: Constituent's state
            user_zip_code: Constituent's ZIP code
            subject: Email subject line
            message: Main message content
            bill_number: Bill number being addressed (optional)
            position: User's position on the bill (optional)
            
        Returns:
            HTML email template as string
        """
        
        # Format position text
        position_text = ""
        if bill_number and position:
            position_map = {
                'support': 'support',
                'oppose': 'oppose',
                'amend': 'request amendments to'
            }
            position_text = f"<p style='margin: 16px 0; color: #374151; font-size: 16px; line-height: 1.5;'><strong>Position:</strong> I {position_map.get(position.lower(), position)} {bill_number}.</p>"
        elif bill_number:
            position_text = f"<p style='margin: 16px 0; color: #374151; font-size: 16px; line-height: 1.5;'>I am writing regarding <strong>{bill_number}</strong>.</p>"

        # Format address
        address_lines = []
        if user_address:
            address_lines.append(user_address)
        
        if user_city and user_state:
            address_lines.append(f"{user_city}, {user_state}")
        elif user_city:
            address_lines.append(user_city)
        elif user_state:
            address_lines.append(user_state)
            
        if user_zip_code:
            if address_lines:
                address_lines[-1] += f" {user_zip_code}"
            else:
                address_lines.append(f"ZIP Code: {user_zip_code}")

        address_html = ""
        if address_lines:
            address_html = "".join([f"<div style='color: #6B7280; font-size: 14px;'>{line}</div>" for line in address_lines])

        html_template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{subject}</title>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
        .header {{ background-color: #1F2937; color: white; padding: 24px; text-align: center; }}
        .content {{ padding: 32px 24px; }}
        .footer {{ background-color: #F9FAFB; padding: 24px; border-top: 1px solid #E5E7EB; }}
        .signature {{ margin-top: 24px; padding-top: 16px; border-top: 1px solid #E5E7EB; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="margin: 0; font-size: 20px; font-weight: 600;">Constituent Message</h1>
            <p style="margin: 8px 0 0 0; color: #D1D5DB; font-size: 14px;">Via ModernAction.io</p>
        </div>
        
        <div class="content">
            <p style="margin: 0 0 16px 0; color: #374151; font-size: 16px; line-height: 1.5;">
                Dear {official_title} {official_name},
            </p>
            
            {position_text}
            
            <div style="margin: 24px 0; color: #374151; font-size: 16px; line-height: 1.6; white-space: pre-line;">
{message}
            </div>
            
            <p style="margin: 24px 0 16px 0; color: #374151; font-size: 16px; line-height: 1.5;">
                As your constituent, I hope you will consider my views on this important matter. Thank you for your service and for taking the time to read my message.
            </p>
            
            <div class="signature">
                <p style="margin: 0 0 8px 0; color: #374151; font-size: 16px; font-weight: 600;">
                    Respectfully,<br>
                    {user_name}
                </p>
                
                {address_html}
                
                <div style="margin-top: 8px; color: #6B7280; font-size: 14px;">
                    Email: {user_email}
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p style="margin: 0 0 8px 0; color: #6B7280; font-size: 12px; text-align: center;">
                This message was sent via <strong>ModernAction.io</strong>, a nonpartisan platform for civic engagement.
            </p>
            <p style="margin: 0; color: #9CA3AF; font-size: 11px; text-align: center;">
                For questions about this service, <NAME_EMAIL>
            </p>
            <p style="margin: 8px 0 0 0; color: #9CA3AF; font-size: 11px; text-align: center;">
                Sent on {datetime.now().strftime('%B %d, %Y at %I:%M %p')} EST
            </p>
        </div>
    </div>
</body>
</html>
"""
        return html_template.strip()

    @staticmethod
    def generate_official_email_text(
        official_name: str,
        official_title: str,
        user_name: str,
        user_email: str,
        user_address: Optional[str],
        user_city: Optional[str],
        user_state: Optional[str],
        user_zip_code: Optional[str],
        subject: str,
        message: str,
        bill_number: Optional[str] = None,
        position: Optional[str] = None
    ) -> str:
        """
        Generate plain text email template for constituent messages.
        
        This serves as a fallback for email clients that don't support HTML.
        """
        
        # Format position text
        position_text = ""
        if bill_number and position:
            position_map = {
                'support': 'support',
                'oppose': 'oppose',
                'amend': 'request amendments to'
            }
            position_text = f"\nI am writing to express that I {position_map.get(position.lower(), position)} {bill_number}.\n"
        elif bill_number:
            position_text = f"\nI am writing regarding {bill_number}.\n"

        # Format address
        address_lines = []
        if user_address:
            address_lines.append(user_address)
        
        if user_city and user_state:
            address_lines.append(f"{user_city}, {user_state}")
        elif user_city:
            address_lines.append(user_city)
        elif user_state:
            address_lines.append(user_state)
            
        if user_zip_code:
            if address_lines:
                address_lines[-1] += f" {user_zip_code}"
            else:
                address_lines.append(f"ZIP Code: {user_zip_code}")

        address_text = "\n".join(address_lines) if address_lines else ""

        text_template = f"""Dear {official_title} {official_name},
{position_text}
{message}

As your constituent, I hope you will consider my views on this important matter. Thank you for your service and for taking the time to read my message.

Respectfully,
{user_name}
{address_text}
Email: {user_email}

---
This message was sent via ModernAction.io, a nonpartisan platform for civic engagement.
For questions about this service, <NAME_EMAIL>
Sent on {datetime.now().strftime('%B %d, %Y at %I:%M %p')} EST
"""
        return text_template.strip()

    @staticmethod
    def generate_confirmation_email_html(
        user_name: str,
        officials_contacted: list,
        bill_title: str,
        bill_number: str,
        position: str,
        message_preview: str
    ) -> str:
        """Generate confirmation email for users after submitting an action."""
        
        officials_list = ""
        for official in officials_contacted:
            officials_list += f"<li style='margin: 4px 0; color: #374151;'>{official}</li>"

        position_text = {
            'support': 'supporting',
            'oppose': 'opposing',
            'amend': 'requesting amendments to'
        }.get(position.lower(), position)

        html_template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Action Confirmation - ModernAction</title>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #F9FAFB;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <div style="background-color: #059669; color: white; padding: 24px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px; font-weight: 600;">Action Submitted Successfully!</h1>
        </div>
        
        <div style="padding: 32px 24px;">
            <p style="margin: 0 0 16px 0; color: #374151; font-size: 16px;">
                Dear {user_name},
            </p>
            
            <p style="margin: 0 0 24px 0; color: #374151; font-size: 16px; line-height: 1.5;">
                Thank you for taking action! Your message {position_text} <strong>{bill_number}</strong> has been sent to your representatives.
            </p>
            
            <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin: 24px 0;">
                <h3 style="margin: 0 0 12px 0; color: #1F2937; font-size: 18px;">Officials Contacted:</h3>
                <ul style="margin: 0; padding-left: 20px;">
                    {officials_list}
                </ul>
            </div>
            
            <div style="background-color: #EFF6FF; padding: 20px; border-radius: 8px; margin: 24px 0; border-left: 4px solid #3B82F6;">
                <h4 style="margin: 0 0 8px 0; color: #1E40AF; font-size: 16px;">Your Message Preview:</h4>
                <p style="margin: 0; color: #1F2937; font-size: 14px; font-style: italic;">
                    "{message_preview}..."
                </p>
            </div>
            
            <p style="margin: 24px 0 0 0; color: #374151; font-size: 16px; line-height: 1.5;">
                Your voice matters in our democracy. Thank you for staying engaged with the legislative process.
            </p>
        </div>
        
        <div style="background-color: #F9FAFB; padding: 24px; border-top: 1px solid #E5E7EB; text-align: center;">
            <p style="margin: 0; color: #6B7280; font-size: 14px;">
                <strong>ModernAction.io</strong> - Making civic engagement accessible to everyone
            </p>
        </div>
    </div>
</body>
</html>
"""
        return html_template.strip()
