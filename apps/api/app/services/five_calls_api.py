"""
5 Calls API Service

This service provides access to the 5 Calls Representatives API,
which serves as a replacement for the Google Civic Information API
that was turned down on April 30, 2025.

5 Calls API Documentation: https://5calls.org/representatives-api/
"""

import requests
import logging
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class Representative:
    """Represents a single elected official"""
    name: str
    title: str
    party: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    address: Optional[str] = None
    state: Optional[str] = None
    district: Optional[str] = None
    level: Optional[str] = None  # federal, state, local


class FiveCallsAPIClient:
    """Client for the 5 Calls Representatives API"""

    def __init__(self):
        self.base_url = "https://5calls.org/api"
        self.enabled = True  # 5 Calls API is free and doesn't require API key

    def health_check(self) -> Dict[str, Any]:
        """Check if the 5 Calls API is accessible"""
        try:
            # Test with a simple request
            response = requests.get(f"{self.base_url}/reps", timeout=10)
            if response.status_code == 200:
                return {
                    "status": "healthy",
                    "enabled": True,
                    "message": "5 Calls API is accessible"
                }
            else:
                return {
                    "status": "error",
                    "enabled": False,
                    "message": f"5 Calls API returned status {response.status_code}"
                }
        except Exception as e:
            logger.error(f"5 Calls API health check failed: {e}")
            return {
                "status": "error",
                "enabled": False,
                "message": f"5 Calls API health check failed: {str(e)}"
            }

    def get_representatives_by_zip(self, zip_code: str) -> List[Representative]:
        """
        Get representatives for a given zip code

        Args:
            zip_code: 5-digit US zip code

        Returns:
            List of Representative objects
        """
        try:
            # 5 Calls API endpoint for representatives by zip code
            url = f"{self.base_url}/reps"
            params = {
                "zip": zip_code
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            representatives = []

            # Parse the response and convert to our Representative format
            if "reps" in data:
                for rep_data in data["reps"]:
                    rep = self._parse_representative(rep_data)
                    if rep:
                        representatives.append(rep)

            logger.info(f"Found {len(representatives)} representatives for zip {zip_code}")
            return representatives

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch representatives from 5 Calls API: {e}")
            return []
        except Exception as e:
            logger.error(f"Error parsing 5 Calls API response: {e}")
            return []

    def get_representatives_by_address(self, address: str) -> List[Representative]:
        """
        Get representatives for a given address

        Args:
            address: Full address string

        Returns:
            List of Representative objects
        """
        try:
            url = f"{self.base_url}/reps"
            params = {
                "address": address
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            representatives = []

            if "reps" in data:
                for rep_data in data["reps"]:
                    rep = self._parse_representative(rep_data)
                    if rep:
                        representatives.append(rep)

            logger.info(f"Found {len(representatives)} representatives for address")
            return representatives

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch representatives from 5 Calls API: {e}")
            return []
        except Exception as e:
            logger.error(f"Error parsing 5 Calls API response: {e}")
            return []

    def get_representatives_by_lat_lng(self, latitude: float, longitude: float) -> List[Representative]:
        """
        Get representatives for given coordinates

        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate

        Returns:
            List of Representative objects
        """
        try:
            url = f"{self.base_url}/reps"
            params = {
                "lat": latitude,
                "lng": longitude
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            representatives = []

            if "reps" in data:
                for rep_data in data["reps"]:
                    rep = self._parse_representative(rep_data)
                    if rep:
                        representatives.append(rep)

            logger.info(f"Found {len(representatives)} representatives for coordinates")
            return representatives

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch representatives from 5 Calls API: {e}")
            return []
        except Exception as e:
            logger.error(f"Error parsing 5 Calls API response: {e}")
            return []

    def _parse_representative(self, rep_data: Dict[str, Any]) -> Optional[Representative]:
        """
        Parse representative data from 5 Calls API response

        Args:
            rep_data: Raw representative data from API

        Returns:
            Representative object or None if parsing fails
        """
        try:
            # Extract basic information
            name = rep_data.get("name", "")
            if not name:
                return None

            # Determine title/office
            title = rep_data.get("office", "")
            if not title:
                # Try to infer from other fields
                if "senate" in rep_data.get("chamber", "").lower():
                    title = "Senator"
                elif "house" in rep_data.get("chamber", "").lower():
                    title = "Representative"
                else:
                    title = "Official"

            # Extract contact information
            phone = rep_data.get("phone", "")
            email = rep_data.get("email", "")
            website = rep_data.get("website", "")

            # Extract location information
            state = rep_data.get("state", "")
            district = rep_data.get("district", "")
            party = rep_data.get("party", "")

            # Build address if available
            address_parts = []
            if rep_data.get("address"):
                address_parts.append(rep_data["address"])
            if rep_data.get("city"):
                address_parts.append(rep_data["city"])
            if rep_data.get("state"):
                address_parts.append(rep_data["state"])
            if rep_data.get("zip"):
                address_parts.append(rep_data["zip"])

            address = ", ".join(address_parts) if address_parts else None

            # Determine level (federal, state, local)
            level = "federal"  # 5 Calls primarily focuses on federal
            if "state" in title.lower() or "assembly" in title.lower():
                level = "state"

            return Representative(
                name=name,
                title=title,
                party=party if party else None,
                phone=phone if phone else None,
                email=email if email else None,
                website=website if website else None,
                address=address,
                state=state if state else None,
                district=district if district else None,
                level=level
            )

        except Exception as e:
            logger.error(f"Error parsing representative data: {e}")
            return None


def get_five_calls_client() -> FiveCallsAPIClient:
    """Get a configured 5 Calls API client"""
    return FiveCallsAPIClient()
