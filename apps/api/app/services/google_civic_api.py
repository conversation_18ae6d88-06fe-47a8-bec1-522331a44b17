# app/services/google_civic_api.py
"""
Google Civic Information API service for fetching real representative data.

This service provides access to the Google Civic Information API to fetch
current elected officials based on address/zip code.
"""

import requests
import logging
from typing import Dict, Any, Optional, List
from app.core.config import get_settings
# Removed enum imports - now using string values directly

logger = logging.getLogger(__name__)
settings = get_settings()


class GoogleCivicAPI:
    """Service for interacting with the Google Civic Information API"""

    def __init__(self):
        """Initialize the Google Civic API client"""
        self.base_url = "https://www.googleapis.com/civicinfo/v2"
        self.api_key = settings.GOOGLE_CIVIC_INFO_API_KEY

        if not self.api_key:
            logger.warning("GOOGLE_CIVIC_INFO_API_KEY not configured. Google Civic API features will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info("Google Civic Information API client initialized successfully")

    def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Make a request to the Google Civic Information API.

        Args:
            endpoint: API endpoint path
            params: Query parameters

        Returns:
            Dict containing API response data or None if request fails
        """
        if not self.enabled:
            logger.error("Google Civic API is not enabled. Please configure GOOGLE_CIVIC_INFO_API_KEY.")
            return None

        url = f"{self.base_url}/{endpoint}"

        # Add API key to parameters
        if params is None:
            params = {}
        params['key'] = self.api_key

        try:
            logger.debug(f"Making request to Google Civic API: {endpoint}")
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            logger.debug("Successfully fetched data from Google Civic API")
            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch data from Google Civic API: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching data from Google Civic API: {e}")
            return None

    def get_representatives_by_address(self, address: str, levels: List[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get representatives for a given address.

        Args:
            address: Address or zip code to look up
            levels: List of government levels to include (e.g., ['federal', 'state', 'local'])

        Returns:
            Dict containing representatives data or None if request fails
        """
        params = {
            'address': address,
            'includeOffices': 'true'
        }

        if levels:
            params['levels'] = levels

        return self._make_request('representatives', params)

    def get_federal_representatives_by_zip(self, zip_code: str) -> List[Dict[str, Any]]:
        """
        Get federal representatives for a zip code.

        Args:
            zip_code: 5-digit zip code

        Returns:
            List of representative dictionaries
        """
        data = self.get_representatives_by_address(zip_code, levels=['federal'])

        if not data:
            return []

        return self._parse_representatives_response(data)

    def _parse_representatives_response(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Parse the Google Civic API response into a standardized format.

        Args:
            data: Raw response from Google Civic API

        Returns:
            List of parsed representative dictionaries
        """
        representatives = []

        offices = data.get('offices', [])
        officials = data.get('officials', [])

        for office in offices:
            office_name = office.get('name', '')
            level = office.get('levels', ['unknown'])[0] if office.get('levels') else 'unknown'

            # Get official indices for this office
            official_indices = office.get('officialIndices', [])

            for index in official_indices:
                if index < len(officials):
                    official = officials[index]

                    # Parse the official data
                    rep_data = self._parse_official_data(official, office_name, level)
                    if rep_data:
                        representatives.append(rep_data)

        return representatives

    def _parse_official_data(self, official: Dict[str, Any], office_name: str, level: str) -> Optional[Dict[str, Any]]:
        """
        Parse individual official data from Google Civic API.

        Args:
            official: Official data from API
            office_name: Name of the office
            level: Government level

        Returns:
            Parsed official dictionary or None if parsing fails
        """
        try:
            # Extract basic info
            name = official.get('name', '')
            party = official.get('party', '')

            # Extract contact info
            phones = official.get('phones', [])
            phone = phones[0] if phones else None

            emails = official.get('emails', [])
            email = emails[0] if emails else None

            urls = official.get('urls', [])
            website = urls[0] if urls else None

            # Extract address
            addresses = official.get('address', [])
            office_address = None
            office_city = None
            office_state = None
            office_zip = None

            if addresses:
                addr = addresses[0]
                office_address = addr.get('line1', '')
                if addr.get('line2'):
                    office_address += f", {addr.get('line2')}"
                office_city = addr.get('city', '')
                office_state = addr.get('state', '')
                office_zip = addr.get('zip', '')

            # Extract social media
            channels = official.get('channels', [])
            twitter_handle = None
            facebook_url = None

            for channel in channels:
                channel_type = channel.get('type', '').lower()
                channel_id = channel.get('id', '')

                if channel_type == 'twitter':
                    twitter_handle = channel_id
                elif channel_type == 'facebook':
                    facebook_url = f"https://facebook.com/{channel_id}"

            # Map level and chamber
            official_level = self._map_level(level)
            official_chamber = self._map_chamber(office_name, level)

            # Determine state from office name or address
            state = self._extract_state(office_name, office_state)
            district = self._extract_district(office_name)

            return {
                'name': name,
                'title': office_name,
                'party': party,
                'email': email,
                'phone': phone,
                'website': website,
                'twitter_handle': twitter_handle,
                'facebook_url': facebook_url,
                'office_address': office_address,
                'office_city': office_city,
                'office_state': office_state,
                'office_zip': office_zip,
                'level': official_level,
                'chamber': official_chamber,
                'state': state,
                'district': district,
                'is_active': True,
                'google_civic_id': f"{name}_{office_name}".replace(' ', '_').lower()
            }

        except Exception as e:
            logger.error(f"Error parsing official data: {e}")
            return None

    def _map_level(self, level: str) -> str:
        """Map Google Civic level to our level string"""
        level_mapping = {
            'federal': 'federal',
            'state': 'state',
            'local': 'local'
        }
        return level_mapping.get(level.lower(), 'federal')

    def _map_chamber(self, office_name: str, level: str) -> str:
        """Map office name to our chamber string"""
        office_lower = office_name.lower()

        if 'president' in office_lower or 'governor' in office_lower or 'mayor' in office_lower:
            return 'executive'
        elif 'senate' in office_lower or 'senator' in office_lower:
            return 'senate'
        elif 'house' in office_lower or 'representative' in office_lower:
            return 'house'
        elif 'judge' in office_lower or 'justice' in office_lower:
            return 'judicial'
        else:
            return 'other'

    def _extract_state(self, office_name: str, office_state: str) -> Optional[str]:
        """Extract state abbreviation from office name or address"""
        if office_state:
            return office_state.upper()

        # Try to extract from office name
        # This is a simplified approach - in production you'd want a more robust parser
        state_names = {
            'california': 'CA', 'new york': 'NY', 'texas': 'TX', 'florida': 'FL',
            'pennsylvania': 'PA', 'illinois': 'IL', 'ohio': 'OH', 'georgia': 'GA',
            'north carolina': 'NC', 'michigan': 'MI', 'virginia': 'VA', 'washington': 'WA'
        }

        office_lower = office_name.lower()
        for state_name, abbrev in state_names.items():
            if state_name in office_lower:
                return abbrev

        return None

    def _extract_district(self, office_name: str) -> Optional[str]:
        """Extract district number from office name"""
        import re

        # Look for patterns like "District 5", "5th District", etc.
        patterns = [
            r'district\s+(\d+)',
            r'(\d+)(?:st|nd|rd|th)\s+district',
            r'district\s+(\d+)',
        ]

        office_lower = office_name.lower()
        for pattern in patterns:
            match = re.search(pattern, office_lower)
            if match:
                return match.group(1)

        return None

    def health_check(self) -> Dict[str, Any]:
        """
        Check the health of the Google Civic API connection.

        Returns:
            Dict containing health status information
        """
        if not self.enabled:
            return {
                "status": "disabled",
                "message": "Google Civic API key not configured",
                "enabled": False
            }

        try:
            # Test with a simple request for a known zip code
            response = self.get_representatives_by_address("90210", levels=['federal'])

            if response:
                return {
                    "status": "healthy",
                    "message": "Google Civic API is accessible",
                    "enabled": True,
                    "test_response_offices": len(response.get('offices', []))
                }
            else:
                return {
                    "status": "error",
                    "message": "Google Civic API returned no data",
                    "enabled": True
                }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Google Civic API health check failed: {str(e)}",
                "enabled": True,
                "error": str(e)
            }


# Convenience function for easy import
def get_google_civic_client() -> GoogleCivicAPI:
    """Get a Google Civic Information API client instance"""
    return GoogleCivicAPI()
