# app/services/location_encryption.py
"""
Location encryption service for privacy-safe geographic data handling.

This service provides encryption/decryption for sensitive location data
while maintaining the ability to perform aggregate analytics at state/district level.
"""

import os
import logging
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

logger = logging.getLogger(__name__)


class LocationEncryptionService:
    """Service for encrypting/decrypting user location data"""

    def __init__(self):
        # Get encryption key from environment
        self.encryption_key = os.getenv('LOCATION_ENCRYPTION_KEY')
        if not self.encryption_key:
            logger.warning("LOCATION_ENCRYPTION_KEY not set. Location encryption disabled.")
            self.enabled = False
            return

        try:
            # Initialize Fernet cipher
            self.cipher = Fernet(self.encryption_key.encode())
            self.enabled = True
            logger.info("Location encryption service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize location encryption: {e}")
            self.enabled = False

    def encrypt_address(self, address: str) -> Optional[str]:
        """
        Encrypt a full address string
        
        Args:
            address: Full address to encrypt
            
        Returns:
            Encrypted address string or None if encryption fails
        """
        if not self.enabled or not address:
            return address

        try:
            encrypted = self.cipher.encrypt(address.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Failed to encrypt address: {e}")
            return None

    def decrypt_address(self, encrypted_address: str) -> Optional[str]:
        """
        Decrypt an encrypted address string
        
        Args:
            encrypted_address: Encrypted address to decrypt
            
        Returns:
            Decrypted address string or None if decryption fails
        """
        if not self.enabled or not encrypted_address:
            return encrypted_address

        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_address.encode())
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Failed to decrypt address: {e}")
            return None

    def encrypt_zip_code(self, zip_code: str) -> Optional[str]:
        """
        Encrypt a ZIP code
        
        Args:
            zip_code: ZIP code to encrypt
            
        Returns:
            Encrypted ZIP code string or None if encryption fails
        """
        if not self.enabled or not zip_code:
            return zip_code

        try:
            encrypted = self.cipher.encrypt(zip_code.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Failed to encrypt ZIP code: {e}")
            return None

    def decrypt_zip_code(self, encrypted_zip: str) -> Optional[str]:
        """
        Decrypt an encrypted ZIP code
        
        Args:
            encrypted_zip: Encrypted ZIP code to decrypt
            
        Returns:
            Decrypted ZIP code string or None if decryption fails
        """
        if not self.enabled or not encrypted_zip:
            return encrypted_zip

        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_zip.encode())
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Failed to decrypt ZIP code: {e}")
            return None

    def extract_analytics_data(self, address: str, zip_code: str) -> Dict[str, Any]:
        """
        Extract analytics-safe data from address/ZIP without storing sensitive info
        
        Args:
            address: Full address
            zip_code: ZIP code
            
        Returns:
            Dict with state_code, congressional_district, approximate coordinates
        """
        try:
            # Extract state from address (basic implementation)
            state_code = self._extract_state_code(address)
            
            # Extract district from ZIP code (would integrate with real geocoding API)
            congressional_district = self._get_congressional_district(zip_code)
            
            # Get approximate city-level coordinates (would integrate with geocoding API)
            lat, lng = self._get_approximate_coordinates(zip_code)
            
            return {
                'state_code': state_code,
                'congressional_district': congressional_district,
                'latitude': lat,
                'longitude': lng,
                'accuracy_level': 'city'  # Always city-level for privacy
            }
        except Exception as e:
            logger.error(f"Failed to extract analytics data: {e}")
            return {}

    def _extract_state_code(self, address: str) -> Optional[str]:
        """Extract state code from address string"""
        if not address:
            return None
            
        # Simple state extraction (would use more sophisticated parsing in production)
        state_abbreviations = {
            'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR',
            'california': 'CA', 'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE',
            'florida': 'FL', 'georgia': 'GA', 'hawaii': 'HI', 'idaho': 'ID',
            'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA', 'kansas': 'KS',
            'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
            'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS',
            'missouri': 'MO', 'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV',
            'new hampshire': 'NH', 'new jersey': 'NJ', 'new mexico': 'NM', 'new york': 'NY',
            'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH', 'oklahoma': 'OK',
            'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
            'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT',
            'vermont': 'VT', 'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV',
            'wisconsin': 'WI', 'wyoming': 'WY'
        }
        
        address_lower = address.lower()
        for state_name, state_code in state_abbreviations.items():
            if state_name in address_lower:
                return state_code
                
        # Check for state abbreviations
        words = address.upper().split()
        for word in words:
            if word in state_abbreviations.values():
                return word
                
        return None

    def _get_congressional_district(self, zip_code: str) -> Optional[str]:
        """Get congressional district for ZIP code (placeholder implementation)"""
        if not zip_code or len(zip_code) != 5:
            return None
            
        # In production, this would integrate with a real congressional district API
        # For now, return a placeholder based on ZIP code patterns
        zip_prefix = zip_code[:3]
        
        # Sample district mappings (would be comprehensive in production)
        district_map = {
            '100': 'NY-12',  # NYC
            '101': 'NY-15',  # NYC
            '606': 'IL-07',  # Chicago
            '902': 'CA-28',  # Los Angeles
        }
        
        return district_map.get(zip_prefix)

    def _get_approximate_coordinates(self, zip_code: str) -> tuple[Optional[float], Optional[float]]:
        """Get approximate city-level coordinates for ZIP code"""
        if not zip_code or len(zip_code) != 5:
            return None, None
            
        # In production, this would integrate with a geocoding API
        # Return approximate coordinates rounded to city-level precision
        zip_prefix = zip_code[:3]
        
        # Sample coordinate mappings (would be comprehensive in production)
        coord_map = {
            '100': (40.7589, -73.9851),  # NYC (rounded)
            '101': (40.7831, -73.9712),  # NYC (rounded)
            '606': (41.8781, -87.6298),  # Chicago (rounded)
            '902': (34.0522, -118.2437), # Los Angeles (rounded)
        }
        
        coords = coord_map.get(zip_prefix)
        return coords if coords else (None, None)

    def health_check(self) -> Dict[str, Any]:
        """Check the health of the location encryption service"""
        return {
            'enabled': self.enabled,
            'status': 'healthy' if self.enabled else 'disabled',
            'message': 'Location encryption service ready' if self.enabled else 'Encryption key not configured'
        }


# Convenience function for easy import
def get_location_encryption_service() -> LocationEncryptionService:
    """Get a location encryption service instance"""
    return LocationEncryptionService()