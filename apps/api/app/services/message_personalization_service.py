# app/services/message_personalization_service.py
"""
Message Personalization Service using OpenAI GPT-4.

This service creates personalized messages to representatives based on:
- Bill content and AI analysis
- User's position (support/oppose/amend)
- Representative information
- User's personal information and location
"""

import asyncio
import logging
import json
from typing import Dict, Any, List, Optional
from tenacity import retry, stop_after_attempt, wait_exponential
from app.services.ai_service import get_ai_service

logger = logging.getLogger(__name__)


class MessagePersonalizationService:
    """Service for creating personalized messages to representatives"""
    
    def __init__(self):
        self.ai_service = get_ai_service()
        logger.info("Message personalization service initialized")
    
    async def create_personalized_messages(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create personalized messages for each representative

        Args:
            request_data: Dict containing:
                - bill: Bill object with AI analysis
                - position: 'support', 'oppose', or 'amend'
                - user_info: User's personal information
                - representatives: List of representative objects
                - custom_message: Optional user's custom message

        Returns:
            Dict with personalized messages for each representative
        """
        logger.info("=== Starting create_personalized_messages ===")
        logger.info(f"Request data keys: {list(request_data.keys())}")
        try:
            logger.info(f"Creating personalized messages for bill {request_data['bill'].bill_number}")
            
            bill = request_data['bill']
            position = request_data['position']
            user_info = request_data['user_info']
            representatives = request_data['representatives']
            custom_message = request_data.get('custom_message', '')
            personal_stories = request_data.get('personal_stories', '')
            
            # Validate required data
            if not bill.ai_processed_at:
                return {
                    'status': 'error',
                    'message': 'Bill has not been processed with AI analysis yet'
                }
            
            # Create personalized messages for each representative
            personalized_messages = []

            logger.info(f"Processing {len(representatives)} representatives concurrently")

            # Create tasks for concurrent processing
            tasks = []
            for i, rep in enumerate(representatives):
                logger.info(f"Creating task for representative {i+1}: {type(rep)} - {rep}")
                task = self._create_message_for_representative(
                    bill=bill,
                    position=position,
                    user_info=user_info,
                    representative=rep,
                    custom_message=custom_message,
                    personal_stories=personal_stories
                )
                tasks.append((rep, task))

            # Execute all tasks concurrently with timeout
            results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)

            # Process results
            for i, ((rep, _), result) in enumerate(zip(tasks, results)):
                if isinstance(result, Exception):
                    logger.error(f"Exception processing representative {rep.get('full_name')}: {result}")
                    continue

                if result.get('success'):
                    personalized_messages.append({
                        'representative': rep,
                        'subject': result['subject'],
                        'body': result['body'],
                        'talking_points': result.get('talking_points', [])
                    })
                else:
                    logger.error(f"Failed to create message for {rep.get('full_name')}: {result.get('error')}")
            
            if not personalized_messages:
                return {
                    'status': 'error',
                    'message': 'Failed to create any personalized messages'
                }
            
            return {
                'status': 'success',
                'bill_number': bill.bill_number,
                'position': position,
                'total_messages': len(personalized_messages),
                'messages': personalized_messages
            }
            
        except Exception as e:
            logger.error(f"Failed to create personalized messages: {e}")
            return {
                'status': 'error',
                'message': f'Message creation failed: {e}'
            }
    
    async def _create_message_for_representative(self, bill: Any, position: str,
                                               user_info: Dict[str, Any],
                                               representative: Dict[str, Any],
                                               custom_message: str = '',
                                               personal_stories: str = '') -> Dict[str, Any]:
        """Create a personalized message for a specific representative"""
        try:
            # Get the appropriate reasons based on position
            reasons = []
            if position == 'support' and bill.support_reasons:
                reasons = bill.support_reasons[:3]  # Top 3 reasons
            elif position == 'oppose' and bill.oppose_reasons:
                reasons = bill.oppose_reasons[:3]
            elif position == 'amend' and bill.amend_reasons:
                reasons = bill.amend_reasons[:3]

            # Get message template if available
            template = None
            if bill.message_templates and isinstance(bill.message_templates, dict):
                template = bill.message_templates.get(position, {})
            
            # Get message template if available
            template = None
            if bill.message_templates and isinstance(bill.message_templates, dict):
                template = bill.message_templates.get(position, {})
            
            # Create the personalization prompt
            prompt = self._build_personalization_prompt(
                bill=bill,
                position=position,
                reasons=reasons,
                user_info=user_info,
                representative=representative,
                template=template,
                custom_message=custom_message,
                personal_stories=personal_stories
            )
            
            # Generate personalized message using AI
            response = await self.ai_service._make_openai_request(
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at writing professional, persuasive messages to elected representatives. Create personalized, respectful messages that are specific to the recipient and include relevant local context."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                max_tokens=800,
                temperature=0.7
            )
            
            # Parse the response
            message_content = response.strip()
            
            # Extract subject and body
            if "SUBJECT:" in message_content and "BODY:" in message_content:
                parts = message_content.split("BODY:", 1)
                subject = parts[0].replace("SUBJECT:", "").strip()
                body = parts[1].strip()
            else:
                # Fallback: create subject from first line
                lines = message_content.split('\n', 1)
                subject = f"Regarding {bill.bill_number}: {bill.title[:50]}..."
                body = message_content
            
            # Extract talking points if present
            talking_points = []
            if "TALKING POINTS:" in body:
                body_parts = body.split("TALKING POINTS:", 1)
                body = body_parts[0].strip()
                talking_points_text = body_parts[1].strip()
                talking_points = [point.strip() for point in talking_points_text.split('\n') if point.strip()]
            
            return {
                'success': True,
                'subject': subject,
                'body': body,
                'talking_points': talking_points
            }
            
        except Exception as e:
            logger.error(f"Error creating message for representative: {e}")
            logger.error(f"Representative type: {type(representative)}")
            logger.error(f"Representative value: {representative}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _build_personalization_prompt(self, bill: Any, position: str, reasons: List[str],
                                    user_info: Dict[str, Any], representative: Dict[str, Any],
                                    template: Optional[Dict[str, Any]], custom_message: str,
                                    personal_stories: str = '') -> str:
        """Build the AI prompt for message personalization"""
        
        # Determine proper title and address
        title = representative.get('title', 'Representative')
        if representative.get('chamber') == 'senate':
            title = 'Senator'
        
        full_title = f"{title} {representative.get('last_name', representative.get('full_name', ''))}"
        
        prompt = f"""
Create a personalized message to {full_title} about {bill.bill_number}: {bill.title}

POSITION: {position.upper()} this bill

BILL SUMMARY:
{bill.ai_summary}

KEY REASONS FOR {position.upper()}:
{chr(10).join(f"• {reason}" for reason in reasons)}

USER INFORMATION:
- Name: {user_info.get('first_name', '')} {user_info.get('last_name', '')}
- Location: {user_info.get('city', '')}, {user_info.get('state', '')} {user_info.get('zip_code', '')}

REPRESENTATIVE INFORMATION:
- Name: {representative.get('full_name', '')}
- Title: {title}
- Chamber: {representative.get('chamber', '').title()}
- State: {representative.get('state', '')}
- Party: {representative.get('party', '')}

"""
        
        if template:
            prompt += f"""
TEMPLATE GUIDANCE:
"""
            if isinstance(template, dict):
                prompt += f"""Subject Template: {template.get('subject', '')}
Body Template: {template.get('body', '')}
"""
            else:
                # Template is a string, use it as body template
                prompt += f"""Body Template: {template}
"""
            prompt += "\n"
        
        if custom_message:
            prompt += f"""
USER'S CUSTOM MESSAGE TO INCORPORATE:
{custom_message}

"""

        if personal_stories:
            prompt += f"""
USER'S PERSONAL STORY TO INCORPORATE:
{personal_stories}

Please weave this personal story naturally into the message to make it more compelling and authentic.

"""

        prompt += f"""
INSTRUCTIONS:
1. Write a professional, respectful message that clearly states the user's position
2. Include specific reasons why the representative should {position} the bill
3. Reference the user's location and how the bill affects their community
4. Keep the tone appropriate for the representative's party and position
5. Make it personal but not overly familiar
6. Include a clear call to action
7. Keep the message concise but substantive (200-400 words)

FORMAT YOUR RESPONSE AS:
SUBJECT: [Clear, specific subject line]

BODY: [The complete message body]

TALKING POINTS: [Optional: 2-3 key points for follow-up]
"""
        
        return prompt
    
    async def create_social_media_content(self, bill: Any, position: str, 
                                        user_info: Dict[str, Any]) -> Dict[str, Any]:
        """Create social media content for sharing the user's position"""
        try:
            logger.info(f"Creating social media content for bill {bill.bill_number}")
            
            # Get reasons based on position
            reasons = []
            if position == 'support' and bill.support_reasons:
                reasons = bill.support_reasons[:2]  # Top 2 for social media
            elif position == 'oppose' and bill.oppose_reasons:
                reasons = bill.oppose_reasons[:2]
            elif position == 'amend' and bill.amend_reasons:
                reasons = bill.amend_reasons[:2]
            
            prompt = f"""
Create social media content for {position}ing {bill.bill_number}: {bill.title}

BILL SUMMARY: {bill.ai_summary[:300]}...

KEY REASONS:
{chr(10).join(f"• {reason}" for reason in reasons)}

USER LOCATION: {user_info.get('city', '')}, {user_info.get('state', '')}

Create:
1. TWITTER POST (under 280 characters)
2. FACEBOOK POST (engaging, 1-2 paragraphs)
3. HASHTAGS (relevant hashtags)

Make it engaging, informative, and appropriate for social sharing.
"""
            
            response = await self.ai_service._make_openai_request(
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at creating engaging social media content about political issues. Create content that is informative, balanced, and encourages civic engagement."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=600,
                temperature=0.8
            )
            
            return {
                'status': 'success',
                'content': response,
                'bill_number': bill.bill_number,
                'position': position
            }
            
        except Exception as e:
            logger.error(f"Failed to create social media content: {e}")
            return {
                'status': 'error',
                'message': f'Social media content creation failed: {e}'
            }


# Convenience function for easy import
def get_message_personalization_service() -> MessagePersonalizationService:
    """Get a message personalization service instance"""
    return MessagePersonalizationService()
