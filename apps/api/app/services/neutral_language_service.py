# app/services/neutral_language_service.py
"""
Service for generating neutral, professional language for values analysis.

This service ensures all user-facing content maintains strict neutrality
while providing clear, factual information about bill impacts.
"""

from typing import Dict, List, Tuple
from dataclasses import dataclass


@dataclass
class NeutralTag:
    """Represents a neutral tag for frontend display."""
    category: str
    tag_type: str
    display_text: str
    description: str
    color_theme: str
    icon_name: str
    severity_level: int


class NeutralLanguageService:
    """Service for generating neutral language for bill values analysis."""
    
    # Neutral tag templates organized by category and impact level
    TAG_TEMPLATES = {
        'democracy': {
            'low_impact': {
                'display_text': 'Civic Procedures',
                'description': 'Contains provisions related to voting and election administration',
                'color_theme': 'blue',
                'icon_name': 'clipboard-list'
            },
            'medium_impact': {
                'display_text': 'Electoral Process Changes',
                'description': 'Modifies voting procedures or election administration',
                'color_theme': 'blue',
                'icon_name': 'vote'
            },
            'high_impact': {
                'display_text': 'Significant Electoral Changes',
                'description': 'Contains substantial modifications to democratic processes',
                'color_theme': 'orange',
                'icon_name': 'alert-circle'
            },
            'critical_impact': {
                'display_text': 'Major Institutional Changes',
                'description': 'Includes fundamental changes to democratic institutions',
                'color_theme': 'red',
                'icon_name': 'exclamation-triangle'
            }
        },
        'human_rights': {
            'low_impact': {
                'display_text': 'Civil Procedures',
                'description': 'Addresses individual rights and civil procedures',
                'color_theme': 'purple',
                'icon_name': 'users'
            },
            'medium_impact': {
                'display_text': 'Civil Rights Considerations',
                'description': 'Contains provisions affecting civil liberties and individual rights',
                'color_theme': 'purple',
                'icon_name': 'shield'
            },
            'high_impact': {
                'display_text': 'Significant Rights Impact',
                'description': 'Includes substantial changes to civil rights framework',
                'color_theme': 'orange',
                'icon_name': 'alert-circle'
            },
            'critical_impact': {
                'display_text': 'Major Rights Framework Changes',
                'description': 'Contains fundamental modifications to civil rights structure',
                'color_theme': 'red',
                'icon_name': 'exclamation-triangle'
            }
        },
        'environment': {
            'low_impact': {
                'display_text': 'Environmental Considerations',
                'description': 'Includes environmental or sustainability provisions',
                'color_theme': 'green',
                'icon_name': 'leaf'
            },
            'medium_impact': {
                'display_text': 'Environmental Policy Changes',
                'description': 'Modifies environmental regulations or policies',
                'color_theme': 'green',
                'icon_name': 'globe'
            },
            'high_impact': {
                'display_text': 'Significant Environmental Impact',
                'description': 'Contains substantial environmental policy modifications',
                'color_theme': 'orange',
                'icon_name': 'alert-circle'
            },
            'critical_impact': {
                'display_text': 'Major Environmental Framework Changes',
                'description': 'Includes fundamental changes to environmental policy structure',
                'color_theme': 'red',
                'icon_name': 'exclamation-triangle'
            }
        }
    }
    
    # Neutral descriptions for different score types
    SCORE_TYPE_LANGUAGE = {
        'threat': {
            'verb': 'may impact',
            'noun': 'considerations',
            'adjective': 'affecting'
        },
        'support': {
            'verb': 'may enhance',
            'noun': 'improvements',
            'adjective': 'supporting'
        },
        'neutral': {
            'verb': 'addresses',
            'noun': 'provisions',
            'adjective': 'related to'
        }
    }
    
    @classmethod
    def generate_neutral_tag(
        cls, 
        category: str, 
        score_type: str, 
        severity: int,
        custom_context: str = None
    ) -> NeutralTag:
        """
        Generate a neutral tag for frontend display.
        
        Args:
            category: 'democracy', 'human_rights', or 'environment'
            score_type: 'threat', 'support', or 'neutral'
            severity: 1-10 severity score
            custom_context: Optional custom context for the tag
            
        Returns:
            NeutralTag object with neutral language
        """
        # Determine impact level from severity
        impact_level = cls._get_impact_level(severity)
        
        # Get base template
        template = cls.TAG_TEMPLATES.get(category, {}).get(impact_level, {})
        
        if not template:
            # Fallback template
            template = {
                'display_text': 'Policy Considerations',
                'description': 'Contains policy provisions requiring review',
                'color_theme': 'gray',
                'icon_name': 'info'
            }
        
        # Adjust language based on score type
        display_text = cls._adjust_for_score_type(template['display_text'], score_type)
        description = cls._adjust_description_for_score_type(
            template['description'], score_type, category, custom_context
        )
        
        return NeutralTag(
            category=category,
            tag_type=score_type,
            display_text=display_text,
            description=description,
            color_theme=template['color_theme'],
            icon_name=template['icon_name'],
            severity_level=severity
        )
    
    @classmethod
    def _get_impact_level(cls, severity: int) -> str:
        """Convert severity score to impact level."""
        if severity <= 3:
            return 'low_impact'
        elif severity <= 6:
            return 'medium_impact'
        elif severity <= 8:
            return 'high_impact'
        else:
            return 'critical_impact'
    
    @classmethod
    def _adjust_for_score_type(cls, base_text: str, score_type: str) -> str:
        """Adjust tag text based on score type while maintaining neutrality."""
        if score_type == 'support':
            # For support, we can be slightly more positive but still neutral
            if 'Changes' in base_text:
                return base_text.replace('Changes', 'Enhancements')
            elif 'Impact' in base_text:
                return base_text.replace('Impact', 'Improvements')
        elif score_type == 'threat':
            # For threats, we use neutral "impact" language
            if 'Enhancements' in base_text:
                return base_text.replace('Enhancements', 'Changes')
            elif 'Improvements' in base_text:
                return base_text.replace('Improvements', 'Impact')
        
        return base_text
    
    @classmethod
    def _adjust_description_for_score_type(
        cls, 
        base_description: str, 
        score_type: str, 
        category: str,
        custom_context: str = None
    ) -> str:
        """Adjust description based on score type while maintaining neutrality."""
        
        language = cls.SCORE_TYPE_LANGUAGE.get(score_type, cls.SCORE_TYPE_LANGUAGE['neutral'])
        
        # Category-specific neutral language
        category_subjects = {
            'democracy': 'democratic processes and civic participation',
            'human_rights': 'individual rights and civil procedures',
            'environment': 'environmental policies and sustainability measures'
        }
        
        subject = category_subjects.get(category, 'policy areas')
        
        if custom_context:
            return f"This bill {language['verb']} {subject}. {custom_context}"
        else:
            return f"This bill {language['verb']} {subject} through various {language['noun']}."
    
    @classmethod
    def generate_summary_language(
        cls,
        democracy_scores: Tuple[int, int],  # (threat, support)
        human_rights_scores: Tuple[int, int],
        environment_scores: Tuple[int, int]
    ) -> str:
        """
        Generate neutral summary language for overall bill impact.
        
        Args:
            democracy_scores: (threat_score, support_score)
            human_rights_scores: (threat_score, support_score)
            environment_scores: (threat_score, support_score)
            
        Returns:
            Neutral summary text
        """
        impacts = []
        
        # Check each category for significant impacts
        categories = [
            ('democratic processes', democracy_scores),
            ('civil procedures', human_rights_scores),
            ('environmental policies', environment_scores)
        ]
        
        for category_name, (threat, support) in categories:
            max_score = max(threat, support)
            if max_score >= 5:  # Only mention significant impacts
                if support > threat:
                    impacts.append(f"may enhance {category_name}")
                elif threat > support:
                    impacts.append(f"may impact {category_name}")
                else:
                    impacts.append(f"addresses {category_name}")
        
        if not impacts:
            return "This bill contains various policy provisions."
        elif len(impacts) == 1:
            return f"This bill {impacts[0]}."
        elif len(impacts) == 2:
            return f"This bill {impacts[0]} and {impacts[1]}."
        else:
            return f"This bill {', '.join(impacts[:-1])}, and {impacts[-1]}."
    
    @classmethod
    def get_confidence_language(cls, confidence_score: float) -> str:
        """Generate neutral language for confidence levels."""
        if confidence_score >= 0.9:
            return "High confidence assessment"
        elif confidence_score >= 0.7:
            return "Moderate confidence assessment"
        elif confidence_score >= 0.5:
            return "Preliminary assessment"
        else:
            return "Assessment requires review"
    
    @classmethod
    def validate_neutrality(cls, text: str) -> Tuple[bool, List[str]]:
        """
        Validate that text maintains political neutrality.
        
        Returns:
            (is_neutral, list_of_issues)
        """
        partisan_indicators = [
            # Political terms
            'liberal', 'conservative', 'progressive', 'right-wing', 'left-wing',
            'socialist', 'capitalist', 'radical', 'extreme',
            
            # Emotional language
            'fight', 'battle', 'attack', 'defend', 'destroy', 'crush',
            'wonderful', 'terrible', 'amazing', 'awful', 'fantastic', 'horrible',
            
            # Biased framing
            'obviously', 'clearly wrong', 'undeniably', 'everyone knows',
            'common sense', 'ridiculous', 'absurd'
        ]
        
        text_lower = text.lower()
        found_issues = [term for term in partisan_indicators if term in text_lower]
        
        return len(found_issues) == 0, found_issues
