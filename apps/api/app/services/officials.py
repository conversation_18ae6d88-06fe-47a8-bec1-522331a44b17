# app/services/officials.py
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime
from app.models.official import Official
from app.schemas.official import OfficialCreate, OfficialUpdate, OfficialSearch

class OfficialService:
    """Service layer for official operations"""

    def __init__(self, db: Session):
        self.db = db

    def get_official(self, official_id: str) -> Optional[Official]:
        """Get a single official by ID"""
        return self.db.query(Official).filter(Official.id == official_id).first()

    def get_officials(self, skip: int = 0, limit: int = 20) -> List[Official]:
        """Get a list of officials with pagination"""
        return self.db.query(Official).filter(Official.is_active == True).offset(skip).limit(limit).all()  # noqa: E712

    def search_officials(self, search_params: OfficialSearch) -> List[Official]:
        """Search officials based on various criteria"""
        query = self.db.query(Official).filter(Official.is_active == True)  # noqa: E712

        # Text search across name and title
        if search_params.query:
            search_term = f"%{search_params.query.lower()}%"
            query = query.filter(
                or_(
                    func.lower(Official.name).contains(search_term),
                    func.lower(Official.title).contains(search_term)
                )
            )

        # Filter by level (federal, state, local)
        if search_params.level:
            query = query.filter(Official.level == search_params.level)

        # Filter by chamber
        if search_params.chamber:
            query = query.filter(Official.chamber == search_params.chamber)

        # Filter by state
        if search_params.state:
            query = query.filter(Official.state == search_params.state)

        # Filter by district
        if search_params.district:
            query = query.filter(Official.district == search_params.district)

        # Filter by party
        if search_params.party:
            query = query.filter(func.lower(Official.party) == search_params.party.lower())

        return query.offset(search_params.offset).limit(search_params.limit).all()

    def get_officials_by_zip_code(self, zip_code: str) -> List[Official]:
        """
        Get officials for a given zip code using OpenStates API as primary source.

        This method implements a caching approach:
        1. First checks if we have cached officials for this zip code
        2. If not, fetches from OpenStates API
        3. Creates/updates official records in our database
        4. Returns the officials

        Args:
            zip_code: 5-digit zip code

        Returns:
            List of Official objects representing this zip code
        """
        import logging
        from app.services.openstates_officials_api import get_openstates_officials_client

        logger = logging.getLogger(__name__)

        # Step 1: Check if we have recent cached data for this zip code
        # Look for officials with this zip code that were updated recently (within 30 days)
        from datetime import datetime, timedelta
        cache_cutoff = datetime.utcnow() - timedelta(days=30)

        cached_officials = self.db.query(Official).filter(
            and_(
                Official.is_active == True,  # noqa: E712
                Official.office_zip == zip_code,
                Official.updated_at >= cache_cutoff
            )
        ).all()

        if cached_officials:
            logger.info(f"Found {len(cached_officials)} cached officials for zip code {zip_code}")
            return cached_officials

        # Step 2: No recent cache, fetch from OpenStates API
        logger.info(f"No cached data for zip code {zip_code}, fetching from OpenStates API...")

        openstates_client = get_openstates_officials_client()
        if openstates_client.enabled:
            try:
                # Fetch officials from OpenStates API
                openstates_officials = openstates_client.get_officials_by_zip(zip_code)

                if openstates_officials:
                    logger.info(f"Successfully fetched {len(openstates_officials)} officials from OpenStates API for zip code {zip_code}")

                    # Create or update officials in our database
                    officials = []
                    for os_official in openstates_officials:
                        try:
                            official = self._get_or_create_official_from_openstates(os_official, zip_code)
                            if official:
                                officials.append(official)
                        except Exception as e:
                            logger.error(f"Error creating/updating official {os_official.name}: {e}")
                            continue

                    if officials:
                        logger.info(f"Successfully processed {len(officials)} officials from OpenStates API for zip code {zip_code}")
                        return officials
                    else:
                        logger.warning(f"No officials could be processed from OpenStates API response for zip code {zip_code}")
                else:
                    logger.warning(f"OpenStates API returned no data for zip code {zip_code}")

            except Exception as e:
                logger.error(f"Error fetching officials from OpenStates API for zip code {zip_code}: {e}")
        else:
            logger.warning("OpenStates API not enabled")

        # Step 3: If OpenStates API fails, return empty list
        logger.warning(f"No officials found for zip code {zip_code}")
        return []

    def _get_or_create_official_from_openstates(self, os_official, zip_code: str) -> Optional[Official]:
        """
        Get or create an official record based on OpenStates data.

        Args:
            os_official: OpenStatesOfficial object
            zip_code: Zip code being queried

        Returns:
            Official object or None if creation fails
        """
        try:
            from datetime import datetime

            # Try to find existing official by OpenStates ID
            existing_official = None
            if os_official.id:
                existing_official = self.db.query(Official).filter(
                    Official.openstates_id == os_official.id
                ).first()

            # If not found by OpenStates ID, try by name and title
            if not existing_official and os_official.name and os_official.title:
                existing_official = self.db.query(Official).filter(
                    and_(
                        Official.name == os_official.name,
                        Official.title == os_official.title,
                        Official.is_active == True  # noqa: E712
                    )
                ).first()

            if existing_official:
                # Update existing official with fresh data
                existing_official.name = os_official.name
                existing_official.title = os_official.title
                existing_official.party = os_official.party
                existing_official.email = os_official.email
                existing_official.phone = os_official.phone
                existing_official.website = os_official.website
                existing_official.twitter_handle = os_official.twitter_handle
                existing_official.facebook_url = os_official.facebook_url
                existing_official.instagram_handle = os_official.instagram_handle
                existing_official.profile_picture_url = os_official.photo_url
                existing_official.bio = os_official.bio
                existing_official.office_address = os_official.office_address
                existing_official.level = os_official.level
                existing_official.chamber = os_official.chamber
                existing_official.state = os_official.state
                existing_official.district = os_official.district
                existing_official.openstates_id = os_official.id
                existing_official.social_media = os_official.social_media
                existing_official.office_zip = zip_code
                existing_official.updated_at = datetime.utcnow()

                self.db.commit()
                self.db.refresh(existing_official)
                return existing_official
            else:
                # Create new official
                new_official = Official(
                    name=os_official.name,
                    title=os_official.title,
                    party=os_official.party,
                    email=os_official.email,
                    phone=os_official.phone,
                    website=os_official.website,
                    twitter_handle=os_official.twitter_handle,
                    facebook_url=os_official.facebook_url,
                    instagram_handle=os_official.instagram_handle,
                    profile_picture_url=os_official.photo_url,
                    bio=os_official.bio,
                    office_address=os_official.office_address,
                    level=os_official.level,
                    chamber=os_official.chamber,
                    state=os_official.state,
                    district=os_official.district,
                    openstates_id=os_official.id,
                    social_media=os_official.social_media,
                    office_zip=zip_code,
                    is_active=True
                )

                self.db.add(new_official)
                self.db.commit()
                self.db.refresh(new_official)
                return new_official

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in _get_or_create_official_from_openstates: {e}")
            self.db.rollback()
            return None



    def create_official(self, official_data: OfficialCreate) -> Official:
        """Create a new official"""
        official = Official(**official_data.model_dump())
        self.db.add(official)
        self.db.commit()
        self.db.refresh(official)
        return official

    def update_official(self, official_id: str, official_data: OfficialUpdate) -> Optional[Official]:
        """Update an existing official"""
        official = self.get_official(official_id)
        if not official:
            return None

        update_data = official_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(official, field, value)

        self.db.commit()
        self.db.refresh(official)
        return official

    def delete_official(self, official_id: str) -> bool:
        """Soft delete an official by setting is_active to False"""
        official = self.get_official(official_id)
        if not official:
            return False

        official.is_active = False
        self.db.commit()
        return True

    def get_officials_by_level(self, level: str) -> List[Official]:
        """Get all officials at a specific government level"""
        return self.db.query(Official).filter(
            and_(Official.level == level, Official.is_active == True)  # noqa: E712
        ).all()

    def get_officials_by_chamber(self, chamber: str) -> List[Official]:
        """Get all officials in a specific chamber"""
        return self.db.query(Official).filter(
            and_(Official.chamber == chamber, Official.is_active == True)  # noqa: E712
        ).all()

    def get_officials_count(self) -> int:
        """Get total count of active officials"""
        return self.db.query(Official).filter(Official.is_active == True).count()  # noqa: E712

    def get_officials_by_external_id(self, external_id: str, id_type: str) -> Optional[Official]:
        """Get official by external ID (bioguide_id, openstates_id, etc.)"""
        if id_type == "bioguide":
            return self.db.query(Official).filter(Official.bioguide_id == external_id).first()
        elif id_type == "openstates":
            return self.db.query(Official).filter(Official.openstates_id == external_id).first()
        elif id_type == "google_civic":
            return self.db.query(Official).filter(Official.google_civic_id == external_id).first()
        else:
            return None
