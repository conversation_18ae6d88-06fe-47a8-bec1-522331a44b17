"""
OpenStates Officials API Service

This service provides access to legislator data from the OpenStates API.
It fetches federal and state legislators by zip code using geographic lookup.
"""

import logging
import requests
from datetime import datetime
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class OpenStatesOfficial:
    """Data class representing an official from OpenStates API"""
    id: str
    name: str
    party: Optional[str]
    title: str
    district: Optional[str]
    chamber: Optional[str]
    level: str  # 'federal' or 'state'
    state: Optional[str]
    email: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    twitter_handle: Optional[str] = None
    facebook_url: Optional[str] = None
    instagram_handle: Optional[str] = None
    photo_url: Optional[str] = None
    bio: Optional[str] = None
    office_address: Optional[str] = None
    social_media: Optional[Dict[str, Any]] = None


class OpenStatesOfficialsAPI:
    """Service for interacting with the OpenStates API for officials data"""

    def __init__(self):
        """Initialize the OpenStates Officials API client"""
        self.base_url = "https://v3.openstates.org"
        self.api_key = settings.OPENSTATES_API_KEY

        if not self.api_key:
            logger.warning("OPENSTATES_API_KEY not configured. OpenStates API features will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info("OpenStates Officials API client initialized successfully")

    def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Make a request to the OpenStates API.

        Args:
            endpoint: API endpoint path
            params: Query parameters

        Returns:
            Dict containing API response data or None if request fails
        """
        if not self.enabled:
            logger.error("OpenStates API is not enabled. Please configure OPENSTATES_API_KEY.")
            return None

        url = f"{self.base_url}/{endpoint}"

        headers = {
            "X-API-KEY": self.api_key,
            "Accept": "application/json"
        }

        try:
            logger.debug(f"Making OpenStates API request: {url} with params: {params}")
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            logger.debug(f"OpenStates API request successful: {len(data.get('results', []))} results")
            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"OpenStates API request failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in OpenStates API request: {e}")
            return None

    def _zip_to_coordinates(self, zip_code: str) -> Optional[tuple[float, float]]:
        """
        Convert zip code to latitude/longitude coordinates using real geocoding.

        Uses the free Nominatim geocoding service (OpenStreetMap) to convert
        ANY US zip code to coordinates. This works for ALL zip codes.

        Args:
            zip_code: 5-digit US zip code

        Returns:
            Tuple of (latitude, longitude) or None if not found
        """
        import requests

        try:
            # Use Nominatim (OpenStreetMap) free geocoding service
            # This works for ANY zip code in the US
            url = "https://nominatim.openstreetmap.org/search"
            params = {
                'q': f"{zip_code}, USA",
                'format': 'json',
                'limit': 1,
                'countrycodes': 'us'
            }

            # Add a user agent as required by Nominatim
            headers = {
                'User-Agent': 'ModernAction-Officials-Lookup/1.0'
            }

            logger.debug(f"Geocoding zip code {zip_code} using Nominatim...")

            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()

            data = response.json()

            if data and len(data) > 0:
                result = data[0]
                lat = float(result['lat'])
                lon = float(result['lon'])
                logger.info(f"Successfully geocoded zip {zip_code} to coordinates: ({lat}, {lon})")
                return (lat, lon)
            else:
                logger.warning(f"No geocoding results found for zip code {zip_code}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Geocoding request failed for zip {zip_code}: {e}")
            return None
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing geocoding response for zip {zip_code}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error geocoding zip {zip_code}: {e}")
            return None

    def get_officials_by_zip(self, zip_code: str) -> List[OpenStatesOfficial]:
        """
        Get officials for a given zip code.

        Args:
            zip_code: 5-digit US zip code

        Returns:
            List of OpenStatesOfficial objects
        """
        if not self.enabled:
            logger.error("OpenStates API is not enabled")
            return []

        # Convert zip code to coordinates
        coords = self._zip_to_coordinates(zip_code)
        if not coords:
            logger.warning(f"Could not get coordinates for zip code {zip_code}")
            return []

        lat, lng = coords

        # Get officials using geographic lookup
        params = {
            "lat": lat,
            "lng": lng
        }

        data = self._make_request("people.geo", params)
        if not data:
            logger.warning(f"No data returned from OpenStates API for zip {zip_code}")
            return []

        results = data.get('results', [])
        logger.info(f"Found {len(results)} officials for zip code {zip_code} (coordinates: {lat}, {lng})")
        logger.info(f"EMERGENCY DEPLOYMENT V4: Container deployed at {datetime.now()}")
        logger.info(f"OPENSTATES INTEGRATION: Force ECS restart after timeout")

        officials = []
        for person_data in results:
            try:
                official = self._parse_official_data(person_data, zip_code)
                if official:
                    officials.append(official)
            except Exception as e:
                logger.error(f"Error parsing official data: {e}")
                continue

        return officials

    def _parse_official_data(self, person_data: Dict[str, Any], zip_code: str) -> Optional[OpenStatesOfficial]:
        """
        Parse OpenStates person data into our OpenStatesOfficial format.

        Args:
            person_data: Raw person data from OpenStates API
            zip_code: The zip code that was queried

        Returns:
            OpenStatesOfficial object or None if parsing fails
        """
        try:
            current_role = person_data.get('current_role', {})

            # Determine level (federal vs state)
            jurisdiction = current_role.get('jurisdiction', {})
            jurisdiction_name = jurisdiction.get('name', '').lower()
            level = 'federal' if 'united states' in jurisdiction_name else 'state'

            # Extract basic info
            name = person_data.get('name', '')
            party = person_data.get('party', '')
            title = current_role.get('title', '')
            district = current_role.get('district', '')
            chamber = current_role.get('chamber', '')

            # Extract state info
            state = None
            if level == 'federal':
                # For federal officials, extract state from district (e.g., "CA-12" -> "CA")
                if district and '-' in district:
                    state = district.split('-')[0]
                elif district and len(district) == 2:
                    state = district
            else:
                # For state officials, get from jurisdiction
                state = jurisdiction.get('name', '')

            # Parse contact details
            contact_details = person_data.get('contact_details', [])
            email = None
            phone = None

            for contact in contact_details:
                contact_type = contact.get('type', '').lower()
                value = contact.get('value', '')

                if contact_type == 'email' and not email:
                    email = value
                elif contact_type in ['phone', 'voice'] and not phone:
                    phone = value

            # Parse social media links
            links = person_data.get('links', [])
            twitter_handle = None
            facebook_url = None
            instagram_handle = None
            website = None

            social_media = {}

            for link in links:
                url = link.get('url', '')
                note = link.get('note', '').lower()

                if 'twitter' in note or 'twitter.com' in url:
                    # Extract Twitter handle from URL
                    if 'twitter.com/' in url:
                        handle = url.split('twitter.com/')[-1].split('/')[0].split('?')[0]
                        if handle and handle != 'intent':
                            twitter_handle = handle
                            social_media['twitter'] = url
                elif 'facebook' in note or 'facebook.com' in url:
                    facebook_url = url
                    social_media['facebook'] = url
                elif 'instagram' in note or 'instagram.com' in url:
                    if 'instagram.com/' in url:
                        handle = url.split('instagram.com/')[-1].split('/')[0].split('?')[0]
                        if handle:
                            instagram_handle = handle
                            social_media['instagram'] = url
                elif 'website' in note or 'official' in note:
                    website = url
                    social_media['website'] = url

            # Get photo URL
            photo_url = person_data.get('image', '')

            # Get bio
            bio = person_data.get('biography', '')

            # Get office address from offices
            offices = person_data.get('offices', [])
            office_address = None
            if offices:
                office_address = offices[0].get('address', '')

            return OpenStatesOfficial(
                id=person_data.get('id', ''),
                name=name,
                party=party if party else None,
                title=title,
                district=district if district else None,
                chamber=chamber if chamber else None,
                level=level,
                state=state,
                email=email,
                phone=phone,
                website=website,
                twitter_handle=twitter_handle,
                facebook_url=facebook_url,
                instagram_handle=instagram_handle,
                photo_url=photo_url if photo_url else None,
                bio=bio if bio else None,
                office_address=office_address,
                social_media=social_media if social_media else None
            )

        except Exception as e:
            logger.error(f"Error parsing official data: {e}")
            return None

    def health_check(self) -> Dict[str, Any]:
        """
        Check the health of the OpenStates API connection.

        Returns:
            Dict containing health status information
        """
        if not self.enabled:
            return {
                "status": "disabled",
                "message": "OPENSTATES_API_KEY not configured",
                "enabled": False
            }

        try:
            # Test with a simple request
            data = self._make_request("people", {"jurisdiction": "us", "per_page": 1})

            if data:
                return {
                    "status": "healthy",
                    "message": "OpenStates API is accessible",
                    "enabled": True,
                    "test_response_count": len(data.get('results', []))
                }
            else:
                return {
                    "status": "error",
                    "message": "OpenStates API returned no data",
                    "enabled": True
                }

        except Exception as e:
            return {
                "status": "error",
                "message": f"OpenStates API health check failed: {e}",
                "enabled": True
            }


def get_openstates_officials_client() -> OpenStatesOfficialsAPI:
    """Get OpenStates Officials API client instance"""
    return OpenStatesOfficialsAPI()
