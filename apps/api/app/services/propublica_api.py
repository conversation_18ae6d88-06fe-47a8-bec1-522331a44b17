# app/services/propublica_api.py
"""
ProPublica Congress API service for fetching real legislative data.

This service provides access to the ProPublica Congress API to fetch
current bills, their status, full text, and other legislative information.
"""

import requests
import logging
from typing import Dict, Any, Optional, List
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ProPublicaCongressAPI:
    """Service for interacting with the ProPublica Congress API"""

    def __init__(self):
        """Initialize the ProPublica Congress API client"""
        self.base_url = "https://api.propublica.org/congress/v1"
        self.api_key = settings.PROPUBLICA_CONGRESS_API_KEY

        if not self.api_key:
            logger.warning("PROPUBLICA_CONGRESS_API_KEY not configured. ProPublica API features will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info("ProPublica Congress API client initialized successfully")

    def _make_request(self, endpoint: str) -> Optional[Dict[str, Any]]:
        """
        Make a request to the ProPublica Congress API.

        Args:
            endpoint: API endpoint path

        Returns:
            Dict containing API response data or None if request fails
        """
        if not self.enabled:
            logger.error("ProPublica API is not enabled. Please configure PROPUBLICA_CONGRESS_API_KEY.")
            return None

        url = f"{self.base_url}/{endpoint}"
        headers = {
            "X-API-Key": self.api_key,
            "Accept": "application/json"
        }

        try:
            logger.debug(f"Making request to ProPublica API: {endpoint}")
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            data = response.json()
            logger.debug("Successfully fetched data from ProPublica API")
            return data

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch data from ProPublica API: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching data from ProPublica API: {e}")
            return None

    def get_bill_by_number(self, congress: int, chamber: str, bill_number: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific bill by its number.

        Args:
            congress: Congress number (e.g., 118 for current Congress)
            chamber: 'house' or 'senate'
            bill_number: Bill number (e.g., 'hr5', 's1234')

        Returns:
            Dict containing bill data or None if not found
        """
        endpoint = f"{congress}/{chamber}/bills/{bill_number}.json"
        response = self._make_request(endpoint)

        if response and response.get('status') == 'OK':
            results = response.get('results', [])
            if results:
                return results[0]  # Return the first (and should be only) result

        return None

    def get_recent_bills(self, congress: int = 118, chamber: str = "both", bill_type: str = "introduced") -> List[Dict[str, Any]]:
        """
        Get recent bills from Congress.

        Args:
            congress: Congress number (default: 118 for current Congress)
            chamber: 'house', 'senate', or 'both'
            bill_type: 'introduced', 'updated', 'active', 'passed', 'enacted', 'vetoed'

        Returns:
            List of bill dictionaries
        """
        if chamber == "both":
            # Get bills from both chambers
            house_bills = self._get_bills_by_chamber(congress, "house", bill_type)
            senate_bills = self._get_bills_by_chamber(congress, "senate", bill_type)
            return house_bills + senate_bills
        else:
            return self._get_bills_by_chamber(congress, chamber, bill_type)

    def _get_bills_by_chamber(self, congress: int, chamber: str, bill_type: str) -> List[Dict[str, Any]]:
        """Get bills from a specific chamber"""
        endpoint = f"{congress}/{chamber}/bills/{bill_type}.json"
        response = self._make_request(endpoint)

        if response and response.get('status') == 'OK':
            results = response.get('results', [])
            if results:
                return results[0].get('bills', [])

        return []

    def get_bill_full_text_url(self, bill_data: Dict[str, Any]) -> Optional[str]:
        """
        Extract the full text URL from bill data.

        Args:
            bill_data: Bill data from ProPublica API

        Returns:
            URL to the full text of the bill or None if not available
        """
        # ProPublica provides links to Congress.gov for full text
        if 'congressdotgov_url' in bill_data:
            # Convert Congress.gov URL to text URL
            congress_url = bill_data['congressdotgov_url']
            # Add /text to get the full text page
            if congress_url and not congress_url.endswith('/text'):
                return f"{congress_url}/text"
            return congress_url

        return None

    def parse_bill_number(self, bill_number: str) -> Dict[str, str]:
        """
        Parse a bill number into its components.

        Args:
            bill_number: Bill number like "H.R.5", "S.1234", etc.

        Returns:
            Dict with 'chamber', 'type', and 'number' keys
        """
        bill_number = bill_number.upper().replace(".", "").replace(" ", "")

        if bill_number.startswith("HR"):
            return {
                "chamber": "house",
                "type": "hr",
                "number": bill_number[2:]
            }
        elif bill_number.startswith("S"):
            return {
                "chamber": "senate",
                "type": "s",
                "number": bill_number[1:]
            }
        elif bill_number.startswith("HRES"):
            return {
                "chamber": "house",
                "type": "hres",
                "number": bill_number[4:]
            }
        elif bill_number.startswith("SRES"):
            return {
                "chamber": "senate",
                "type": "sres",
                "number": bill_number[4:]
            }
        else:
            # Default to house bill
            return {
                "chamber": "house",
                "type": "hr",
                "number": bill_number
            }

    def health_check(self) -> Dict[str, Any]:
        """
        Check the health of the ProPublica API connection.

        Returns:
            Dict containing health status information
        """
        if not self.enabled:
            return {
                "status": "disabled",
                "message": "ProPublica API key not configured",
                "enabled": False
            }

        try:
            # Test with a simple request for recent House bills
            response = self._make_request("118/house/bills/introduced.json")

            if response and response.get('status') == 'OK':
                return {
                    "status": "healthy",
                    "message": "ProPublica API is accessible",
                    "enabled": True,
                    "test_response_status": response.get('status')
                }
            else:
                return {
                    "status": "error",
                    "message": "ProPublica API returned unexpected response",
                    "enabled": True,
                    "response": response
                }

        except Exception as e:
            return {
                "status": "error",
                "message": f"ProPublica API health check failed: {str(e)}",
                "enabled": True,
                "error": str(e)
            }


# Convenience function for easy import
def get_propublica_client() -> ProPublicaCongressAPI:
    """Get a ProPublica Congress API client instance"""
    return ProPublicaCongressAPI()
