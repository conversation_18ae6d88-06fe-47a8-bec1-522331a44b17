# app/services/simple_action_tracking.py
"""
Simple action tracking service - KISS approach.

This service provides the minimum viable functionality for action tracking:
1. Track user reasoning for actions
2. Basic error logging
3. Simple analytics queries

No complex aggregations, encryption, or real-time updates. 
Just the essentials that provide immediate value.
"""

import logging
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import func
import uuid

from app.models.action_tracking import (
    ReasoningOption, ActionReasoning, CustomReasonsPool, ActionError
)
from app.models.action import Action, ActionStatus

logger = logging.getLogger(__name__)


class SimpleActionTracking:
    """Simple action tracking service focused on essentials"""

    def __init__(self, db: Session):
        self.db = db

    def track_user_reasoning(self, action_id: str, selected_reasons: List[str] = None, 
                           custom_reason: str = None) -> bool:
        """
        Track user's reasoning for an action - simple version
        
        Args:
            action_id: Action ID
            selected_reasons: List of reasoning option IDs
            custom_reason: User's custom reason text
            
        Returns:
            bool: Success status
        """
        try:
            # Get the action to get bill_id, user_id, stance
            action = self.db.query(Action).filter(Action.id == action_id).first()
            if not action:
                logger.error(f"Action {action_id} not found")
                return False

            # Track selected predefined reasons
            if selected_reasons:
                for reason_id in selected_reasons:
                    reasoning = ActionReasoning(
                        id=str(uuid.uuid4()),
                        action_id=action_id,
                        reasoning_option_id=reason_id
                    )
                    self.db.add(reasoning)

            # Track custom reason
            if custom_reason and custom_reason.strip():
                custom = CustomReasonsPool(
                    id=str(uuid.uuid4()),
                    action_id=action_id,
                    user_id=action.user_id,
                    bill_id=action.bill_id,
                    stance=action.position or 'support',
                    custom_reason=custom_reason.strip()
                )
                self.db.add(custom)

            self.db.commit()
            logger.info(f"Tracked reasoning for action {action_id}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to track reasoning for action {action_id}: {e}")
            return False

    def log_simple_error(self, action_id: str, error_message: str, error_type: str = "unknown") -> bool:
        """
        Log a simple error for an action
        
        Args:
            action_id: Action ID
            error_message: Error message
            error_type: Type of error (api_error, network_error, etc.)
            
        Returns:
            bool: Success status
        """
        try:
            error = ActionError(
                id=str(uuid.uuid4()),
                action_id=action_id,
                error_type=error_type,
                error_message=error_message
            )
            self.db.add(error)
            
            # Update action status to failed
            action = self.db.query(Action).filter(Action.id == action_id).first()
            if action:
                action.status = ActionStatus.FAILED
                action.error_message = error_message

            self.db.commit()
            logger.error(f"Logged error for action {action_id}: {error_message}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to log error for action {action_id}: {e}")
            return False

    def get_bill_stats(self, bill_id: str) -> Dict[str, int]:
        """
        Get simple support/oppose stats for a bill
        
        Args:
            bill_id: Bill ID
            
        Returns:
            Dict with support, oppose, amend counts
        """
        try:
            stats = self.db.query(
                Action.position,
                func.count(Action.id).label('count')
            ).filter(
                Action.bill_id == bill_id,
                Action.status.in_([ActionStatus.SENT, ActionStatus.DELIVERED])
            ).group_by(Action.position).all()

            result = {'support': 0, 'oppose': 0, 'amend': 0, 'total': 0}
            
            for stat in stats:
                if stat.position in result:
                    result[stat.position] = stat.count
                    result['total'] += stat.count

            return result

        except Exception as e:
            logger.error(f"Failed to get bill stats for {bill_id}: {e}")
            return {'support': 0, 'oppose': 0, 'amend': 0, 'total': 0}

    def get_top_reasons(self, bill_id: str, stance: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get top predefined reasons for a bill/stance
        
        Args:
            bill_id: Bill ID
            stance: support, oppose, or amend
            limit: Number of reasons to return
            
        Returns:
            List of reason dicts with text and count
        """
        try:
            reasons = self.db.query(
                ReasoningOption.reason_text,
                func.count(ActionReasoning.id).label('count')
            ).join(ActionReasoning).join(Action).filter(
                Action.bill_id == bill_id,
                Action.position == stance,
                Action.status.in_([ActionStatus.SENT, ActionStatus.DELIVERED])
            ).group_by(
                ReasoningOption.id, ReasoningOption.reason_text
            ).order_by(
                func.count(ActionReasoning.id).desc()
            ).limit(limit).all()

            return [
                {'reason_text': r.reason_text, 'count': r.count}
                for r in reasons
            ]

        except Exception as e:
            logger.error(f"Failed to get top reasons for {bill_id}/{stance}: {e}")
            return []

    def get_custom_reasons(self, bill_id: str, stance: str, limit: int = 10) -> List[str]:
        """
        Get recent custom reasons for a bill/stance
        
        Args:
            bill_id: Bill ID
            stance: support, oppose, or amend
            limit: Number of reasons to return
            
        Returns:
            List of custom reason texts
        """
        try:
            reasons = self.db.query(
                CustomReasonsPool.custom_reason
            ).filter(
                CustomReasonsPool.bill_id == bill_id,
                CustomReasonsPool.stance == stance,
                CustomReasonsPool.is_flagged == False  # Only non-flagged reasons
            ).order_by(
                CustomReasonsPool.created_at.desc()
            ).limit(limit).all()

            return [r.custom_reason for r in reasons]

        except Exception as e:
            logger.error(f"Failed to get custom reasons for {bill_id}/{stance}: {e}")
            return []

    def get_reasoning_options(self, bill_id: str, stance: str) -> List[Dict[str, Any]]:
        """
        Get available predefined reasoning options for a bill/stance
        
        Args:
            bill_id: Bill ID
            stance: support, oppose, or amend
            
        Returns:
            List of reasoning option dicts
        """
        try:
            options = self.db.query(ReasoningOption).filter(
                ReasoningOption.bill_id == bill_id,
                ReasoningOption.stance == stance,
                ReasoningOption.is_active == True
            ).order_by(ReasoningOption.display_order).all()

            return [
                {
                    'id': option.id,
                    'reason_text': option.reason_text,
                    'usage_count': option.usage_count
                }
                for option in options
            ]

        except Exception as e:
            logger.error(f"Failed to get reasoning options for {bill_id}/{stance}: {e}")
            return []

    def add_reasoning_options(self, bill_id: str, stance: str, reasons: List[str]) -> bool:
        """
        Add predefined reasoning options for a bill (simple version)
        
        Args:
            bill_id: Bill ID
            stance: support, oppose, or amend
            reasons: List of reason texts
            
        Returns:
            bool: Success status
        """
        try:
            for i, reason_text in enumerate(reasons):
                option = ReasoningOption(
                    id=str(uuid.uuid4()),
                    bill_id=bill_id,
                    stance=stance,
                    reason_text=reason_text,
                    display_order=i
                )
                self.db.add(option)

            self.db.commit()
            logger.info(f"Added {len(reasons)} reasoning options for {bill_id}/{stance}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to add reasoning options for {bill_id}/{stance}: {e}")
            return False

    def get_action_summary(self, action_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a simple summary of an action with its reasoning
        
        Args:
            action_id: Action ID
            
        Returns:
            Dict with action details and reasoning, or None if not found
        """
        try:
            action = self.db.query(Action).filter(Action.id == action_id).first()
            if not action:
                return None

            # Get selected reasons
            selected_reasons = self.db.query(
                ReasoningOption.reason_text
            ).join(ActionReasoning).filter(
                ActionReasoning.action_id == action_id
            ).all()

            # Get custom reason
            custom_reason = self.db.query(
                CustomReasonsPool.custom_reason
            ).filter(
                CustomReasonsPool.action_id == action_id
            ).first()

            return {
                'action_id': action.id,
                'bill_id': action.bill_id,
                'user_id': action.user_id,
                'stance': action.position,
                'status': action.status.value,
                'selected_reasons': [r.reason_text for r in selected_reasons],
                'custom_reason': custom_reason.custom_reason if custom_reason else None,
                'created_at': action.created_at.isoformat() if action.created_at else None
            }

        except Exception as e:
            logger.error(f"Failed to get action summary for {action_id}: {e}")
            return None


# Convenience function
def get_simple_tracking(db: Session) -> SimpleActionTracking:
    """Get a simple action tracking service instance"""
    return SimpleActionTracking(db)