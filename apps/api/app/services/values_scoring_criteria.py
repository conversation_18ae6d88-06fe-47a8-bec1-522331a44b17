# app/services/values_scoring_criteria.py
"""
Comprehensive scoring criteria for bill values analysis.

This module defines the detailed rubric for scoring bills on a 1-10 scale across
three core values: Democracy, Human Rights, and Environmental Justice.

The scoring system uses neutral, factual language to avoid partisan appearance
while maintaining clear criteria for consistent analysis.
"""

from typing import Dict, List, Tuple
from dataclasses import dataclass


@dataclass
class ScoringCriteria:
    """Defines scoring criteria for a specific category and score level."""
    score_range: Tuple[int, int]  # (min, max) score range
    threat_indicators: List[str]  # What indicates a threat at this level
    support_indicators: List[str]  # What indicates support at this level
    neutral_language: str  # How to describe this level neutrally
    examples: List[str]  # Real-world bill examples


class ValuesScoring:
    """
    Comprehensive scoring system for bill values analysis.
    
    Scores range from 1-10 where:
    - 1-3: Low impact/threat/support
    - 4-6: Medium impact/threat/support  
    - 7-8: High impact/threat/support
    - 9-10: Critical/transformative impact
    """
    
    # Democracy Protection Scoring Criteria
    DEMOCRACY_CRITERIA = {
        "threat": {
            1: ScoringCriteria(
                score_range=(1, 3),
                threat_indicators=[
                    "Minor procedural changes to voting processes",
                    "Small adjustments to election administration",
                    "Limited impact on voter access"
                ],
                support_indicators=[],
                neutral_language="Minor adjustments to democratic procedures",
                examples=[
                    "Bill changing polling location signage requirements",
                    "Technical updates to voter registration forms"
                ]
            ),
            4: ScoringCriteria(
                score_range=(4, 6),
                threat_indicators=[
                    "Moderate restrictions on voting access",
                    "Changes to voter ID requirements",
                    "Redistricting with potential partisan impact",
                    "Campaign finance regulation changes"
                ],
                support_indicators=[],
                neutral_language="Moderate changes to voting procedures",
                examples=[
                    "Voter ID requirement bills",
                    "Changes to early voting periods",
                    "Campaign contribution limit modifications"
                ]
            ),
            7: ScoringCriteria(
                score_range=(7, 8),
                threat_indicators=[
                    "Significant voting access restrictions",
                    "Major changes to election oversight",
                    "Substantial redistricting changes",
                    "Limits on voter registration drives"
                ],
                support_indicators=[],
                neutral_language="Significant changes to electoral processes",
                examples=[
                    "Bills restricting mail-in voting access",
                    "Major redistricting legislation",
                    "Limits on third-party voter registration"
                ]
            ),
            9: ScoringCriteria(
                score_range=(9, 10),
                threat_indicators=[
                    "Fundamental changes to voting rights",
                    "Major restrictions on democratic participation",
                    "Significant alterations to election integrity measures",
                    "Constitutional-level democratic process changes"
                ],
                support_indicators=[],
                neutral_language="Fundamental changes to democratic institutions",
                examples=[
                    "Constitutional amendments affecting voting rights",
                    "Major overhauls of election systems",
                    "Significant restrictions on ballot access"
                ]
            )
        },
        "support": {
            1: ScoringCriteria(
                score_range=(1, 3),
                threat_indicators=[],
                support_indicators=[
                    "Minor improvements to voting accessibility",
                    "Small enhancements to election transparency",
                    "Technical improvements to voting systems"
                ],
                neutral_language="Minor enhancements to civic participation",
                examples=[
                    "Bills improving polling place accessibility",
                    "Technical updates to voting machines"
                ]
            ),
            4: ScoringCriteria(
                score_range=(4, 6),
                threat_indicators=[],
                support_indicators=[
                    "Moderate expansion of voting access",
                    "Enhanced election transparency measures",
                    "Improved voter registration processes",
                    "Campaign finance transparency improvements"
                ],
                neutral_language="Moderate improvements to democratic processes",
                examples=[
                    "Automatic voter registration bills",
                    "Extended early voting periods",
                    "Enhanced campaign finance disclosure"
                ]
            ),
            7: ScoringCriteria(
                score_range=(7, 8),
                threat_indicators=[],
                support_indicators=[
                    "Significant voting access expansion",
                    "Major transparency improvements",
                    "Substantial democratic participation enhancements",
                    "Strong anti-corruption measures"
                ],
                neutral_language="Significant enhancements to civic engagement",
                examples=[
                    "Comprehensive voting rights restoration",
                    "Major campaign finance reform",
                    "Significant redistricting reform"
                ]
            ),
            9: ScoringCriteria(
                score_range=(9, 10),
                threat_indicators=[],
                support_indicators=[
                    "Transformative democratic reforms",
                    "Constitutional-level democratic protections",
                    "Fundamental voting rights expansions",
                    "Major institutional democracy strengthening"
                ],
                neutral_language="Transformative democratic institutional changes",
                examples=[
                    "Constitutional voting rights amendments",
                    "Comprehensive democracy reform packages",
                    "Major institutional anti-corruption overhauls"
                ]
            )
        }
    }
    
    # Human Rights Scoring Criteria
    HUMAN_RIGHTS_CRITERIA = {
        "threat": {
            1: ScoringCriteria(
                score_range=(1, 3),
                threat_indicators=[
                    "Minor restrictions on specific civil liberties",
                    "Small changes to existing protections",
                    "Limited impact on individual rights"
                ],
                support_indicators=[],
                neutral_language="Minor adjustments to civil procedures",
                examples=[
                    "Small changes to permit requirements",
                    "Minor adjustments to existing regulations"
                ]
            ),
            4: ScoringCriteria(
                score_range=(4, 6),
                threat_indicators=[
                    "Moderate restrictions on civil liberties",
                    "Changes to anti-discrimination protections",
                    "Healthcare access limitations",
                    "Education access modifications"
                ],
                support_indicators=[],
                neutral_language="Moderate changes to civil procedures",
                examples=[
                    "Changes to healthcare coverage requirements",
                    "Modifications to education funding formulas",
                    "Updates to employment discrimination laws"
                ]
            ),
            7: ScoringCriteria(
                score_range=(7, 8),
                threat_indicators=[
                    "Significant civil liberties restrictions",
                    "Major changes to equality protections",
                    "Substantial healthcare access impacts",
                    "Significant privacy right modifications"
                ],
                support_indicators=[],
                neutral_language="Significant changes to civil procedures",
                examples=[
                    "Major healthcare access restrictions",
                    "Significant changes to privacy laws",
                    "Substantial modifications to equality protections"
                ]
            ),
            9: ScoringCriteria(
                score_range=(9, 10),
                threat_indicators=[
                    "Fundamental civil rights restrictions",
                    "Constitutional-level rights modifications",
                    "Major equality protection rollbacks",
                    "Transformative restrictions on individual liberties"
                ],
                support_indicators=[],
                neutral_language="Fundamental changes to civil rights framework",
                examples=[
                    "Constitutional amendments affecting civil rights",
                    "Major overhauls of anti-discrimination law",
                    "Fundamental changes to privacy rights"
                ]
            )
        },
        "support": {
            1: ScoringCriteria(
                score_range=(1, 3),
                threat_indicators=[],
                support_indicators=[
                    "Minor civil rights enhancements",
                    "Small improvements to existing protections",
                    "Limited expansion of individual rights"
                ],
                neutral_language="Minor enhancements to civil procedures",
                examples=[
                    "Small improvements to accessibility requirements",
                    "Minor enhancements to existing protections"
                ]
            ),
            4: ScoringCriteria(
                score_range=(4, 6),
                threat_indicators=[],
                support_indicators=[
                    "Moderate civil rights expansions",
                    "Enhanced anti-discrimination protections",
                    "Improved healthcare access",
                    "Education access improvements"
                ],
                neutral_language="Moderate improvements to civil procedures",
                examples=[
                    "Enhanced healthcare coverage expansions",
                    "Improved education access programs",
                    "Strengthened employment protections"
                ]
            ),
            7: ScoringCriteria(
                score_range=(7, 8),
                threat_indicators=[],
                support_indicators=[
                    "Significant civil rights expansions",
                    "Major equality protection enhancements",
                    "Substantial healthcare access improvements",
                    "Strong privacy right protections"
                ],
                neutral_language="Significant enhancements to civil procedures",
                examples=[
                    "Comprehensive healthcare access expansion",
                    "Major equality protection legislation",
                    "Significant privacy right enhancements"
                ]
            ),
            9: ScoringCriteria(
                score_range=(9, 10),
                threat_indicators=[],
                support_indicators=[
                    "Transformative civil rights expansions",
                    "Constitutional-level rights protections",
                    "Fundamental equality enhancements",
                    "Major institutional civil rights reforms"
                ],
                neutral_language="Transformative civil rights enhancements",
                examples=[
                    "Constitutional civil rights amendments",
                    "Comprehensive equality legislation",
                    "Major institutional rights reforms"
                ]
            )
        }
    }
    
    # Environmental Justice Scoring Criteria  
    ENVIRONMENTAL_CRITERIA = {
        "threat": {
            1: ScoringCriteria(
                score_range=(1, 3),
                threat_indicators=[
                    "Minor regulatory adjustments",
                    "Small changes to environmental standards",
                    "Limited impact on environmental protections"
                ],
                support_indicators=[],
                neutral_language="Minor adjustments to environmental regulations",
                examples=[
                    "Small changes to permit processing timelines",
                    "Minor adjustments to reporting requirements"
                ]
            ),
            4: ScoringCriteria(
                score_range=(4, 6),
                threat_indicators=[
                    "Moderate environmental regulation changes",
                    "Changes to pollution standards",
                    "Modifications to conservation programs",
                    "Energy policy adjustments"
                ],
                support_indicators=[],
                neutral_language="Moderate changes to environmental policies",
                examples=[
                    "Changes to emission standards",
                    "Modifications to conservation funding",
                    "Updates to energy efficiency requirements"
                ]
            ),
            7: ScoringCriteria(
                score_range=(7, 8),
                threat_indicators=[
                    "Significant environmental deregulation",
                    "Major changes to pollution controls",
                    "Substantial conservation program impacts",
                    "Significant climate policy modifications"
                ],
                support_indicators=[],
                neutral_language="Significant changes to environmental frameworks",
                examples=[
                    "Major deregulation of environmental protections",
                    "Significant changes to climate policies",
                    "Substantial conservation program cuts"
                ]
            ),
            9: ScoringCriteria(
                score_range=(9, 10),
                threat_indicators=[
                    "Fundamental environmental protection rollbacks",
                    "Major institutional environmental changes",
                    "Transformative deregulation measures",
                    "Constitutional-level environmental policy changes"
                ],
                support_indicators=[],
                neutral_language="Fundamental changes to environmental institutions",
                examples=[
                    "Major overhauls of environmental agencies",
                    "Fundamental changes to environmental law",
                    "Constitutional environmental policy modifications"
                ]
            )
        },
        "support": {
            1: ScoringCriteria(
                score_range=(1, 3),
                threat_indicators=[],
                support_indicators=[
                    "Minor environmental improvements",
                    "Small enhancements to existing protections",
                    "Limited conservation expansions"
                ],
                neutral_language="Minor environmental enhancements",
                examples=[
                    "Small improvements to recycling programs",
                    "Minor enhancements to park funding"
                ]
            ),
            4: ScoringCriteria(
                score_range=(4, 6),
                threat_indicators=[],
                support_indicators=[
                    "Moderate environmental protections",
                    "Enhanced pollution controls",
                    "Improved conservation programs",
                    "Clean energy policy improvements"
                ],
                neutral_language="Moderate environmental improvements",
                examples=[
                    "Enhanced renewable energy incentives",
                    "Improved conservation funding",
                    "Strengthened pollution controls"
                ]
            ),
            7: ScoringCriteria(
                score_range=(7, 8),
                threat_indicators=[],
                support_indicators=[
                    "Significant environmental protections",
                    "Major conservation expansions",
                    "Substantial clean energy initiatives",
                    "Strong climate action measures"
                ],
                neutral_language="Significant environmental protections",
                examples=[
                    "Major clean energy legislation",
                    "Comprehensive conservation programs",
                    "Significant climate action bills"
                ]
            ),
            9: ScoringCriteria(
                score_range=(9, 10),
                threat_indicators=[],
                support_indicators=[
                    "Transformative environmental legislation",
                    "Constitutional environmental protections",
                    "Fundamental climate action measures",
                    "Major institutional environmental reforms"
                ],
                neutral_language="Transformative environmental initiatives",
                examples=[
                    "Constitutional environmental amendments",
                    "Comprehensive climate action legislation",
                    "Major environmental institutional reforms"
                ]
            )
        }
    }
    
    @classmethod
    def get_score_description(cls, category: str, score_type: str, score: int) -> str:
        """Get neutral language description for a given score."""
        criteria_map = {
            'democracy': cls.DEMOCRACY_CRITERIA,
            'human_rights': cls.HUMAN_RIGHTS_CRITERIA,
            'environment': cls.ENVIRONMENTAL_CRITERIA
        }
        
        criteria = criteria_map.get(category, {}).get(score_type, {})
        
        for level_score, level_criteria in criteria.items():
            min_score, max_score = level_criteria.score_range
            if min_score <= score <= max_score:
                return level_criteria.neutral_language
        
        return "Impact assessment pending"
    
    @classmethod
    def get_examples_for_testing(cls) -> Dict[str, List[str]]:
        """Get real-world examples for manual scoring validation."""
        examples = {
            'democracy_threat': [],
            'democracy_support': [],
            'human_rights_threat': [],
            'human_rights_support': [],
            'environment_threat': [],
            'environment_support': []
        }
        
        # Collect examples from all criteria
        for category_name, category_data in [
            ('democracy', cls.DEMOCRACY_CRITERIA),
            ('human_rights', cls.HUMAN_RIGHTS_CRITERIA),
            ('environment', cls.ENVIRONMENTAL_CRITERIA)
        ]:
            for score_type in ['threat', 'support']:
                key = f"{category_name}_{score_type}"
                for level_criteria in category_data[score_type].values():
                    examples[key].extend(level_criteria.examples)
        
        return examples
