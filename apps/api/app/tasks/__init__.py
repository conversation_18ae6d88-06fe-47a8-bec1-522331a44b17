# app/tasks/__init__.py
"""
Background tasks module for the ModernAction API.

This module contains background task definitions for various operations
like AI processing, bill analysis, and data synchronization.
"""

# Import task functions to make them available at package level
try:
    from .values_analysis_tasks import (
        task_analyze_bill_values,
        task_analyze_bills_batch,
        task_analyze_bills_without_values,
        task_reanalyze_bill_values
    )
except ImportError:
    # Handle import errors gracefully during development
    task_analyze_bill_values = None
    task_analyze_bills_batch = None
    task_analyze_bills_without_values = None
    task_reanalyze_bill_values = None

# Add missing task imports that are referenced in bills.py
try:
    from .bill_tasks import (
        task_generate_summary_for_bill,
        task_regenerate_summary_for_bill
    )
except ImportError:
    # Create placeholder functions if tasks don't exist
    def task_generate_summary_for_bill(*args, **kwargs):
        pass

    def task_regenerate_summary_for_bill(*args, **kwargs):
        pass

# Re-export for convenience
__all__ = [
    'task_analyze_bill_values',
    'task_analyze_bills_batch',
    'task_analyze_bills_without_values',
    'task_reanalyze_bill_values',
    'task_generate_summary_for_bill',
    'task_regenerate_summary_for_bill'
]
