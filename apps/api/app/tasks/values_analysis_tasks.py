# app/tasks/values_analysis_tasks.py
"""
Background tasks for bill values analysis.

This module contains tasks that can be run asynchronously to analyze bills
for values alignment without blocking the main application flow.
"""

import logging
from typing import Optional
from sqlalchemy.orm import Session

from app.db.database import get_db
from app.models.bill import Bill
from app.services.bill_values_analysis_service import BillValuesAnalysisService

logger = logging.getLogger(__name__)


async def task_analyze_bill_values(bill_id: str, db: Session = None) -> bool:
    """
    Background task to analyze a bill's values alignment.
    
    Args:
        bill_id: ID of the bill to analyze
        db: Database session (optional, will create if not provided)
        
    Returns:
        True if analysis completed successfully, False otherwise
    """
    if db is None:
        db = next(get_db())
    
    try:
        # Get the bill
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            logger.error(f"Bill {bill_id} not found for values analysis")
            return False
        
        # Check if analysis already exists
        values_service = BillValuesAnalysisService(db)
        existing_analysis = values_service.get_bill_analysis(bill_id)
        
        if existing_analysis:
            logger.info(f"Values analysis already exists for bill {bill_id}")
            return True
        
        # Perform analysis
        analysis = await values_service.analyze_bill_values(bill)
        db.commit()
        
        logger.info(f"Values analysis completed for bill {bill_id}")
        return True
        
    except Exception as e:
        logger.error(f"Values analysis task failed for bill {bill_id}: {e}")
        if db:
            db.rollback()
        return False
    finally:
        if db:
            db.close()


async def task_analyze_bills_batch(bill_ids: list[str], db: Session = None) -> dict:
    """
    Background task to analyze multiple bills' values alignment.
    
    Args:
        bill_ids: List of bill IDs to analyze
        db: Database session (optional, will create if not provided)
        
    Returns:
        Dict with success/failure counts and details
    """
    if db is None:
        db = next(get_db())
    
    results = {
        'total': len(bill_ids),
        'successful': 0,
        'failed': 0,
        'skipped': 0,
        'errors': []
    }
    
    try:
        values_service = BillValuesAnalysisService(db)
        
        for bill_id in bill_ids:
            try:
                # Get the bill
                bill = db.query(Bill).filter(Bill.id == bill_id).first()
                if not bill:
                    logger.warning(f"Bill {bill_id} not found for values analysis")
                    results['failed'] += 1
                    results['errors'].append(f"Bill {bill_id} not found")
                    continue
                
                # Check if analysis already exists
                existing_analysis = values_service.get_bill_analysis(bill_id)
                if existing_analysis:
                    logger.debug(f"Values analysis already exists for bill {bill_id}")
                    results['skipped'] += 1
                    continue
                
                # Perform analysis
                analysis = await values_service.analyze_bill_values(bill)
                results['successful'] += 1
                
                logger.info(f"Values analysis completed for bill {bill_id}")
                
            except Exception as e:
                logger.error(f"Values analysis failed for bill {bill_id}: {e}")
                results['failed'] += 1
                results['errors'].append(f"Bill {bill_id}: {str(e)}")
                continue
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Batch values analysis task failed: {e}")
        if db:
            db.rollback()
        results['errors'].append(f"Batch operation failed: {str(e)}")
    finally:
        if db:
            db.close()
    
    logger.info(f"Batch values analysis completed: {results['successful']} successful, "
                f"{results['failed']} failed, {results['skipped']} skipped")
    
    return results


async def task_analyze_bills_without_values() -> dict:
    """
    Background task to find and analyze all bills that don't have values analysis.
    
    Returns:
        Dict with analysis results
    """
    db = next(get_db())
    
    try:
        # Find bills without values analysis
        bills_without_analysis = db.query(Bill).outerjoin(
            Bill.values_analysis
        ).filter(
            Bill.values_analysis == None
        ).all()
        
        if not bills_without_analysis:
            logger.info("All bills already have values analysis")
            return {
                'total': 0,
                'successful': 0,
                'failed': 0,
                'skipped': 0,
                'errors': []
            }
        
        bill_ids = [bill.id for bill in bills_without_analysis]
        logger.info(f"Found {len(bill_ids)} bills without values analysis")
        
        # Analyze them in batch
        return await task_analyze_bills_batch(bill_ids, db)
        
    except Exception as e:
        logger.error(f"Failed to find bills without values analysis: {e}")
        return {
            'total': 0,
            'successful': 0,
            'failed': 1,
            'skipped': 0,
            'errors': [f"Query failed: {str(e)}"]
        }
    finally:
        if db:
            db.close()


async def task_reanalyze_bill_values(bill_id: str, force: bool = False, db: Session = None) -> bool:
    """
    Background task to re-analyze a bill's values (useful when criteria change).
    
    Args:
        bill_id: ID of the bill to re-analyze
        force: If True, re-analyze even if analysis exists
        db: Database session (optional, will create if not provided)
        
    Returns:
        True if analysis completed successfully, False otherwise
    """
    if db is None:
        db = next(get_db())
    
    try:
        # Get the bill
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            logger.error(f"Bill {bill_id} not found for values re-analysis")
            return False
        
        values_service = BillValuesAnalysisService(db)
        
        if not force:
            # Check if analysis already exists
            existing_analysis = values_service.get_bill_analysis(bill_id)
            if existing_analysis:
                logger.info(f"Values analysis already exists for bill {bill_id}, use force=True to re-analyze")
                return True
        
        # Perform analysis (this will update existing analysis if it exists)
        analysis = await values_service.analyze_bill_values(bill)
        db.commit()
        
        logger.info(f"Values re-analysis completed for bill {bill_id}")
        return True
        
    except Exception as e:
        logger.error(f"Values re-analysis task failed for bill {bill_id}: {e}")
        if db:
            db.rollback()
        return False
    finally:
        if db:
            db.close()


def task_generate_values_summary_for_bill(bill_id: str, db: Session = None) -> Optional[str]:
    """
    Background task to generate a neutral summary of a bill's values impact.
    
    Args:
        bill_id: ID of the bill
        db: Database session (optional, will create if not provided)
        
    Returns:
        Neutral summary string or None if failed
    """
    if db is None:
        db = next(get_db())
    
    try:
        # Get the bill and its analysis
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            logger.error(f"Bill {bill_id} not found for summary generation")
            return None
        
        values_service = BillValuesAnalysisService(db)
        analysis = values_service.get_bill_analysis(bill_id)
        
        if not analysis:
            logger.warning(f"No values analysis found for bill {bill_id}")
            return None
        
        # Generate neutral summary using the neutral language service
        from app.services.neutral_language_service import NeutralLanguageService
        
        summary = NeutralLanguageService.generate_summary_language(
            (analysis.democracy_threat_score, analysis.democracy_support_score),
            (analysis.human_rights_threat_score, analysis.human_rights_support_score),
            (analysis.environmental_threat_score, analysis.environmental_support_score)
        )
        
        logger.info(f"Values summary generated for bill {bill_id}")
        return summary
        
    except Exception as e:
        logger.error(f"Values summary generation failed for bill {bill_id}: {e}")
        return None
    finally:
        if db:
            db.close()
