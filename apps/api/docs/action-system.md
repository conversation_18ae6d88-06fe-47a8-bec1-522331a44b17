# Action System Documentation

## Overview

The Action System is the core feature of ModernAction that enables citizens to send emails to their government representatives about specific legislation. This system handles the complete flow from user input to email delivery.

## Architecture

### Frontend Components

#### ActionModal Component
- **Location**: `apps/web/src/components/shared/ActionModal.tsx`
- **Purpose**: Modal dialog for composing and sending messages to representatives
- **Features**:
  - Email validation with React Hook Form
  - Pre-populated message from campaign talking points
  - Representative preview with photos
  - Loading states and error handling
  - Accessibility with Headless UI

#### ActionForm Component
- **Location**: `apps/web/src/components/campaign/ActionForm.tsx`
- **Purpose**: Primary interface for taking action on campaigns
- **Features**:
  - Zip code input and validation
  - Representative lookup via API
  - Modal integration for message composition
  - Toast notifications for user feedback
  - Custom message options

### Backend Services

#### Action Model
- **Location**: `apps/api/app/models/action.py`
- **Purpose**: Database model for storing action records
- **Key Fields**:
  - User information (name, email, address, zip code)
  - Contact information (official's email, phone, address)
  - Action content (subject, message)
  - Status tracking (pending, sent, delivered, failed)
  - Delivery details (method, ID, timestamps)
  - Error handling (retry count, error messages)

#### Action Service
- **Location**: `apps/api/app/services/action.py`
- **Purpose**: Business logic for action management
- **Key Methods**:
  - `create_action()`: Creates new action records
  - `get_actions()`: Retrieves actions with filtering
  - `update_action()`: Updates action status and delivery info
  - `get_action_stats()`: Provides statistics and analytics
  - `retry_failed_action()`: Handles failed action retries

#### Email Service
- **Location**: `apps/api/app/services/email.py`
- **Purpose**: Handles email sending via AWS SES
- **Key Methods**:
  - `send_action_email()`: Sends formatted constituent emails
  - `send_email()`: Generic email sending
  - `health_check()`: Monitors SES service health
  - `get_send_statistics()`: Retrieves sending metrics

### API Endpoints

#### POST /api/v1/actions
- **Purpose**: Create new action and queue for sending
- **Response**: 202 Accepted (async processing)
- **Background Task**: Schedules email sending

#### GET /api/v1/actions
- **Purpose**: Retrieve actions with filtering
- **Parameters**: user_id, campaign_id, status, date range
- **Response**: List of action summaries

#### GET /api/v1/actions/{id}
- **Purpose**: Get detailed action information
- **Response**: Action with campaign and official details

#### POST /api/v1/actions/{id}/retry
- **Purpose**: Retry failed action
- **Response**: Updated action status

### Background Tasks

#### task_send_action_email
- **Location**: `apps/api/app/tasks.py`
- **Purpose**: Asynchronous email sending
- **Process**:
  1. Fetch action details from database
  2. Validate contact information
  3. Send email via EmailService
  4. Update action status based on result
  5. Handle errors and retries

## Email Format

### Structure
```
Subject: Re: [Bill Title] ([Bill Number])

Dear [Official Name],

[User's personalized message]

Sincerely,
[User Name]
[User Email]
[User Address]
[User Zip Code]

---
This message was sent through ModernAction.io on behalf of your constituent.
For questions about this platform, <NAME_EMAIL>.
```

### Compliance Features
- Clear sender identification
- Constituent verification through zip code
- Platform attribution
- Support contact information
- Bounce and complaint handling

## Data Flow

### User Action Flow
1. **User Input**: User enters zip code on campaign page
2. **Representative Lookup**: System fetches officials for zip code
3. **Modal Display**: ActionModal shows representatives and message form
4. **Form Submission**: User customizes message and submits
5. **Action Creation**: System creates action records for each official
6. **Background Processing**: Emails queued for asynchronous sending
7. **Email Delivery**: AWS SES sends emails to officials
8. **Status Updates**: Action status updated based on delivery results

### Database Relationships
```
User (1) → (N) Action (N) ← (1) Official
                 ↓
            (1) Campaign (1) ← (1) Bill
```

## Configuration

### Environment Variables
```env
# AWS SES Configuration
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=ModernAction
```

### Required AWS Permissions
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ses:SendEmail",
                "ses:SendRawEmail",
                "ses:GetSendQuota",
                "ses:GetSendStatistics"
            ],
            "Resource": "*"
        }
    ]
}
```

## Error Handling

### Frontend Error Handling
- **Network Errors**: Toast notifications for connection issues
- **Validation Errors**: Form field validation with clear messages
- **API Errors**: Graceful degradation with user-friendly messages
- **Loading States**: Clear indicators during async operations

### Backend Error Handling
- **Email Failures**: Automatic retry with exponential backoff
- **SES Errors**: Detailed error logging and status tracking
- **Database Errors**: Transaction rollback and error recovery
- **Rate Limiting**: Respect SES sending limits

### Common Error Scenarios
1. **Invalid Email Address**: Validation prevents submission
2. **SES Quota Exceeded**: Graceful queuing and retry
3. **Network Timeout**: Automatic retry with backoff
4. **Invalid Zip Code**: User feedback and correction prompt
5. **Missing Officials**: Clear messaging about coverage gaps

## Monitoring and Analytics

### Action Statistics
- **Success Rate**: Percentage of successfully sent emails
- **Delivery Time**: Average time from creation to delivery
- **Error Rates**: Breakdown of failure reasons
- **Campaign Performance**: Actions per campaign
- **User Engagement**: Repeat action rates

### Health Checks
- **SES Status**: Service availability and quotas
- **Database Health**: Connection and query performance
- **Background Tasks**: Queue status and processing times
- **API Response Times**: Endpoint performance metrics

## Security Considerations

### Data Protection
- **User Privacy**: Minimal data collection and retention
- **Email Security**: Encrypted transmission via AWS SES
- **Access Control**: API authentication and authorization
- **Audit Logging**: All actions logged for compliance

### Anti-Abuse Measures
- **Rate Limiting**: Prevent spam and abuse
- **Zip Code Validation**: Ensure legitimate constituent contacts
- **Content Filtering**: Basic spam detection
- **Blacklist Management**: Block problematic users/content

## Performance Optimization

### Frontend Performance
- **Component Lazy Loading**: Reduce initial bundle size
- **API Caching**: Cache representative data
- **Optimistic Updates**: Immediate UI feedback
- **Error Boundaries**: Graceful error recovery

### Backend Performance
- **Database Indexing**: Optimize action queries
- **Connection Pooling**: Efficient database connections
- **Background Processing**: Non-blocking email sending
- **Caching**: Redis for frequently accessed data

## Testing Strategy

### Unit Tests
- **Action Service**: Business logic validation
- **Email Service**: SES integration testing
- **Background Tasks**: Async processing verification
- **API Endpoints**: Request/response validation

### Integration Tests
- **End-to-End Flow**: Complete user journey
- **Email Delivery**: SES integration validation
- **Database Operations**: Action CRUD operations
- **Error Scenarios**: Failure handling verification

### Manual Testing
- **User Experience**: Modal interaction and flow
- **Email Formatting**: Actual email appearance
- **Representative Data**: Accuracy and completeness
- **Cross-Browser**: Compatibility testing

## Deployment Considerations

### Production Checklist
- [ ] AWS SES production access approved
- [ ] Domain verification completed
- [ ] DKIM and SPF records configured
- [ ] IAM policies and credentials secured
- [ ] Database migrations applied
- [ ] Background task workers running
- [ ] Monitoring and alerts configured
- [ ] Error tracking enabled

### Scaling Considerations
- **Email Volume**: SES sending limits and quotas
- **Background Workers**: Horizontal scaling for tasks
- **Database Performance**: Indexing and query optimization
- **CDN Integration**: Static asset delivery
- **Load Balancing**: Multiple API instances

## Future Enhancements

### Planned Features
- **Email Templates**: Rich HTML formatting
- **Delivery Tracking**: Read receipts and analytics
- **Bulk Actions**: Multi-campaign sending
- **Scheduled Sending**: Time-delayed delivery
- **Response Handling**: Official reply processing

### Integration Opportunities
- **CRM Systems**: Constituent relationship management
- **Analytics Platforms**: Advanced engagement tracking
- **Social Media**: Action sharing and amplification
- **Mobile Apps**: Native mobile experience
- **Third-Party APIs**: Additional representative data sources