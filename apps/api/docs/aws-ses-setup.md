# AWS SES Configuration Guide

This guide walks through setting up Amazon Simple Email Service (SES) for the ModernAction platform to send action emails to government officials.

## Prerequisites

1. AWS Account with appropriate permissions
2. Domain or email address for sending emails
3. IAM user with SES permissions

## Step 1: SES Setup in AWS Console

### 1.1 Verify Domain or Email Address

**For Production (Recommended): Domain Verification**
1. Go to AWS SES Console → Configuration → Verified identities
2. Click "Create identity"
3. Select "Domain" 
4. Enter your domain (e.g., `modernaction.io`)
5. Follow DNS verification steps:
   - Add TXT record to your domain's DNS
   - Wait for verification (can take up to 72 hours)

**For Development/Testing: Email Verification**
1. Go to AWS SES Console → Configuration → Verified identities
2. Click "Create identity"
3. Select "Email address"
4. Enter your email (e.g., `<EMAIL>`)
5. Check your email and click verification link

### 1.2 Request Production Access (Critical)

**WARNING**: SES starts in sandbox mode - you can only send to verified addresses!

1. Go to AWS SES Console → Configuration → Account dashboard
2. Click "Request production access"
3. Fill out the form:
   - **Use case**: Citizen engagement platform for contacting government officials
   - **Website URL**: Your platform URL
   - **Use case description**: 
     ```
     ModernAction is a civic engagement platform that helps citizens contact 
     their elected representatives about important legislation. We send 
     personalized emails from constituents to their representatives' official 
     email addresses. All emails are user-generated and relate to specific 
     bills and campaigns.
     ```
   - **Contact method**: Email
   - **Additional contacts**: Add relevant team members
   - **Acknowledge**: Check the compliance acknowledgment

4. Submit and wait for approval (usually 24-48 hours)

## Step 2: IAM User Setup

### 2.1 Create IAM User

1. Go to AWS IAM Console → Users
2. Click "Add users"
3. Username: `modernaction-ses`
4. Access type: "Programmatic access"
5. Click "Next: Permissions"

### 2.2 Create SES Policy

1. Click "Attach existing policies directly"
2. Click "Create policy"
3. Use JSON editor and paste:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ses:SendEmail",
                "ses:SendRawEmail",
                "ses:SendTemplatedEmail"
            ],
            "Resource": "*",
            "Condition": {
                "StringEquals": {
                    "ses:FromAddress": [
                        "<EMAIL>",
                        "<EMAIL>"
                    ]
                }
            }
        },
        {
            "Effect": "Allow",
            "Action": [
                "ses:GetSendQuota",
                "ses:GetSendStatistics",
                "ses:GetAccountSendingEnabled"
            ],
            "Resource": "*"
        }
    ]
}
```

4. Name: `ModernActionSESPolicy`
5. Attach to the user

### 2.3 Get Access Keys

1. Complete user creation
2. **IMPORTANT**: Download and securely store the Access Key ID and Secret Access Key
3. You cannot retrieve the Secret Access Key again!

## Step 3: Environment Configuration

### 3.1 Add to `.env` file

```env
# AWS SES Configuration
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=ModernAction
```

### 3.2 Add to AWS Secrets Manager (Production)

For production, store credentials in AWS Secrets Manager:

1. Go to AWS Secrets Manager Console
2. Store new secret → Other type of secret
3. Key/Value pairs:
   - `AWS_ACCESS_KEY_ID`: your_access_key
   - `AWS_SECRET_ACCESS_KEY`: your_secret_key
4. Secret name: `modernaction/ses/credentials`

## Step 4: Testing Configuration

### 4.1 Test Email Sending

Run the email service health check:

```bash
cd /apps/api
poetry run python -c "
from app.services.email import EmailService
service = EmailService()
result = service.health_check()
print('SES Health Check:', result)
"
```

### 4.2 Send Test Email

```bash
poetry run python -c "
from app.services.email import send_email
result = send_email(
    to_address='<EMAIL>',
    subject='Test Email',
    body='This is a test email from ModernAction.'
)
print('Email sent:', result)
"
```

## Step 5: Production Checklist

### 5.1 Monitoring Setup

- [ ] Set up CloudWatch alarms for bounce rate
- [ ] Set up CloudWatch alarms for complaint rate
- [ ] Monitor sending quotas

### 5.2 Email Configuration

- [ ] Configure DKIM signing
- [ ] Set up SPF records
- [ ] Configure DMARC policy
- [ ] Set up bounce and complaint handling

### 5.3 Security

- [ ] Rotate access keys regularly
- [ ] Use least privilege IAM policies
- [ ] Enable AWS CloudTrail for audit logging
- [ ] Set up MFA for AWS account access

## Step 6: Email Templates

### 6.1 Action Email Template

The system will send emails with this structure:

```
Subject: Re: [Bill Title] ([Bill Number])

Dear [Official Name],

[User's personalized message]

Sincerely,
[User Name]
[User Address]
[User Zip Code]

---
This message was sent through ModernAction.io on behalf of your constituent.
```

### 6.2 Compliance Features

- All emails include sender identification
- Bounce and complaint handling
- Unsubscribe mechanisms where required
- Rate limiting to prevent abuse

## Troubleshooting

### Common Issues

1. **"Email address not verified"**
   - Verify the sender email address in SES Console
   - Check spam folder for verification email

2. **"Sending quota exceeded"**
   - Check your sending limits in SES Console
   - Request limit increase if needed

3. **"Account is in sandbox mode"**
   - Request production access as described above
   - Wait for approval before sending to unverified addresses

4. **High bounce rate**
   - Check email addresses for validity
   - Implement email validation
   - Monitor bounce notifications

### Support Resources

- AWS SES Documentation: https://docs.aws.amazon.com/ses/
- SES Sending Limits: https://docs.aws.amazon.com/ses/latest/DeveloperGuide/manage-sending-limits.html
- SES Best Practices: https://docs.aws.amazon.com/ses/latest/DeveloperGuide/best-practices.html

## Security Considerations

1. **Never commit AWS credentials to version control**
2. **Use IAM roles in production when possible**
3. **Implement proper error handling for failed sends**
4. **Monitor for suspicious sending patterns**
5. **Implement rate limiting on the application level**

## Cost Optimization

- SES pricing: $0.10 per 1,000 emails
- Consider using SES in the same region as your application
- Monitor usage with CloudWatch metrics
- Implement email deduplication where appropriate