# Bills API Documentation

## Overview

The Bills API provides comprehensive CRUD operations for managing legislative bills in the ModernAction.io platform. It supports advanced search, filtering, external API integration, and proper handling of complex data types.

## Base URL

```
/api/v1/bills
```

## Authentication

Currently, the API is open for development. Authentication will be added in future iterations.

## Endpoints

### List Bills

**GET** `/api/v1/bills/`

Returns a paginated list of bills ordered by creation date (newest first).

**Query Parameters:**
- `skip` (int, optional): Number of bills to skip (default: 0)
- `limit` (int, optional): Maximum number of bills to return (default: 20, max: 100)

**Response:**
```json
[
  {
    "id": "uuid",
    "title": "Healthcare Reform Act",
    "bill_number": "HR-101",
    "bill_type": "house_bill",
    "status": "introduced",
    "session_year": 2024,
    "chamber": "house",
    "state": "federal",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

### Advanced Search

**GET** `/api/v1/bills/search`

Search bills with multiple criteria and filters.

**Query Parameters:**
- `query` (string, optional): Search term for title, description, summary, or bill number
- `bill_type` (enum, optional): Type of bill (house_bill, senate_bill, etc.)
- `status` (enum, optional): Bill status (introduced, committee, passed, etc.)
- `session_year` (int, optional): Legislative session year
- `chamber` (string, optional): Chamber (house, senate)
- `state` (string, optional): State abbreviation or 'federal'
- `sponsor_name` (string, optional): Sponsor name (partial match)
- `is_featured` (bool, optional): Filter by featured status
- `skip` (int, optional): Number of results to skip
- `limit` (int, optional): Maximum number of results to return

**Example:**
```
GET /api/v1/bills/search?query=healthcare&status=introduced&session_year=2024
```

### Get Bill by ID

**GET** `/api/v1/bills/{bill_id}`

Returns detailed information about a specific bill.

**Response:**
```json
{
  "id": "uuid",
  "title": "Healthcare Reform Act",
  "description": "A comprehensive bill to reform healthcare...",
  "bill_number": "HR-101",
  "bill_type": "house_bill",
  "status": "introduced",
  "session_year": 2024,
  "chamber": "house",
  "state": "federal",
  "summary": "This bill aims to...",
  "ai_summary": "AI-generated summary...",
  "sponsor_name": "John Doe",
  "sponsor_party": "Democrat",
  "sponsor_state": "CA",
  "is_featured": false,
  "priority_score": 75,
  "tags": ["healthcare", "reform"],
  "categories": ["health", "policy"],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Create Bill

**POST** `/api/v1/bills/`

Creates a new bill record.

**Request Body:**
```json
{
  "title": "New Healthcare Bill",
  "description": "Description of the bill",
  "bill_number": "HR-123",
  "bill_type": "house_bill",
  "status": "introduced",
  "session_year": 2024,
  "chamber": "house",
  "state": "federal",
  "summary": "Bill summary",
  "sponsor_name": "Jane Smith",
  "tags": ["healthcare", "reform"],
  "categories": ["health"],
  "openstates_id": "ocd-bill/12345",
  "congress_gov_id": "hr123-118"
}
```

### Update Bill

**PUT** `/api/v1/bills/{bill_id}`

Updates an existing bill. Only provided fields will be updated.

**Request Body:**
```json
{
  "title": "Updated Bill Title",
  "status": "committee",
  "summary": "Updated summary"
}
```

### Delete Bill

**DELETE** `/api/v1/bills/{bill_id}`

Permanently removes a bill from the database.

**Response:**
```json
{
  "message": "Bill deleted successfully"
}
```

## Specialized Endpoints

### Featured Bills

**GET** `/api/v1/bills/featured`

Returns bills marked as featured, ordered by priority score.

### Bills by Status

**GET** `/api/v1/bills/status/{status}`

Returns bills with the specified status.

**Status Values:**
- `draft`
- `introduced`
- `committee`
- `floor`
- `passed`
- `signed`
- `vetoed`
- `failed`

### Bills by Type

**GET** `/api/v1/bills/type/{bill_type}`

Returns bills of the specified type.

**Type Values:**
- `house_bill`
- `senate_bill`
- `house_resolution`
- `senate_resolution`
- `joint_resolution`
- `concurrent_resolution`

### Bills by Session Year

**GET** `/api/v1/bills/session/{session_year}`

Returns bills from the specified legislative session year.

### Bills by Sponsor

**GET** `/api/v1/bills/sponsor/{sponsor_name}`

Returns bills sponsored by the specified legislator (partial name match).

### External ID Lookup

**GET** `/api/v1/bills/external/{id_type}/{external_id}`

Returns a bill by external ID.

**Supported ID Types:**
- `openstates`: Open States API ID
- `congress_gov`: Congress.gov ID

**Example:**
```
GET /api/v1/bills/external/openstates/ocd-bill/12345
```

### Statistics

**GET** `/api/v1/bills/stats/count`

Returns the total number of bills in the database.

**Response:**
```json
{
  "total_bills": 1234
}
```

## Error Responses

### 400 Bad Request
```json
{
  "detail": "Invalid bill type. Must be one of: house_bill, senate_bill, ..."
}
```

### 404 Not Found
```json
{
  "detail": "Bill not found"
}
```

### 422 Validation Error
```json
{
  "detail": [
    {
      "loc": ["body", "title"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

## Data Types

### Bill Types
- `house_bill`: House Bill
- `senate_bill`: Senate Bill
- `house_resolution`: House Resolution
- `senate_resolution`: Senate Resolution
- `joint_resolution`: Joint Resolution
- `concurrent_resolution`: Concurrent Resolution

### Bill Status
- `draft`: Draft
- `introduced`: Introduced
- `committee`: In Committee
- `floor`: Floor Vote
- `passed`: Passed
- `signed`: Signed into Law
- `vetoed`: Vetoed
- `failed`: Failed

## Implementation Notes

### JSON Fields
Tags and categories are stored as JSON arrays in the database but returned as Python lists in API responses.

### External IDs
The API prevents duplicate bills by checking external IDs (OpenStates, Congress.gov) before creation.

### Pagination
All list endpoints support pagination with `skip` and `limit` parameters. Default limit is 20, maximum is 100.

### Search
Text search is case-insensitive and searches across title, description, summary, and bill number fields.

### Sorting
Results are sorted by priority score (descending) and creation date (newest first) for optimal user experience.
