#!/usr/bin/env python3
"""
Enhanced Bill Processing Command Line Tool - Implementation-2.MD

This tool allows processing individual bills with comprehensive AI analysis using OpenAI GPT-4.
Usage examples:
    python enhanced_seed.py --bill HR5 --session 118
    python enhanced_seed.py --bill S1234 --session 118
    python enhanced_seed.py --bill HR5 --session 118 --force  # Force reprocessing
"""

import asyncio
import argparse
import logging
import sys
import os
from typing import Dict, Any

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.enhanced_bill_service import get_enhanced_bill_service
from app.core.config import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def process_bill_command(bill_number: str, session: str = "118", force: bool = False) -> Dict[str, Any]:
    """
    Process a single bill with comprehensive AI analysis
    
    Args:
        bill_number: Bill number like "HR5", "S1234"
        session: Congress session (default: "118")
        force: Force reprocessing even if already processed
        
    Returns:
        Dict with processing results
    """
    try:
        logger.info(f"Starting enhanced bill processing: {bill_number} from session {session}")
        
        # Initialize the enhanced bill service
        bill_service = get_enhanced_bill_service()
        
        if force:
            logger.info("Force flag set - will reprocess even if bill exists")
        
        # Process the bill
        result = await bill_service.process_bill(bill_number, session)
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to process bill {bill_number}: {e}")
        return {
            'status': 'error',
            'bill_number': bill_number,
            'session': session,
            'error': str(e)
        }


async def check_bill_status(bill_id: int) -> Dict[str, Any]:
    """
    Check the processing status of a bill by ID
    
    Args:
        bill_id: Database ID of the bill
        
    Returns:
        Dict with bill status information
    """
    try:
        bill_service = get_enhanced_bill_service()
        return await bill_service.get_bill_status(bill_id)
        
    except Exception as e:
        logger.error(f"Failed to check bill status: {e}")
        return {'status': 'error', 'message': str(e)}


def print_result(result: Dict[str, Any]) -> None:
    """Print the processing result in a user-friendly format"""
    print("\n" + "="*70)
    print("ENHANCED BILL PROCESSING RESULT")
    print("="*70)
    
    status = result.get('status', 'unknown')
    print(f"Status: {status.upper()}")
    
    if 'bill_number' in result:
        print(f"Bill Number: {result['bill_number']}")
    
    if 'session' in result:
        print(f"Session: {result['session']}")
    
    if 'bill_id' in result:
        print(f"Database ID: {result['bill_id']}")
    
    if 'message' in result:
        print(f"Message: {result['message']}")
    
    if status == 'success':
        print("\n✅ SUCCESS: Bill processed with comprehensive AI analysis!")
        
        if 'ai_summary' in result:
            print(f"\n📄 AI Summary Preview:")
            summary = result['ai_summary']
            if len(summary) > 300:
                print(f"{summary[:300]}...")
            else:
                print(summary)
        
        if 'tags' in result and result['tags']:
            print(f"\n🏷️  Tags: {', '.join(result['tags'])}")
            
        print(f"\n🤖 AI Analysis Complete:")
        print(f"   • Support reasons generated")
        print(f"   • Opposition reasons generated") 
        print(f"   • Amendment reasons generated")
        print(f"   • Message templates created")
        print(f"   • Categorization tags assigned")
            
    elif status == 'already_processed':
        print("\n⚠️  SKIPPED: Bill was already processed with AI analysis")
        
    elif status == 'error':
        print("\n❌ ERROR: Processing failed")
        if 'error' in result:
            print(f"Error details: {result['error']}")
    
    print("="*70)


async def main():
    """Main command line interface"""
    parser = argparse.ArgumentParser(
        description='Process bills with comprehensive AI analysis using OpenAI GPT-4',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python enhanced_seed.py --bill HR5 --session 118
  python enhanced_seed.py --bill S1234 --session 118
  python enhanced_seed.py --bill HR5 --force
  python enhanced_seed.py --status 123
  
Features:
  • Fetches bill data from Congress.gov API
  • Generates plain English summaries with OpenAI GPT-4
  • Creates support/oppose/amend reason lists
  • Generates message templates for contacting representatives
  • Assigns categorization tags
  • Stores structured data in JSONB fields
        """
    )
    
    # Add command line arguments
    parser.add_argument(
        '--bill', 
        type=str, 
        help='Bill number to process (e.g., HR5, S1234)'
    )
    
    parser.add_argument(
        '--session', 
        type=str, 
        default='118',
        help='Congress session number (default: 118)'
    )
    
    parser.add_argument(
        '--force', 
        action='store_true',
        help='Force reprocessing even if bill already exists'
    )
    
    parser.add_argument(
        '--status',
        type=int,
        help='Check status of bill by database ID'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Verbose logging enabled")
    
    # Validate arguments
    if not args.bill and not args.status:
        parser.error("Must specify either --bill or --status")
    
    if args.bill and args.status:
        parser.error("Cannot specify both --bill and --status")
    
    # Check environment configuration
    settings = get_settings()
    
    required_vars = ['CONGRESS_GOV_API_KEY', 'OPENAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not getattr(settings, var, None):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ ERROR: Missing required environment variables: {', '.join(missing_vars)}")
        print("Please configure these variables before running the tool.")
        print("\nRequired variables:")
        print("  • CONGRESS_GOV_API_KEY - Get from https://api.congress.gov/")
        print("  • OPENAI_API_KEY - Get from https://platform.openai.com/")
        sys.exit(1)
    
    print("🚀 Enhanced Bill Processing Tool - Implementation-2.MD")
    print("Using OpenAI GPT-4 for comprehensive AI analysis\n")
    
    try:
        if args.status:
            # Check bill status
            logger.info(f"Checking status for bill ID {args.status}")
            result = await check_bill_status(args.status)
            print_result(result)
            
        elif args.bill:
            # Process bill
            logger.info(f"Processing bill {args.bill}")
            result = await process_bill_command(args.bill, args.session, args.force)
            print_result(result)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
