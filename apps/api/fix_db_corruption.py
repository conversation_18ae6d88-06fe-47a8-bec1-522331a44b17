#!/usr/bin/env python3
"""
Fix database corruption where list fields are stored as {} instead of []
"""
import psycopg2

def fix_database_corruption():
    try:
        conn = psycopg2.connect('postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction')
        cur = conn.cursor()
        
        print('🔍 Checking for corrupted bill records...')
        
        # Check current corruption
        cur.execute('''
            SELECT COUNT(*) FROM bills 
            WHERE tags = '{}' OR categories = '{}' OR reasons_for_support = '{}' OR reasons_for_opposition = '{}'
        ''')
        corrupted_count = cur.fetchone()[0]
        print(f'Found {corrupted_count} corrupted records')
        
        if corrupted_count > 0:
            print('🔧 Fixing corrupted bill records...')
            
            # Fix all corrupted records by converting {} to []
            cur.execute('''
                UPDATE bills 
                SET 
                    tags = CASE WHEN tags = '{}' THEN '[]' ELSE tags END,
                    categories = CASE WHEN categories = '{}' THEN '[]' ELSE categories END,
                    reasons_for_support = CASE WHEN reasons_for_support = '{}' THEN '[]' ELSE reasons_for_support END,
                    reasons_for_opposition = CASE WHEN reasons_for_opposition = '{}' THEN '[]' ELSE reasons_for_opposition END
                WHERE 
                    tags = '{}' OR categories = '{}' OR 
                    reasons_for_support = '{}' OR reasons_for_opposition = '{}';
            ''')
            
            affected_rows = cur.rowcount
            print(f'✅ Fixed {affected_rows} corrupted records')
            
            # Also handle NULL values
            cur.execute('''
                UPDATE bills 
                SET 
                    tags = COALESCE(tags, '[]'),
                    categories = COALESCE(categories, '[]'),
                    reasons_for_support = COALESCE(reasons_for_support, '[]'),
                    reasons_for_opposition = COALESCE(reasons_for_opposition, '[]')
                WHERE 
                    tags IS NULL OR categories IS NULL OR 
                    reasons_for_support IS NULL OR reasons_for_opposition IS NULL;
            ''')
            
            null_fixes = cur.rowcount
            print(f'✅ Fixed {null_fixes} NULL records')
            
            conn.commit()
            
            # Verify the fix
            cur.execute('''
                SELECT COUNT(*) FROM bills 
                WHERE tags = '{}' OR categories = '{}' OR reasons_for_support = '{}' OR reasons_for_opposition = '{}'
            ''')
            remaining = cur.fetchone()[0]
            print(f'Remaining corrupted records: {remaining}')
            
            if remaining == 0:
                print('✅ All database corruption fixed!')
            else:
                print(f'⚠️  Still have {remaining} corrupted records')
        else:
            print('✅ No corruption found - database is clean!')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ Database error: {e}')
        return False
    
    return True

if __name__ == '__main__':
    fix_database_corruption()
