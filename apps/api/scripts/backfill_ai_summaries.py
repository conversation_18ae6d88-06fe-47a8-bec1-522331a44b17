#!/usr/bin/env python3
"""
Backfill script for generating AI summaries for existing bills.

This script finds all bills in the database that lack AI summaries and
generates them using our AI service. It processes bills in batches to
avoid long-running database transactions.
"""

import sys
import os
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import or_

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.db.database import SessionLocal  # noqa: E402
from app.models.bill import Bill  # noqa: E402
from app.services.ai import summarize_bill, health_check  # noqa: E402

def get_db() -> Session:
    """Get database session"""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        db.close()
        raise e

def get_bills_without_ai_summary(db: Session, batch_size: int = 50, offset: int = 0) -> List[Bill]:
    """
    Get bills that don't have AI summaries.

    Args:
        db: Database session
        batch_size: Number of bills to return
        offset: Number of bills to skip

    Returns:
        List of Bill objects without AI summaries
    """
    return db.query(Bill).filter(
        or_(
            Bill.ai_summary is None,
            Bill.ai_summary == "",
            Bill.ai_summary == " "
        )
    ).filter(
        # Only process bills that have full text
        Bill.full_text is not None,
        Bill.full_text != "",
        Bill.full_text != " "
    ).offset(offset).limit(batch_size).all()

def count_bills_without_ai_summary(db: Session) -> int:
    """
    Count total bills that need AI summaries.

    Args:
        db: Database session

    Returns:
        Number of bills without AI summaries
    """
    return db.query(Bill).filter(
        or_(
            Bill.ai_summary is None,
            Bill.ai_summary == "",
            Bill.ai_summary == " "
        )
    ).filter(
        # Only count bills that have full text
        Bill.full_text is not None,
        Bill.full_text != "",
        Bill.full_text != " "
    ).count()

def generate_ai_summary_for_bill(bill: Bill, db: Session) -> bool:
    """
    Generate AI summary for a single bill.

    Args:
        bill: Bill object to process
        db: Database session

    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"Processing Bill #{bill.bill_number}: {bill.title[:50]}...")

        # Generate AI summary
        ai_summary = summarize_bill(
            bill_text=bill.full_text,
            title=bill.title
        )

        # Update the bill
        bill.ai_summary = ai_summary

        print(f"  ✅ Summary generated: {ai_summary[:100]}...")
        return True

    except Exception as e:
        print(f"  ❌ Failed to generate summary: {e}")
        return False

def backfill_ai_summaries(batch_size: int = 50, max_bills: Optional[int] = None) -> None:
    """
    Backfill AI summaries for all existing bills.

    Args:
        batch_size: Number of bills to process in each batch
        max_bills: Maximum number of bills to process (None for all)
    """
    db = get_db()

    try:
        # Get total count
        total_bills = count_bills_without_ai_summary(db)

        if total_bills == 0:
            print("✅ All bills already have AI summaries!")
            return

        if max_bills:
            total_bills = min(total_bills, max_bills)

        print(f"🔄 Processing {total_bills} bills in batches of {batch_size}")

        processed = 0
        successful = 0
        failed = 0
        offset = 0

        while processed < total_bills:
            # Get next batch
            bills_batch = get_bills_without_ai_summary(db, batch_size, offset)

            if not bills_batch:
                print("No more bills to process")
                break

            print(f"\n📦 Processing batch {offset//batch_size + 1}: {len(bills_batch)} bills")

            # Process each bill in the batch
            batch_successful = 0
            batch_failed = 0

            for bill in bills_batch:
                if generate_ai_summary_for_bill(bill, db):
                    batch_successful += 1
                else:
                    batch_failed += 1

                processed += 1

                # Stop if we've hit the max limit
                if max_bills and processed >= max_bills:
                    break

            # Commit the batch
            try:
                db.commit()
                print(f"✅ Batch committed: {batch_successful} successful, {batch_failed} failed")
                successful += batch_successful
                failed += batch_failed

            except Exception as e:
                db.rollback()
                print(f"❌ Failed to commit batch: {e}")
                failed += len(bills_batch)

            offset += batch_size

            # Stop if we've hit the max limit
            if max_bills and processed >= max_bills:
                break

        print("\n=== BACKFILL COMPLETE ===")
        print(f"📊 Total processed: {processed} bills")
        print(f"✅ Successfully updated: {successful} bills")
        print(f"❌ Failed to update: {failed} bills")

        if successful > 0:
            print(f"🎉 Success rate: {(successful/processed)*100:.1f}%")

    except Exception as e:
        print(f"❌ Unexpected error during backfill: {e}")
        db.rollback()

    finally:
        db.close()

def preview_bills_to_process(limit: int = 10) -> None:
    """
    Preview bills that would be processed.

    Args:
        limit: Number of bills to preview
    """
    db = get_db()

    try:
        total_count = count_bills_without_ai_summary(db)
        bills = get_bills_without_ai_summary(db, limit, 0)

        print(f"📋 Total bills without AI summaries: {total_count}")
        print(f"🔍 Preview of first {min(limit, len(bills))} bills:")
        print()

        for i, bill in enumerate(bills, 1):
            print(f"{i}. {bill.bill_number} - {bill.title[:60]}...")
            print(f"   Full text length: {len(bill.full_text) if bill.full_text else 0} chars")
            print(f"   Current AI summary: {bill.ai_summary[:50] if bill.ai_summary else 'None'}...")
            print()

    finally:
        db.close()

def main():
    """Main function for command-line usage"""
    import argparse

    parser = argparse.ArgumentParser(
        description="Backfill AI summaries for existing bills"
    )
    parser.add_argument(
        '--batch-size',
        type=int,
        default=50,
        help="Number of bills to process in each batch (default: 50)"
    )
    parser.add_argument(
        '--max-bills',
        type=int,
        help="Maximum number of bills to process (default: all)"
    )
    parser.add_argument(
        '--preview',
        action='store_true',
        help="Preview bills that would be processed without actually processing them"
    )
    parser.add_argument(
        '--preview-limit',
        type=int,
        default=10,
        help="Number of bills to show in preview (default: 10)"
    )

    args = parser.parse_args()

    print("🚀 AI Summary Backfill Script")
    print("=" * 50)

    # Check AI service health
    print("🔍 Checking AI service health...")
    health = health_check()

    if health['status'] != 'healthy':
        print(f"❌ AI service health check failed: {health.get('error', 'Unknown error')}")
        print("Please ensure the AI service is working before running backfill")
        sys.exit(1)

    print("✅ AI service is healthy")
    print(f"📋 Model info: {health['model_info']['model_name']}")
    print()

    if args.preview:
        preview_bills_to_process(args.preview_limit)
    else:
        # Confirm before processing
        print("⚠️  This will modify the database by adding AI summaries to bills")
        response = input("Do you want to continue? (y/N): ")

        if response.lower() not in ['y', 'yes']:
            print("❌ Backfill cancelled")
            sys.exit(0)

        backfill_ai_summaries(args.batch_size, args.max_bills)

if __name__ == "__main__":
    main()
