#!/usr/bin/env python3
"""
<PERSON>ript to fix campaign action counts by recalculating them from actual actions.

This script addresses the data inconsistency issue where campaigns show "0 of 0 actions"
by recalculating the actual_actions count for each campaign based on the real action records.
"""

import sys
import os
from sqlalchemy import func

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.database import SessionLocal  # noqa: E402
from app.models.campaign import Campaign  # noqa: E402
from app.models.action import Action  # noqa: E402


def fix_campaign_action_counts():
    """
    Fix campaign action counts by recalculating from actual actions.
    """
    db = SessionLocal()

    try:
        print("Starting campaign action count fix...")

        # Get all campaigns with their current action counts
        campaigns = db.query(Campaign).all()
        print(f"Found {len(campaigns)} campaigns to check")

        fixed_count = 0

        for campaign in campaigns:
            # Count actual actions for this campaign
            actual_count = db.query(func.count(Action.id)).filter(
                Action.campaign_id == campaign.id
            ).scalar()

            # Check if the count is different
            if campaign.actual_actions != actual_count:
                print(f"Campaign '{campaign.title}' (ID: {campaign.id})")
                print(f"  Current count: {campaign.actual_actions}")
                print(f"  Actual count: {actual_count}")
                print("  Fixing...")

                # Update the campaign
                campaign.actual_actions = actual_count
                fixed_count += 1
            else:
                print(f"Campaign '{campaign.title}' is already correct ({actual_count} actions)")

        # Commit all changes
        if fixed_count > 0:
            db.commit()
            print(f"\nFixed {fixed_count} campaigns")
        else:
            print("\nNo campaigns needed fixing")

        print("Campaign action count fix completed successfully!")

    except Exception as e:
        print(f"Error fixing campaign action counts: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def verify_fix():
    """
    Verify that the fix worked by checking all campaigns.
    """
    db = SessionLocal()

    try:
        print("\nVerifying fix...")

        # Get campaigns with their action counts
        campaigns_with_counts = db.query(
            Campaign.id,
            Campaign.title,
            Campaign.actual_actions,
            func.count(Action.id).label('real_count')
        ).outerjoin(Action).group_by(Campaign.id, Campaign.title, Campaign.actual_actions).all()

        print("\nCampaign verification results:")
        print("-" * 80)

        all_correct = True
        for campaign_id, title, stored_count, real_count in campaigns_with_counts:
            status = "✓" if stored_count == real_count else "✗"
            print(f"{status} {title[:50]:<50} | Stored: {stored_count:>3} | Actual: {real_count:>3}")

            if stored_count != real_count:
                all_correct = False

        print("-" * 80)
        if all_correct:
            print("✓ All campaigns have correct action counts!")
        else:
            print("✗ Some campaigns still have incorrect counts")

    finally:
        db.close()


if __name__ == "__main__":
    fix_campaign_action_counts()
    verify_fix()
