-- Database initialization script
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create additional schemas if needed
-- CREATE SCHEMA IF NOT EXISTS analytics;

-- Set timezone
SET timezone = 'UTC';

-- Create initial indexes that will be commonly used
-- These will be recreated by Alembic migrations, but having them here ensures basic functionality

-- Database is ready for application use
SELECT 'Database initialized successfully' AS status;