#!/usr/bin/env python3
"""
Process bills with full AI analysis pipeline.

This script:
1. Fetches bills from the database that don't have AI summaries
2. Scrapes full text from Congress.gov using Beautiful Soup
3. Generates comprehensive AI summaries using OpenAI GPT-4
4. Updates the database with structured JSONB summary data
"""

import os
import sys
import asyncio
import logging
from typing import List, Optional
from sqlalchemy.orm import Session

# Add the app directory to the path
sys.path.append('/app')

from app.db.database import get_db
from app.models.bill import Bill
from app.services.ai_service import AIService
from app.services.congress_gov_api import CongressGovAPI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_bills_without_ai_summary(db: Session, limit: int = 10) -> List[Bill]:
    """Get bills that don't have AI summaries yet."""
    return db.query(Bill).filter(
        Bill.ai_processed_at.is_(None)
    ).limit(limit).all()

async def process_bill_with_ai(bill: Bill, ai_service: AIService, congress_api: CongressGovAPI, db: Session) -> bool:
    """
    Process a single bill with full AI analysis.
    
    Args:
        bill: Bill object to process
        ai_service: AI service instance
        text_scraper: Text scraper instance
        db: Database session
        
    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"\n🔍 Processing Bill #{bill.bill_number}: {bill.title[:50]}...")
        
        # Step 1: Fetch full text if not available
        if not bill.full_text or not bill.full_text.strip():
            print(f"  📄 Fetching full text using Congress.gov API...")

            # Parse bill number and type
            try:
                # Extract bill type and number from bill_number (e.g., "HR-3" -> hr, 3 or "4984" -> hr, 4984)
                import re

                # Try to extract numeric part from bill number
                if '-' in bill.bill_number:
                    # Format like "HR-3", "S-1234"
                    parts = bill.bill_number.split('-')
                    bill_type_prefix = parts[0].lower()
                    bill_num = int(parts[1])

                    # Map prefix to bill type
                    if bill_type_prefix in ['hr', 'h']:
                        bill_type = "hr"
                    elif bill_type_prefix in ['s', 'sen']:
                        bill_type = "s"
                    else:
                        # Default based on chamber
                        bill_type = "hr" if bill.chamber == "H" else "s"
                else:
                    # Format like "4984" (numeric only)
                    bill_num = int(bill.bill_number)
                    bill_type = "hr" if bill.chamber == "H" else "s"

                congress = bill.session_year or 118

                print(f"     Congress: {congress}, Type: {bill_type.upper()}, Number: {bill_num}")

                full_text = await congress_api.get_bill_full_text(congress, bill_type, bill_num)
                if full_text and len(full_text.strip()) > 100:
                    bill.full_text = full_text
                    print(f"  ✅ Fetched {len(full_text)} characters of bill text via API")
                else:
                    print(f"  ⚠️ Could not fetch bill text via Congress.gov API (bill may not exist)")
                    print(f"  🔄 Generating AI summary based on title and description...")
                    # For test/mock bills, use title and description as the "full text"
                    mock_text = f"Title: {bill.title}\n\nDescription: {bill.description or 'No description available'}"
                    bill.full_text = mock_text
                    print(f"  ✅ Using mock text for AI processing ({len(mock_text)} characters)")
            except Exception as e:
                print(f"  ❌ Error fetching bill text: {e}")
                return False
        else:
            print(f"  ✅ Bill already has full text ({len(bill.full_text)} characters)")
        
        # Step 2: Generate simple summary for bill cards
        print(f"  📝 Generating simple summary...")

        bill_metadata = {
            'title': bill.title,
            'bill_number': bill.bill_number,
            'chamber': bill.chamber,
            'status': bill.status.value if bill.status else 'Unknown',
            'sponsor': bill.sponsor_name,
            'introduced_date': bill.introduced_date.isoformat() if bill.introduced_date else None
        }

        simple_summary = await ai_service.generate_simple_summary(bill.full_text, bill_metadata)
        bill.simple_summary = simple_summary
        print(f"  ✅ Simple summary generated: {len(simple_summary)} characters")

        # Step 3: Generate comprehensive AI analysis
        print(f"  🤖 Generating comprehensive AI analysis...")

        ai_result = await ai_service.process_bill_complete(
            bill_text=bill.full_text,
            bill_metadata=bill_metadata
        )
        
        # Step 3: Update bill with AI analysis results
        if ai_result:
            # Update structured summary fields
            if 'structured_summary' in ai_result:
                summary = ai_result['structured_summary']
                bill.summary_what_does = summary.get('what_does')
                bill.summary_who_affects = summary.get('who_affects')
                bill.summary_why_matters = summary.get('why_matters')
                bill.summary_key_provisions = summary.get('key_provisions')
                bill.summary_timeline = summary.get('timeline')
                bill.summary_cost_impact = summary.get('cost_impact')
            
            # Update reasons and templates
            bill.support_reasons = ai_result.get('support_reasons', [])
            bill.oppose_reasons = ai_result.get('oppose_reasons', [])
            bill.amend_reasons = ai_result.get('amend_reasons', [])
            bill.message_templates = ai_result.get('message_templates', {})
            bill.ai_tags = ai_result.get('tags', [])
            
            # Mark as processed
            from datetime import datetime, timezone
            bill.ai_processed_at = datetime.now(timezone.utc)
            
            # Commit changes
            db.commit()
            
            print(f"  ✅ AI analysis complete and saved to database")
            print(f"     - Structured summary: {len(ai_result.get('structured_summary', {})) > 0}")
            print(f"     - Support reasons: {len(ai_result.get('support_reasons', []))}")
            print(f"     - Oppose reasons: {len(ai_result.get('oppose_reasons', []))}")
            print(f"     - Tags: {len(ai_result.get('tags', []))}")
            
            return True
        else:
            print(f"  ❌ AI analysis failed - no results returned")
            return False
            
    except Exception as e:
        print(f"  ❌ Error processing bill {bill.bill_number}: {e}")
        logger.error(f"Error processing bill {bill.bill_number}: {e}")
        db.rollback()
        return False

async def main():
    """Main processing function."""
    print("🚀 ModernAction.io Bill AI Processing Pipeline")
    print("=" * 60)
    
    # Set OpenAI API key
    os.environ['OPENAI_API_KEY'] = '********************************************************************************************************************************************************************'
    
    # Initialize services
    print("🔧 Initializing services...")
    ai_service = AIService()
    congress_api = CongressGovAPI()

    if not ai_service.enabled:
        print("❌ AI service is not enabled. Please check OpenAI API key configuration.")
        return

    if not congress_api.enabled:
        print("❌ Congress.gov API is not enabled. Please check CONGRESS_GOV_API_KEY configuration.")
        return

    print("✅ AI service and Congress.gov API initialized successfully")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Get bills that need AI processing
        bills_to_process = get_bills_without_ai_summary(db, limit=10)
        
        if not bills_to_process:
            print("✅ No bills found that need AI processing")
            return
        
        print(f"📋 Found {len(bills_to_process)} bills to process")
        
        # Process each bill
        successful = 0
        failed = 0
        
        for bill in bills_to_process:
            success = await process_bill_with_ai(bill, ai_service, congress_api, db)
            if success:
                successful += 1
            else:
                failed += 1
        
        print("\n" + "=" * 60)
        print(f"🎉 Processing complete!")
        print(f"✅ Successfully processed: {successful} bills")
        print(f"❌ Failed to process: {failed} bills")
        print(f"📊 Total processed: {successful + failed} bills")
        
        if successful > 0:
            print("\n🚀 Bills now have comprehensive AI summaries!")
            print("   - Structured summaries with 6 sections")
            print("   - Support/oppose/amend reasons")
            print("   - Message templates")
            print("   - AI-generated tags")
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        logger.error(f"Error during processing: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(main())
