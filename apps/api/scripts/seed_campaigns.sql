-- ModernAction.io Campaign Seeding Script
-- This script creates the 5 launch campaigns for the MVP

-- Insert campaigns
INSERT INTO campaigns (title, description, target_audience, call_to_action, is_active, created_at, updated_at) VALUES
('Stop Corporate Tax Loopholes', 'Close tax loopholes that allow corporations to avoid paying their fair share', 'taxpayers', 'Contact your representative to support corporate tax reform', true, NOW(), NOW()),
('Protect Voting Rights', 'Ensure every eligible citizen can vote safely and securely', 'voters', 'Urge Congress to pass comprehensive voting rights legislation', true, NOW(), NOW()),
('Climate Action Now', 'Take immediate action to address the climate crisis', 'environmentalists', 'Demand your senators support clean energy legislation', true, NOW(), NOW()),
('Healthcare for All', 'Ensure affordable healthcare access for every American', 'patients', 'Tell your representative to support universal healthcare', true, NOW(), NOW()),
('Student Debt Relief', 'Provide relief for millions struggling with student loan debt', 'students', 'Contact Congress to support student debt forgiveness', true, NOW(), NOW());

-- Insert bills for each campaign
INSERT INTO bills (bill_number, title, summary, status, campaign_id, created_at, updated_at) VALUES
('HR-1234', 'Corporate Tax Accountability Act', 'Closes major corporate tax loopholes and ensures fair taxation', 'COMMITTEE', (SELECT id FROM campaigns WHERE title = 'Stop Corporate Tax Loopholes'), NOW(), NOW()),
('S-5678', 'Voting Rights Restoration Act', 'Restores and expands voting rights protections nationwide', 'FLOOR', (SELECT id FROM campaigns WHERE title = 'Protect Voting Rights'), NOW(), NOW()),
('HR-9012', 'Clean Energy Investment Act', 'Massive investment in renewable energy and climate solutions', 'INTRODUCED', (SELECT id FROM campaigns WHERE title = 'Climate Action Now'), NOW(), NOW()),
('S-3456', 'Medicare for All Act', 'Establishes a national single-payer healthcare system', 'COMMITTEE', (SELECT id FROM campaigns WHERE title = 'Healthcare for All'), NOW(), NOW()),
('HR-7890', 'Student Debt Forgiveness Act', 'Provides comprehensive student loan debt relief', 'INTRODUCED', (SELECT id FROM campaigns WHERE title = 'Student Debt Relief'), NOW(), NOW());

-- Display results
SELECT 'Campaigns created:' as result;
SELECT id, title, is_active FROM campaigns ORDER BY id;

SELECT 'Bills created:' as result;
SELECT id, bill_number, title, status FROM bills ORDER BY id;
