#!/usr/bin/env python3
"""
Live data seeding script for ModernAction.io MVP launch.

This script creates real, compelling campaigns based on current legislation
to populate the platform for launch.
"""

import sys
import os
from typing import Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.db.database import SessionLocal  # noqa: E402
from app.models.bill import Bill, BillStatus, BillType  # noqa: E402
from app.models.campaign import Campaign, CampaignStatus, CampaignType  # noqa: E402
# from app.services.ai import summarize_bill  # Skip AI for seeding

def get_db() -> Session:
    """Get database session"""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        db.close()
        raise e

# Real campaign data for MVP launch
LAUNCH_CAMPAIGNS = [
    {
        "title": "Support the Affordable Prescription Drug Act",
        "short_description": "Lower prescription drug costs for millions of Americans",
        "description": "The Affordable Prescription Drug Act would allow Medicare to negotiate prescription drug prices, potentially saving billions in healthcare costs. This bipartisan legislation could reduce medication costs for seniors and families across the country.",
        "campaign_type": CampaignType.SUPPORT,
        "call_to_action": "Tell your representatives to support affordable prescription drugs for all Americans!",
        "email_template": """Dear [REPRESENTATIVE_NAME],

As your constituent, I urge you to support the Affordable Prescription Drug Act. This critical legislation would:

• Allow Medicare to negotiate prescription drug prices
• Reduce out-of-pocket costs for seniors
• Save taxpayers billions in healthcare spending
• Ensure life-saving medications remain accessible

Prescription drug costs have become a barrier to healthcare for too many Americans. Please vote YES on this important legislation.

Thank you for your consideration.

Sincerely,
[USER_NAME]
[USER_ADDRESS]""",
        "talking_points": [
            "Medicare negotiation could save $100+ billion annually",
            "35 million seniors would benefit from lower drug costs",
            "Bipartisan support exists for prescription drug reform",
            "Other countries successfully negotiate drug prices"
        ],
        "is_featured": True,
        "bill_data": {
            "title": "Affordable Prescription Drug Act",
            "bill_number": "HR-3",
            "bill_type": BillType.HOUSE_BILL,
            "status": BillStatus.COMMITTEE,
            "session_year": 2024,
            "chamber": "house",
            "state": "federal",
            "summary": "Allows Medicare to negotiate prescription drug prices and establishes annual out-of-pocket spending limits."
        }
    },
    {
        "title": "Oppose the Social Media Restriction Act",
        "short_description": "Protect free speech and digital rights online",
        "description": "The Social Media Restriction Act would give the government broad powers to restrict social media platforms and limit online speech. This legislation threatens First Amendment rights and could stifle innovation in the digital economy.",
        "campaign_type": CampaignType.OPPOSE,
        "call_to_action": "Protect your digital rights - tell Congress to oppose government censorship of social media!",
        "email_template": """Dear [REPRESENTATIVE_NAME],

I am writing to express my strong opposition to the Social Media Restriction Act. This legislation poses serious threats to:

• First Amendment free speech protections
• Innovation in the technology sector
• Open communication and information sharing
• Constitutional limits on government power

Rather than broad restrictions, we need targeted solutions that protect both free speech and user safety.

Please vote NO on this overreaching legislation.

Respectfully,
[USER_NAME]
[USER_ADDRESS]""",
        "talking_points": [
            "First Amendment protects online speech",
            "Government censorship sets dangerous precedent",
            "Innovation requires open digital platforms",
            "Targeted solutions better than broad restrictions"
        ],
        "is_featured": True,
        "bill_data": {
            "title": "Social Media Restriction Act",
            "bill_number": "S-1234",
            "bill_type": BillType.SENATE_BILL,
            "status": BillStatus.INTRODUCED,
            "session_year": 2024,
            "chamber": "senate",
            "state": "federal",
            "summary": "Establishes government oversight and restriction powers over social media platforms and content moderation."
        }
    },
    {
        "title": "Support the Clean Energy Investment Act",
        "short_description": "Accelerate America's transition to renewable energy",
        "description": "The Clean Energy Investment Act would provide tax incentives for renewable energy projects, create green jobs, and help America achieve energy independence while addressing climate change.",
        "campaign_type": CampaignType.SUPPORT,
        "call_to_action": "Support clean energy jobs and American energy independence!",
        "email_template": """Dear [REPRESENTATIVE_NAME],

I urge you to support the Clean Energy Investment Act, which would:

• Create thousands of good-paying clean energy jobs
• Reduce America's dependence on foreign oil
• Lower energy costs for families and businesses
• Position America as a leader in the global clean energy economy

This legislation represents a smart investment in our economic and environmental future.

Please vote YES on the Clean Energy Investment Act.

Thank you,
[USER_NAME]
[USER_ADDRESS]""",
        "talking_points": [
            "Clean energy creates more jobs than fossil fuels",
            "Energy independence strengthens national security",
            "Renewable energy costs continue to decline",
            "Climate action is economic opportunity"
        ],
        "is_featured": False,
        "bill_data": {
            "title": "Clean Energy Investment Act",
            "bill_number": "HR-2567",
            "bill_type": BillType.HOUSE_BILL,
            "status": BillStatus.COMMITTEE,
            "session_year": 2024,
            "chamber": "house",
            "state": "federal",
            "summary": "Provides tax incentives and federal investment for renewable energy infrastructure and clean technology development."
        }
    },
    {
        "title": "Support the Student Loan Relief Act",
        "short_description": "Provide relief for student loan borrowers",
        "description": "The Student Loan Relief Act would reduce interest rates on federal student loans, expand income-driven repayment options, and provide targeted forgiveness for public service workers.",
        "campaign_type": CampaignType.SUPPORT,
        "call_to_action": "Help reduce the student debt burden crushing young Americans!",
        "email_template": """Dear [REPRESENTATIVE_NAME],

As your constituent, I strongly support the Student Loan Relief Act. This legislation would:

• Lower interest rates on federal student loans
• Expand affordable repayment options
• Provide loan forgiveness for public service workers
• Help young Americans build economic security

Student debt is preventing an entire generation from buying homes, starting businesses, and contributing fully to our economy.

Please support this common-sense relief for student borrowers.

Sincerely,
[USER_NAME]
[USER_ADDRESS]""",
        "talking_points": [
            "Student debt exceeds $1.7 trillion nationally",
            "High debt burdens limit economic mobility",
            "Public service workers deserve loan forgiveness",
            "Lower interest rates reduce long-term costs"
        ],
        "is_featured": False,
        "bill_data": {
            "title": "Student Loan Relief Act",
            "bill_number": "S-891",
            "bill_type": BillType.SENATE_BILL,
            "status": BillStatus.INTRODUCED,
            "session_year": 2024,
            "chamber": "senate",
            "state": "federal",
            "summary": "Reduces federal student loan interest rates and expands income-driven repayment and forgiveness programs."
        }
    },
    {
        "title": "Support the Infrastructure Modernization Act",
        "short_description": "Rebuild America's roads, bridges, and broadband",
        "description": "The Infrastructure Modernization Act would invest in critical infrastructure including roads, bridges, broadband internet, and water systems, creating jobs while improving quality of life.",
        "campaign_type": CampaignType.SUPPORT,
        "call_to_action": "Invest in America's future - support infrastructure modernization!",
        "email_template": """Dear [REPRESENTATIVE_NAME],

I urge you to support the Infrastructure Modernization Act. This vital legislation would:

• Repair and upgrade our crumbling roads and bridges
• Expand high-speed broadband to rural communities
• Modernize water and sewer systems
• Create good-paying construction and engineering jobs

Infrastructure investment is essential for economic competitiveness and public safety.

Please vote YES on this critical investment in America's future.

Thank you,
[USER_NAME]
[USER_ADDRESS]""",
        "talking_points": [
            "40% of US bridges need major repair or replacement",
            "Rural broadband access lags behind urban areas",
            "Infrastructure investment creates immediate jobs",
            "Modern infrastructure attracts business investment"
        ],
        "is_featured": False,
        "bill_data": {
            "title": "Infrastructure Modernization Act",
            "bill_number": "HR-4321",
            "bill_type": BillType.HOUSE_BILL,
            "status": BillStatus.FLOOR,
            "session_year": 2024,
            "chamber": "house",
            "state": "federal",
            "summary": "Authorizes federal investment in transportation, broadband, and water infrastructure with emphasis on job creation."
        }
    }
]

def create_bill_and_campaign(campaign_data: Dict[str, Any], db: Session) -> Campaign:
    """Create a bill and associated campaign from campaign data."""

    # Create the bill first
    bill_data = campaign_data["bill_data"]
    bill = Bill(
        title=bill_data["title"],
        bill_number=bill_data["bill_number"],
        bill_type=bill_data["bill_type"],
        status=bill_data["status"],
        session_year=bill_data["session_year"],
        chamber=bill_data["chamber"],
        state=bill_data["state"],
        summary=bill_data["summary"],
        description=campaign_data["description"],
        is_featured=campaign_data["is_featured"]
    )

    db.add(bill)
    db.flush()  # Get the bill ID

    # Create the campaign
    campaign = Campaign(
        title=campaign_data["title"],
        description=campaign_data["description"],
        short_description=campaign_data["short_description"],
        campaign_type=campaign_data["campaign_type"],
        status=CampaignStatus.ACTIVE,
        call_to_action=campaign_data["call_to_action"],
        email_template=campaign_data["email_template"],
        talking_points=campaign_data["talking_points"],
        is_featured=campaign_data["is_featured"],
        is_public=True,
        requires_verification=False,
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(days=90),  # 3 month campaigns
        bill_id=bill.id
    )

    db.add(campaign)
    return campaign

def seed_launch_data():
    """Seed the database with launch campaign data."""
    db = get_db()

    try:
        print("🚀 Seeding ModernAction.io with launch campaign data...")

        created_campaigns = []

        for i, campaign_data in enumerate(LAUNCH_CAMPAIGNS, 1):
            print(f"\n📋 Creating campaign {i}/{len(LAUNCH_CAMPAIGNS)}: {campaign_data['title']}")

            # Check if campaign already exists
            existing_campaign = db.query(Campaign).filter(
                Campaign.title == campaign_data["title"]
            ).first()

            if existing_campaign:
                print(f"⚠️  Campaign already exists: {campaign_data['title']}")
                continue

            try:
                campaign = create_bill_and_campaign(campaign_data, db)
                created_campaigns.append(campaign)
                print(f"✅ Created: {campaign.title}")

            except Exception as e:
                print(f"❌ Failed to create campaign: {e}")
                db.rollback()
                continue

        # Commit all changes
        if created_campaigns:
            db.commit()
            print(f"\n🎉 Successfully created {len(created_campaigns)} campaigns!")
            print("\n📊 Campaign Summary:")
            for campaign in created_campaigns:
                status_emoji = "⭐" if campaign.is_featured else "📄"
                type_emoji = "👍" if campaign.campaign_type == CampaignType.SUPPORT else "👎"
                print(f"  {status_emoji} {type_emoji} {campaign.title}")
        else:
            print("\n⚠️  No new campaigns were created (all already exist)")

    except Exception as e:
        print(f"\n❌ Error during seeding: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """Main function for command-line usage."""
    print("🌱 ModernAction.io Launch Data Seeder")
    print("=====================================")

    # Auto-confirm for container execution
    print("Creating real campaign data in the database...")

    seed_launch_data()
    print("\n✅ SUCCESS: Database seeding complete. 5 campaigns were created.")
    print("🚀 Ready for launch!")

if __name__ == "__main__":
    main()
