#!/usr/bin/env python3
"""
Minimal seeding script for ModernAction.io launch data.
This script creates the essential campaign data without any AI dependencies.
"""

import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import get_db  # noqa: E402
from app.models.campaign import Campaign  # noqa: E402
from app.models.bill import Bill  # noqa: E402
from sqlalchemy.orm import Session  # noqa: E402

def create_campaigns(db: Session):
    """Create the 5 launch campaigns."""

    campaigns_data = [
        {
            "title": "Stop Corporate Tax Loopholes",
            "description": "Close tax loopholes that allow corporations to avoid paying their fair share",
            "target_audience": "taxpayers",
            "call_to_action": "Contact your representative to support corporate tax reform",
            "is_active": True
        },
        {
            "title": "Protect Voting Rights",
            "description": "Ensure every eligible citizen can vote safely and securely",
            "target_audience": "voters",
            "call_to_action": "Urge Congress to pass comprehensive voting rights legislation",
            "is_active": True
        },
        {
            "title": "Climate Action Now",
            "description": "Take immediate action to address the climate crisis",
            "target_audience": "environmentalists",
            "call_to_action": "Demand your senators support clean energy legislation",
            "is_active": True
        },
        {
            "title": "Healthcare for All",
            "description": "Ensure affordable healthcare access for every American",
            "target_audience": "patients",
            "call_to_action": "Tell your representative to support universal healthcare",
            "is_active": True
        },
        {
            "title": "Student Debt Relief",
            "description": "Provide relief for millions struggling with student loan debt",
            "target_audience": "students",
            "call_to_action": "Contact Congress to support student debt forgiveness",
            "is_active": True
        }
    ]

    created_campaigns = []
    for campaign_data in campaigns_data:
        # Check if campaign already exists
        existing = db.query(Campaign).filter(Campaign.title == campaign_data["title"]).first()
        if existing:
            print(f"✓ Campaign '{campaign_data['title']}' already exists")
            created_campaigns.append(existing)
            continue

        campaign = Campaign(**campaign_data)
        db.add(campaign)
        db.flush()  # Get the ID
        created_campaigns.append(campaign)
        print(f"✓ Created campaign: {campaign.title}")

    return created_campaigns

def create_bills(db: Session, campaigns):
    """Create sample bills for the campaigns."""

    bills_data = [
        {
            "bill_number": "HR-1234",
            "title": "Corporate Tax Accountability Act",
            "summary": "Closes major corporate tax loopholes and ensures fair taxation",
            "status": "COMMITTEE",
            "campaign_id": campaigns[0].id
        },
        {
            "bill_number": "S-5678",
            "title": "Voting Rights Restoration Act",
            "summary": "Restores and expands voting rights protections nationwide",
            "status": "FLOOR",
            "campaign_id": campaigns[1].id
        },
        {
            "bill_number": "HR-9012",
            "title": "Clean Energy Investment Act",
            "summary": "Massive investment in renewable energy and climate solutions",
            "status": "INTRODUCED",
            "campaign_id": campaigns[2].id
        },
        {
            "bill_number": "S-3456",
            "title": "Medicare for All Act",
            "summary": "Establishes a national single-payer healthcare system",
            "status": "COMMITTEE",
            "campaign_id": campaigns[3].id
        },
        {
            "bill_number": "HR-7890",
            "title": "Student Debt Forgiveness Act",
            "summary": "Provides comprehensive student loan debt relief",
            "status": "INTRODUCED",
            "campaign_id": campaigns[4].id
        }
    ]

    created_bills = []
    for bill_data in bills_data:
        # Check if bill already exists
        existing = db.query(Bill).filter(Bill.bill_number == bill_data["bill_number"]).first()
        if existing:
            print(f"✓ Bill '{bill_data['bill_number']}' already exists")
            created_bills.append(existing)
            continue

        bill = Bill(**bill_data)
        db.add(bill)
        db.flush()
        created_bills.append(bill)
        print(f"✓ Created bill: {bill.bill_number} - {bill.title}")

    return created_bills

def seed_launch_data():
    """Main seeding function."""
    print("🌱 ModernAction.io Launch Data Seeder")
    print("=====================================")

    # Get database session
    db = next(get_db())

    try:
        print("\n📋 Creating campaigns...")
        campaigns = create_campaigns(db)

        print("\n📜 Creating bills...")
        bills = create_bills(db, campaigns)

        # Commit all changes
        db.commit()

        print("\n✅ Successfully created:")
        print(f"   • {len(campaigns)} campaigns")
        print(f"   • {len(bills)} bills")

    except Exception as e:
        print(f"\n❌ Error during seeding: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """Main function for command-line usage."""
    print("🌱 ModernAction.io Launch Data Seeder")
    print("=====================================")

    # Auto-confirm for container execution
    print("Creating real campaign data in the database...")

    seed_launch_data()
    print("\n🚀 Database seeding complete! Ready for launch!")

if __name__ == "__main__":
    main()
