#!/usr/bin/env python3
"""
Authoritative Production Database Seeding Script

This script creates a complete, functional dataset for ModernAction.io MVP launch.
It uses the service layer to ensure all data conforms to application validation and business logic.

Order of Operations:
1. Bills (no dependencies)
2. Officials (no dependencies)
3. Campaigns (depends on Bills)

All NOT NULL constraints are handled with sensible defaults.
"""

import sys
import os
from typing import List

# Add the app directory to the Python path
sys.path.append('/app')

from sqlalchemy import create_engine, text  # noqa: E402
from sqlalchemy.orm import sessionmaker  # noqa: E402
from app.models.bill import BillType, BillStatus  # noqa: E402
# Removed enum imports - now using string values directly  # noqa: E402
from app.models.campaign import CampaignType, CampaignStatus  # noqa: E402
from app.schemas.bill import BillCreate  # noqa: E402
from app.schemas.official import OfficialCreate  # noqa: E402
from app.schemas.campaign import CampaignCreate  # noqa: E402
from app.services.bills import BillService  # noqa: E402
from app.services.officials import OfficialService  # noqa: E402
from app.services.campaigns import CampaignService  # noqa: E402

def get_database_session():
    """Create database session using environment variables"""
    db_host = os.getenv('DB_HOST')
    db_name = os.getenv('DB_NAME')
    db_user = os.getenv('DB_USERNAME')
    db_pass = os.getenv('DB_PASSWORD')
    db_port = os.getenv('DB_PORT', '5432')

    if not all([db_host, db_name, db_user, db_pass]):
        raise ValueError("Missing required database environment variables")

    DATABASE_URL = f"postgresql://{db_user}:{db_pass}@{db_host}:{db_port}/{db_name}"
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()

def clear_existing_data(db):
    """Clear all existing data to ensure clean slate"""
    print("🧹 Clearing existing data...")
    db.execute(text("TRUNCATE TABLE campaigns, bills, officials RESTART IDENTITY CASCADE;"))
    db.commit()
    print("✅ Database cleared")

def create_bills(bill_service: BillService) -> List[str]:
    """Create real federal bills for campaigns"""
    print("📜 Creating bills...")

    bills_data = [
        {
            "title": "Equality Act",
            "description": "A bill to prohibit discrimination on the basis of sex, gender identity, and sexual orientation.",
            "bill_number": "5",
            "bill_type": BillType.HOUSE_BILL,
            "status": BillStatus.INTRODUCED,
            "session_year": 2023,
            "chamber": "house",
            "state": "federal",
            "summary": "This bill prohibits discrimination based on sex, sexual orientation, and gender identity in areas including public accommodations and facilities, education, federal funding, employment, housing, credit, and the jury system.",
            "sponsor_name": "David Cicilline",
            "sponsor_party": "Democratic",
            "sponsor_state": "RI",
            "is_featured": True,
            "priority_score": 100,
            "congress_gov_id": "hr5-118"
        },
        {
            "title": "Bipartisan Background Checks Act",
            "description": "A bill to require a background check for every firearm sale.",
            "bill_number": "8",
            "bill_type": BillType.HOUSE_BILL,
            "status": BillStatus.INTRODUCED,
            "session_year": 2023,
            "chamber": "house",
            "state": "federal",
            "summary": "This bill establishes new background check requirements for firearm transfers between private parties (i.e., unlicensed individuals).",
            "sponsor_name": "Mike Thompson",
            "sponsor_party": "Democratic",
            "sponsor_state": "CA",
            "is_featured": True,
            "priority_score": 95,
            "congress_gov_id": "hr8-118"
        },
        {
            "title": "For the People Act",
            "description": "A bill to expand Americans' right to vote and clean up corruption in Washington.",
            "bill_number": "1",
            "bill_type": BillType.SENATE_BILL,
            "status": BillStatus.INTRODUCED,
            "session_year": 2023,
            "chamber": "senate",
            "state": "federal",
            "summary": "This bill addresses voter access, election integrity and security, campaign finance, and ethics for the three branches of government.",
            "sponsor_name": "Jeff Merkley",
            "sponsor_party": "Democratic",
            "sponsor_state": "OR",
            "is_featured": True,
            "priority_score": 90,
            "congress_gov_id": "s1-118"
        }
    ]

    bill_ids = []
    for bill_data in bills_data:
        bill_create = BillCreate(**bill_data)
        bill = bill_service.create_bill(bill_create)
        bill_ids.append(bill.id)
        print(f"✅ Created bill: {bill.title} ({bill.bill_number})")

    return bill_ids

def create_officials(official_service: OfficialService) -> List[str]:
    """Create real federal officials for key zip codes"""
    print("🏛️ Creating officials...")

    officials_data = [
        # California officials (for 90210 - Beverly Hills)
        {
            "name": "Dianne Feinstein",
            "title": "Senator",
            "party": "Democratic",
            "email": "<EMAIL>",
            "phone": "************",
            "website": "https://www.feinstein.senate.gov",
            "level": "federal",
            "chamber": "senate",
            "state": "CA",
            "district": None,
            "is_active": True
        },
        {
            "name": "Alex Padilla",
            "title": "Senator",
            "party": "Democratic",
            "email": "<EMAIL>",
            "phone": "************",
            "website": "https://www.padilla.senate.gov",
            "level": "federal",
            "chamber": "senate",
            "state": "CA",
            "district": None,
            "is_active": True
        },
        {
            "name": "Brad Sherman",
            "title": "Representative",
            "party": "Democratic",
            "email": "<EMAIL>",
            "phone": "************",
            "website": "https://sherman.house.gov",
            "level": "federal",
            "chamber": "house",
            "state": "CA",
            "district": "30",
            "is_active": True
        },
        # New York officials (for 10001 - Manhattan)
        {
            "name": "Chuck Schumer",
            "title": "Senator",
            "party": "Democratic",
            "email": "<EMAIL>",
            "phone": "************",
            "website": "https://www.schumer.senate.gov",
            "level": "federal",
            "chamber": "senate",
            "state": "NY",
            "district": None,
            "is_active": True
        },
        {
            "name": "Kirsten Gillibrand",
            "title": "Senator",
            "party": "Democratic",
            "email": "<EMAIL>",
            "phone": "************",
            "website": "https://www.gillibrand.senate.gov",
            "level": "federal",
            "chamber": "senate",
            "state": "NY",
            "district": None,
            "is_active": True
        },
        {
            "name": "Jerry Nadler",
            "title": "Representative",
            "party": "Democratic",
            "email": "<EMAIL>",
            "phone": "************",
            "website": "https://nadler.house.gov",
            "level": "federal",
            "chamber": "house",
            "state": "NY",
            "district": "12",
            "is_active": True
        }
    ]

    official_ids = []
    for official_data in officials_data:
        official_create = OfficialCreate(**official_data)
        official = official_service.create_official(official_create)
        official_ids.append(official.id)
        print(f"✅ Created official: {official.name} ({official.title})")

    return official_ids

def create_campaigns(campaign_service: CampaignService, bill_ids: List[str]) -> List[str]:
    """Create campaigns linked to bills"""
    print("🚀 Creating campaigns...")

    campaigns_data = [
        {
            "title": "Support the Equality Act",
            "description": "The Equality Act would provide consistent and explicit anti-discrimination protections for LGBTQ+ people across key areas of life.",
            "campaign_type": CampaignType.SUPPORT,
            "status": CampaignStatus.ACTIVE,
            "call_to_action": "Tell your representatives to support the Equality Act!",
            "email_template": "Dear [OFFICIAL_NAME], I am writing to urge you to support the Equality Act (H.R. 5). This critical legislation would provide consistent and explicit anti-discrimination protections for LGBTQ+ people across key areas of life, including employment, housing, credit, education, public spaces and services, federally funded programs, and jury service. Please vote YES on the Equality Act. Thank you.",
            "social_media_message": "I urge @[OFFICIAL_HANDLE] to support the Equality Act (H.R. 5) to protect LGBTQ+ Americans from discrimination! #EqualityAct",
            "is_featured": True,
            "is_public": True,
            "bill_id": bill_ids[0]  # Equality Act
        },
        {
            "title": "Universal Background Checks",
            "description": "The Bipartisan Background Checks Act would help keep guns out of dangerous hands by requiring background checks on all gun sales.",
            "campaign_type": CampaignType.SUPPORT,
            "status": CampaignStatus.ACTIVE,
            "call_to_action": "Tell your representatives to support universal background checks!",
            "email_template": "Dear [OFFICIAL_NAME], I am writing to urge you to support the Bipartisan Background Checks Act (H.R. 8). This common-sense legislation would help keep guns out of dangerous hands by requiring background checks on all gun sales. The majority of Americans support universal background checks. Please vote YES on H.R. 8. Thank you.",
            "social_media_message": "I urge @[OFFICIAL_HANDLE] to support universal background checks (H.R. 8) to help keep our communities safe! #BackgroundChecks",
            "is_featured": True,
            "is_public": True,
            "bill_id": bill_ids[1]  # Background Checks Act
        },
        {
            "title": "For the People Act",
            "description": "The For the People Act would expand voting rights, limit gerrymandering, and reduce the influence of money in politics.",
            "campaign_type": CampaignType.SUPPORT,
            "status": CampaignStatus.ACTIVE,
            "call_to_action": "Tell your senators to support the For the People Act!",
            "email_template": "Dear [OFFICIAL_NAME], I am writing to urge you to support the For the People Act (S. 1). This comprehensive legislation would expand Americans' right to vote, end gerrymandering, and clean up corruption in Washington. Our democracy depends on fair elections and equal representation. Please vote YES on S. 1. Thank you.",
            "social_media_message": "I urge @[OFFICIAL_HANDLE] to support the For the People Act (S. 1) to protect our democracy! #ForThePeople",
            "is_featured": True,
            "is_public": True,
            "bill_id": bill_ids[2]  # For the People Act
        }
    ]

    campaign_ids = []
    for campaign_data in campaigns_data:
        campaign_create = CampaignCreate(**campaign_data)
        campaign = campaign_service.create_campaign(campaign_create)
        campaign_ids.append(campaign.id)
        print(f"✅ Created campaign: {campaign.title}")

    return campaign_ids

def verify_data(db):
    """Verify that all data was created successfully"""
    print("🔍 Verifying data...")

    # Count records
    bills_count = db.execute(text("SELECT COUNT(*) FROM bills")).fetchone()[0]
    officials_count = db.execute(text("SELECT COUNT(*) FROM officials")).fetchone()[0]
    campaigns_count = db.execute(text("SELECT COUNT(*) FROM campaigns")).fetchone()[0]

    print("📊 Data Summary:")
    print(f"   Bills: {bills_count}")
    print(f"   Officials: {officials_count}")
    print(f"   Campaigns: {campaigns_count}")

    if bills_count >= 3 and officials_count >= 6 and campaigns_count >= 3:
        print("✅ All data created successfully!")
        return True
    else:
        print("❌ Data creation incomplete!")
        return False

def main():
    """Main seeding function"""
    print("🌱 Starting Production Database Seeding...")
    print("=" * 50)

    try:
        # Get database session
        db = get_database_session()

        # Clear existing data
        clear_existing_data(db)

        # Initialize services
        bill_service = BillService(db)
        official_service = OfficialService(db)
        campaign_service = CampaignService(db)

        # Create data in correct order
        bill_ids = create_bills(bill_service)
        official_ids = create_officials(official_service)
        campaign_ids = create_campaigns(campaign_service, bill_ids)

        # Verify success
        success = verify_data(db)

        if success:
            print("=" * 50)
            print("🎉 PRODUCTION DATABASE SEEDING COMPLETE!")
            print("🚀 ModernAction.io is ready for launch!")
        else:
            print("❌ SEEDING FAILED - Check logs for errors")
            sys.exit(1)

    except Exception as e:
        print(f"💥 CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    main()
