#!/usr/bin/env python3
"""
Real bill data seeding script for ModernAction.io.

This script fetches real, current bills from the Congress.gov API,
downloads their full text, generates AI summaries, and creates campaigns.
"""

import sys
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import argparse

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.db.database import SessionLocal  # noqa: E402
from app.models.bill import Bill, BillStatus, BillType  # noqa: E402
from app.models.campaign import Campaign, CampaignStatus, CampaignType  # noqa: E402
from app.services.congress_gov_api import get_congress_gov_client  # noqa: E402
from app.services.bill_text_scraper import get_bill_text_scraper  # noqa: E402
from app.tasks import task_generate_summary_for_bill  # noqa: E402


def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        return db
    except Exception:
        db.close()
        raise


def map_congress_gov_to_bill_type(bill_type: str) -> BillType:
    """Map Congress.gov bill type to our BillType enum"""
    type_mapping = {
        'hr': BillType.HOUSE_BILL,
        'hres': BillType.HOUSE_RESOLUTION,
        'hjres': BillType.JOINT_RESOLUTION,
        'hconres': BillType.CONCURRENT_RESOLUTION,
        's': BillType.SENATE_BILL,
        'sres': BillType.SENATE_RESOLUTION,
        'sjres': BillType.JOINT_RESOLUTION,
        'sconres': BillType.CONCURRENT_RESOLUTION
    }
    return type_mapping.get(bill_type.lower(), BillType.HOUSE_BILL)


def map_congress_gov_to_bill_status(latest_action: str) -> BillStatus:
    """Map Congress.gov latest action to our BillStatus enum"""
    if not latest_action:
        return BillStatus.INTRODUCED

    action_lower = latest_action.lower()

    if any(word in action_lower for word in ['introduced', 'referred']):
        return BillStatus.INTRODUCED
    elif any(word in action_lower for word in ['committee', 'markup', 'reported']):
        return BillStatus.COMMITTEE
    elif any(word in action_lower for word in ['passed', 'agreed to']):
        return BillStatus.PASSED
    elif 'signed' in action_lower or 'became public law' in action_lower:
        return BillStatus.SIGNED
    elif 'vetoed' in action_lower:
        return BillStatus.VETOED
    else:
        return BillStatus.INTRODUCED


def create_bill_from_congress_gov_data(bill_data: Dict[str, Any], db) -> Optional[Bill]:
    """
    Create a Bill record from Congress.gov API data.

    Args:
        bill_data: Bill data from Congress.gov API
        db: Database session

    Returns:
        Created Bill instance or None if creation fails
    """
    try:
        # Parse bill number and type
        bill_number = bill_data.get('number', '')
        bill_type_str = bill_data.get('type', 'hr')
        congress_num = bill_data.get('congress', 118)

        # Check if bill already exists
        existing_bill = db.query(Bill).filter(
            Bill.bill_number == bill_number,
            Bill.session_year == congress_num
        ).first()

        if existing_bill:
            print(f"Bill {bill_number} already exists, skipping...")
            return existing_bill

        # Get latest action
        latest_action = ""
        latest_action_date = None
        if bill_data.get('latestAction'):
            latest_action = bill_data['latestAction'].get('text', '')
            action_date_str = bill_data['latestAction'].get('actionDate')
            if action_date_str:
                latest_action_date = datetime.strptime(action_date_str, '%Y-%m-%d')

        # Parse introduced date
        introduced_date = None
        if bill_data.get('introducedDate'):
            introduced_date = datetime.strptime(bill_data['introducedDate'], '%Y-%m-%d')

        # Create new bill
        bill = Bill(
            title=bill_data.get('title', '').strip(),
            description=None,  # Will be filled by AI summary
            bill_number=bill_number,
            bill_type=map_congress_gov_to_bill_type(bill_type_str),
            status=map_congress_gov_to_bill_status(latest_action),
            session_year=congress_num,
            chamber=bill_type_str[0] if bill_type_str else 'h',  # 'h' for house, 's' for senate
            state='federal',

            # Content
            summary=None,  # Will be filled by AI summary

            # External references
            congress_gov_id=f"{congress_num}-{bill_type_str}-{bill_number}",

            # URLs
            source_url=bill_data.get('url'),
            text_url=f"https://www.congress.gov/bill/{congress_num}th-congress/{bill_type_str.replace('h', 'house-').replace('s', 'senate-')}{bill_type_str[1:]}/{bill_number}/text" if bill_type_str and bill_number else None,

            # Dates
            introduced_date=introduced_date,
            last_action_date=latest_action_date,

            # Sponsorship
            sponsor_name=None,  # Will be filled if available
            sponsor_party=None,
            sponsor_state=None,

            # Metadata
            is_featured=False,
            priority_score=50  # Default priority
        )

        # Add sponsor information if available
        if bill_data.get('sponsors') and len(bill_data['sponsors']) > 0:
            sponsor = bill_data['sponsors'][0]
            bill.sponsor_name = sponsor.get('fullName')
            bill.sponsor_party = sponsor.get('party')
            bill.sponsor_state = sponsor.get('state')

        db.add(bill)
        db.flush()  # Get the bill ID

        print(f"✅ Created bill: {bill.bill_number} - {bill.title[:60]}...")
        return bill

    except Exception as e:
        print(f"❌ Failed to create bill from Congress.gov data: {e}")
        db.rollback()
        return None


def create_campaign_for_bill(bill: Bill, campaign_type: CampaignType, db) -> Optional[Campaign]:
    """
    Create a campaign for a bill.

    Args:
        bill: Bill instance
        campaign_type: Type of campaign (support, oppose, etc.)
        db: Database session

    Returns:
        Created Campaign instance or None if creation fails
    """
    try:
        # Generate campaign content based on bill
        if campaign_type == CampaignType.SUPPORT:
            title = f"Support {bill.bill_number}: {bill.title}"
            call_to_action = f"Urge your representatives to support {bill.bill_number}"
            description = f"This important legislation needs your support. Contact your representatives and ask them to vote YES on {bill.bill_number}."
        elif campaign_type == CampaignType.OPPOSE:
            title = f"Oppose {bill.bill_number}: {bill.title}"
            call_to_action = f"Urge your representatives to oppose {bill.bill_number}"
            description = f"This legislation could have negative impacts. Contact your representatives and ask them to vote NO on {bill.bill_number}."
        else:
            title = f"Take Action on {bill.bill_number}: {bill.title}"
            call_to_action = f"Contact your representatives about {bill.bill_number}"
            description = f"This legislation affects important issues. Make your voice heard by contacting your representatives about {bill.bill_number}."

        # Create talking points
        talking_points = [
            f"I am writing about {bill.bill_number}, {bill.title}",
            "This legislation is important to me as your constituent",
            "I urge you to carefully consider the impact of this bill",
            "Please vote in accordance with the best interests of our district"
        ]

        # Email template
        email_template = f"""Dear [OFFICIAL_NAME],

I am writing to you as your constituent regarding {bill.bill_number}, "{bill.title}".

{description}

As someone who cares deeply about this issue, I urge you to {campaign_type.value} this legislation.

Thank you for your time and consideration.

Sincerely,
[USER_NAME]
[USER_ADDRESS]"""

        campaign = Campaign(
            title=title,
            description=description,
            short_description=description[:200] + "..." if len(description) > 200 else description,
            campaign_type=campaign_type,
            status=CampaignStatus.ACTIVE,
            call_to_action=call_to_action,
            email_template=email_template,
            talking_points=talking_points,
            is_featured=True,  # Make real bills featured
            is_public=True,
            requires_verification=False,
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow() + timedelta(days=90),  # 3 month campaigns
            bill_id=bill.id
        )

        db.add(campaign)
        db.commit()

        print(f"✅ Created campaign: {campaign.title[:60]}...")
        return campaign

    except Exception as e:
        print(f"❌ Failed to create campaign: {e}")
        db.rollback()
        return None


def fetch_and_seed_real_bills(bill_numbers: List[str] = None, limit: int = 5):
    """
    Fetch real bills from Congress.gov API and seed the database.

    Args:
        bill_numbers: Specific bill numbers to fetch (e.g., ['H.R.5', 'S.1234'])
        limit: Maximum number of bills to fetch if no specific bills provided
    """
    print("🚀 Starting real bill data seeding from Congress.gov API...")

    # Initialize services
    congress_client = get_congress_gov_client()
    get_bill_text_scraper()  # Initialize but don't store unused variable
    db = get_db()

    if not congress_client.enabled:
        print("❌ Congress.gov API is not enabled. Please configure CONGRESS_GOV_API_KEY.")
        return

    try:
        created_bills = []
        created_campaigns = []

        if bill_numbers:
            # Fetch specific bills
            for bill_number in bill_numbers:
                print(f"\n📋 Fetching specific bill: {bill_number}")

                try:
                    # Parse bill number
                    parsed = congress_client.parse_bill_number(bill_number)

                    # Fetch bill data
                    bill_data = congress_client.get_bill_by_number(
                        congress=parsed['congress'],
                        bill_type=parsed['bill_type'],
                        bill_number=parsed['number']
                    )

                    if bill_data:
                        # Create bill
                        bill = create_bill_from_congress_gov_data(bill_data, db)
                        if bill:
                            created_bills.append(bill)

                            # Create a campaign for the bill
                            campaign = create_campaign_for_bill(bill, CampaignType.SUPPORT, db)
                            if campaign:
                                created_campaigns.append(campaign)
                    else:
                        print(f"❌ Could not fetch data for bill {bill_number}")

                except Exception as e:
                    print(f"❌ Error processing bill {bill_number}: {e}")

        else:
            # Fetch recent bills
            print(f"\n📋 Fetching {limit} recent bills from Congress...")

            recent_bills = congress_client.get_recent_bills(
                congress=118,
                limit=limit
            )

            # Process each bill
            for bill_data in recent_bills:
                try:
                    bill = create_bill_from_congress_gov_data(bill_data, db)
                    if bill:
                        created_bills.append(bill)

                        # Create a campaign for the bill
                        campaign = create_campaign_for_bill(bill, CampaignType.SUPPORT, db)
                        if campaign:
                            created_campaigns.append(campaign)
                except Exception as e:
                    print(f"❌ Error processing bill: {e}")

        # Generate AI summaries for created bills
        print(f"\n🤖 Scheduling AI summary generation for {len(created_bills)} bills...")
        for bill in created_bills:
            if bill.text_url:
                # Schedule AI summary generation as background task
                task_generate_summary_for_bill(bill.id, db)

        print(f"\n✅ SUCCESS: Seeded {len(created_bills)} real bills and {len(created_campaigns)} campaigns")
        print("🚀 Ready for testing with real legislative data!")

    except Exception as e:
        print(f"❌ Error during seeding: {e}")
        db.rollback()
    finally:
        db.close()


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Seed database with real bill data from Congress.gov API")
    parser.add_argument('--bills', nargs='+', help='Specific bill numbers to fetch (e.g., H.R.5 S.1234)')
    parser.add_argument('--limit', type=int, default=5, help='Number of recent bills to fetch (default: 5)')

    args = parser.parse_args()

    print("🌱 ModernAction.io Real Bill Data Seeder")
    print("=======================================")

    if args.bills:
        print(f"Fetching specific bills: {', '.join(args.bills)}")
        fetch_and_seed_real_bills(bill_numbers=args.bills)
    else:
        print(f"Fetching {args.limit} recent bills from Congress...")
        fetch_and_seed_real_bills(limit=args.limit)


if __name__ == "__main__":
    main()
