#!/usr/bin/env python3
"""
Simple seeding script for ModernAction.io launch data.
This script creates the essential campaign data using direct SQL.
"""

import os
import sys
import psycopg2

def get_database_url():
    """Get database URL from environment."""
    return os.environ.get('DATABASE_URL')

def seed_campaigns():
    """Create the 5 launch campaigns using direct SQL."""

    database_url = get_database_url()
    if not database_url:
        print("❌ ERROR: DATABASE_URL environment variable not set")
        sys.exit(1)

    print("🌱 ModernAction.io Simple Campaign Seeder")
    print("==========================================")

    try:
        # Connect to database
        print("📡 Connecting to database...")
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()

        # Insert campaigns
        print("📋 Creating campaigns...")
        campaigns_sql = """
        INSERT INTO campaigns (title, description, target_audience, call_to_action, is_active, created_at, updated_at) VALUES
        ('Stop Corporate Tax Loopholes', 'Close tax loopholes that allow corporations to avoid paying their fair share', 'taxpayers', 'Contact your representative to support corporate tax reform', true, NOW(), NOW()),
        ('Protect Voting Rights', 'Ensure every eligible citizen can vote safely and securely', 'voters', 'Urge Congress to pass comprehensive voting rights legislation', true, NOW(), NOW()),
        ('Climate Action Now', 'Take immediate action to address the climate crisis', 'environmentalists', 'Demand your senators support clean energy legislation', true, NOW(), NOW()),
        ('Healthcare for All', 'Ensure affordable healthcare access for every American', 'patients', 'Tell your representative to support universal healthcare', true, NOW(), NOW()),
        ('Student Debt Relief', 'Provide relief for millions struggling with student loan debt', 'students', 'Contact Congress to support student debt forgiveness', true, NOW(), NOW())
        ON CONFLICT (title) DO NOTHING;
        """

        cur.execute(campaigns_sql)
        campaigns_created = cur.rowcount
        print(f"✓ Created {campaigns_created} campaigns")

        # Insert bills
        print("📜 Creating bills...")
        bills_sql = """
        INSERT INTO bills (bill_number, title, summary, status, campaign_id, created_at, updated_at) VALUES
        ('HR-1234', 'Corporate Tax Accountability Act', 'Closes major corporate tax loopholes and ensures fair taxation', 'COMMITTEE', (SELECT id FROM campaigns WHERE title = 'Stop Corporate Tax Loopholes'), NOW(), NOW()),
        ('S-5678', 'Voting Rights Restoration Act', 'Restores and expands voting rights protections nationwide', 'FLOOR', (SELECT id FROM campaigns WHERE title = 'Protect Voting Rights'), NOW(), NOW()),
        ('HR-9012', 'Clean Energy Investment Act', 'Massive investment in renewable energy and climate solutions', 'INTRODUCED', (SELECT id FROM campaigns WHERE title = 'Climate Action Now'), NOW(), NOW()),
        ('S-3456', 'Medicare for All Act', 'Establishes a national single-payer healthcare system', 'COMMITTEE', (SELECT id FROM campaigns WHERE title = 'Healthcare for All'), NOW(), NOW()),
        ('HR-7890', 'Student Debt Forgiveness Act', 'Provides comprehensive student loan debt relief', 'INTRODUCED', (SELECT id FROM campaigns WHERE title = 'Student Debt Relief'), NOW(), NOW())
        ON CONFLICT (bill_number) DO NOTHING;
        """

        cur.execute(bills_sql)
        bills_created = cur.rowcount
        print(f"✓ Created {bills_created} bills")

        # Commit changes
        conn.commit()

        # Verify results
        print("\n📊 Verification:")
        cur.execute("SELECT COUNT(*) FROM campaigns WHERE is_active = true")
        campaign_count = cur.fetchone()[0]
        print(f"✓ Active campaigns in database: {campaign_count}")

        cur.execute("SELECT COUNT(*) FROM bills")
        bill_count = cur.fetchone()[0]
        print(f"✓ Bills in database: {bill_count}")

        print("\n✅ SUCCESS: Database seeding complete. 5 campaigns were created.")
        print("🚀 Ready for launch!")

    except Exception as e:
        print(f"\n❌ ERROR during seeding: {e}")
        if 'conn' in locals():
            conn.rollback()
        sys.exit(1)
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    seed_campaigns()
