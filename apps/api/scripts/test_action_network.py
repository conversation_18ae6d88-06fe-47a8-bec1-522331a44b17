#!/usr/bin/env python3
"""
Test script for Action Network integration.

This script tests the Action Network API connection and basic functionality.
Run this after setting up your Action Network API key to verify the integration works.

Usage:
    python scripts/test_action_network.py
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.services.action_network_service import ActionNetworkService

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_action_network_health():
    """Test Action Network API health check"""
    print("🔍 Testing Action Network API health...")
    
    service = ActionNetworkService()
    
    if not service.enabled:
        print("❌ Action Network service is not enabled")
        print("   Please set ACTION_NETWORK_API_KEY environment variable")
        return False
    
    health_result = await service.health_check()
    
    if health_result['status'] == 'healthy':
        print("✅ Action Network API is healthy and accessible")
        return True
    else:
        print(f"❌ Action Network API health check failed: {health_result['message']}")
        return False


async def test_action_network_submission():
    """Test a sample message submission to Action Network"""
    print("\n📤 Testing Action Network message submission...")
    
    service = ActionNetworkService()
    
    if not service.enabled:
        print("❌ Action Network service is not enabled")
        return False
    
    # Sample test data (this won't actually send to real officials)
    test_message_data = {
        'person': {
            'first_name': 'Test',
            'last_name': 'User',
            'email': '<EMAIL>',
            'address_line1': '123 Test St',
            'city': 'Test City',
            'state': 'CA',
            'zip_code': '90210'
        },
        'targets': [
            {
                'first_name': 'Test',
                'last_name': 'Representative',
                'title': 'Representative',
                'chamber': 'House',
                'ocd_id': 'ocd-division/country:us/state:ca/cd:1'
            }
        ],
        'subject': 'Test Message from ModernAction Integration',
        'body': 'This is a test message to verify Action Network integration is working properly.',
        'bill_number': 'TEST-001',
        'position': 'support'
    }
    
    try:
        result = await service.submit_message(test_message_data)
        
        if result.get('status') == 'success':
            print("✅ Action Network message submission test successful")
            print(f"   Total targets: {result.get('total_targets', 0)}")
            print(f"   Successful submissions: {result.get('successful_submissions', 0)}")
            print(f"   Failed submissions: {result.get('failed_submissions', 0)}")
            return True
        else:
            print(f"❌ Action Network message submission failed: {result.get('message', 'Unknown error')}")
            if result.get('error'):
                print(f"   Error details: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Action Network submission test failed with exception: {e}")
        return False


def print_setup_instructions():
    """Print setup instructions for Action Network"""
    print("\n" + "="*60)
    print("🚀 ACTION NETWORK SETUP INSTRUCTIONS")
    print("="*60)
    print()
    print("1. Create Action Network Account:")
    print("   • Go to https://actionnetwork.org")
    print("   • Sign up for a free account")
    print("   • Verify your email address")
    print()
    print("2. Get API Key:")
    print("   • Log into your Action Network dashboard")
    print("   • Go to Settings > API & Sync")
    print("   • Generate a new API key")
    print("   • Copy the API key")
    print()
    print("3. Configure API Key:")
    print("   • For local development:")
    print("     export ACTION_NETWORK_API_KEY='your-api-key-here'")
    print()
    print("   • For production (AWS Secrets Manager):")
    print("     aws secretsmanager create-secret \\")
    print("       --name 'action-network-api-key' \\")
    print("       --secret-string 'your-api-key-here'")
    print()
    print("4. Test the Integration:")
    print("   python scripts/test_action_network.py")
    print()
    print("5. Verify in Action Network Dashboard:")
    print("   • Check the 'Messages' section for test submissions")
    print("   • Monitor API usage in Settings > API & Sync")
    print()
    print("📚 Documentation:")
    print("   • Action Network API: https://actionnetwork.org/docs")
    print("   • OSDI Spec: http://opensupporter.github.io/osdi-docs/")
    print()


async def main():
    """Main test function"""
    print("🧪 Action Network Integration Test")
    print("="*40)
    
    # Check if API key is configured
    api_key = os.getenv('ACTION_NETWORK_API_KEY')
    if not api_key:
        print("❌ ACTION_NETWORK_API_KEY environment variable not set")
        print_setup_instructions()
        return
    
    print(f"✅ API Key configured (ends with: ...{api_key[-4:]})")
    
    # Run health check
    health_ok = await test_action_network_health()
    
    if health_ok:
        # Run submission test
        submission_ok = await test_action_network_submission()
        
        if submission_ok:
            print("\n🎉 All Action Network tests passed!")
            print("   Your Action Network integration is ready for production.")
        else:
            print("\n⚠️  Action Network health check passed but submission test failed.")
            print("   Check your API permissions and try again.")
    else:
        print("\n❌ Action Network integration is not working properly.")
        print("   Please check your API key and network connection.")
    
    print_setup_instructions()


if __name__ == "__main__":
    asyncio.run(main())
