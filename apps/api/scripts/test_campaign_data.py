#!/usr/bin/env python3
"""
Script to test campaign data and action counts on the staging environment.

This script can be used to:
1. Check the current state of campaigns and their action counts
2. Trigger the action count recalculation via the admin API
3. Verify that the fix worked

Usage:
    python test_campaign_data.py --url http://staging-api-url.com
"""

import requests
import argparse
from typing import List, Dict, Any


def get_campaigns(api_url: str) -> List[Dict[str, Any]]:
    """Get all campaigns from the API."""
    try:
        response = requests.get(f"{api_url}/api/v1/campaigns")
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"Error fetching campaigns: {e}")
        return []


def recalculate_action_counts(api_url: str) -> Dict[str, Any]:
    """Trigger action count recalculation via admin API."""
    try:
        response = requests.post(f"{api_url}/api/v1/campaigns/admin/recalculate-action-counts")
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"Error recalculating action counts: {e}")
        return {"error": str(e)}


def analyze_campaigns(campaigns: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze campaign data for issues."""
    total_campaigns = len(campaigns)
    zero_action_campaigns = 0
    zero_goal_campaigns = 0
    campaigns_with_actions = 0

    for campaign in campaigns:
        actual_actions = campaign.get('actual_actions', 0)
        goal_actions = campaign.get('goal_actions', 0)

        if actual_actions == 0:
            zero_action_campaigns += 1
        else:
            campaigns_with_actions += 1

        if goal_actions == 0:
            zero_goal_campaigns += 1

    return {
        "total_campaigns": total_campaigns,
        "zero_action_campaigns": zero_action_campaigns,
        "zero_goal_campaigns": zero_goal_campaigns,
        "campaigns_with_actions": campaigns_with_actions,
        "percentage_with_zero_actions": (zero_action_campaigns / total_campaigns * 100) if total_campaigns > 0 else 0
    }


def print_campaign_summary(campaigns: List[Dict[str, Any]]):
    """Print a summary of campaign data."""
    print("\n" + "="*80)
    print("CAMPAIGN DATA SUMMARY")
    print("="*80)

    if not campaigns:
        print("No campaigns found!")
        return

    analysis = analyze_campaigns(campaigns)

    print(f"Total Campaigns: {analysis['total_campaigns']}")
    print(f"Campaigns with 0 actions: {analysis['zero_action_campaigns']} ({analysis['percentage_with_zero_actions']:.1f}%)")
    print(f"Campaigns with actions: {analysis['campaigns_with_actions']}")
    print(f"Campaigns with 0 goal: {analysis['zero_goal_campaigns']}")

    print("\nDetailed Campaign List:")
    print("-" * 80)
    print(f"{'Title':<40} {'Actual':<8} {'Goal':<8} {'Status':<12}")
    print("-" * 80)

    for campaign in campaigns[:10]:  # Show first 10 campaigns
        title = campaign.get('title', 'Unknown')[:39]
        actual = campaign.get('actual_actions', 0)
        goal = campaign.get('goal_actions', 0) or 0
        status = campaign.get('status', 'unknown')

        print(f"{title:<40} {actual:<8} {goal:<8} {status:<12}")

    if len(campaigns) > 10:
        print(f"... and {len(campaigns) - 10} more campaigns")

    print("-" * 80)


def main():
    parser = argparse.ArgumentParser(description="Test campaign data and action counts")
    parser.add_argument("--url", required=True, help="API base URL (e.g., http://staging-api-url.com)")
    parser.add_argument("--fix", action="store_true", help="Trigger action count recalculation")
    parser.add_argument("--before-after", action="store_true", help="Show before and after comparison when fixing")

    args = parser.parse_args()

    api_url = args.url.rstrip('/')

    print(f"Testing campaign data at: {api_url}")

    # Get initial state
    print("\nFetching campaigns...")
    campaigns_before = get_campaigns(api_url)

    if not campaigns_before:
        print("Failed to fetch campaigns or no campaigns found.")
        return

    print_campaign_summary(campaigns_before)

    if args.fix:
        print("\n" + "="*80)
        print("TRIGGERING ACTION COUNT RECALCULATION")
        print("="*80)

        result = recalculate_action_counts(api_url)

        if "error" in result:
            print(f"Failed to recalculate: {result['error']}")
            return

        print(f"✓ {result.get('message', 'Recalculation completed')}")

        if args.before_after:
            print("\nFetching updated campaigns...")
            campaigns_after = get_campaigns(api_url)

            if campaigns_after:
                print("\nAFTER RECALCULATION:")
                print_campaign_summary(campaigns_after)

                # Compare before and after
                analysis_before = analyze_campaigns(campaigns_before)
                analysis_after = analyze_campaigns(campaigns_after)

                print("\n" + "="*80)
                print("COMPARISON")
                print("="*80)
                print("Campaigns with 0 actions:")
                print(f"  Before: {analysis_before['zero_action_campaigns']} ({analysis_before['percentage_with_zero_actions']:.1f}%)")
                print(f"  After:  {analysis_after['zero_action_campaigns']} ({analysis_after['percentage_with_zero_actions']:.1f}%)")

                improvement = analysis_before['zero_action_campaigns'] - analysis_after['zero_action_campaigns']
                if improvement > 0:
                    print(f"  ✓ Improved: {improvement} campaigns now show correct action counts!")
                elif improvement == 0:
                    print("  → No change: Counts were already correct")
                else:
                    print("  ✗ Something went wrong: More campaigns now show zero actions")


if __name__ == "__main__":
    main()
