#!/bin/bash
# Don't exit on errors - we'll handle them manually
set +e

echo "Starting ModernAction API..."

# Run database migration with error handling
echo "Running database migration..."

# First, try to run the migration normally
echo "Running Alembic migration to create base schema..."
alembic upgrade head
MIGRATION_EXIT_CODE=$?

# Always check and add missing columns, regardless of migration status
echo "🔧 Checking for missing columns and adding if needed..."
python3 -c "
import psycopg2
import os

try:
    conn = psycopg2.connect(
        host=os.environ['DB_HOST'],
        port=os.environ['DB_PORT'],
        database=os.environ['DB_NAME'],
        user=os.environ['DB_USERNAME'],
        password=os.environ['DB_PASSWORD']
    )
    cursor = conn.cursor()
    
    # Check if reasons_for_support column exists
    cursor.execute('''
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'bills' AND column_name = 'reasons_for_support'
    ''')
    
    missing_columns = cursor.fetchone() is None
    
    if missing_columns:
        print('🔧 Adding missing columns to bills table...')
        cursor.execute('ALTER TABLE bills ADD COLUMN IF NOT EXISTS reasons_for_support TEXT;')
        cursor.execute('ALTER TABLE bills ADD COLUMN IF NOT EXISTS reasons_for_opposition TEXT;')
        print('✅ Missing columns added to bills table')
    else:
        print('✅ All required columns already exist')
    
    conn.commit()
    conn.close()
    print('✅ Schema check complete')
except Exception as e:
    print(f'❌ Failed to check schema: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ Failed to check schema, exiting..."
    exit 1
fi

echo "Migration complete. Starting API server..."

# Start the FastAPI application
exec python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4