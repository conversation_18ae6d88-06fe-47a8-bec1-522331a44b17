{"containerDefinitions": [{"name": "web", "image": "308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-api-staging:migration-v5-amd64", "cpu": 0, "links": [], "portMappings": [{"containerPort": 8000, "hostPort": 8000, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "DB_NAME", "value": "modernaction"}, {"name": "DB_PORT", "value": "5432"}, {"name": "DB_HOST", "value": "modernaction-staging-modernactiondatabasefdd241de-giohrnseld4l.csdoaiogadw5.us-east-1.rds.amazonaws.com"}, {"name": "ENVIRONMENT", "value": "staging"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:JwtSecretB8834B39-L7riLTT0OUAn-7XWK3y:jwt_secret::"}, {"name": "DB_USERNAME", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:DatabaseCredentials8547B3E7-7zaDeXDnFrx0-xLhnhX:username::"}, {"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:DatabaseCredentials8547B3E7-7zaDeXDnFrx0-xLhnhX:password::"}], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "modernaction-staging-ApiServiceTaskDefwebLogGroup57352A09-kCmYUnlEncmx", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "api"}, "secretOptions": []}, "systemControls": [], "credentialSpecs": []}], "family": "ModernActionstagingApiServiceTaskDef63E102B3", "taskRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskRole30FC0FBB-YQ5wIqK35bv0", "executionRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8J2CBwsOvvxh", "networkMode": "awsvpc", "volumes": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024"}