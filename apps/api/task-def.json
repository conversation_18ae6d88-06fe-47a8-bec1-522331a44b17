{"taskDefinitionArn": "arn:aws:ecs:us-east-1:308755113449:task-definition/ModernActionstagingApiServiceTaskDef63E102B3:20", "containerDefinitions": [{"name": "web", "image": "308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-api-staging:final-fix-official-relationship", "cpu": 0, "links": [], "portMappings": [{"containerPort": 8000, "hostPort": 8000, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "OPENSTATES_API_KEY", "value": "6080864a-b9e2-4570-b67e-0abdfcdee059"}, {"name": "GOOGLE_CIVIC_INFO_API_KEY", "value": "AIzaSyAA5ShGbL9sifQFSBdLKn2fGOa2JHvQ4To"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ENVIRONMENT", "value": "staging"}, {"name": "DB_NAME", "value": "modernaction"}, {"name": "CONGRESS_GOV_API_KEY", "value": "T1RKX9f8kEspd5nynWFRGqg18S4wd1aH3TFPh3HW"}, {"name": "DB_HOST", "value": "modernaction-staging-modernactiondatabasefdd241de-giohrnseld4l.csdoaiogadw5.us-east-1.rds.amazonaws.com"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:JwtSecretB8834B39-L7riLTT0OUAn-7XWK3y:jwt_secret::"}, {"name": "DB_USERNAME", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:DatabaseCredentials8547B3E7-7zaDeXDnFrx0-xLhnhX:username::"}, {"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:DatabaseCredentials8547B3E7-7zaDeXDnFrx0-xLhnhX:password::"}], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "modernaction-staging-ApiServiceTaskDefwebLogGroup57352A09-kCmYUnlEncmx", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "api"}, "secretOptions": []}, "systemControls": [], "credentialSpecs": []}], "family": "ModernActionstagingApiServiceTaskDef63E102B3", "taskRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskRole30FC0FBB-YQ5wIqK35bv0", "executionRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8J2CBwsOvvxh", "networkMode": "awsvpc", "revision": 20, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.secrets.asm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.17"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "registeredAt": "2025-07-24T15:24:58.601000-04:00", "registeredBy": "arn:aws:iam::308755113449:user/modernaction-deployer"}