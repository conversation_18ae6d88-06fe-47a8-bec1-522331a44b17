#!/usr/bin/env python3
"""
Test script for AI bill processing functionality
"""

import os
import sys
import asyncio
sys.path.append('.')

def test_hugging_face_ai():
    """Test the Hugging Face AI service"""
    print("🧪 Testing Hugging Face AI Service...")
    
    try:
        from app.services.ai import get_summarizer, summarize_bill
        
        # Test if transformers is available
        summarizer = get_summarizer()
        if summarizer is None:
            print("❌ Transformers library not available")
            return False
            
        # Test with sample bill text
        sample_text = """
        A BILL
        To provide for the establishment of a national infrastructure bank, and for other purposes.
        
        Be it enacted by the Senate and House of Representatives of the United States of America in Congress assembled,
        
        SECTION 1. SHORT TITLE.
        This Act may be cited as the "National Infrastructure Bank Act of 2023".
        
        SECTION 2. ESTABLISHMENT OF NATIONAL INFRASTRUCTURE BANK.
        There is established a National Infrastructure Bank to provide financing for infrastructure projects
        that will create jobs, improve economic competitiveness, and address climate change.
        """
        
        print("Testing bill summarization...")
        summary = summarize_bill(sample_text, "National Infrastructure Bank Act")
        
        if summary and len(summary) > 0:
            print(f"✅ SUCCESS: Generated summary ({len(summary)} chars)")
            print(f"   Summary: {summary[:150]}...")
            return True
        else:
            print("❌ Failed to generate summary")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_openai_ai():
    """Test the OpenAI AI service"""
    print("\n🧪 Testing OpenAI AI Service...")
    
    # Check if OpenAI API key is available
    openai_key = os.getenv('OPENAI_API_KEY')
    if not openai_key:
        print("⚠️  OpenAI API key not set - skipping OpenAI tests")
        print("   To test OpenAI features, set OPENAI_API_KEY environment variable")
        return False
    
    try:
        from app.services.ai_service import AIService
        
        ai_service = AIService()
        if not ai_service.enabled:
            print("❌ OpenAI AI service not enabled")
            return False
            
        # Test with sample bill data
        sample_text = """
        A BILL
        To provide for the establishment of a national infrastructure bank, and for other purposes.
        
        Be it enacted by the Senate and House of Representatives of the United States of America in Congress assembled,
        
        SECTION 1. SHORT TITLE.
        This Act may be cited as the "National Infrastructure Bank Act of 2023".
        
        SECTION 2. ESTABLISHMENT OF NATIONAL INFRASTRUCTURE BANK.
        There is established a National Infrastructure Bank to provide financing for infrastructure projects
        that will create jobs, improve economic competitiveness, and address climate change.
        """
        
        sample_metadata = {
            'title': 'National Infrastructure Bank Act of 2023',
            'bill_number': 'HR1234',
            'congress': 118
        }
        
        print("Testing comprehensive AI processing...")
        result = await ai_service.process_bill_complete(sample_text, sample_metadata)
        
        if result and result.get('ai_summary'):
            print(f"✅ SUCCESS: Generated comprehensive AI analysis")
            print(f"   Summary: {result['ai_summary'][:100]}...")
            print(f"   Support reasons: {len(result.get('support_reasons', []))}")
            print(f"   Oppose reasons: {len(result.get('oppose_reasons', []))}")
            print(f"   Tags: {result.get('tags', [])[:3]}")
            return True
        else:
            print("❌ Failed to generate AI analysis")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_bill_processing_pipeline():
    """Test the complete bill processing pipeline"""
    print("\n🧪 Testing Complete Bill Processing Pipeline...")
    
    try:
        # Test Congress API + AI processing together
        from app.services.congress_gov_api import CongressGovAPI
        
        congress_api = CongressGovAPI()
        if not congress_api.enabled:
            print("❌ Congress API not enabled")
            return False
            
        # Get a real bill
        print("Fetching real bill data...")
        bill_data = congress_api.get_bill_by_number(118, 'hr', 1)
        if not bill_data:
            print("❌ Failed to fetch bill data")
            return False
            
        print(f"✅ Fetched bill: {bill_data.get('title', 'Unknown')[:80]}...")
        
        # Try to get bill text
        bill_text_data = congress_api.get_bill_text(118, 'hr', 1)
        if bill_text_data:
            print(f"✅ Retrieved bill text metadata")
            # Note: The actual text would need to be scraped from the URLs
            # For now, we'll use the title and summary
            text_for_ai = f"{bill_data.get('title', '')}\n\n{bill_data.get('summary', '')}"
        else:
            text_for_ai = bill_data.get('title', '') + "\n\nNo full text available for processing."
            
        # Test AI processing if available
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            from app.services.ai_service import AIService
            ai_service = AIService()
            
            if ai_service.enabled and len(text_for_ai) > 50:
                print("Processing with OpenAI...")
                ai_result = await ai_service.process_bill_complete(text_for_ai, bill_data)
                if ai_result:
                    print(f"✅ AI processing successful")
                    return True
                else:
                    print("❌ AI processing failed")
                    return False
            else:
                print("⚠️  OpenAI not available, testing Hugging Face...")
                from app.services.ai import summarize_bill
                summary = summarize_bill(text_for_ai, bill_data.get('title', ''))
                if summary:
                    print(f"✅ Hugging Face processing successful")
                    return True
                else:
                    print("❌ Hugging Face processing failed")
                    return False
        else:
            print("⚠️  No AI processing available (no OpenAI key)")
            return True  # Still successful if we got the bill data
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("=" * 60)
    print("🤖 AI BILL PROCESSING TESTING")
    print("=" * 60)
    
    # Run tests
    test1 = test_hugging_face_ai()
    test2 = await test_openai_ai()
    test3 = await test_bill_processing_pipeline()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    print(f"Hugging Face AI:     {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"OpenAI AI:           {'✅ PASS' if test2 else '❌ FAIL/SKIP'}")
    print(f"Processing Pipeline: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    if test1 or test2:
        print("\n🎉 AI processing is working! Ready for bill summarization.")
    else:
        print("\n⚠️  AI processing needs setup. Install transformers or set OPENAI_API_KEY.")

if __name__ == "__main__":
    asyncio.run(main())
