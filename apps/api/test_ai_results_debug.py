#!/usr/bin/env python3
"""
Test what the AI service actually returns
"""

import os
import sys
import asyncio
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the path
sys.path.append('/Users/<USER>/modern-action-2.0/apps/api')

from app.services.ai_service import AIService

async def test_ai_results():
    """Test what the AI service actually returns"""
    
    print("🧪 Testing AI Service Results...")
    
    ai_service = AIService()
    
    if not ai_service.enabled:
        print("❌ AI service is not enabled")
        return
    
    # Test with a simple bill
    test_metadata = {
        'title': 'Environmental Protection Enhancement Act',
        'number': 'HR.123'
    }
    test_text = """
    This bill establishes new environmental protection standards for air quality.
    It requires companies to reduce emissions by 50% over the next 10 years.
    The bill also provides funding for clean energy research and development.
    """
    
    print(f"\n📄 Testing with bill: {test_metadata['title']}")
    print(f"📄 Text length: {len(test_text)} characters")
    
    try:
        result = await ai_service.process_bill_complete(test_text, test_metadata)
        
        print(f"\n✅ AI processing successful!")
        print(f"📊 Result keys: {list(result.keys())}")
        
        for key, value in result.items():
            print(f"\n🔍 {key}:")
            if isinstance(value, str):
                print(f"   Type: string")
                print(f"   Length: {len(value)} characters")
                print(f"   Preview: {value[:100]}...")
            elif isinstance(value, list):
                print(f"   Type: list")
                print(f"   Length: {len(value)} items")
                if value:
                    print(f"   First item: {value[0]}")
                    print(f"   All items: {value}")
                else:
                    print(f"   ❌ Empty list!")
            elif isinstance(value, dict):
                print(f"   Type: dict")
                print(f"   Keys: {list(value.keys())}")
                print(f"   Content: {json.dumps(value, indent=2)}")
            elif value is None:
                print(f"   ❌ Value is None!")
            else:
                print(f"   Type: {type(value)}")
                print(f"   Value: {value}")
        
        # Check for missing or None values
        expected_keys = ['ai_summary', 'support_reasons', 'oppose_reasons', 'amend_reasons', 'message_templates', 'tags']
        missing_keys = []
        none_keys = []
        
        for key in expected_keys:
            if key not in result:
                missing_keys.append(key)
            elif result[key] is None:
                none_keys.append(key)
        
        if missing_keys:
            print(f"\n❌ Missing keys: {missing_keys}")
        if none_keys:
            print(f"\n❌ None values: {none_keys}")
        
        if not missing_keys and not none_keys:
            print(f"\n✅ All expected keys present and non-None!")
        
    except Exception as e:
        print(f"\n❌ AI processing failed: {e}")
        import traceback
        print(f"🔍 Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_ai_results())
