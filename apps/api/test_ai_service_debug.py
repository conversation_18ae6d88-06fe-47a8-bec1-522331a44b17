#!/usr/bin/env python3
"""
Test the AI service initialization and configuration
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the path
sys.path.append('/Users/<USER>/modern-action-2.0/apps/api')

from app.services.ai_service import AIService, get_ai_service
from app.core.config import settings

async def test_ai_service_initialization():
    """Test AI service initialization"""
    
    print("🧪 Testing AI Service Initialization...")
    
    # Test 1: Check environment variables
    print(f"\n1. Environment Variables:")
    openai_key_env = os.getenv('OPENAI_API_KEY')
    print(f"   - OPENAI_API_KEY from os.getenv(): {bool(openai_key_env)}")
    if openai_key_env:
        print(f"   - Key preview: {openai_key_env[:10]}...{openai_key_env[-4:]}")
    
    # Test 2: Check settings
    print(f"\n2. Settings Configuration:")
    print(f"   - settings.OPENAI_API_KEY: {bool(settings.OPENAI_API_KEY)}")
    if settings.OPENAI_API_KEY:
        print(f"   - Settings key preview: {settings.OPENAI_API_KEY[:10]}...{settings.OPENAI_API_KEY[-4:]}")
    
    # Test 3: Create AI service directly
    print(f"\n3. Direct AI Service Creation:")
    try:
        ai_service = AIService()
        print(f"   - AI service created successfully")
        print(f"   - AI service enabled: {ai_service.enabled}")
        print(f"   - AI service has api_key: {bool(ai_service.api_key)}")
        if ai_service.api_key:
            print(f"   - AI service key preview: {ai_service.api_key[:10]}...{ai_service.api_key[-4:]}")
        
        # Test 4: Try a simple AI call
        if ai_service.enabled:
            print(f"\n4. Testing Simple AI Call:")
            try:
                test_metadata = {'title': 'Test Bill'}
                test_text = "This is a test bill about environmental protection."
                
                result = await ai_service.process_bill_complete(test_text, test_metadata)
                print(f"   - AI call successful!")
                print(f"   - Result keys: {list(result.keys())}")
                print(f"   - Summary: {result.get('ai_summary', 'No summary')[:100]}...")
                
                # Check if it's fallback data
                if "currently being processed" in result.get('ai_summary', ''):
                    print(f"   - ❌ Got fallback data - AI processing failed")
                else:
                    print(f"   - ✅ Got real AI data!")
                    
            except Exception as e:
                print(f"   - ❌ AI call failed: {e}")
                import traceback
                print(f"   - Full traceback: {traceback.format_exc()}")
        else:
            print(f"\n4. AI Service Disabled - Skipping AI Call Test")
            
    except Exception as e:
        print(f"   - ❌ AI service creation failed: {e}")
        import traceback
        print(f"   - Full traceback: {traceback.format_exc()}")
    
    # Test 5: Test get_ai_service function
    print(f"\n5. Testing get_ai_service() Function:")
    try:
        ai_service_func = get_ai_service()
        print(f"   - get_ai_service() successful")
        print(f"   - Function AI service enabled: {ai_service_func.enabled}")
    except Exception as e:
        print(f"   - ❌ get_ai_service() failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_ai_service_initialization())
