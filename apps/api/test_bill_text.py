#!/usr/bin/env python3
"""
Test bill text fetching from Congress.gov API
"""

import asyncio
import aiohttp
import json
from app.services.congress_gov_api import CongressGovAPI

async def test_bill_text_fetching():
    """Test fetching full bill text"""
    
    # Initialize the API client
    api = CongressGovAPI()
    
    print("🔍 Testing Bill Text Fetching for H.R.1 (Lower Energy Costs Act)")
    print("=" * 60)
    
    # Test 1: Get bill text versions
    print("\n1. Getting text versions...")
    text_versions = api.get_bill_text(118, "hr", 1)
    
    if text_versions:
        print(f"✅ Found {len(text_versions)} text versions")
        
        for i, version in enumerate(text_versions):
            print(f"\n   Version {i+1}:")
            print(f"   - Type: {version.get('type', 'Unknown')}")
            print(f"   - Date: {version.get('date', 'Unknown')}")
            
            formats = version.get('formats', [])
            print(f"   - Available formats: {len(formats)}")
            
            for j, fmt in enumerate(formats):
                print(f"     Format {j+1}: {fmt.get('type', 'Unknown')} - {fmt.get('url', 'No URL')}")
    else:
        print("❌ No text versions found")
        return
    
    # Test 2: Fetch actual text content
    print("\n2. Fetching actual text content...")
    
    if text_versions and len(text_versions) > 0:
        latest_version = text_versions[0]
        formats = latest_version.get('formats', [])
        
        if formats:
            # Try to get the first available format
            text_url = formats[0].get('url')
            
            if text_url:
                print(f"📥 Fetching from: {text_url}")
                
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(text_url) as response:
                            if response.status == 200:
                                content = await response.text()
                                print(f"✅ Successfully fetched {len(content)} characters")
                                
                                # Show first 500 characters
                                print("\n📄 First 500 characters:")
                                print("-" * 40)
                                print(content[:500])
                                print("-" * 40)
                                
                                # Check if it's XML, HTML, or plain text
                                if content.strip().startswith('<?xml'):
                                    print("📋 Format: XML")
                                elif content.strip().startswith('<html') or content.strip().startswith('<!DOCTYPE'):
                                    print("📋 Format: HTML")
                                else:
                                    print("📋 Format: Plain text or other")
                                    
                            else:
                                print(f"❌ HTTP Error: {response.status}")
                                
                except Exception as e:
                    print(f"❌ Error fetching text: {e}")
            else:
                print("❌ No URL found in format")
        else:
            print("❌ No formats available")
    
    print("\n" + "=" * 60)
    print("✅ Bill text fetching test complete!")

if __name__ == "__main__":
    asyncio.run(test_bill_text_fetching())
