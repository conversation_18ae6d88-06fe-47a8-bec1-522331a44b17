#!/usr/bin/env python3
"""
Test script for Congress.gov API integration
"""

import os
import sys
import asyncio
sys.path.append('.')

# Set environment variables
os.environ['CONGRESS_GOV_API_KEY'] = 'T1RKX9f8kEspd5nynWFRGqg18S4wd1aH3TFPh3HW'

def test_congress_api():
    """Test the Congress.gov API service"""
    print("🧪 Testing Congress.gov API Service...")
    print(f"API Key: {os.getenv('CONGRESS_GOV_API_KEY')[:10]}...")
    print()

    try:
        from app.services.congress_gov_api import CongressGovAPI
        congress_api = CongressGovAPI()
        print(f"Congress API enabled: {congress_api.enabled}")
        
        if congress_api.enabled:
            print("Testing bill lookup for HR1 (118th Congress)...")
            result = congress_api.get_bill_by_number(118, 'hr', 1)
            if result:
                print(f"✅ SUCCESS: Found bill")
                print(f"   Title: {result.get('title', 'No title')[:100]}...")
                print(f"   Status: {result.get('latestAction', {}).get('text', 'No status')[:100]}...")
                print(f"   Introduced: {result.get('introducedDate', 'Unknown')}")
                print(f"   Congress: {result.get('congress', 'Unknown')}")
                return True
            else:
                print("❌ No bill found or API error")
                return False
        else:
            print("❌ Congress API not enabled")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bill_text_fetch():
    """Test fetching bill text"""
    print("\n🧪 Testing Bill Text Fetch...")
    
    try:
        from app.services.congress_gov_api import CongressGovAPI
        congress_api = CongressGovAPI()
        
        if congress_api.enabled:
            print("Fetching bill text for HR1...")
            text = congress_api.get_bill_text(118, 'hr', 1)
            if text:
                print(f"✅ SUCCESS: Retrieved bill text ({len(text)} characters)")
                print(f"   First 200 chars: {text[:200]}...")
                return True
            else:
                print("❌ No bill text found")
                return False
        else:
            print("❌ Congress API not enabled")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_bills_list():
    """Test listing recent bills"""
    print("\n🧪 Testing Bills List...")
    
    try:
        from app.services.congress_gov_api import CongressGovAPI
        congress_api = CongressGovAPI()
        
        if congress_api.enabled:
            print("Fetching recent bills...")
            bills = congress_api.get_recent_bills(limit=5)
            if bills:
                print(f"✅ SUCCESS: Retrieved {len(bills)} bills")
                for i, bill in enumerate(bills[:3], 1):
                    print(f"   {i}. {bill.get('number', 'Unknown')}: {bill.get('title', 'No title')[:80]}...")
                return True
            else:
                print("❌ No bills found")
                return False
        else:
            print("❌ Congress API not enabled")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🏛️  CONGRESS.GOV API TESTING")
    print("=" * 60)
    
    # Run tests
    test1 = test_congress_api()
    test2 = test_bill_text_fetch()
    test3 = test_bills_list()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    print(f"Bill Lookup: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"Bill Text:   {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"Bills List:  {'✅ PASS' if test3 else '❌ FAIL'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 ALL TESTS PASSED! Congress.gov API is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
