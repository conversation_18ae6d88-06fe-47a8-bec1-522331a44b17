#!/usr/bin/env python3
"""
Test script for Congress.gov API integration.

This script tests the Congress.gov API connection and functionality
to verify the integration is working correctly.
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env.local')

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.services.congress_gov_api import get_congress_gov_client  # noqa: E402


def test_congress_gov_api():
    """Test Congress.gov API connection and functionality"""
    print("🧪 Testing Congress.gov API Integration")
    print("=" * 40)

    # Initialize client
    client = get_congress_gov_client()

    # Test health check
    print("\n1. Testing API Health Check...")
    health = client.health_check()
    print(f"Status: {health['status']}")
    print(f"Enabled: {health['enabled']}")
    print(f"Message: {health['message']}")

    if not health['enabled']:
        print("❌ Congress.gov API is not enabled. Please configure CONGRESS_GOV_API_KEY.")
        print("   You can get an API key at: https://api.congress.gov/sign-up/")
        return

    if health['status'] != 'healthy':
        print("❌ Congress.gov API health check failed.")
        return

    print("✅ Congress.gov API is healthy!")

    # Test fetching a specific bill
    print("\n2. Testing Specific Bill Fetch (H.R.5)...")
    try:
        # Parse H.R.5
        parsed = client.parse_bill_number("H.R.5")
        print(f"   Parsed H.R.5: {parsed}")

        bill_data = client.get_bill_by_number(
            congress=parsed['congress'],
            bill_type=parsed['bill_type'],
            bill_number=parsed['number']
        )

        if bill_data:
            print(f"✅ Successfully fetched bill: {bill_data.get('title', 'Unknown Title')}")
            print(f"   Bill Number: {bill_data.get('number', 'Unknown')}")
            print(f"   Type: {bill_data.get('type', 'Unknown')}")
            print(f"   Congress: {bill_data.get('congress', 'Unknown')}")
            print(f"   Introduced: {bill_data.get('introducedDate', 'Unknown')}")

            # Check for latest action
            if bill_data.get('latestAction'):
                latest_action = bill_data['latestAction']
                print(f"   Latest Action: {latest_action.get('text', 'Unknown')}")
                print(f"   Action Date: {latest_action.get('actionDate', 'Unknown')}")

            # Check for sponsors
            if bill_data.get('sponsors'):
                sponsors = bill_data['sponsors']
                if sponsors:
                    sponsor = sponsors[0]
                    print(f"   Sponsor: {sponsor.get('fullName', 'Unknown')} ({sponsor.get('party', 'Unknown')}-{sponsor.get('state', 'Unknown')})")

            # Check for URL
            if bill_data.get('url'):
                print(f"   URL: {bill_data['url']}")
        else:
            print("❌ Could not fetch H.R.5 data")

    except Exception as e:
        print(f"❌ Error fetching specific bill: {e}")

    # Test fetching recent bills
    print("\n3. Testing Recent Bills Fetch...")
    try:
        recent_bills = client.get_recent_bills(congress=118, limit=5)

        if recent_bills:
            print(f"✅ Successfully fetched {len(recent_bills)} recent bills")

            # Show first 3 bills
            for i, bill in enumerate(recent_bills[:3]):
                print(f"   {i+1}. {bill.get('number', 'Unknown')} - {bill.get('title', 'Unknown Title')[:60]}...")
                print(f"      Type: {bill.get('type', 'Unknown')}")
                print(f"      Introduced: {bill.get('introducedDate', 'Unknown')}")
        else:
            print("❌ Could not fetch recent bills")

    except Exception as e:
        print(f"❌ Error fetching recent bills: {e}")

    # Test bill number parsing
    print("\n4. Testing Bill Number Parsing...")
    test_numbers = ["H.R.5", "S.1234", "H.Res.100", "S.Res.50", "H.J.Res.1", "S.J.Res.2"]

    for bill_num in test_numbers:
        try:
            parsed = client.parse_bill_number(bill_num)
            print(f"   {bill_num} -> Type: {parsed['bill_type']}, Number: {parsed['number']}, Congress: {parsed['congress']}")
        except Exception as e:
            print(f"   {bill_num} -> Error: {e}")

    # Test bill text fetching
    print("\n5. Testing Bill Text Fetch (H.R.5)...")
    try:
        parsed = client.parse_bill_number("H.R.5")
        text_data = client.get_bill_text(
            congress=parsed['congress'],
            bill_type=parsed['bill_type'],
            bill_number=parsed['number']
        )

        if text_data:
            print(f"✅ Successfully fetched text versions: {len(text_data)}")

            # Show first text version
            if text_data:
                first_version = text_data[0]
                print(f"   First version: {first_version.get('type', 'Unknown')}")
                print(f"   Date: {first_version.get('date', 'Unknown')}")
                if first_version.get('formats'):
                    formats = first_version['formats']
                    print(f"   Available formats: {[f.get('type') for f in formats]}")
        else:
            print("❌ Could not fetch bill text")

    except Exception as e:
        print(f"❌ Error fetching bill text: {e}")

    # Test bill actions
    print("\n6. Testing Bill Actions Fetch (H.R.5)...")
    try:
        parsed = client.parse_bill_number("H.R.5")
        actions = client.get_bill_actions(
            congress=parsed['congress'],
            bill_type=parsed['bill_type'],
            bill_number=parsed['number']
        )

        if actions:
            print(f"✅ Successfully fetched {len(actions)} actions")

            # Show first 3 actions
            for i, action in enumerate(actions[:3]):
                print(f"   {i+1}. {action.get('actionDate', 'Unknown')}: {action.get('text', 'Unknown')[:80]}...")
        else:
            print("❌ Could not fetch bill actions")

    except Exception as e:
        print(f"❌ Error fetching bill actions: {e}")

    print("\n✅ Congress.gov API integration test completed!")


def test_seeding_script():
    """Test the seeding script functionality"""
    print("\n🧪 Testing Seeding Script")
    print("=" * 25)

    try:
        # Import the seeding function
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'scripts'))

        print("\n1. Testing seeding with specific bill (H.R.5)...")

        # Test with a specific bill (dry run - we won't actually seed)
        print("   Note: This is a dry run test - not actually seeding database")
        print("   To actually seed, run: python scripts/seed_real_bills.py --bills H.R.5")

    except Exception as e:
        print(f"❌ Error testing seeding script: {e}")


if __name__ == "__main__":
    test_congress_gov_api()
    test_seeding_script()
