#!/usr/bin/env python3
"""
Test the new full text fetching functionality
"""

import asyncio
from app.services.congress_gov_api import CongressGovAPI

async def test_full_text_fetch():
    """Test the new get_bill_full_text method"""
    
    api = CongressGovAPI()
    
    print("🔍 Testing Full Text Fetch for H.R.1 (Lower Energy Costs Act)")
    print("=" * 60)
    
    try:
        full_text = await api.get_bill_full_text(118, "hr", 1)
        
        if full_text:
            print(f"✅ Successfully fetched {len(full_text)} characters of full text")
            
            # Show first 1000 characters
            print("\n📄 First 1000 characters:")
            print("-" * 50)
            print(full_text[:1000])
            print("-" * 50)
            
            # Show some key sections
            lines = full_text.split('\n')
            print(f"\n📊 Text Statistics:")
            print(f"   - Total characters: {len(full_text)}")
            print(f"   - Total lines: {len(lines)}")
            print(f"   - Non-empty lines: {len([l for l in lines if l.strip()])}")
            
            # Look for key bill sections
            sections_found = []
            for i, line in enumerate(lines[:100]):  # Check first 100 lines
                line_lower = line.lower().strip()
                if any(keyword in line_lower for keyword in ['section', 'sec.', 'title', 'chapter']):
                    if len(line.strip()) > 5:  # Avoid very short lines
                        sections_found.append(f"Line {i+1}: {line.strip()[:80]}")
            
            if sections_found:
                print(f"\n📋 Key Sections Found:")
                for section in sections_found[:5]:  # Show first 5
                    print(f"   {section}")
            
        else:
            print("❌ Failed to fetch full text")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_full_text_fetch())
