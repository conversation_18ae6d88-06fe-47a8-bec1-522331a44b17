#!/usr/bin/env python3
"""
Test script for Google Civic Information API integration.

This script tests the Google Civic API connection and the officials service
to verify the integration is working correctly.
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env.local')

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.services.google_civic_api import get_google_civic_client  # noqa: E402
from app.services.officials import OfficialService  # noqa: E402
from app.db.database import SessionLocal  # noqa: E402


def test_google_civic_api():
    """Test Google Civic Information API connection and functionality"""
    print("🧪 Testing Google Civic Information API Integration")
    print("=" * 55)

    # Initialize client
    client = get_google_civic_client()

    # Test health check
    print("\n1. Testing API Health Check...")
    health = client.health_check()
    print(f"Status: {health['status']}")
    print(f"Enabled: {health['enabled']}")
    print(f"Message: {health['message']}")

    if not health['enabled']:
        print("❌ Google Civic API is not enabled. Please configure GOOGLE_CIVIC_INFO_API_KEY.")
        return

    if health['status'] != 'healthy':
        print("❌ Google Civic API health check failed.")
        return

    print("✅ Google Civic API is healthy!")

    # Test fetching representatives for a known zip code
    print("\n2. Testing Representatives Fetch (90210 - Beverly Hills, CA)...")
    try:
        representatives = client.get_federal_representatives_by_zip("90210")

        if representatives:
            print(f"✅ Successfully fetched {len(representatives)} representatives")

            # Show first 3 representatives
            for i, rep in enumerate(representatives[:3]):
                print(f"   {i+1}. {rep.get('name', 'Unknown')} - {rep.get('title', 'Unknown Title')}")
                print(f"      Party: {rep.get('party', 'Unknown')}")
                print(f"      Email: {rep.get('email', 'Not available')}")
                print(f"      Phone: {rep.get('phone', 'Not available')}")
                print(f"      Level: {rep.get('level', 'Unknown')}")
                print(f"      Chamber: {rep.get('chamber', 'Unknown')}")
                print()
        else:
            print("❌ Could not fetch representatives for 90210")

    except Exception as e:
        print(f"❌ Error fetching representatives: {e}")

    # Test with another zip code
    print("\n3. Testing Representatives Fetch (10001 - New York, NY)...")
    try:
        representatives = client.get_federal_representatives_by_zip("10001")

        if representatives:
            print(f"✅ Successfully fetched {len(representatives)} representatives")

            # Show representative names
            for rep in representatives:
                print(f"   - {rep.get('name', 'Unknown')} ({rep.get('title', 'Unknown')})")
        else:
            print("❌ Could not fetch representatives for 10001")

    except Exception as e:
        print(f"❌ Error fetching representatives: {e}")

    print("\n✅ Google Civic API integration test completed!")


def test_officials_service():
    """Test the officials service with Google Civic API integration"""
    print("\n🧪 Testing Officials Service Integration")
    print("=" * 40)

    # Get database session
    db = SessionLocal()

    try:
        # Initialize service
        service = OfficialService(db)

        # Test zip code lookup
        print("\n1. Testing Zip Code Lookup (90210)...")
        try:
            officials = service.get_officials_by_zip_code("90210")

            if officials:
                print(f"✅ Successfully retrieved {len(officials)} officials from service")

                # Show officials
                for official in officials:
                    print(f"   - {official.name} ({official.title})")
                    print(f"     Party: {official.party or 'Unknown'}")
                    print(f"     Email: {official.email or 'Not available'}")
                    print(f"     Phone: {official.phone or 'Not available'}")
                    print(f"     Level: {official.level}")
                    print(f"     Chamber: {official.chamber}")
                    print(f"     State: {official.state or 'Unknown'}")
                    print(f"     District: {official.district or 'Unknown'}")
                    print(f"     Google Civic ID: {official.google_civic_id or 'None'}")
                    print()
            else:
                print("❌ No officials returned from service")

        except Exception as e:
            print(f"❌ Error in officials service: {e}")
            import traceback
            traceback.print_exc()

        # Test caching - call again to see if it uses cached data
        print("\n2. Testing Caching (90210 again)...")
        try:
            officials = service.get_officials_by_zip_code("90210")

            if officials:
                print(f"✅ Successfully retrieved {len(officials)} officials (should be cached)")
            else:
                print("❌ No officials returned from cached lookup")

        except Exception as e:
            print(f"❌ Error in cached lookup: {e}")

        # Test different zip code
        print("\n3. Testing Different Zip Code (10001)...")
        try:
            officials = service.get_officials_by_zip_code("10001")

            if officials:
                print(f"✅ Successfully retrieved {len(officials)} officials for NYC")

                # Show just names
                for official in officials:
                    print(f"   - {official.name} ({official.title})")
            else:
                print("❌ No officials returned for NYC")

        except Exception as e:
            print(f"❌ Error in NYC lookup: {e}")

        print("\n✅ Officials service integration test completed!")

    finally:
        db.close()


def test_api_endpoint():
    """Test the API endpoint directly"""
    print("\n🧪 Testing API Endpoint")
    print("=" * 25)

    try:
        import requests

        # Test the endpoint
        print("\n1. Testing /api/v1/officials/by-zip/90210...")

        # Assuming API is running on localhost:8000
        response = requests.get("http://localhost:8000/api/v1/officials/by-zip/90210", timeout=30)

        if response.status_code == 200:
            data = response.json()
            print(f"✅ API endpoint returned {len(data)} officials")

            for official in data:
                print(f"   - {official.get('name', 'Unknown')} ({official.get('title', 'Unknown')})")
        else:
            print(f"❌ API endpoint returned status {response.status_code}")
            print(f"Response: {response.text}")

    except requests.exceptions.ConnectionError:
        print("⚠️  API server not running - skipping endpoint test")
    except Exception as e:
        print(f"❌ Error testing API endpoint: {e}")


if __name__ == "__main__":
    test_google_civic_api()
    test_officials_service()
    test_api_endpoint()
