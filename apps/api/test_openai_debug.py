#!/usr/bin/env python3
"""
Debug script to test OpenAI API connection and identify issues
"""

import os
import asyncio
import openai
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_openai_api():
    """Test OpenAI API with various scenarios"""
    
    # Check API key
    api_key = os.getenv('OPENAI_API_KEY')
    print(f"🔑 API Key found: {bool(api_key)}")
    if api_key:
        print(f"🔑 API Key preview: {api_key[:10]}...{api_key[-4:]}")
    
    if not api_key:
        print("❌ No OPENAI_API_KEY found in environment!")
        return
    
    try:
        client = openai.AsyncOpenAI(api_key=api_key)
        
        # Test 1: Simple API call
        print("\n🧪 Test 1: Simple API call...")
        try:
            response = await client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": "Say 'API is working' if you can read this."}],
                max_tokens=10
            )
            
            result = response.choices[0].message.content
            print(f"✅ OpenAI Response: {result}")
            
        except openai.BadRequestError as e:
            print(f"❌ BadRequestError in simple test: {e}")
            print(f"❌ Error details: {e.response.text if hasattr(e, 'response') else 'No details'}")
            return
        except Exception as e:
            print(f"❌ Unexpected error in simple test: {e}")
            return
        
        # Test 2: Test with longer content (similar to bill processing)
        print("\n🧪 Test 2: Test with bill-like content...")
        
        test_bill_text = """
        H.R.1 - Lower Energy Costs Act
        
        This Act may be cited as the "Lower Energy Costs Act".
        
        SECTION 2. DOMESTIC ENERGY PRODUCTION.
        
        (a) FINDINGS.—Congress finds that—
        (1) domestic energy production is vital to American energy security;
        (2) reducing regulatory barriers will lower energy costs for consumers;
        (3) American energy independence strengthens national security.
        
        (b) POLICY.—It is the policy of the United States to—
        (1) promote domestic energy production;
        (2) reduce unnecessary regulatory burdens on energy producers;
        (3) lower energy costs for American families and businesses.
        """
        
        prompt = f"""
        Generate specific, selectable reasons why someone would SUPPORT this bill.

        BILL: Lower Energy Costs Act
        TEXT: {test_bill_text}

        Create 3 distinct reasons someone might support this bill. Each reason should be:
        - One clear, concise sentence (10-15 words max)
        - Specific to this bill's actual provisions
        - Something a real person would say

        Respond with ONLY a valid JSON array, no other text:
        ["Reason 1", "Reason 2", "Reason 3"]

        JSON Array:
        """
        
        try:
            response = await client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.3
            )
            
            result = response.choices[0].message.content
            print(f"✅ Bill analysis response: {result}")
            
            # Try to parse as JSON
            import json
            try:
                parsed = json.loads(result.strip())
                print(f"✅ JSON parsing successful: {parsed}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                print(f"❌ Raw response: {result}")
            
        except openai.BadRequestError as e:
            print(f"❌ BadRequestError in bill test: {e}")
            print(f"❌ Error details: {e.response.text if hasattr(e, 'response') else 'No details'}")
            
            # Check if it's a model issue
            print("\n🧪 Test 3: Try with gpt-3.5-turbo...")
            try:
                response = await client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": "Say 'GPT-3.5 is working'"}],
                    max_tokens=10
                )
                result = response.choices[0].message.content
                print(f"✅ GPT-3.5 Response: {result}")
            except Exception as e2:
                print(f"❌ GPT-3.5 also failed: {e2}")
            
        except Exception as e:
            print(f"❌ Unexpected error in bill test: {e}")
            
    except openai.AuthenticationError:
        print("❌ OpenAI API Key is invalid!")
    except openai.RateLimitError:
        print("❌ OpenAI API rate limit exceeded!")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(test_openai_api())
