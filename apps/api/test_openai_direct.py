#!/usr/bin/env python3
"""
Direct test of OpenAI API to debug issues
"""

import asyncio
import os
from dotenv import load_dotenv
import openai

# Load environment variables
load_dotenv()

async def test_openai_direct():
    """Test OpenAI API directly"""
    api_key = os.getenv('OPENAI_API_KEY')
    
    if not api_key:
        print("❌ OPENAI_API_KEY not found in environment")
        return
    
    print(f"✅ API Key found: {api_key[:20]}...")
    
    try:
        client = openai.AsyncOpenAI(api_key=api_key)
        
        # Simple test prompt
        prompt = """
        You are a civic education expert. Summarize this bill in plain English for concerned citizens.

        BILL TITLE: Lower Energy Costs Act
        BILL TEXT: BILL TITLE: Lower Energy Costs Act

        SPONSOR: Rep. Sc<PERSON>e, <PERSON> [R-LA-1]

        LATEST ACTION: The Clerk was authorized to correct section numbers, punctuation, and cross references, and to make other necessary technical and conforming corrections in the engrossment of H.R. 1.

        BILL CONTENT: [Full text not yet available - analysis based on title and summary]

        Create a detailed summary with these specific sections:

        **What This Bill Does:**
        [2-3 sentences explaining the bill's main purpose and key provisions in simple terms]

        **Who This Affects:**
        [1-2 sentences identifying the specific groups, industries, or demographics impacted]

        **Why It Matters to You:**
        [2-3 sentences explaining the practical impact on everyday citizens and why they should care]

        **Key Provisions:**
        [3-4 bullet points of the most important specific changes or requirements]

        Requirements:
        - Use plain English, avoid political jargon
        - Be completely neutral and factual
        - Focus on concrete impacts and changes
        - Make it accessible to general public

        Summary:
        """
        
        print("🔄 Making OpenAI API call...")
        
        response = await client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=800,
            temperature=0.3
        )
        
        print("✅ OpenAI API call successful!")
        print("\n📝 Response:")
        print("=" * 50)
        print(response.choices[0].message.content.strip())
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ OpenAI API call failed: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_openai_direct())
