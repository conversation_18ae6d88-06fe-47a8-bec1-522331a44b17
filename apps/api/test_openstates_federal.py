#!/usr/bin/env python3
"""
Investigation script for OpenStates API federal bill capabilities.

This script tests whether OpenStates API can search for federal bills
by bill number (e.g., H.R.5) and return the required information:
- Official title
- Summary
- Latest status
- URL to full text
"""

import sys
import os
import requests
from typing import Dict, Any

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.core.config import get_settings  # noqa: E402

settings = get_settings()


def test_openstates_federal_search():
    """
    Test OpenStates API for federal bill search capabilities.

    Key question: Can we search for a specific federal bill (e.g., H.R.5)
    and get back its official title, summary, status, and full text URL?
    """
    print("🔍 Investigating OpenStates API for Federal Bill Search")
    print("=" * 60)

    if not settings.OPENSTATES_API_KEY:
        print("❌ OPENSTATES_API_KEY not configured. Cannot test OpenStates API.")
        return False

    base_url = "https://v3.openstates.org"
    headers = {
        "X-API-KEY": settings.OPENSTATES_API_KEY,
        "Accept": "application/json"
    }

    # Test 1: Search for bills with query parameters
    print("\n1. Testing Bills Search Endpoint...")
    print("   Endpoint: GET /bills")

    # Try searching for H.R.5 (a common bill number)
    search_params = {
        "q": "H.R.5",
        "jurisdiction": "us",  # Federal jurisdiction
    }

    try:
        response = requests.get(f"{base_url}/bills", headers=headers, params=search_params, timeout=30)
        print(f"   Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            print(f"   Results Found: {len(results)}")

            if results:
                print("   ✅ Search endpoint works!")

                # Examine first result
                first_bill = results[0]
                print("\n   First Result Analysis:")
                print(f"   - ID: {first_bill.get('id', 'N/A')}")
                print(f"   - Title: {first_bill.get('title', 'N/A')[:100]}...")
                print(f"   - Identifier: {first_bill.get('identifier', 'N/A')}")
                print(f"   - Classification: {first_bill.get('classification', 'N/A')}")
                print(f"   - Session: {first_bill.get('session', 'N/A')}")
                print(f"   - Jurisdiction: {first_bill.get('jurisdiction', 'N/A')}")
                print(f"   - Subject: {first_bill.get('subject', 'N/A')}")
                print(f"   - Abstract: {first_bill.get('abstract', 'N/A')[:100] if first_bill.get('abstract') else 'N/A'}...")

                # Check for federal bills specifically
                federal_bills = [bill for bill in results if bill.get('jurisdiction', {}).get('name') == 'United States']
                print(f"   - Federal Bills: {len(federal_bills)}")

                if federal_bills:
                    print("   ✅ Found federal bills!")
                    return analyze_federal_bill(federal_bills[0], headers)
                else:
                    print("   ❌ No federal bills found in search results")
            else:
                print("   ❌ No results found for H.R.5 search")
        else:
            print(f"   ❌ Search failed with status {response.status_code}")
            print(f"   Response: {response.text[:200]}...")

    except Exception as e:
        print(f"   ❌ Error testing search endpoint: {e}")

    # Test 2: Try different search approaches
    print("\n2. Testing Alternative Search Methods...")

    # Try searching with different parameters
    alternative_searches = [
        {"q": "H.R. 5", "jurisdiction": "us"},
        {"q": "HR5", "jurisdiction": "us"},
        {"q": "Equality Act", "jurisdiction": "us"},
        {"identifier": "H.R.5"},
        {"classification": "bill", "jurisdiction": "us"}
    ]

    for i, params in enumerate(alternative_searches, 1):
        print(f"\n   2.{i} Testing with params: {params}")
        try:
            response = requests.get(f"{base_url}/bills", headers=headers, params=params, timeout=30)
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                federal_bills = [bill for bill in results if bill.get('jurisdiction', {}).get('name') == 'United States']
                print(f"        Results: {len(results)}, Federal: {len(federal_bills)}")

                if federal_bills:
                    print("        ✅ Found federal bills with this search!")
                    # Don't analyze again, just note success
            else:
                print(f"        ❌ Failed with status {response.status_code}")
        except Exception as e:
            print(f"        ❌ Error: {e}")

    # Test 3: Check if we can get bill details by ID
    print("\n3. Testing Individual Bill Fetch...")

    # Try to fetch a known federal bill ID (if we found one above)
    # For now, let's try a generic approach
    try:
        # First, let's see what jurisdictions are available
        response = requests.get(f"{base_url}/jurisdictions", headers=headers, timeout=30)
        if response.status_code == 200:
            jurisdictions = response.json()
            print(f"   Available jurisdictions: {len(jurisdictions.get('results', []))}")

            # Look for US federal jurisdiction
            us_jurisdiction = None
            for jurisdiction in jurisdictions.get('results', []):
                if jurisdiction.get('name') == 'United States':
                    us_jurisdiction = jurisdiction
                    break

            if us_jurisdiction:
                print(f"   ✅ Found US federal jurisdiction: {us_jurisdiction.get('id')}")
            else:
                print("   ❌ Could not find US federal jurisdiction")

    except Exception as e:
        print(f"   ❌ Error checking jurisdictions: {e}")

    return False


def analyze_federal_bill(bill_data: Dict[str, Any], headers: Dict[str, str]) -> bool:
    """
    Analyze a federal bill to see if it has all required information.

    Required information:
    - Official title ✓
    - Summary/abstract
    - Latest status
    - URL to full text
    """
    print(f"\n📋 Analyzing Federal Bill: {bill_data.get('identifier', 'Unknown')}")
    print("=" * 50)

    # Check required fields
    has_title = bool(bill_data.get('title'))
    has_summary = bool(bill_data.get('abstract') or bill_data.get('summary'))

    print(f"✅ Title: {bill_data.get('title', 'N/A')[:100]}...")
    print(f"{'✅' if has_summary else '❌'} Summary/Abstract: {bill_data.get('abstract', bill_data.get('summary', 'N/A'))[:100] if has_summary else 'Not available'}...")

    # Check for status information
    actions = bill_data.get('actions', [])
    latest_action = actions[0] if actions else None
    has_status = bool(latest_action)

    print(f"{'✅' if has_status else '❌'} Latest Action: {latest_action.get('description', 'N/A') if latest_action else 'No actions found'}")

    # Check for full text
    sources = bill_data.get('sources', [])
    versions = bill_data.get('versions', [])
    documents = bill_data.get('documents', [])

    has_full_text_url = bool(sources or versions or documents)

    print(f"{'✅' if has_full_text_url else '❌'} Full Text Sources:")
    if sources:
        for source in sources[:2]:  # Show first 2 sources
            print(f"   - Source: {source.get('url', 'N/A')}")
    if versions:
        for version in versions[:2]:  # Show first 2 versions
            print(f"   - Version: {version.get('url', 'N/A')}")
    if documents:
        for doc in documents[:2]:  # Show first 2 documents
            print(f"   - Document: {doc.get('url', 'N/A')}")

    if not has_full_text_url:
        print("   No full text URLs found")

    # Try to fetch more details if we have a bill ID
    bill_id = bill_data.get('id')
    if bill_id:
        print("\n🔍 Fetching detailed bill information...")
        try:
            response = requests.get(f"https://v3.openstates.org/bills/{bill_id}", headers=headers, timeout=30)
            if response.status_code == 200:
                detailed_data = response.json()
                print("   ✅ Successfully fetched detailed bill data")

                # Check if detailed data has more information
                detailed_sources = detailed_data.get('sources', [])
                detailed_versions = detailed_data.get('versions', [])

                if detailed_sources or detailed_versions:
                    print("   ✅ Detailed data includes additional source/version information")
                    has_full_text_url = True

            else:
                print(f"   ❌ Failed to fetch detailed data: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error fetching detailed data: {e}")

    # Final assessment
    print("\n📊 ASSESSMENT SUMMARY:")
    print(f"   Title: {'✅' if has_title else '❌'}")
    print(f"   Summary: {'✅' if has_summary else '❌'}")
    print(f"   Status: {'✅' if has_status else '❌'}")
    print(f"   Full Text URL: {'✅' if has_full_text_url else '❌'}")

    all_requirements_met = has_title and has_summary and has_status and has_full_text_url

    print(f"\n🎯 FINAL RESULT: {'✅ YES' if all_requirements_met else '❌ NO'}")

    if all_requirements_met:
        print("   OpenStates API CAN provide all required information for federal bills!")
    else:
        missing = []
        if not has_title:
            missing.append("title")
        if not has_summary:
            missing.append("summary")
        if not has_status:
            missing.append("status")
        if not has_full_text_url:
            missing.append("full text URL")
        print(f"   Missing: {', '.join(missing)}")

    return all_requirements_met


if __name__ == "__main__":
    print("🧪 OpenStates API Federal Bill Investigation")
    print("=" * 45)
    print("Question: Can OpenStates API search for federal bills by number")
    print("          and return title, summary, status, and full text URL?")
    print()

    result = test_openstates_federal_search()

    print(f"\n🏁 FINAL ANSWER: {'YES' if result else 'NO'}")

    if result:
        print("✅ OpenStates API can meet our federal bill data needs!")
        print("   Recommendation: Proceed with Path A (OpenStates)")
    else:
        print("❌ OpenStates API cannot fully meet our federal bill data needs")
        print("   Recommendation: Proceed with Path B (GovTrack API)")
