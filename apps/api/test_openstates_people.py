#!/usr/bin/env python3
"""
Test script for OpenStates API people.geo endpoint.

This script tests the OpenStates people.geo endpoint to understand
how to fetch legislators by zip code for our officials service.
"""

import sys
import os
import requests
from typing import Dict, Any, List

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.core.config import get_settings

settings = get_settings()


def test_openstates_people_geo():
    """
    Test OpenStates API people.geo endpoint for fetching legislators by location.
    """
    print("🔍 Testing OpenStates API people.geo endpoint")
    print("=" * 60)

    if not settings.OPEN_STATES_API_KEY:
        print("❌ OPEN_STATES_API_KEY not configured. Cannot test OpenStates API.")
        return False

    base_url = "https://v3.openstates.org"
    headers = {
        "X-API-KEY": settings.OPEN_STATES_API_KEY,
        "Accept": "application/json"
    }

    # Test coordinates for known locations
    test_locations = [
        {"name": "Beverly Hills, CA (90210)", "lat": 34.0901, "lng": -118.4065},
        {"name": "Manhattan, NY (10001)", "lat": 40.7505, "lng": -73.9934},
        {"name": "Washington, DC (20001)", "lat": 38.9072, "lng": -77.0369}
    ]

    for location in test_locations:
        print(f"\n📍 Testing location: {location['name']}")
        print("-" * 40)

        # Test people.geo endpoint with coordinates
        try:
            params = {
                "lat": location["lat"],
                "lng": location["lng"]
            }

            response = requests.get(f"{base_url}/people.geo", headers=headers, params=params, timeout=30)
            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                print(f"Results Found: {len(results)}")

                if results:
                    print("✅ Found legislators!")

                    # Analyze first few results
                    for i, person in enumerate(results[:3]):
                        print(f"\n  Legislator {i+1}:")
                        print(f"  - ID: {person.get('id', 'N/A')}")
                        print(f"  - Name: {person.get('name', 'N/A')}")
                        print(f"  - Party: {person.get('party', 'N/A')}")

                        current_role = person.get('current_role', {})
                        print(f"  - Title: {current_role.get('title', 'N/A')}")
                        print(f"  - District: {current_role.get('district', 'N/A')}")
                        print(f"  - Chamber: {current_role.get('chamber', 'N/A')}")

                        # Check for contact info
                        contact_details = person.get('contact_details', [])
                        print(f"  - Contact Details: {len(contact_details)} items")
                        for contact in contact_details[:2]:  # Show first 2
                            print(f"    * {contact.get('type', 'unknown')}: {contact.get('value', 'N/A')}")

                        # Check for social media links
                        links = person.get('links', [])
                        print(f"  - Links: {len(links)} items")
                        for link in links[:2]:  # Show first 2
                            print(f"    * {link.get('note', 'unknown')}: {link.get('url', 'N/A')}")
                else:
                    print("❌ No legislators found")
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Response: {response.text[:200]}...")

        except Exception as e:
            print(f"❌ Error testing location {location['name']}: {e}")

    # Test alternative endpoint - regular people search with jurisdiction
    print(f"\n🔍 Testing alternative: /people endpoint with jurisdiction filter")
    print("-" * 60)

    try:
        params = {
            "jurisdiction": "us",  # Federal jurisdiction
            "current_role": "true"
        }

        response = requests.get(f"{base_url}/people", headers=headers, params=params, timeout=30)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            print(f"Federal legislators found: {len(results)}")

            if results:
                print("✅ Found federal legislators!")

                # Show first few
                for i, person in enumerate(results[:3]):
                    print(f"\n  Federal Legislator {i+1}:")
                    print(f"  - Name: {person.get('name', 'N/A')}")
                    print(f"  - Party: {person.get('party', 'N/A')}")
                    current_role = person.get('current_role', {})
                    print(f"  - Title: {current_role.get('title', 'N/A')}")
                    print(f"  - District: {current_role.get('district', 'N/A')}")
                    print(f"  - Chamber: {current_role.get('chamber', 'N/A')}")
        else:
            print(f"❌ Request failed with status {response.status_code}")

    except Exception as e:
        print(f"❌ Error testing federal people endpoint: {e}")

    return True


if __name__ == "__main__":
    print("🧪 OpenStates API People Investigation")
    print("=" * 45)
    print("Testing people.geo endpoint for zip code lookups")
    print()

    test_openstates_people_geo()

    print(f"\n🏁 Test completed!")
