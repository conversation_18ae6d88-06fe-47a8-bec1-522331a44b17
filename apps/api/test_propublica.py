#!/usr/bin/env python3
"""
Test script for ProPublica Congress API integration.

This script tests the ProPublica API connection and fetches sample bill data
to verify the integration is working correctly.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.services.propublica_api import get_propublica_client  # noqa: E402
from app.services.bill_text_scraper import get_bill_text_scraper  # noqa: E402


def test_propublica_api():
    """Test ProPublica Congress API connection and functionality"""
    print("🧪 Testing ProPublica Congress API Integration")
    print("=" * 50)

    # Initialize client
    client = get_propublica_client()

    # Test health check
    print("\n1. Testing API Health Check...")
    health = client.health_check()
    print(f"Status: {health['status']}")
    print(f"Enabled: {health['enabled']}")
    print(f"Message: {health['message']}")

    if not health['enabled']:
        print("❌ ProPublica API is not enabled. Please configure PROPUBLICA_CONGRESS_API_KEY.")
        return

    if health['status'] != 'healthy':
        print("❌ ProPublica API health check failed.")
        return

    print("✅ ProPublica API is healthy!")

    # Test fetching a specific bill
    print("\n2. Testing Specific Bill Fetch (H.R.5)...")
    try:
        bill_data = client.get_bill_by_number(
            congress=118,
            chamber="house",
            bill_number="hr5"
        )

        if bill_data:
            print(f"✅ Successfully fetched bill: {bill_data.get('title', 'Unknown Title')}")
            print(f"   Bill Number: {bill_data.get('number', 'Unknown')}")
            print(f"   Sponsor: {bill_data.get('sponsor_name', 'Unknown')}")
            print(f"   Introduced: {bill_data.get('introduced_date', 'Unknown')}")
            print(f"   Latest Action: {bill_data.get('latest_major_action', 'Unknown')}")

            # Test getting full text URL
            text_url = client.get_bill_full_text_url(bill_data)
            if text_url:
                print(f"   Full Text URL: {text_url}")
            else:
                print("   Full Text URL: Not available")
        else:
            print("❌ Could not fetch H.R.5 data")

    except Exception as e:
        print(f"❌ Error fetching specific bill: {e}")

    # Test fetching recent bills
    print("\n3. Testing Recent Bills Fetch...")
    try:
        recent_bills = client.get_recent_bills(congress=118, chamber="house", bill_type="introduced")

        if recent_bills:
            print(f"✅ Successfully fetched {len(recent_bills)} recent House bills")

            # Show first 3 bills
            for i, bill in enumerate(recent_bills[:3]):
                print(f"   {i+1}. {bill.get('number', 'Unknown')} - {bill.get('title', 'Unknown Title')[:60]}...")
        else:
            print("❌ Could not fetch recent bills")

    except Exception as e:
        print(f"❌ Error fetching recent bills: {e}")

    # Test bill number parsing
    print("\n4. Testing Bill Number Parsing...")
    test_numbers = ["H.R.5", "S.1234", "H.Res.100", "S.Res.50"]

    for bill_num in test_numbers:
        parsed = client.parse_bill_number(bill_num)
        print(f"   {bill_num} -> Chamber: {parsed['chamber']}, Type: {parsed['type']}, Number: {parsed['number']}")

    print("\n✅ ProPublica API integration test completed!")


def test_bill_text_scraper():
    """Test bill text scraping functionality"""
    print("\n🧪 Testing Bill Text Scraper")
    print("=" * 30)

    scraper = get_bill_text_scraper()

    # Test with a sample Congress.gov URL
    test_url = "https://www.congress.gov/bill/118th-congress/house-bill/5/text"

    print("\n1. Testing URL validation...")
    is_valid = scraper.validate_url(test_url)
    print(f"URL {test_url} is valid: {is_valid}")

    if is_valid:
        print("\n2. Testing text extraction...")
        try:
            bill_text = scraper.fetch_bill_text(test_url)

            if bill_text:
                print(f"✅ Successfully extracted {len(bill_text)} characters of bill text")
                print(f"   Preview: {bill_text[:200]}...")

                # Test summary extraction
                summary = scraper.get_bill_summary_from_text(bill_text, max_length=300)
                if summary:
                    print(f"   Summary: {summary}")
            else:
                print("❌ Could not extract bill text")

        except Exception as e:
            print(f"❌ Error extracting bill text: {e}")

    print("\n✅ Bill text scraper test completed!")


if __name__ == "__main__":
    test_propublica_api()
    test_bill_text_scraper()
