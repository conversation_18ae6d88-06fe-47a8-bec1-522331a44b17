#!/usr/bin/env python3
"""
Test script for simple action tracking - KISS approach.

This script tests the basic functionality:
1. Populate reasoning options for test bills
2. Test simple analytics endpoints  
3. Test action tracking integration
"""

import asyncio
import requests
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000/api/v1"

# Test bill ID (using existing test bill)
TEST_BILL_ID = "bd9c4dfb-a7b7-406d-ac41-263f36548c50"

def test_health_check():
    """Test simple health check endpoint"""
    print("🔍 Testing simple health check...")
    
    try:
        response = requests.get(f"{BASE_URL}/simple/health/simple", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Health check passed: {result['status']} - {result['message']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def populate_test_reasoning_options():
    """Populate reasoning options for test bill"""
    print(f"📝 Populating reasoning options for bill {TEST_BILL_ID}...")
    
    # Support reasons
    support_reasons = [
        "Will create affordable housing for families",
        "Generates construction jobs in the community", 
        "Helps prevent displacement due to rising rents",
        "Provides housing for seniors and disabled individuals",
        "Reduces homelessness in our area"
    ]
    
    # Oppose reasons  
    oppose_reasons = [
        "Too expensive for taxpayers to fund",
        "Government should not be in housing business",
        "Will not solve the underlying housing crisis",
        "Could decrease property values in neighborhood",
        "Private sector can handle this more efficiently"
    ]
    
    # Amendment reasons
    amend_reasons = [
        "Should include more funding for rural areas",
        "Needs stronger environmental requirements",
        "Should prioritize veterans and first responders",
        "Timeline is too aggressive, needs extension",
        "Should include small business incentives"
    ]
    
    success_count = 0
    
    for stance, reasons in [("support", support_reasons), ("oppose", oppose_reasons), ("amend", amend_reasons)]:
        try:
            response = requests.post(
                f"{BASE_URL}/simple/bills/{TEST_BILL_ID}/reasoning-options",
                params={"stance": stance},
                json=reasons,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Added {result['added_count']} {stance} reasons")
                success_count += 1
            else:
                print(f"❌ Failed to add {stance} reasons: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error adding {stance} reasons: {e}")
    
    return success_count == 3

def test_get_reasoning_options():
    """Test getting reasoning options"""
    print("🔍 Testing get reasoning options...")
    
    for stance in ["support", "oppose", "amend"]:
        try:
            response = requests.get(
                f"{BASE_URL}/simple/bills/{TEST_BILL_ID}/reasoning-options",
                params={"stance": stance},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                options = result.get('reasoning_options', [])
                print(f"✅ Found {len(options)} {stance} options")
                
                # Show first few options
                for i, option in enumerate(options[:2]):
                    print(f"   {i+1}. {option['reason_text']}")
                    
            else:
                print(f"❌ Failed to get {stance} options: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error getting {stance} options: {e}")
            return False
    
    return True

def test_simple_bill_stats():
    """Test getting simple bill stats"""
    print("🔍 Testing simple bill stats...")
    
    try:
        response = requests.get(f"{BASE_URL}/simple/bills/{TEST_BILL_ID}/stats", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            stats = result.get('stats', {})
            print(f"✅ Bill stats: {stats['support']} support, {stats['oppose']} oppose, {stats['amend']} amend, {stats['total']} total")
            return True
        else:
            print(f"❌ Failed to get bill stats: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error getting bill stats: {e}")
        return False

def test_action_submission_with_reasoning():
    """Test action submission with reasoning tracking"""
    print("🔍 Testing action submission with reasoning...")
    
    # First, get some reasoning options to use
    try:
        response = requests.get(
            f"{BASE_URL}/simple/bills/{TEST_BILL_ID}/reasoning-options",
            params={"stance": "support"},
            timeout=10
        )
        
        if response.status_code != 200:
            print("❌ Could not get reasoning options for testing")
            return False
            
        options = response.json().get('reasoning_options', [])
        if not options:
            print("❌ No reasoning options available for testing")
            return False
            
        # Use first two reasoning options
        selected_reasons = [options[0]['id'], options[1]['id']] if len(options) >= 2 else [options[0]['id']]
        custom_reason = "This bill is important for my community because we have a severe housing shortage."
        
    except Exception as e:
        print(f"❌ Error getting reasoning options: {e}")
        return False
    
    # Test action submission
    action_data = {
        "bill_id": TEST_BILL_ID,
        "stance": "support",
        "selected_reasons": selected_reasons,  # Use actual selected reasons
        "custom_reasons": [],
        "personal_stories": "",
        "custom_message": "",
        "zip_code": "10002",
        "email": "<EMAIL>",
        "address": "123 Test St",
        "city": "New York", 
        "state": "NY",
        "custom_reason": custom_reason
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/actions/submit-dev",
            json=action_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            action_id = result.get('action_id')
            print(f"✅ Action submitted successfully: {action_id}")
            
            # Test getting action summary
            if action_id:
                summary_response = requests.get(
                    f"{BASE_URL}/simple/actions/{action_id}/summary",
                    timeout=10
                )
                
                if summary_response.status_code == 200:
                    summary = summary_response.json().get('action_summary', {})
                    print(f"✅ Action summary retrieved:")
                    print(f"   Stance: {summary.get('stance')}")
                    print(f"   Selected reasons: {len(summary.get('selected_reasons', []))}")
                    print(f"   Custom reason: {'Yes' if summary.get('custom_reason') else 'No'}")
                    return True
                else:
                    print(f"❌ Failed to get action summary: {summary_response.status_code}")
                    
        else:
            print(f"❌ Action submission failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error submitting action: {e}")
        
    return False

def test_top_reasons():
    """Test getting top reasons after some actions"""
    print("🔍 Testing top reasons...")
    
    try:
        response = requests.get(
            f"{BASE_URL}/simple/bills/{TEST_BILL_ID}/top-reasons",
            params={"stance": "support", "limit": 3},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            reasons = result.get('top_reasons', [])
            print(f"✅ Found {len(reasons)} top support reasons:")
            
            for i, reason in enumerate(reasons):
                print(f"   {i+1}. {reason['reason_text']} ({reason['count']} users)")
                
            return True
        else:
            print(f"❌ Failed to get top reasons: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting top reasons: {e}")
        
    return False

def test_custom_reasons():
    """Test getting custom reasons"""
    print("🔍 Testing custom reasons...")
    
    try:
        response = requests.get(
            f"{BASE_URL}/simple/bills/{TEST_BILL_ID}/custom-reasons",
            params={"stance": "support", "limit": 5},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            reasons = result.get('custom_reasons', [])
            print(f"✅ Found {len(reasons)} custom support reasons:")
            
            for i, reason in enumerate(reasons):
                print(f"   {i+1}. \"{reason}\"")
                
            return True
        else:
            print(f"❌ Failed to get custom reasons: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting custom reasons: {e}")
        
    return False

def main():
    """Run all simple tracking tests"""
    print("🚀 Starting Simple Action Tracking Tests")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_check),
        ("Populate Reasoning Options", populate_test_reasoning_options),
        ("Get Reasoning Options", test_get_reasoning_options),
        ("Simple Bill Stats", test_simple_bill_stats),
        ("Action Submission with Reasoning", test_action_submission_with_reasoning),
        ("Top Reasons", test_top_reasons),
        ("Custom Reasons", test_custom_reasons),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All simple tracking tests PASSED!")
        print("✅ Ready to move to advanced features (Option 2)")
    else:
        print(f"⚠️  {total - passed} tests failed - need to fix issues first")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)