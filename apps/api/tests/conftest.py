import pytest
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from dotenv import load_dotenv

from app.main import app
from app.db.database import get_db
from app.db.base_class import Base

# Load test environment variables from .env.test file
load_dotenv(".env.test")


@pytest.fixture(scope="session")
def test_engine():
    """Create a test database and engine for the entire test session"""
    # Use DATABASE_URL from environment, defaulting to SQLite for local testing
    database_url = os.getenv("DATABASE_URL", "sqlite:///./test_modernaction.db")

    if database_url.startswith("sqlite"):
        # SQLite configuration for local testing
        from sqlalchemy.pool import StaticPool
        test_engine = create_engine(
            database_url,
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
            echo=False
        )
        # Create all tables
        Base.metadata.create_all(bind=test_engine)

        yield test_engine

        # Cleanup for SQLite
        test_engine.dispose()
        # Optionally remove the test database file
        db_file = database_url.replace("sqlite:///", "")
        if os.path.exists(db_file):
            os.remove(db_file)

    else:
        # PostgreSQL configuration for CI - use the provided DATABASE_URL directly
        # The CI environment provides a complete DATABASE_URL like:
        # postgresql://postgres:postgres@localhost:5432/modernaction_test
        test_engine = create_engine(database_url, echo=False)

        # Create all tables
        Base.metadata.create_all(bind=test_engine)

        yield test_engine

        # Cleanup for PostgreSQL - just dispose the engine
        # In CI, the database is managed by the service container
        test_engine.dispose()


@pytest.fixture
def test_db_session(test_engine):
    """
    Yields a new, isolated transaction for each test function.
    Rolls back the transaction after the test is complete.
    This ensures perfect test isolation.
    """
    connection = test_engine.connect()

    # begin a non-ORM transaction
    transaction = connection.begin()

    # bind an individual session to the connection
    Session = sessionmaker(bind=connection)
    session = Session()

    yield session

    # rollback the transaction and close the connection
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def test_db(test_engine):
    """Create a test database session factory for each test function"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    yield TestingSessionLocal


@pytest.fixture(scope="function")
def test_client(test_db_session):
    """Create a test client with test database"""
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass  # Session cleanup handled by test_db_session fixture

    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as client:
        yield client

    app.dependency_overrides.clear()


@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User",
        "zip_code": "12345",
        "auth0_user_id": "auth0|test123456789"  # Required for Auth0 integration
    }


@pytest.fixture
def sample_bill_data():
    """Sample bill data for testing"""
    return {
        "title": "Test Bill",
        "description": "A test bill for testing purposes",
        "bill_number": "HR-123",
        "bill_type": "house_bill",
        "status": "introduced",
        "session_year": 2024,
        "chamber": "house",
        "state": "federal",
        "full_text": "This is the full text of the test bill.",
        "summary": "This is a summary of the test bill.",
        "is_featured": False,
        "priority_score": 0
    }


@pytest.fixture
def sample_official_data():
    """Sample official data for testing"""
    return {
        "name": "Test Official",
        "title": "Representative",
        "party": "Independent",
        "email": "<EMAIL>",
        "phone": "555-0123",
        "level": "federal",
        "chamber": "house",
        "state": "CA",
        "district": "1",
        "is_active": True
    }


@pytest.fixture
def sample_campaign_data():
    """Sample campaign data for testing"""
    return {
        "title": "Test Campaign",
        "description": "A test campaign for testing purposes",
        "campaign_type": "support",
        "status": "active",
        "call_to_action": "Support this important legislation!",
        "is_featured": False,
        "is_public": True,
        "requires_verification": False,
        "actual_actions": 0
    }
