# tests/test_ai_service.py
"""
Test suite for the AI service module.

These tests verify the bill summarization functionality and ensure
proper error handling and performance optimization.
"""

import pytest
from unittest.mock import Mock, patch
from app.services.ai import (
    get_summarizer,
    summarize_text,
    summarize_bill,
    get_model_info,
    health_check
)


class TestAIService:
    """Test suite for AI service functionality"""

    def setup_method(self):
        """Clear the LRU cache before each test"""
        get_summarizer.cache_clear()

    def test_get_summarizer_singleton(self):
        """Test that get_summarizer returns the same instance (singleton pattern)"""
        with patch('app.services.ai.pipeline') as mock_pipeline:
            mock_pipeline.return_value = Mock()

            # Call get_summarizer multiple times
            summarizer1 = get_summarizer()
            summarizer2 = get_summarizer()

            # Should return the same instance
            assert summarizer1 is summarizer2
            # Pipeline should only be called once due to @lru_cache
            mock_pipeline.assert_called_once()

    def test_get_summarizer_initialization(self):
        """Test proper initialization of the summarizer"""
        with patch('app.services.ai.pipeline') as mock_pipeline:
            mock_summarizer = Mock()
            mock_pipeline.return_value = mock_summarizer

            result = get_summarizer()

            # Check that pipeline was called with correct parameters
            mock_pipeline.assert_called_once_with(
                "summarization",
                model="t5-small",
                tokenizer="t5-small",
                framework="pt",
                device=-1
            )
            assert result == mock_summarizer

    def test_get_summarizer_custom_model(self):
        """Test get_summarizer with custom model from settings"""
        with patch('app.services.ai.pipeline') as mock_pipeline:
            with patch('app.services.ai.settings') as mock_settings:
                mock_settings.SUMMARIZATION_MODEL = "custom-model"
                mock_pipeline.return_value = Mock()

                get_summarizer()

                mock_pipeline.assert_called_once_with(
                    "summarization",
                    model="custom-model",
                    tokenizer="custom-model",
                    framework="pt",
                    device=-1
                )

    def test_get_summarizer_failure(self):
        """Test get_summarizer handles initialization failure"""
        with patch('app.services.ai.pipeline') as mock_pipeline:
            mock_pipeline.side_effect = Exception("Model loading failed")

            with pytest.raises(RuntimeError, match="Could not initialize summarization model"):
                get_summarizer()

    def test_summarize_text_success(self):
        """Test successful text summarization"""
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "This is a test summary."}]

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            result = summarize_text("This is a long text that needs to be summarized.")

            assert result == "This is a test summary."
            mock_summarizer.assert_called_once_with(
                "This is a long text that needs to be summarized.",
                max_length=150,
                min_length=30,
                do_sample=False,
                truncation=True
            )

    def test_summarize_text_empty_input(self):
        """Test summarize_text with empty input"""
        with pytest.raises(ValueError, match="Text to summarize cannot be empty"):
            summarize_text("")

        with pytest.raises(ValueError, match="Text to summarize cannot be empty"):
            summarize_text("   ")

    def test_summarize_text_short_input(self):
        """Test summarize_text with very short input"""
        short_text = "Short text"
        result = summarize_text(short_text)

        # Should return the original text if too short
        assert result == short_text

    def test_summarize_text_long_input(self):
        """Test summarize_text with very long input (truncation)"""
        long_text = "A" * 2000  # Longer than MAX_INPUT_LENGTH
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "Truncated summary."}]

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            result = summarize_text(long_text)

            # Should truncate the input
            assert result == "Truncated summary."
            # Check that the input was truncated to 1024 characters
            call_args = mock_summarizer.call_args[0]
            assert len(call_args[0]) == 1024

    def test_summarize_text_custom_parameters(self):
        """Test summarize_text with custom max/min lengths"""
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "Custom summary."}]

        long_text = "This is a long piece of text that is definitely longer than the minimum length required for summarization to occur."

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            result = summarize_text(long_text, max_length=100, min_length=20)

            assert result == "Custom summary."
            mock_summarizer.assert_called_once_with(
                long_text,
                max_length=100,
                min_length=20,
                do_sample=False,
                truncation=True
            )

    def test_summarize_text_failure(self):
        """Test summarize_text handles summarization failure"""
        mock_summarizer = Mock()
        mock_summarizer.side_effect = Exception("Summarization failed")

        long_text = "This is a long piece of text that is definitely longer than the minimum length required for summarization to occur."

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            with pytest.raises(RuntimeError, match="Failed to generate summary"):
                summarize_text(long_text)

    def test_summarize_bill_with_title(self):
        """Test bill summarization with title context"""
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "Bill summary with context."}]

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            result = summarize_bill("Bill text content", "Test Bill Title")

            assert result == "Bill summary with context."
            # Check that title was added as context
            call_args = mock_summarizer.call_args[0]
            assert "Bill Title: Test Bill Title" in call_args[0]
            assert "Bill Text: Bill text content" in call_args[0]

    def test_summarize_bill_without_title(self):
        """Test bill summarization without title"""
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "Bill summary without title."}]

        long_bill_text = "This is a comprehensive bill text that contains multiple sections and provisions that need to be summarized for citizens to understand."

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            result = summarize_bill(long_bill_text)

            assert result == "Bill summary without title."
            # Check that only bill text was used
            call_args = mock_summarizer.call_args[0]
            assert call_args[0] == long_bill_text

    def test_summarize_bill_empty_text(self):
        """Test bill summarization with empty text"""
        result = summarize_bill("")
        assert result == "No bill text available for summarization."

    def test_summarize_bill_post_processing(self):
        """Test bill summarization post-processing"""
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "SEC. 1 defines SECTION 2."}]

        long_bill_text = "This is a comprehensive bill text that contains multiple sections and provisions that need to be summarized for citizens to understand."

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            result = summarize_bill(long_bill_text)

            # Should replace legal jargon
            assert result == "Section 1 defines Section 2."

    def test_summarize_bill_failure(self):
        """Test bill summarization handles failure gracefully"""
        with patch('app.services.ai.summarize_text') as mock_summarize:
            mock_summarize.side_effect = Exception("Summarization failed")

            result = summarize_bill("Test bill text")

            # Should return error message instead of raising exception
            assert "Summary generation failed" in result

    def test_get_model_info_success(self):
        """Test get_model_info with loaded model"""
        mock_summarizer = Mock()

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            with patch('app.services.ai.settings') as mock_settings:
                mock_settings.SUMMARIZATION_MODEL = "t5-small"

                info = get_model_info()

                assert info["model_name"] == "t5-small"
                assert info["status"] == "loaded"
                assert info["framework"] == "transformers"
                assert info["task"] == "summarization"
                assert info["max_input_length"] == 1024

    def test_get_model_info_failure(self):
        """Test get_model_info with failed model"""
        with patch('app.services.ai.get_summarizer') as mock_get_summarizer:
            mock_get_summarizer.side_effect = Exception("Model failed")

            info = get_model_info()

            assert info["status"] == "failed"
            assert "Model failed" in info["error"]

    def test_health_check_healthy(self):
        """Test health check with healthy service"""
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "Test summary"}]

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            health = health_check()

            assert health["status"] == "healthy"
            assert health["model_loaded"] is True
            assert health["test_summary_length"] == len("Test summary")
            assert "model_info" in health

    def test_health_check_unhealthy(self):
        """Test health check with unhealthy service"""
        with patch('app.services.ai.summarize_text') as mock_summarize:
            mock_summarize.side_effect = Exception("Service failed")

            health = health_check()

            assert health["status"] == "unhealthy"
            assert health["model_loaded"] is False
            assert "Service failed" in health["error"]

    def test_summarize_text_whitespace_handling(self):
        """Test that summarize_text properly handles whitespace"""
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "  Summary with whitespace  "}]

        long_text = "  This is a long piece of text with whitespace that is definitely longer than the minimum length required for summarization to occur.  "

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            result = summarize_text(long_text)

            # Should strip whitespace from result
            assert result == "Summary with whitespace"

    @patch('app.services.ai.logger')
    def test_logging_behavior(self, mock_logger):
        """Test that appropriate logging occurs"""
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "Test summary"}]

        long_text = "This is a long piece of text that is definitely longer than the minimum length required for summarization to occur."

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            summarize_text(long_text)

            # Should log summary generation
            mock_logger.info.assert_called_with("Generated summary: 12 characters")

    def test_summarize_bill_uses_correct_parameters(self):
        """Test that summarize_bill uses correct parameters for bills"""
        mock_summarizer = Mock()
        mock_summarizer.return_value = [{"summary_text": "Bill summary"}]

        long_bill_text = "This is a comprehensive bill text that contains multiple sections and provisions that need to be summarized for citizens to understand."

        with patch('app.services.ai.get_summarizer', return_value=mock_summarizer):
            summarize_bill(long_bill_text)

            # Should use longer max_length for bills
            call_args = mock_summarizer.call_args[1]
            assert call_args["max_length"] == 200
            assert call_args["min_length"] == 50
