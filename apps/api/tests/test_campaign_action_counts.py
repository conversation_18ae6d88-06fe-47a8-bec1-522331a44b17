"""
Integration tests for campaign action count functionality.

This test verifies that the campaign action count bug is fixed and that
campaigns correctly display their actual action counts.
"""

from sqlalchemy.orm import Session
from app.models.campaign import Campaign, CampaignStatus, CampaignType
from app.models.action import Action, ActionStatus, ActionType
from app.models.bill import Bill
from app.models.user import User
from app.models.official import Official
from app.services.campaigns import CampaignService
from app.services.action import ActionService
from app.schemas.campaign import CampaignCreate
from app.schemas.action import ActionCreate
from uuid import uuid4


def test_campaign_action_count_consistency(test_db_session: Session):
    """
    Test that campaign action counts are correctly calculated and updated.

    This test creates a campaign, adds actions to it, and verifies that
    the actual_actions count matches the real number of actions.
    """
    # Create test data
    bill = Bill(
        id=str(uuid4()),
        title="Test Bill",
        bill_number="HB-123",
        description="Test bill description",
        status="introduced",
        state="federal",
        bill_type="house_bill",
        session_year=2024,
        chamber="house"
    )
    test_db_session.add(bill)

    user = User(
        id=str(uuid4()),
        email="<EMAIL>",
        auth0_user_id="auth0|test123"
    )
    test_db_session.add(user)

    official = Official(
        id=str(uuid4()),
        name="Test Official",
        title="Representative",
        email="<EMAIL>",
        level="federal",
        chamber="house"
    )
    test_db_session.add(official)

    test_db_session.commit()

    # Create campaign
    campaign_service = CampaignService(test_db_session)
    campaign_data = CampaignCreate(
        title="Test Campaign",
        description="Test campaign description",
        campaign_type=CampaignType.SUPPORT,
        status=CampaignStatus.ACTIVE,
        call_to_action="Take action now!",
        bill_id=str(bill.id)
    )

    campaign = campaign_service.create_campaign(campaign_data)
    assert campaign.actual_actions == 0  # Should start at 0

    # Create actions using the service (which should update the count)
    action_service = ActionService(test_db_session)

    # Create first action
    action_data_1 = ActionCreate(
        subject="Test Action 1",
        message="Test message 1",
        action_type=ActionType.EMAIL,
        user_name="Test User",
        user_email="<EMAIL>",
        campaign_id=str(campaign.id),
        official_id=str(official.id),
        user_id=str(user.id)
    )

    action_service.create_action(action_data_1)

    # Refresh campaign and check count
    test_db_session.refresh(campaign)
    assert campaign.actual_actions == 1

    # Create second action
    action_data_2 = ActionCreate(
        subject="Test Action 2",
        message="Test message 2",
        action_type=ActionType.EMAIL,
        user_name="Test User",
        user_email="<EMAIL>",
        campaign_id=str(campaign.id),
        official_id=str(official.id),
        user_id=str(user.id)
    )

    action_service.create_action(action_data_2)

    # Refresh campaign and check count
    test_db_session.refresh(campaign)
    assert campaign.actual_actions == 2


def test_recalculate_action_counts_fixes_inconsistency(test_db_session: Session):
    """
    Test that the recalculate_action_counts method fixes data inconsistencies.

    This test simulates a scenario where the actual_actions count is out of sync
    and verifies that the recalculation method fixes it.
    """
    # Create test data
    bill = Bill(
        id=str(uuid4()),
        title="Test Bill 2",
        bill_number="HB-456",
        description="Test bill description 2",
        status="introduced",
        state="federal",
        bill_type="house_bill",
        session_year=2024,
        chamber="house"
    )
    test_db_session.add(bill)

    user = User(
        id=str(uuid4()),
        email="<EMAIL>",
        auth0_user_id="auth0|test456"
    )
    test_db_session.add(user)

    official = Official(
        id=str(uuid4()),
        name="Test Official 2",
        title="Senator",
        email="<EMAIL>",
        level="federal",
        chamber="senate"
    )
    test_db_session.add(official)

    # Create campaign manually (bypassing service to simulate old data)
    campaign = Campaign(
        id=str(uuid4()),
        title="Test Campaign 2",
        description="Test campaign description 2",
        campaign_type=CampaignType.SUPPORT,
        status=CampaignStatus.ACTIVE,
        call_to_action="Take action now!",
        bill_id=str(bill.id),
        actual_actions=0  # This will be incorrect after we add actions
    )
    test_db_session.add(campaign)

    # Create actions manually (bypassing service to simulate inconsistency)
    action_1 = Action(
        id=str(uuid4()),
        subject="Manual Action 1",
        message="Manual message 1",
        action_type=ActionType.EMAIL,
        user_name="Test User",
        user_email="<EMAIL>",
        user_id=str(user.id),
        campaign_id=str(campaign.id),
        official_id=str(official.id),
        status=ActionStatus.PENDING
    )

    action_2 = Action(
        id=str(uuid4()),
        subject="Manual Action 2",
        message="Manual message 2",
        action_type=ActionType.EMAIL,
        user_name="Test User",
        user_email="<EMAIL>",
        user_id=str(user.id),
        campaign_id=str(campaign.id),
        official_id=str(official.id),
        status=ActionStatus.PENDING
    )

    test_db_session.add(action_1)
    test_db_session.add(action_2)
    test_db_session.commit()

    # Verify inconsistency exists
    assert campaign.actual_actions == 0  # Incorrect count
    actual_count = test_db_session.query(Action).filter(Action.campaign_id == campaign.id).count()
    assert actual_count == 2  # Real count

    # Fix the inconsistency using the service method
    campaign_service = CampaignService(test_db_session)
    updated_count = campaign_service.recalculate_action_counts()

    # Verify the fix
    assert updated_count == 1  # One campaign was updated
    test_db_session.refresh(campaign)
    assert campaign.actual_actions == 2  # Now correct
