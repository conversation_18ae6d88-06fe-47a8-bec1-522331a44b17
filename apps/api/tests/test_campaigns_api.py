from app.models.campaign import Campaign, CampaignStatus, CampaignType
from app.models.bill import Bill


class TestCampaignsAPI:
    """Test suite for campaigns API endpoints"""

    def test_get_campaigns_empty(self, test_client):
        """Test getting campaigns when database is empty"""
        response = test_client.get("/api/v1/campaigns/")
        assert response.status_code == 200
        assert response.json() == []

    def test_create_campaign(self, test_client, test_db_session, sample_bill_data, sample_campaign_data):
        """Test creating a new campaign"""
        # First create a bill
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        # Create campaign data with bill_id
        campaign_data = sample_campaign_data.copy()
        campaign_data["bill_id"] = str(bill.id)

        response = test_client.post("/api/v1/campaigns/", json=campaign_data)
        assert response.status_code == 200

        data = response.json()
        assert data["title"] == campaign_data["title"]
        assert data["campaign_type"] == campaign_data["campaign_type"]
        assert data["status"] == campaign_data["status"]
        assert data["bill_id"] == campaign_data["bill_id"]
        assert "id" in data
        assert "bill" in data  # Should include nested bill object
        assert data["bill"]["title"] == sample_bill_data["title"]

        # Verify campaign was created in database
        campaign_id = data["id"]
        response = test_client.get(f"/api/v1/campaigns/{campaign_id}")
        assert response.status_code == 200
        assert response.json()["id"] == campaign_id

    def test_create_campaign_invalid_bill(self, test_client, sample_campaign_data):
        """Test creating campaign with invalid bill ID"""
        campaign_data = sample_campaign_data.copy()
        campaign_data["bill_id"] = "invalid-bill-id"

        response = test_client.post("/api/v1/campaigns/", json=campaign_data)
        assert response.status_code == 400
        assert "Bill not found" in response.json()["detail"]

    def test_get_campaign(self, test_client, test_db_session, sample_bill_data):
        """Test getting a single campaign by ID"""
        # Create bill and campaign
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        campaign = Campaign(
            title="Test Campaign",
            description="Test description",
            campaign_type=CampaignType.SUPPORT,
            status=CampaignStatus.ACTIVE,
            call_to_action="Support this bill!",
            bill_id=bill.id
        )
        test_db_session.add(campaign)
        test_db_session.commit()

        response = test_client.get(f"/api/v1/campaigns/{campaign.id}")
        assert response.status_code == 200

        data = response.json()
        assert data["id"] == str(campaign.id)
        assert data["title"] == "Test Campaign"
        assert data["bill"]["title"] == sample_bill_data["title"]

    def test_get_campaign_not_found(self, test_client):
        """Test getting a campaign that doesn't exist"""
        response = test_client.get("/api/v1/campaigns/nonexistent-id")
        assert response.status_code == 404
        assert response.json()["detail"] == "Campaign not found"

    def test_update_campaign(self, test_client, test_db_session, sample_bill_data):
        """Test updating an existing campaign"""
        # Create bill and campaign
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        campaign = Campaign(
            title="Original Title",
            description="Original description",
            campaign_type=CampaignType.SUPPORT,
            status=CampaignStatus.DRAFT,
            call_to_action="Original CTA",
            bill_id=bill.id
        )
        test_db_session.add(campaign)
        test_db_session.commit()

        # Update campaign
        update_data = {
            "title": "Updated Title",
            "status": "active",
            "is_featured": True
        }

        response = test_client.put(f"/api/v1/campaigns/{campaign.id}", json=update_data)
        assert response.status_code == 200

        data = response.json()
        assert data["title"] == "Updated Title"
        assert data["status"] == "active"
        assert data["is_featured"] is True
        assert data["description"] == "Original description"  # Unchanged field

    def test_update_campaign_not_found(self, test_client):
        """Test updating a campaign that doesn't exist"""
        update_data = {"title": "Updated Title"}

        response = test_client.put("/api/v1/campaigns/nonexistent-id", json=update_data)
        assert response.status_code == 404
        assert response.json()["detail"] == "Campaign not found"

    def test_delete_campaign(self, test_client, test_db_session, sample_bill_data):
        """Test deleting a campaign"""
        # Create bill and campaign
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        campaign = Campaign(
            title="Campaign to Delete",
            campaign_type=CampaignType.SUPPORT,
            status=CampaignStatus.DRAFT,
            call_to_action="Delete me!",
            bill_id=bill.id
        )
        test_db_session.add(campaign)
        test_db_session.commit()

        campaign_id = str(campaign.id)

        # Delete campaign
        response = test_client.delete(f"/api/v1/campaigns/{campaign_id}")
        assert response.status_code == 200
        assert response.json()["message"] == "Campaign deleted successfully"

        # Verify campaign is deleted
        response = test_client.get(f"/api/v1/campaigns/{campaign_id}")
        assert response.status_code == 404

    def test_delete_campaign_not_found(self, test_client):
        """Test deleting a campaign that doesn't exist"""
        response = test_client.delete("/api/v1/campaigns/nonexistent-id")
        assert response.status_code == 404
        assert response.json()["detail"] == "Campaign not found"

    def test_search_campaigns(self, test_client, test_db_session, sample_bill_data):
        """Test searching campaigns with various filters"""
        # Create bill
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        # Create test campaigns
        campaigns = [
            Campaign(
                title="Healthcare Campaign",
                description="Support healthcare reform",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.ACTIVE,
                call_to_action="Support healthcare!",
                is_featured=True,
                bill_id=bill.id
            ),
            Campaign(
                title="Education Campaign",
                description="Oppose education cuts",
                campaign_type=CampaignType.OPPOSE,
                status=CampaignStatus.DRAFT,
                call_to_action="Oppose cuts!",
                is_featured=False,
                bill_id=bill.id
            )
        ]

        for campaign in campaigns:
            test_db_session.add(campaign)
        test_db_session.commit()

        # Test text search
        response = test_client.get("/api/v1/campaigns/search?query=healthcare")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Healthcare Campaign"

        # Test filter by campaign type
        response = test_client.get("/api/v1/campaigns/search?campaign_type=oppose")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Education Campaign"

        # Test filter by status
        response = test_client.get("/api/v1/campaigns/search?status=active")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Healthcare Campaign"

        # Test filter by featured
        response = test_client.get("/api/v1/campaigns/search?is_featured=true")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Healthcare Campaign"

    def test_get_featured_campaigns(self, test_client, test_db_session, sample_bill_data):
        """Test getting featured campaigns"""
        # Create bill
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        # Create campaigns
        campaigns = [
            Campaign(
                title="Featured Campaign",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.ACTIVE,
                call_to_action="Featured!",
                is_featured=True,
                bill_id=bill.id
            ),
            Campaign(
                title="Regular Campaign",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.ACTIVE,
                call_to_action="Regular",
                is_featured=False,
                bill_id=bill.id
            )
        ]

        for campaign in campaigns:
            test_db_session.add(campaign)
        test_db_session.commit()

        response = test_client.get("/api/v1/campaigns/featured")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Featured Campaign"

    def test_get_active_campaigns(self, test_client, test_db_session, sample_bill_data):
        """Test getting active campaigns"""
        # Create bill
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        # Create campaigns
        campaigns = [
            Campaign(
                title="Active Campaign",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.ACTIVE,
                call_to_action="Active!",
                bill_id=bill.id
            ),
            Campaign(
                title="Draft Campaign",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.DRAFT,
                call_to_action="Draft",
                bill_id=bill.id
            )
        ]

        for campaign in campaigns:
            test_db_session.add(campaign)
        test_db_session.commit()

        response = test_client.get("/api/v1/campaigns/active")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Active Campaign"

    def test_get_campaigns_by_status(self, test_client, test_db_session, sample_bill_data):
        """Test getting campaigns by status"""
        # Create bill
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        # Create campaigns with different statuses
        campaigns = [
            Campaign(
                title="Draft Campaign",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.DRAFT,
                call_to_action="Draft!",
                bill_id=bill.id
            ),
            Campaign(
                title="Active Campaign",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.ACTIVE,
                call_to_action="Active!",
                bill_id=bill.id
            ),
            Campaign(
                title="Completed Campaign",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.COMPLETED,
                call_to_action="Completed!",
                bill_id=bill.id
            )
        ]

        for campaign in campaigns:
            test_db_session.add(campaign)
        test_db_session.commit()

        # Test each status
        response = test_client.get("/api/v1/campaigns/status/draft")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Draft Campaign"

        response = test_client.get("/api/v1/campaigns/status/active")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Active Campaign"

    def test_get_campaigns_by_bill(self, test_client, test_db_session, sample_bill_data):
        """Test getting campaigns for a specific bill"""
        # Create two bills
        bill1 = Bill(**sample_bill_data)
        test_db_session.add(bill1)

        bill2_data = sample_bill_data.copy()
        bill2_data["bill_number"] = "HR-456"
        bill2_data["title"] = "Second Bill"
        bill2 = Bill(**bill2_data)
        test_db_session.add(bill2)
        test_db_session.commit()

        # Create campaigns for each bill
        campaigns = [
            Campaign(
                title="Campaign for Bill 1",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.ACTIVE,
                call_to_action="Support Bill 1!",
                bill_id=bill1.id
            ),
            Campaign(
                title="Another Campaign for Bill 1",
                campaign_type=CampaignType.OPPOSE,
                status=CampaignStatus.DRAFT,
                call_to_action="Oppose Bill 1!",
                bill_id=bill1.id
            ),
            Campaign(
                title="Campaign for Bill 2",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.ACTIVE,
                call_to_action="Support Bill 2!",
                bill_id=bill2.id
            )
        ]

        for campaign in campaigns:
            test_db_session.add(campaign)
        test_db_session.commit()

        # Test getting campaigns for bill1
        response = test_client.get(f"/api/v1/campaigns/bill/{bill1.id}")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        bill1_campaign_titles = [c["title"] for c in data]
        assert "Campaign for Bill 1" in bill1_campaign_titles
        assert "Another Campaign for Bill 1" in bill1_campaign_titles

        # Test getting campaigns for bill2
        response = test_client.get(f"/api/v1/campaigns/bill/{bill2.id}")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Campaign for Bill 2"

    def test_create_campaign_with_json_fields(self, test_client, test_db_session, sample_bill_data):
        """Test creating campaign with JSON fields (talking_points, geographic_scope, hashtags)"""
        # Create bill
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        campaign_data = {
            "title": "Campaign with JSON Fields",
            "description": "Test campaign with arrays",
            "campaign_type": "support",
            "status": "active",
            "call_to_action": "Support this bill!",
            "bill_id": str(bill.id),
            "talking_points": ["Point 1", "Point 2", "Point 3"],
            "geographic_scope": ["CA", "NY", "TX"],
            "hashtags": ["#healthcare", "#reform", "#urgent"]
        }

        response = test_client.post("/api/v1/campaigns/", json=campaign_data)
        assert response.status_code == 200

        data = response.json()
        assert data["title"] == campaign_data["title"]
        assert data["talking_points"] == campaign_data["talking_points"]
        assert data["geographic_scope"] == campaign_data["geographic_scope"]
        assert data["hashtags"] == campaign_data["hashtags"]

    def test_update_campaign_with_json_fields(self, test_client, test_db_session, sample_bill_data):
        """Test updating campaign with JSON fields"""
        # Create bill and campaign
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        campaign = Campaign(
            title="Original Campaign",
            campaign_type=CampaignType.SUPPORT,
            status=CampaignStatus.DRAFT,
            call_to_action="Original CTA",
            bill_id=bill.id
        )
        test_db_session.add(campaign)
        test_db_session.commit()

        # Update with JSON fields
        update_data = {
            "talking_points": ["Updated Point 1", "Updated Point 2"],
            "geographic_scope": ["FL", "WA"],
            "hashtags": ["#updated", "#campaign"]
        }

        response = test_client.put(f"/api/v1/campaigns/{campaign.id}", json=update_data)
        assert response.status_code == 200

        data = response.json()
        assert data["talking_points"] == update_data["talking_points"]
        assert data["geographic_scope"] == update_data["geographic_scope"]
        assert data["hashtags"] == update_data["hashtags"]

    def test_pagination(self, test_client, test_db_session, sample_bill_data):
        """Test pagination for campaigns list"""
        # Create bill
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        # Create multiple campaigns
        campaigns = []
        for i in range(25):
            campaign = Campaign(
                title=f"Campaign {i}",
                campaign_type=CampaignType.SUPPORT,
                status=CampaignStatus.ACTIVE,
                call_to_action=f"Support campaign {i}!",
                bill_id=bill.id
            )
            campaigns.append(campaign)
            test_db_session.add(campaign)
        test_db_session.commit()

        # Test default pagination
        response = test_client.get("/api/v1/campaigns/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 20  # Default limit

        # Test custom pagination
        response = test_client.get("/api/v1/campaigns/?skip=10&limit=5")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 5

        # Test limit validation
        response = test_client.get("/api/v1/campaigns/?limit=150")
        assert response.status_code == 422  # Validation error for limit > 100
