import os
from unittest.mock import patch
from app.core.config import Settings


class TestConfiguration:
    def test_default_settings(self):
        """Test default configuration values"""
        test_settings = Settings()

        assert test_settings.API_V1_STR == "/api/v1"
        assert test_settings.PROJECT_NAME == "ModernAction API"
        assert test_settings.VERSION == "1.0.0"
        # Environment can be overridden by .env.test in CI, so check it's a valid value
        assert test_settings.ENVIRONMENT in ["development", "test", "production"]
        # DEBUG can be overridden by .env.test in CI
        assert isinstance(test_settings.DEBUG, bool)
        assert test_settings.JWT_ALGORITHM == "HS256"
        assert test_settings.JWT_EXPIRATION_MINUTES == 30
        assert test_settings.CORS_ORIGINS == "http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003"
        assert test_settings.AWS_REGION == "us-east-1"
        assert test_settings.AWS_SES_FROM_EMAIL == "<EMAIL>"
        assert test_settings.LOG_LEVEL == "INFO"
        assert test_settings.RATE_LIMIT_ENABLED is True
        assert test_settings.RATE_LIMIT_REQUESTS_PER_MINUTE == 100
        assert test_settings.MODEL_CACHE_SIZE == 1
        assert test_settings.SUMMARIZATION_MODEL == "t5-small"

    def test_environment_variable_override(self):
        """Test that environment variables override defaults"""
        with patch.dict(os.environ, {
            'API_V1_STR': '/api/v2',
            'PROJECT_NAME': 'Test API',
            'DEBUG': 'false',
            'JWT_EXPIRATION_MINUTES': '60',
            'RATE_LIMIT_REQUESTS_PER_MINUTE': '200'
        }):
            test_settings = Settings()

            assert test_settings.API_V1_STR == "/api/v2"
            assert test_settings.PROJECT_NAME == "Test API"
            assert test_settings.DEBUG is False
            assert test_settings.JWT_EXPIRATION_MINUTES == 60
            assert test_settings.RATE_LIMIT_REQUESTS_PER_MINUTE == 200

    def test_database_url_setting(self):
        """Test database URL configuration"""
        with patch.dict(os.environ, {
            'DATABASE_URL': 'postgresql://user:pass@localhost:5432/testdb'
        }):
            test_settings = Settings()
            assert test_settings.DATABASE_URL == 'postgresql://user:pass@localhost:5432/testdb'

    def test_cors_origins_list(self):
        """Test CORS origins configuration"""
        with patch.dict(os.environ, {
            'CORS_ORIGINS': 'http://localhost:3000,http://localhost:3001,https://example.com'
        }):
            test_settings = Settings()
            expected_origins = ['http://localhost:3000', 'http://localhost:3001', 'https://example.com']
            assert test_settings.cors_origins_list == expected_origins

    def test_optional_settings(self):
        """Test optional settings can be None"""
        # Test with clean environment (no API keys set)
        with patch.dict(os.environ, {
            'OPENSTATES_API_KEY': '',
            'GOOGLE_CIVIC_INFO_API_KEY': '',
            'HUGGING_FACE_API_KEY': '',
            'AWS_ACCESS_KEY_ID': '',
            'AWS_SECRET_ACCESS_KEY': ''
        }, clear=False):
            test_settings = Settings()

            # These should be None when empty strings are provided
            assert test_settings.OPENSTATES_API_KEY == '' or test_settings.OPENSTATES_API_KEY is None
            assert test_settings.GOOGLE_CIVIC_INFO_API_KEY == '' or test_settings.GOOGLE_CIVIC_INFO_API_KEY is None
            assert test_settings.HUGGING_FACE_API_KEY == '' or test_settings.HUGGING_FACE_API_KEY is None
            assert test_settings.AWS_ACCESS_KEY_ID == '' or test_settings.AWS_ACCESS_KEY_ID is None
            assert test_settings.AWS_SECRET_ACCESS_KEY == '' or test_settings.AWS_SECRET_ACCESS_KEY is None

    def test_boolean_settings(self):
        """Test boolean settings parsing"""
        # Test various boolean representations
        boolean_tests = [
            ('true', True),
            ('True', True),
            ('TRUE', True),
            ('1', True),
            ('false', False),
            ('False', False),
            ('FALSE', False),
            ('0', False),
        ]

        for env_value, expected in boolean_tests:
            with patch.dict(os.environ, {'DEBUG': env_value}):
                test_settings = Settings()
                assert test_settings.DEBUG is expected

    def test_integer_settings(self):
        """Test integer settings parsing"""
        with patch.dict(os.environ, {
            'JWT_EXPIRATION_MINUTES': '120',
            'RATE_LIMIT_REQUESTS_PER_MINUTE': '500',
            'MODEL_CACHE_SIZE': '2'
        }):
            test_settings = Settings()

            assert test_settings.JWT_EXPIRATION_MINUTES == 120
            assert test_settings.RATE_LIMIT_REQUESTS_PER_MINUTE == 500
            assert test_settings.MODEL_CACHE_SIZE == 2

    def test_case_sensitivity(self):
        """Test that settings are case sensitive"""
        test_settings = Settings()

        # The Config class is set to case_sensitive = True
        # This means environment variables must match exactly
        with patch.dict(os.environ, {
            'project_name': 'lowercase',  # This should NOT override
            'PROJECT_NAME': 'uppercase'   # This should override
        }):
            test_settings = Settings()
            assert test_settings.PROJECT_NAME == 'uppercase'

    def test_global_settings_instance(self):
        """Test the global settings instance"""
        from app.core.config import settings

        # Test that it's a Settings instance
        assert isinstance(settings, Settings)

        # Test that it has expected attributes
        assert hasattr(settings, 'API_V1_STR')
        assert hasattr(settings, 'PROJECT_NAME')
        assert hasattr(settings, 'DATABASE_URL')

    def test_production_settings(self):
        """Test production-specific settings"""
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'DEBUG': 'false',
            'SECRET_KEY': 'super-secret-production-key',
            'DATABASE_URL': '***********************************************/prod_db'
        }):
            test_settings = Settings()

            assert test_settings.ENVIRONMENT == 'production'
            assert test_settings.DEBUG is False
            assert test_settings.SECRET_KEY == 'super-secret-production-key'
            assert 'prod_host' in test_settings.DATABASE_URL

    def test_redis_url_setting(self):
        """Test Redis URL configuration"""
        with patch.dict(os.environ, {
            'REDIS_URL': 'redis://localhost:6380/1'
        }):
            test_settings = Settings()
            assert test_settings.REDIS_URL == 'redis://localhost:6380/1'

    def test_ses_settings(self):
        """Test SES configuration"""
        with patch.dict(os.environ, {
            'AWS_SES_FROM_EMAIL': '<EMAIL>'
        }):
            test_settings = Settings()
            assert test_settings.AWS_SES_FROM_EMAIL == '<EMAIL>'

    def test_api_key_settings(self):
        """Test external API key settings"""
        with patch.dict(os.environ, {
            'OPENSTATES_API_KEY': 'openstates-key-123',
            'GOOGLE_CIVIC_INFO_API_KEY': 'google-civic-key-456',
            'HUGGING_FACE_API_KEY': 'huggingface-key-789'
        }):
            test_settings = Settings()
            assert test_settings.OPENSTATES_API_KEY == 'openstates-key-123'
            assert test_settings.GOOGLE_CIVIC_INFO_API_KEY == 'google-civic-key-456'
            assert test_settings.HUGGING_FACE_API_KEY == 'huggingface-key-789'
