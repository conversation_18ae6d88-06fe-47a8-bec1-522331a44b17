from sqlalchemy import text
from app.models.user import User
from app.models.bill import Bill
from app.models.official import Official


class TestDatabaseConnection:
    def test_database_connection(self, test_db_session):
        """Test that we can connect to the database"""
        result = test_db_session.execute(text("SELECT 1")).scalar()
        assert result == 1

    def test_database_transaction(self, test_db_session):
        """Test database transaction handling"""
        # Create a user
        user = User(
            auth0_user_id="auth0|transaction_test_123456789",
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="Transaction",
            last_name="Test"
        )

        test_db_session.add(user)
        test_db_session.commit()

        # Verify user exists
        saved_user = test_db_session.query(User).filter_by(email="<EMAIL>").first()
        assert saved_user is not None
        assert saved_user.email == "<EMAIL>"

    def test_database_rollback(self, test_db_session):
        """Test database rollback functionality"""
        # Create a user
        user = User(
            auth0_user_id="auth0|rollback_test_123456789",
            email="<EMAIL>",
            hashed_password="hashed_password",
            first_name="Rollback",
            last_name="Test"
        )

        test_db_session.add(user)
        # Don't commit, just rollback
        test_db_session.rollback()

        # Verify user doesn't exist
        saved_user = test_db_session.query(User).filter_by(email="<EMAIL>").first()
        assert saved_user is None

    def test_database_session_isolation(self, test_db):
        """Test that database sessions are isolated"""
        session1 = test_db()
        session2 = test_db()

        try:
            # Create user in session1
            user1 = User(
                auth0_user_id="auth0|session1_test_123456789",
                email="<EMAIL>",
                hashed_password="hashed_password",
                first_name="Session1",
                last_name="Test"
            )
            session1.add(user1)
            session1.commit()

            # Create user in session2
            user2 = User(
                auth0_user_id="auth0|session2_test_123456789",
                email="<EMAIL>",
                hashed_password="hashed_password",
                first_name="Session2",
                last_name="Test"
            )
            session2.add(user2)
            session2.commit()

            # Each session should see both users
            users_in_session1 = session1.query(User).all()
            users_in_session2 = session2.query(User).all()

            assert len(users_in_session1) == 2
            assert len(users_in_session2) == 2

        finally:
            session1.close()
            session2.close()

    def test_database_table_creation(self, test_db_session):
        """
        Verifies that all expected tables have been created in the database.
        This test is resilient to other data existing in the DB.
        """
        from sqlalchemy import inspect

        inspector = inspect(test_db_session.bind)
        tables = inspector.get_table_names()

        expected_tables = {
            "users",
            "officials",
            "bills",
            "campaigns",
            "actions",
            "bill_status_pipeline"  # Actual table name with underscores
        }

        # Assert that the set of expected tables is a subset of all existing tables.
        # This means all our tables exist, and we don't care if other tables exist.
        assert expected_tables.issubset(set(tables)), f"Missing tables: {expected_tables - set(tables)}"

        print(f"SUCCESS: Found all {len(expected_tables)} expected tables.")

    def test_database_foreign_key_constraints(self, test_db_session, sample_user_data, sample_bill_data, sample_campaign_data, sample_official_data):
        """Test foreign key constraints are working"""
        # Create required entities
        user = User(
            auth0_user_id=sample_user_data["auth0_user_id"],
            email=sample_user_data["email"],
            hashed_password="hashed_password",
            first_name=sample_user_data["first_name"],
            last_name=sample_user_data["last_name"]
        )
        test_db_session.add(user)

        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)

        official = Official(**sample_official_data)
        test_db_session.add(official)

        test_db_session.commit()

        # Create campaign
        from app.models.campaign import Campaign
        campaign = Campaign(
            title="Test Campaign",
            campaign_type="support",
            status="active",
            call_to_action="Support this bill!",
            bill_id=bill.id
        )
        test_db_session.add(campaign)
        test_db_session.commit()

        # Create action
        from app.models.action import Action
        action = Action(
            subject="Test Action",
            message="Test message",
            action_type="email",
            status="pending",
            user_name=user.full_name,
            user_email=user.email,
            user_id=user.id,
            campaign_id=campaign.id,
            official_id=official.id
        )
        test_db_session.add(action)
        test_db_session.commit()

        # Verify relationships work
        assert action.user == user
        assert action.campaign == campaign
        assert action.official == official
        assert campaign.bill == bill
        assert action in user.actions
        assert action in campaign.actions
        assert action in official.actions
