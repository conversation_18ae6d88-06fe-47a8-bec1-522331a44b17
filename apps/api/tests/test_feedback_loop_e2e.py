"""
End-to-End Integration Tests for the Feedback Loop System.

This test suite validates the complete asynchronous chain:
1. Bill Status Update Lambda detects status change
2. Publishes message to SQS queue
3. Notification Sender <PERSON>da processes SQS message
4. Sends email notification via SES

These tests use moto to mock all AWS services and ensure the complete
feedback loop works as designed without external dependencies.
"""

import json
import os
import sys
from unittest.mock import Mock, patch
from moto import mock_aws
import boto3
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import uuid

# Set up test environment variables
os.environ.update({
    'OPEN_STATES_API_KEY': 'test-api-key',
    'AWS_REGION': 'us-east-1',
    'SQS_QUEUE_URL': 'https://sqs.us-east-1.amazonaws.com/123456789012/test-feedback-loop-queue',
    'FROM_EMAIL': '<EMAIL>',
    'REPLY_TO_EMAIL': '<EMAIL>'
})

# Import Lambda functions after environment setup
sys.path.append('/Users/<USER>/modern-action-2.0/apps/lambda/bill_status_update')
sys.path.append('/Users/<USER>/modern-action-2.0/apps/lambda/notification_sender')
sys.path.append('/Users/<USER>/modern-action-2.0/apps/lambda/bill_status_update/shared')
sys.path.append('/Users/<USER>/modern-action-2.0/apps/lambda/notification_sender/shared')

# Add current directory to path for relative imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, '../../lambda/bill_status_update'))
sys.path.insert(0, os.path.join(current_dir, '../../lambda/notification_sender'))

# Now import the modules
try:
    from shared.bill_status_service import BillStatusUpdateService
    from shared.notification_service import NotificationService
    from handler import lambda_handler as notification_handler
except ImportError as e:
    print(f"Import error: {e}")
    # Fallback: try direct path imports
    sys.path.insert(0, '/Users/<USER>/modern-action-2.0/apps/lambda/bill_status_update/shared')
    sys.path.insert(0, '/Users/<USER>/modern-action-2.0/apps/lambda/notification_sender/shared')
    from bill_status_service import BillStatusUpdateService
    from notification_service import NotificationService


class TestFeedbackLoopE2E:
    """End-to-end tests for the complete feedback loop system"""

    def setup_method(self):
        """Set up test database and mock AWS services for each test"""
        # Create in-memory SQLite database for testing
        self.engine = create_engine("sqlite:///:memory:", echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

        # Create test tables
        self.create_test_tables()

        # Create test session
        self.db = self.SessionLocal()

    def teardown_method(self):
        """Clean up after each test"""
        if self.db:
            self.db.close()
        if self.engine:
            self.engine.dispose()

    def create_test_tables(self):
        """Create the necessary tables for testing"""
        with self.engine.connect() as conn:
            # Users table
            conn.execute(text("""
                CREATE TABLE users (
                    id TEXT PRIMARY KEY,
                    email TEXT NOT NULL,
                    first_name TEXT,
                    last_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))

            # Bills table
            conn.execute(text("""
                CREATE TABLE bills (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    bill_number TEXT,
                    status TEXT DEFAULT 'introduced',
                    openstates_id TEXT,
                    last_action_date TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))

            # Actions table
            conn.execute(text("""
                CREATE TABLE actions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT,
                    bill_id TEXT,
                    message TEXT,
                    status TEXT DEFAULT 'completed',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (bill_id) REFERENCES bills (id)
                )
            """))

            # Bill Status Pipeline table
            conn.execute(text("""
                CREATE TABLE bill_status_pipeline (
                    id TEXT PRIMARY KEY,
                    bill_id TEXT,
                    previous_status TEXT,
                    current_status TEXT,
                    status_changed_at TIMESTAMP,
                    detected_at TIMESTAMP,
                    external_data TEXT,
                    vote_details TEXT,
                    notification_sent BOOLEAN DEFAULT FALSE,
                    is_significant_change BOOLEAN DEFAULT FALSE,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (bill_id) REFERENCES bills (id)
                )
            """))

            conn.commit()

    def seed_test_data(self):
        """Seed the database with test data"""
        # Create test user
        user_id = str(uuid.uuid4())
        self.db.execute(text("""
            INSERT INTO users (id, email, first_name, last_name)
            VALUES (:id, :email, :first_name, :last_name)
        """), {
            'id': user_id,
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        })

        # Create test bill
        bill_id = str(uuid.uuid4())
        self.db.execute(text("""
            INSERT INTO bills (id, title, bill_number, status, openstates_id)
            VALUES (:id, :title, :bill_number, :status, :openstates_id)
        """), {
            'id': bill_id,
            'title': 'Environmental Protection Act',
            'bill_number': 'HB 2024-123',
            'status': 'committee',
            'openstates_id': 'ocd-bill/12345'
        })

        # Create test action
        action_id = str(uuid.uuid4())
        self.db.execute(text("""
            INSERT INTO actions (id, user_id, bill_id, message, status)
            VALUES (:id, :user_id, :bill_id, :message, :status)
        """), {
            'id': action_id,
            'user_id': user_id,
            'bill_id': bill_id,
            'message': 'I strongly support this environmental protection bill.',
            'status': 'completed'
        })

        self.db.commit()

        return {
            'user_id': user_id,
            'bill_id': bill_id,
            'action_id': action_id
        }

    @mock_aws
    @patch('shared.bill_status_service.requests.get')
    def test_complete_feedback_loop_e2e(self, mock_requests_get):
        """
        Test the complete feedback loop from bill status change to email notification.

        This test validates:
        1. Bill status update detection
        2. SQS message publishing
        3. SQS message consumption
        4. Email notification sending
        """
        # === SETUP ===
        test_data = self.seed_test_data()
        bill_id = test_data['bill_id']

        # Set up AWS mocks
        sqs_client = boto3.client('sqs', region_name='us-east-1')
        queue = sqs_client.create_queue(QueueName='test-feedback-loop-queue')
        queue_url = queue['QueueUrl']

        ses_client = boto3.client('ses', region_name='us-east-1')
        ses_client.verify_email_identity(EmailAddress='<EMAIL>')

        # === PART 1: Bill Status Update Lambda -> SQS ===

        # Mock OpenStates API response indicating bill passed
        mock_openstates_response = {
            'id': 'ocd-bill/12345',
            'title': 'Environmental Protection Act',
            'actions': [
                {
                    'description': 'Passed final reading',
                    'date': '2024-07-19T15:30:00Z',
                    'organization': {'name': 'House Floor'},
                    'result': 'pass'
                }
            ]
        }

        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = mock_openstates_response
        mock_requests_get.return_value = mock_response

        # Create notification service with test queue URL
        with patch.dict(os.environ, {'SQS_QUEUE_URL': queue_url}):
            notification_service = NotificationService()
            bill_service = BillStatusUpdateService(self.db, notification_service)

            # Get the test bill
            bill_query = text("SELECT * FROM bills WHERE id = :bill_id")
            result = self.db.execute(bill_query, {'bill_id': bill_id})
            bill = dict(result.mappings().fetchone())

            # Process the bill status update
            status_change = bill_service.update_bill_status(bill)
            self.db.commit()

            # Verify status change was detected
            assert status_change is not None
            assert status_change['previous_status'] == 'committee'
            assert status_change['current_status'] == 'passed'
            assert status_change['is_significant_change'] is True

            # Verify SQS message was published
            messages = sqs_client.receive_message(
                QueueUrl=queue_url,
                MaxNumberOfMessages=10,
                MessageAttributeNames=['All']
            )
            assert 'Messages' in messages, "No SQS messages found"
            assert len(messages['Messages']) == 1

            sqs_message = messages['Messages'][0]
            message_body = json.loads(sqs_message['Body'])

            # Verify SQS message content
            assert message_body['event_type'] == 'bill_status_change'
            assert message_body['bill_id'] == bill_id
            assert message_body['previous_status'] == 'committee'
            assert message_body['current_status'] == 'passed'
            assert message_body['is_significant_change'] is True
            assert 'vote_details' in message_body
            assert 'timestamp' in message_body

            # Verify message attributes
            message_attrs = sqs_message.get('MessageAttributes', {})
            assert message_attrs['bill_id']['StringValue'] == bill_id
            assert message_attrs['event_type']['StringValue'] == 'bill_status_change'
            assert message_attrs['status_change']['StringValue'] == 'committee->passed'

        # === PART 2: SQS -> Notification Sender Lambda -> SES ===

        # Create mock SQS event for notification sender Lambda
        sqs_event = {
            'Records': [
                {
                    'messageId': 'test-message-id',
                    'receiptHandle': 'test-receipt-handle',
                    'body': sqs_message['Body'],
                    'attributes': {
                        'ApproximateReceiveCount': '1',
                        'SentTimestamp': '1626883200000',
                        'SenderId': 'AIDACKCEVSQ6C2EXAMPLE',
                        'ApproximateFirstReceiveTimestamp': '1626883200000'
                    },
                    'messageAttributes': message_attrs,
                    'md5OfBody': 'test-md5',
                    'eventSource': 'aws:sqs',
                    'eventSourceARN': 'arn:aws:sqs:us-east-1:123456789012:test-feedback-loop-queue',
                    'awsRegion': 'us-east-1'
                }
            ]
        }

        # Mock context for Lambda function
        mock_context = Mock()
        mock_context.function_name = 'test-notification-sender'
        mock_context.get_remaining_time_in_millis.return_value = 300000

        # Patch database connection for notification sender
        with patch('shared.database.get_database_session', return_value=self.db):
            # Execute notification sender Lambda
            result = notification_handler(sqs_event, mock_context)

            # Verify Lambda execution was successful
            assert result['statusCode'] == 200
            assert result['body']['total_messages'] == 1
            assert result['body']['successful_notifications'] == 1
            assert result['body']['failed_notifications'] == 0

        # === VERIFICATION: Email was sent correctly ===

        # Note: In moto, we can't directly verify SES send_email calls,
        # but we can verify the Lambda completed successfully and the
        # email service was properly invoked by checking the response

        # Verify bill status was updated in database
        updated_bill_query = text("SELECT status FROM bills WHERE id = :bill_id")
        updated_result = self.db.execute(updated_bill_query, {'bill_id': bill_id})
        updated_bill = updated_result.fetchone()
        assert updated_bill[0] == 'passed'

        # Verify bill status pipeline record was created
        pipeline_query = text("""
            SELECT * FROM bill_status_pipeline
            WHERE bill_id = :bill_id AND is_significant_change = 1
        """)
        pipeline_result = self.db.execute(pipeline_query, {'bill_id': bill_id})
        pipeline_record = pipeline_result.fetchone()
        assert pipeline_record is not None

        print("✅ Complete feedback loop E2E test passed!")
        print("   • Bill status updated: committee → passed")
        print("   • SQS message published and consumed")
        print("   • Notification sender processed 1 message successfully")
        print("   • Database records updated correctly")

    @mock_aws
    @patch('shared.bill_status_service.requests.get')
    def test_non_significant_status_change_no_notification(self, mock_requests_get):
        """
        Test that non-significant status changes do not trigger notifications.
        """
        # === SETUP ===
        test_data = self.seed_test_data()
        bill_id = test_data['bill_id']

        # Set up SQS mock
        sqs_client = boto3.client('sqs', region_name='us-east-1')
        queue = sqs_client.create_queue(QueueName='test-no-notification-queue')
        queue_url = queue['QueueUrl']

        # Mock OpenStates API response with same status (no change)
        mock_openstates_response = {
            'id': 'ocd-bill/12345',
            'title': 'Environmental Protection Act',
            'actions': [
                {
                    'description': 'Still in committee review',
                    'date': '2024-07-19T15:30:00Z',
                    'organization': {'name': 'House Committee'},
                    'result': 'pending'
                }
            ]
        }

        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = mock_openstates_response
        mock_requests_get.return_value = mock_response

        # Process status update
        with patch.dict(os.environ, {'SQS_QUEUE_URL': queue_url}):
            notification_service = NotificationService()
            bill_service = BillStatusUpdateService(self.db, notification_service)

            bill_query = text("SELECT * FROM bills WHERE id = :bill_id")
            result = self.db.execute(bill_query, {'bill_id': bill_id})
            bill = dict(result.mappings().fetchone())

            # Process the bill (should result in no status change)
            status_change = bill_service.update_bill_status(bill)

            # Verify no status change was detected
            assert status_change is None

            # Verify no SQS message was published
            messages = sqs_client.receive_message(QueueUrl=queue_url, MaxNumberOfMessages=10)
            assert 'Messages' not in messages, "Unexpected SQS message found for non-significant change"

        print("✅ Non-significant status change test passed!")
        print("   • No status change detected (committee → committee)")
        print("   • No SQS message published")
        print("   • No unnecessary notifications triggered")

    @mock_aws
    def test_notification_sender_handles_invalid_bill_id(self):
        """
        Test that notification sender handles SQS messages with invalid bill IDs gracefully.
        """
        # Set up AWS mocks
        sqs_client = boto3.client('sqs', region_name='us-east-1')
        sqs_client.create_queue(QueueName='test-invalid-bill-queue')

        ses_client = boto3.client('ses', region_name='us-east-1')
        ses_client.verify_email_identity(EmailAddress='<EMAIL>')

        # Create SQS event with invalid bill ID
        invalid_message = {
            'event_type': 'bill_status_change',
            'bill_id': 'nonexistent-bill-id',
            'previous_status': 'committee',
            'current_status': 'passed',
            'is_significant_change': True,
            'timestamp': '2024-07-19T15:30:00Z'
        }

        sqs_event = {
            'Records': [
                {
                    'messageId': 'test-invalid-message',
                    'receiptHandle': 'test-receipt-handle',
                    'body': json.dumps(invalid_message),
                    'attributes': {
                        'ApproximateReceiveCount': '1',
                        'SentTimestamp': '1626883200000'
                    },
                    'messageAttributes': {},
                    'md5OfBody': 'test-md5',
                    'eventSource': 'aws:sqs',
                    'eventSourceARN': 'arn:aws:sqs:us-east-1:123456789012:test-invalid-bill-queue',
                    'awsRegion': 'us-east-1'
                }
            ]
        }

        mock_context = Mock()
        mock_context.function_name = 'test-notification-sender'

        # Execute notification sender with invalid bill ID
        with patch('shared.database.get_database_session', return_value=self.db):
            result = notification_handler(sqs_event, mock_context)

            # Verify Lambda handled the error gracefully
            assert result['statusCode'] == 200
            assert result['body']['total_messages'] == 1
            assert result['body']['successful_notifications'] == 0
            assert result['body']['failed_notifications'] == 1
            assert len(result['body']['errors']) > 0

        print("✅ Invalid bill ID handling test passed!")
        print("   • Notification sender handled invalid bill ID gracefully")
        print("   • No crash or exception propagated")
        print("   • Error properly recorded and reported")


if __name__ == '__main__':
    # Run the E2E tests
    test_instance = TestFeedbackLoopE2E()

    print("🚀 Running Feedback Loop E2E Tests...")
    print("=" * 60)

    try:
        # Test 1: Complete feedback loop
        test_instance.setup_method()
        test_instance.test_complete_feedback_loop_e2e()
        test_instance.teardown_method()

        # Test 2: Non-significant change
        test_instance.setup_method()
        test_instance.test_non_significant_status_change_no_notification()
        test_instance.teardown_method()

        # Test 3: Error handling
        test_instance.setup_method()
        test_instance.test_notification_sender_handles_invalid_bill_id()
        test_instance.teardown_method()

        print("\n🎉 ALL FEEDBACK LOOP E2E TESTS PASSED!")
        print("=" * 60)
        print("✅ Complete feedback loop validated")
        print("✅ Error handling verified")
        print("✅ System ready for production")

    except Exception as e:
        print(f"\n❌ E2E Test failed: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
