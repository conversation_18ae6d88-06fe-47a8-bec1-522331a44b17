from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_health_check():
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_health_check_with_test_client(test_client):
    """Test health check with test client fixture"""
    response = test_client.get("/api/v1/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_health_check_headers():
    """Test health check response headers"""
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    assert "application/json" in response.headers.get("content-type", "")

def test_health_check_cors():
    """Test health check CORS headers"""
    response = client.options("/api/v1/health")
    # Note: This test depends on CORS middleware configuration
    # The actual test may need to be adjusted based on your CORS setup
    assert response.status_code in [200, 405]  # 405 if OPTIONS not explicitly handled
