{"containerDefinitions": [{"name": "WorkerContainer", "image": "308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-worker:latest", "cpu": 0, "links": [], "portMappings": [], "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "DB_NAME", "value": "modernaction"}, {"name": "DB_PORT", "value": "5432"}, {"name": "DB_HOST", "value": "modernaction-staging-modernactiondatabasefdd241de-giohrnseld4l.csdoaiogadw5.us-east-1.rds.amazonaws.com"}, {"name": "ENVIRONMENT", "value": "staging"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:JwtSecretB8834B39-L7riLTT0OUAn-7XWK3y:jwt_secret::"}, {"name": "OPENSTATES_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:ApiKeysSecret5A58C5CD-crnudnHX1i32-u1GB46:OPENSTATES_API_KEY::"}, {"name": "GOOGLE_CIVIC_INFO_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:ApiKeysSecret5A58C5CD-crnudnHX1i32-u1GB46:GOOGLE_CIVIC_INFO_API_KEY::"}, {"name": "CONGRESS_GOV_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:ApiKeysSecret5A58C5CD-crnudnHX1i32-u1GB46:CONGRESS_GOV_API_KEY::"}, {"name": "HUGGING_FACE_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:ApiKeysSecret5A58C5CD-crnudnHX1i32-u1GB46:HUGGING_FACE_API_KEY::"}, {"name": "DB_USERNAME", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:DatabaseCredentials8547B3E7-7zaDeXDnFrx0-xLhnhX:username::"}, {"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:DatabaseCredentials8547B3E7-7zaDeXDnFrx0-xLhnhX:password::"}], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "modernaction-staging-WorkerTaskDefinitionWorkerContainerLogGroup5CB4CB61-qApxu4YWPs4N", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "worker"}, "secretOptions": []}, "systemControls": [], "credentialSpecs": []}], "family": "ModernActionstagingWorkerTaskDefinitionBA6EE2A9", "taskRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskRole30FC0FBB-YQ5wIqK35bv0", "executionRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8J2CBwsOvvxh", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048"}