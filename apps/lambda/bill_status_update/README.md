# Bill Status Update Lambda Function

This AWS Lambda function automatically checks for bill status updates from the OpenStates API and updates our database accordingly. It's designed to run on a schedule (e.g., daily) to keep our bill information current.

## Overview

The function performs the following tasks:
1. Queries the database for all bills in "active" status (not yet passed, failed, signed, or vetoed)
2. For each active bill, fetches the latest status from the OpenStates API
3. Compares the API status with our database status
4. If the status has changed, creates a new record in the `bill_status_pipeline` table
5. Updates the bill's current status in the database

## Architecture

- **Handler**: `handler.py` - Main Lambda entry point
- **Database**: `shared/database.py` - Database connection utilities
- **Service**: `shared/bill_status_service.py` - Business logic for status updates

## Environment Variables

The Lambda function requires the following environment variables:

- `OPEN_STATES_API_KEY` - API key for OpenStates API
- `DATABASE_URL` - PostgreSQL connection string
- Or individual database components:
  - `DB_HOST` - Database host
  - `DB_PORT` - Database port (default: 5432)
  - `DB_NAME` - Database name (default: modernaction)
  - `DB_USER` - Database username
  - `DB_PASSWORD` - Database password

## Deployment

### Manual Deployment

1. Build the deployment package:
   ```bash
   ./deploy.sh
   ```

2. Upload the generated `build/lambda-package.zip` to AWS Lambda console

### AWS CLI Deployment

```bash
# Update function code
aws lambda update-function-code \
    --function-name modernaction-bill-status-update \
    --zip-file fileb://build/lambda-package.zip

# Update environment variables
aws lambda update-function-configuration \
    --function-name modernaction-bill-status-update \
    --environment Variables='{
        "OPEN_STATES_API_KEY":"your-api-key",
        "DATABASE_URL":"********************************/db"
    }'
```

### CDK Deployment

The function can also be deployed using AWS CDK. Add to your CDK stack:

```python
# Lambda function
bill_status_lambda = _lambda.Function(
    self, "BillStatusUpdateFunction",
    runtime=_lambda.Runtime.PYTHON_3_11,
    handler="handler.lambda_handler",
    code=_lambda.Code.from_asset("apps/lambda/bill_status_update"),
    timeout=Duration.minutes(15),
    memory_size=512,
    environment={
        "OPEN_STATES_API_KEY": "your-api-key",
        "DATABASE_URL": "your-database-url"
    }
)

# Schedule the function to run daily
rule = events.Rule(
    self, "BillStatusUpdateSchedule",
    schedule=events.Schedule.rate(Duration.days(1))
)
rule.add_target(targets.LambdaFunction(bill_status_lambda))
```

## Local Testing

To test the function locally:

```bash
cd apps/lambda/bill_status_update
python handler.py
```

Make sure to set the required environment variables before testing.

## Monitoring

The function logs important events and metrics:
- Number of bills checked
- Number of bills updated
- Any errors encountered
- Execution time

Monitor these logs in CloudWatch to ensure the function is working correctly.

## Error Handling

The function includes comprehensive error handling:
- Database connection failures
- API rate limiting and timeouts
- Individual bill update failures (won't stop processing other bills)
- Malformed API responses

## Performance Considerations

- The function processes bills sequentially to avoid overwhelming the OpenStates API
- Database connections are optimized for Lambda (no connection pooling)
- Function timeout is set to 15 minutes to handle large numbers of bills
- Memory is set to 512MB for optimal performance

## Status Change Detection

The function determines significant status changes that warrant user notification:
- Any progression through the legislative process
- Final outcomes (passed, signed, vetoed, failed)

These significant changes are flagged in the `bill_status_pipeline` table for the notification system to process.
