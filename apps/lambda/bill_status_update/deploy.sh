#!/bin/bash

# Deployment script for Bill Status Update Lambda function
# This script packages the Lambda function and its dependencies for deployment

set -e

FUNCTION_NAME="modernaction-bill-status-update"
LAMBDA_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$LAMBDA_DIR/build"
PACKAGE_FILE="$BUILD_DIR/lambda-package.zip"

echo "Building Lambda deployment package for $FUNCTION_NAME..."

# Clean and create build directory
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"

# Install dependencies
echo "Installing Python dependencies..."
pip install -r "$LAMBDA_DIR/requirements.txt" -t "$BUILD_DIR/python"

# Copy function code
echo "Copying function code..."
cp "$LAMBDA_DIR/handler.py" "$BUILD_DIR/"
cp -r "$LAMBDA_DIR/shared" "$BUILD_DIR/"

# Create deployment package
echo "Creating deployment package..."
cd "$BUILD_DIR"
zip -r "$PACKAGE_FILE" . -x "*.pyc" "*__pycache__*"

echo "Deployment package created: $PACKAGE_FILE"
echo "Package size: $(du -h "$PACKAGE_FILE" | cut -f1)"

# Optional: Deploy to AWS (uncomment if AWS CLI is configured)
# echo "Deploying to AWS Lambda..."
# aws lambda update-function-code \
#     --function-name "$FUNCTION_NAME" \
#     --zip-file "fileb://$PACKAGE_FILE"

echo "Build complete!"
echo ""
echo "To deploy manually:"
echo "1. Upload $PACKAGE_FILE to AWS Lambda console"
echo "2. Or use AWS CLI:"
echo "   aws lambda update-function-code --function-name $FUNCTION_NAME --zip-file fileb://$PACKAGE_FILE"
