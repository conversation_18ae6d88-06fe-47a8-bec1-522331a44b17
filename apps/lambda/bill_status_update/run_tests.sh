#!/bin/bash

# Test runner for Bill Status Update Lambda function
# This script runs all tests for the Lambda function

set -e

LAMBDA_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "Running tests for Bill Status Update Lambda function..."

# Check if pytest is available
if ! command -v pytest &> /dev/null; then
    echo "pytest not found. Installing..."
    pip install pytest pytest-mock
fi

# Run the tests
echo "Running Lambda integration tests..."
cd "$LAMBDA_DIR"
python -m pytest test_lambda.py -v

echo "Running API service tests..."
cd "$LAMBDA_DIR/../../api"
python -m pytest tests/test_bill_status_update.py -v

echo "All tests completed!"
