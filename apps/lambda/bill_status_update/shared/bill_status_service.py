"""
Simplified bill status update service for Lambda functions.

This is a Lambda-optimized version of the bill status update service
with minimal dependencies and optimized for serverless execution.
Fixed SQLite compatibility by using Python datetime instead of NOW().
"""

import logging
import os
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timezone
import uuid
import requests
from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)


class BillStatusUpdateService:
    """Simplified service for updating bill statuses in Lambda environment"""
    
    def __init__(self, db: Session, notification_service=None):
        self.db = db
        self.api_key = os.getenv('OPEN_STATES_API_KEY')
        if not self.api_key:
            raise ValueError("OPEN_STATES_API_KEY environment variable is required")
        
        # Initialize notification service for SQS publishing
        self.notification_service = notification_service
    
    def get_active_bills(self) -> List[Dict[str, Any]]:
        """
        Get all bills that need status checking.
        
        Returns:
            List of bill dictionaries
        """
        query = text("""
            SELECT id, title, bill_number, status, openstates_id, last_action_date
            FROM bills 
            WHERE status IN ('draft', 'introduced', 'committee', 'floor')
            AND openstates_id IS NOT NULL
            ORDER BY last_action_date DESC NULLS LAST
        """)
        
        result = self.db.execute(query)
        bills = [dict(row._mapping) for row in result]
        
        logger.info(f"Found {len(bills)} active bills to check")
        return bills
    
    def fetch_bill_status_from_openstates(self, openstates_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch current bill status from OpenStates API.
        
        Args:
            openstates_id: OpenStates bill ID
            
        Returns:
            Dict containing bill data or None if not found
        """
        api_url = f"https://v3.openstates.org/bills/{openstates_id}"
        headers = {
            "X-API-KEY": self.api_key,
            "Accept": "application/json"
        }
        
        try:
            response = requests.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch bill {openstates_id}: {e}")
            return None
    
    def map_openstates_status_to_bill_status(self, openstates_data: Dict[str, Any]) -> str:
        """
        Map OpenStates status to our bill status.
        
        Args:
            openstates_data: Raw data from OpenStates API
            
        Returns:
            Bill status string
        """
        actions = openstates_data.get('actions', [])
        
        # Check for final statuses first
        for action in actions:
            description = action.get('description', '').lower()
            if 'signed' in description:
                return 'signed'
            elif 'vetoed' in description:
                return 'vetoed'
            elif 'passed' in description and 'final' in description:
                return 'passed'
            elif 'failed' in description or 'defeated' in description:
                return 'failed'
        
        # Check for intermediate statuses
        if actions:
            latest_description = actions[-1].get('description', '').lower()
            if 'floor' in latest_description or 'third reading' in latest_description:
                return 'floor'
            elif 'committee' in latest_description:
                return 'committee'
        
        return 'introduced'
    
    def is_significant_status_change(self, old_status: str, new_status: str) -> bool:
        """
        Determine if a status change is significant.
        
        Args:
            old_status: Previous bill status
            new_status: New bill status
            
        Returns:
            True if the change is significant
        """
        status_order = {
            'draft': 0,
            'introduced': 1,
            'committee': 2,
            'floor': 3,
            'passed': 4,
            'signed': 5,
            'vetoed': 5,
            'failed': 5
        }
        
        old_order = status_order.get(old_status, 0)
        new_order = status_order.get(new_status, 0)
        
        return (new_order > old_order) or new_status in ['passed', 'signed', 'vetoed', 'failed']
    
    def update_bill_status(self, bill: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Update a single bill's status.
        
        Args:
            bill: Bill dictionary
            
        Returns:
            Status change record if updated, None otherwise
        """
        openstates_id = bill['openstates_id']
        if not openstates_id:
            return None
        
        # Fetch current status from API
        external_data = self.fetch_bill_status_from_openstates(openstates_id)
        if not external_data:
            return None
        
        # Determine new status
        new_status = self.map_openstates_status_to_bill_status(external_data)
        old_status = bill['status']
        
        # Check if status has changed
        if new_status == old_status:
            return None
        
        # Determine when the status changed
        actions = external_data.get('actions', [])
        status_changed_at = datetime.utcnow()
        if actions:
            latest_action_date = actions[-1].get('date')
            if latest_action_date:
                try:
                    status_changed_at = datetime.fromisoformat(latest_action_date.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    pass
        
        is_significant = self.is_significant_status_change(old_status, new_status)
        
        # Extract vote details
        vote_details = None
        for action in reversed(actions):
            if any(keyword in action.get('description', '').lower() for keyword in ['vote', 'passed', 'failed']):
                vote_details = {
                    'action_description': action.get('description'),
                    'action_date': action.get('date'),
                    'organization': action.get('organization', {}).get('name'),
                    'result': action.get('result')
                }
                break
        
        # Create status change record
        status_id = str(uuid.uuid4())
        now = datetime.now(timezone.utc)
        insert_query = text("""
            INSERT INTO bill_status_pipeline
            (id, created_at, updated_at, bill_id, previous_status, current_status,
             status_changed_at, detected_at, external_data, vote_details,
             notification_sent, is_significant_change, notes)
            VALUES (:status_id, :created_at, :updated_at, :bill_id, :previous_status, :current_status,
                    :status_changed_at, :detected_at, :external_data, :vote_details,
                    false, :is_significant_change, :notes)
        """)
        
        self.db.execute(insert_query, {
            'status_id': status_id,
            'created_at': now,
            'updated_at': now,
            'bill_id': bill['id'],
            'previous_status': old_status,
            'current_status': new_status,
            'status_changed_at': status_changed_at,
            'detected_at': now,
            'external_data': json.dumps(external_data) if external_data else None,
            'vote_details': json.dumps(vote_details) if vote_details else None,
            'is_significant_change': is_significant,
            'notes': f"Status updated from {old_status} to {new_status}"
        })
        
        # Update the bill's current status
        update_query = text("""
            UPDATE bills
            SET status = :new_status, last_action_date = :status_changed_at, updated_at = :updated_at
            WHERE id = :bill_id
        """)

        self.db.execute(update_query, {
            'new_status': new_status,
            'status_changed_at': status_changed_at,
            'bill_id': bill['id'],
            'updated_at': now
        })
        
        logger.info(f"Updated bill {bill['id']}: {old_status} -> {new_status}")
        
        status_change = {
            'bill_id': bill['id'],
            'previous_status': old_status,
            'current_status': new_status,
            'is_significant_change': is_significant,
            'vote_details': vote_details,
            'timestamp': status_changed_at.isoformat()
        }
        
        # Perform values analysis if bill doesn't already have it
        try:
            # Check if values analysis already exists
            check_query = text("SELECT id FROM bill_values_analysis WHERE bill_id = :bill_id")
            existing = self.db.execute(check_query, {'bill_id': bill['id']}).first()

            if not existing:
                # Perform basic values analysis using raw SQL for Lambda compatibility
                self._perform_values_analysis_lambda(bill)
                logger.info(f"Values analysis completed for updated bill {bill['id']}")
        except Exception as e:
            logger.error(f"Values analysis failed for updated bill {bill['id']}: {e}")
            # Don't fail the entire update process if values analysis fails

        # Publish notification if significant change and notification service is available
        if is_significant and self.notification_service:
            try:
                self.notification_service.publish_status_change(status_change)
                logger.info(f"Published notification for bill {bill['id']} status change")
            except Exception as e:
                logger.error(f"Failed to publish notification for bill {bill['id']}: {e}")
                # Don't fail the entire update process if notification fails

        return status_change
    
    def update_all_active_bills(self) -> Tuple[int, int]:
        """
        Update status for all active bills.
        
        Returns:
            Tuple of (total_checked, total_updated)
        """
        active_bills = self.get_active_bills()
        total_checked = len(active_bills)
        total_updated = 0
        
        for bill in active_bills:
            try:
                status_record = self.update_bill_status(bill)
                if status_record:
                    total_updated += 1
                
            except Exception as e:
                logger.error(f"Error updating bill {bill['id']}: {e}")
                continue
        
        logger.info(f"Bill status update complete: {total_updated}/{total_checked} bills updated")
        return total_checked, total_updated

    def _perform_values_analysis_lambda(self, bill: Dict[str, Any]) -> None:
        """
        Perform basic values analysis using raw SQL for Lambda compatibility.
        This is a simplified version that creates basic analysis records.
        """
        try:
            # Simple keyword-based analysis for Lambda environment
            title = bill.get('title', '').lower()

            # Basic scoring based on keywords
            democracy_threat = 0
            democracy_support = 0
            human_rights_threat = 0
            human_rights_support = 0
            environmental_threat = 0
            environmental_support = 0

            # Democracy keywords
            if any(word in title for word in ['voting', 'election', 'ballot', 'democracy']):
                democracy_support = 6
            elif any(word in title for word in ['restrict', 'limit', 'reduce']):
                democracy_threat = 4

            # Human rights keywords
            if any(word in title for word in ['healthcare', 'education', 'equality', 'rights']):
                human_rights_support = 5
            elif any(word in title for word in ['discriminat', 'restrict', 'ban']):
                human_rights_threat = 3

            # Environmental keywords
            if any(word in title for word in ['environment', 'climate', 'clean', 'renewable']):
                environmental_support = 7
            elif any(word in title for word in ['deregulat', 'pollut', 'drill']):
                environmental_threat = 5

            # Determine overall levels
            max_threat = max(democracy_threat, human_rights_threat, environmental_threat)
            max_support = max(democracy_support, human_rights_support, environmental_support)

            threat_level = self._score_to_level(max_threat)
            support_level = self._score_to_level(max_support)

            # Create analysis record
            analysis_id = str(uuid.uuid4())
            now = datetime.now(timezone.utc)

            insert_analysis_query = text("""
                INSERT INTO bill_values_analysis
                (id, created_at, updated_at, bill_id, democracy_threat_score, democracy_support_score,
                 human_rights_threat_score, human_rights_support_score, environmental_threat_score,
                 environmental_support_score, overall_threat_level, overall_support_level,
                 analysis_reasoning, confidence_score, requires_human_review, is_flagged, is_blocked,
                 analyzed_at, ai_model_version)
                VALUES (:analysis_id, :created_at, :updated_at, :bill_id, :democracy_threat_score,
                        :democracy_support_score, :human_rights_threat_score, :human_rights_support_score,
                        :environmental_threat_score, :environmental_support_score, :overall_threat_level,
                        :overall_support_level, :analysis_reasoning, :confidence_score, :requires_human_review,
                        false, false, :analyzed_at, :ai_model_version)
            """)

            reasoning = {
                'democracy': 'Basic keyword analysis of democratic processes',
                'human_rights': 'Basic keyword analysis of civil liberties',
                'environment': 'Basic keyword analysis of environmental impact',
                'methodology': 'Lambda-compatible keyword analysis v1.0'
            }

            self.db.execute(insert_analysis_query, {
                'analysis_id': analysis_id,
                'created_at': now,
                'updated_at': now,
                'bill_id': bill['id'],
                'democracy_threat_score': democracy_threat,
                'democracy_support_score': democracy_support,
                'human_rights_threat_score': human_rights_threat,
                'human_rights_support_score': human_rights_support,
                'environmental_threat_score': environmental_threat,
                'environmental_support_score': environmental_support,
                'overall_threat_level': threat_level,
                'overall_support_level': support_level,
                'analysis_reasoning': json.dumps(reasoning),
                'confidence_score': 0.6,  # Lower confidence for basic analysis
                'requires_human_review': True,  # Always require review for Lambda analysis
                'analyzed_at': now,
                'ai_model_version': 'lambda_basic_v1.0'
            })

            # Create basic tags
            self._create_basic_tags_lambda(analysis_id, bill['id'], max_threat, max_support)

        except Exception as e:
            logger.error(f"Failed to perform Lambda values analysis for bill {bill['id']}: {e}")
            raise

    def _score_to_level(self, score: int) -> str:
        """Convert numeric score to categorical level."""
        if score == 0:
            return 'none'
        elif score <= 3:
            return 'low'
        elif score <= 6:
            return 'medium'
        elif score <= 8:
            return 'high'
        else:
            return 'critical'

    def _create_basic_tags_lambda(self, analysis_id: str, bill_id: str, max_threat: int, max_support: int) -> None:
        """Create basic tags for Lambda analysis."""
        now = datetime.now(timezone.utc)

        # Create tags based on highest scores
        if max_support >= 5:
            tag_id = str(uuid.uuid4())
            insert_tag_query = text("""
                INSERT INTO bill_values_tags
                (id, created_at, updated_at, bill_id, analysis_id, tag_category, tag_type,
                 tag_name, display_text, severity_level, display_priority, color_theme,
                 icon_name, is_active)
                VALUES (:tag_id, :created_at, :updated_at, :bill_id, :analysis_id, :tag_category,
                        :tag_type, :tag_name, :display_text, :severity_level, :display_priority,
                        :color_theme, :icon_name, true)
            """)

            self.db.execute(insert_tag_query, {
                'tag_id': tag_id,
                'created_at': now,
                'updated_at': now,
                'bill_id': bill_id,
                'analysis_id': analysis_id,
                'tag_category': 'general',
                'tag_type': 'support',
                'tag_name': 'policy_support',
                'display_text': 'Policy Considerations',
                'severity_level': max_support,
                'display_priority': max_support,
                'color_theme': 'blue',
                'icon_name': 'info'
            })

        elif max_threat >= 5:
            tag_id = str(uuid.uuid4())
            insert_tag_query = text("""
                INSERT INTO bill_values_tags
                (id, created_at, updated_at, bill_id, analysis_id, tag_category, tag_type,
                 tag_name, display_text, severity_level, display_priority, color_theme,
                 icon_name, is_active)
                VALUES (:tag_id, :created_at, :updated_at, :bill_id, :analysis_id, :tag_category,
                        :tag_type, :tag_name, :display_text, :severity_level, :display_priority,
                        :color_theme, :icon_name, true)
            """)

            self.db.execute(insert_tag_query, {
                'tag_id': tag_id,
                'created_at': now,
                'updated_at': now,
                'bill_id': bill_id,
                'analysis_id': analysis_id,
                'tag_category': 'general',
                'tag_type': 'impact',
                'tag_name': 'policy_impact',
                'display_text': 'Policy Impact Considerations',
                'severity_level': max_threat,
                'display_priority': max_threat,
                'color_theme': 'orange',
                'icon_name': 'alert-circle'
            })
