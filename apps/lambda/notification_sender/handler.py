"""
AWS Lambda function for sending bill status update notifications.

This function is triggered by SQS messages from the bill status update Lambda
and sends email notifications to users who have taken actions on bills that
have had significant status changes.
"""

import json
import logging
import os
import sys
from typing import Dict, Any, List

# Load environment variables from .env file for local testing
if os.path.exists(os.path.join(os.path.dirname(__file__), '.env')):
    from dotenv import load_dotenv
    load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))

# Add the shared code to the path
sys.path.append('/opt/python')
sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler for processing bill status update notifications.
    
    Args:
        event: SQS event data containing bill status change messages
        context: Lambda context object
        
    Returns:
        Dict with processing results
    """
    logger.info("Starting notification sender job")
    
    # Track processing results
    total_messages = 0
    successful_notifications = 0
    failed_notifications = 0
    errors = []
    
    try:
        # Import here to avoid cold start issues
        from shared.database import get_database_session
        from shared.email_service import EmailService
        
        # Get database session
        db = get_database_session()
        if not db:
            raise Exception("Could not establish database connection")
        
        try:
            # Create email service
            email_service = EmailService()
            
            # Process each SQS message
            for record in event.get('Records', []):
                total_messages += 1
                
                try:
                    # Parse the message body
                    message_body = json.loads(record['body'])
                    logger.info(f"Processing message for bill {message_body.get('bill_id')}")
                    
                    # Process the bill status change notification
                    success, error_msg = process_bill_notification(db, email_service, message_body)

                    if success:
                        successful_notifications += 1
                    else:
                        failed_notifications += 1
                        if error_msg:
                            errors.append(error_msg)
                        
                except Exception as e:
                    logger.error(f"Error processing SQS message: {e}", exc_info=True)
                    failed_notifications += 1
                    errors.append(str(e))
            
            # Commit any database changes
            db.commit()
            
            # Prepare response
            result = {
                'statusCode': 200,
                'body': {
                    'message': 'Notification processing completed',
                    'total_messages': total_messages,
                    'successful_notifications': successful_notifications,
                    'failed_notifications': failed_notifications,
                    'errors': errors
                }
            }
            
            logger.info(f"Notification processing completed: {successful_notifications}/{total_messages} successful")
            return result
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Notification sender job failed: {str(e)}", exc_info=True)
        
        # Return error response
        return {
            'statusCode': 500,
            'body': {
                'message': 'Notification sender failed',
                'error': str(e),
                'total_messages': total_messages,
                'successful_notifications': successful_notifications,
                'failed_notifications': failed_notifications
            }
        }


def process_bill_notification(db, email_service, message_data: Dict[str, Any]) -> tuple[bool, str]:
    """
    Process a single bill status change notification.

    Args:
        db: Database session
        email_service: Email service instance
        message_data: Message data from SQS

    Returns:
        Tuple of (success: bool, error_message: str)
    """
    try:
        bill_id = message_data.get('bill_id')
        if not bill_id:
            error_msg = "Missing bill_id in message data"
            logger.error(error_msg)
            return False, error_msg

        # Get bill information
        bill_data = get_bill_data(db, bill_id)
        if not bill_data:
            error_msg = f"Bill not found: {bill_id}"
            logger.error(error_msg)
            return False, error_msg
        
        # Get users who have taken actions on this bill
        user_actions = get_user_actions_for_bill(db, bill_id)
        if not user_actions:
            logger.info(f"No user actions found for bill {bill_id}")
            return True, None  # Not an error, just no notifications to send
        
        logger.info(f"Found {len(user_actions)} user actions for bill {bill_id}")
        
        # Send notifications to each user
        notifications_sent = 0
        for action in user_actions:
            try:
                success = email_service.send_vote_update_email(
                    to_email=action['user_email'],
                    bill_data=bill_data,
                    status_change=message_data,
                    user_action=action
                )
                
                if success:
                    notifications_sent += 1
                    # Mark notification as sent in the database
                    mark_notification_sent(db, bill_id, action['user_id'])
                
            except Exception as e:
                logger.error(f"Failed to send notification to user {action['user_id']}: {e}")
        
        logger.info(f"Sent {notifications_sent}/{len(user_actions)} notifications for bill {bill_id}")
        return notifications_sent > 0, None
        
    except Exception as e:
        error_msg = f"Error processing bill notification: {e}"
        logger.error(error_msg, exc_info=True)
        return False, error_msg


def get_bill_data(db, bill_id: int) -> Dict[str, Any]:
    """Get bill information from database"""
    from sqlalchemy import text
    
    query = text("""
        SELECT id, title, bill_number, status, openstates_id, last_action_date
        FROM bills 
        WHERE id = :bill_id
    """)
    
    result = db.execute(query, {'bill_id': bill_id}).fetchone()
    if result:
        return dict(result._mapping)
    return None


def get_user_actions_for_bill(db, bill_id: int) -> List[Dict[str, Any]]:
    """Get all user actions for a specific bill"""
    from sqlalchemy import text
    
    query = text("""
        SELECT DISTINCT a.id as action_id, a.user_id, a.message, a.created_at,
               u.id as user_id, u.email as user_email, u.first_name, u.last_name
        FROM actions a
        JOIN users u ON a.user_id = u.id
        WHERE a.bill_id = :bill_id
        AND a.status = 'completed'
        ORDER BY a.created_at DESC
    """)
    
    result = db.execute(query, {'bill_id': bill_id})
    return [dict(row._mapping) for row in result]


def mark_notification_sent(db, bill_id: int, user_id: int):
    """Mark that a notification has been sent for this bill/user combination"""
    from sqlalchemy import text
    
    # Update the bill_status_pipeline to mark notification as sent
    query = text("""
        UPDATE bill_status_pipeline 
        SET notification_sent = true, updated_at = NOW()
        WHERE bill_id = :bill_id 
        AND is_significant_change = true
        AND notification_sent = false
    """)
    
    db.execute(query, {'bill_id': bill_id})


def local_test(test_event_file: str = None):
    """
    Function for local testing of the Lambda handler.
    
    Args:
        test_event_file: Optional path to JSON file containing test SQS event
    """
    print("Testing notification sender Lambda function locally...")
    
    # Load test event from file if provided
    if test_event_file and os.path.exists(test_event_file):
        with open(test_event_file, 'r') as f:
            event = json.load(f)
        print(f"Loaded test event from {test_event_file}")
    else:
        # Default mock SQS event
        event = {
            "Records": [
                {
                    "messageId": "test-message-id",
                    "receiptHandle": "test-receipt-handle",
                    "body": json.dumps({
                        "event_type": "bill_status_change",
                        "bill_id": 1,
                        "previous_status": "committee",
                        "current_status": "floor",
                        "is_significant_change": True,
                        "vote_details": {
                            "action_description": "Passed committee vote",
                            "action_date": "2024-07-18",
                            "organization": "House Committee on Energy"
                        },
                        "timestamp": "2024-07-18T12:00:00Z"
                    }),
                    "attributes": {
                        "ApproximateReceiveCount": "1",
                        "SentTimestamp": "1626883200000",
                        "SenderId": "AIDACKCEVSQ6C2EXAMPLE",
                        "ApproximateFirstReceiveTimestamp": "1626883200000"
                    },
                    "messageAttributes": {},
                    "md5OfBody": "test-md5",
                    "eventSource": "aws:sqs",
                    "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
                    "awsRegion": "us-east-1"
                }
            ]
        }
    
    class MockContext:
        def __init__(self):
            self.function_name = 'modernaction-notification-sender'
            self.function_version = '$LATEST'
            self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:modernaction-notification-sender'
            self.memory_limit_in_mb = '512'
            self.remaining_time_in_millis = 300000
            self.log_group_name = '/aws/lambda/modernaction-notification-sender'
            self.log_stream_name = '2024/07/18/[$LATEST]test'
            self.aws_request_id = 'test-request-id'
        
        def get_remaining_time_in_millis(self):
            return self.remaining_time_in_millis
    
    context = MockContext()
    
    # Run the handler
    print("Executing Lambda handler...")
    result = lambda_handler(event, context)
    print(f"Result: {json.dumps(result, indent=2)}")
    
    return result


if __name__ == "__main__":
    import sys
    test_event_file = sys.argv[1] if len(sys.argv) > 1 else None
    local_test(test_event_file)