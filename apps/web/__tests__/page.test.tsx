import { render, screen } from '@testing-library/react';
import { UserProvider } from '@auth0/nextjs-auth0/client';
import Home from '../src/app/page';

// Mock Auth0 user for testing
const mockUser = {
  sub: 'auth0|test123',
  email: '<EMAIL>',
  name: 'Test User'
};

test('renders a heading', () => {
  render(
    <UserProvider user={mockUser}>
      <Home />
    </UserProvider>
  );
  const heading = screen.getByText(/Empower Your/i);
  expect(heading).toBeInTheDocument();
});
