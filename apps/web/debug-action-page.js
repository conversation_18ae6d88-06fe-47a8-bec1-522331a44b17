// Debug action page buttons
const { chromium } = require('playwright');

async function debugActionPage() {
  console.log('🔍 Debugging action page...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    console.log('📍 Navigating to bills page...');
    await page.goto('http://localhost:3001/bills');
    
    console.log('📍 Waiting for bills to load...');
    await page.waitForSelector('text=Lower Energy Costs Act', { timeout: 15000 });
    
    console.log('📍 Clicking on first bill...');
    await page.click('a:has-text("Take Action")');
    
    console.log('📍 Waiting for action page to load...');
    await page.waitForURL('**/action', { timeout: 10000 });
    
    console.log('📍 Waiting for page to stabilize...');
    await page.waitForTimeout(3000);
    
    console.log('📍 Getting all button texts on action page...');
    const allButtons = await page.locator('button').all();
    
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      const text = await button.textContent();
      const isVisible = await button.isVisible();
      const isEnabled = await button.isEnabled();
      console.log(`Button ${i}: "${text}" (visible: ${isVisible}, enabled: ${isEnabled})`);
    }
    
    console.log('📍 Looking for stance-related text...');
    const supportText = await page.locator('text=support').count();
    console.log(`"support" text: ${supportText}`);
    
    const opposeText = await page.locator('text=oppose').count();
    console.log(`"oppose" text: ${opposeText}`);
    
    const amendText = await page.locator('text=amend').count();
    console.log(`"amend" text: ${amendText}`);
    
    // Check for radio buttons or other input types
    const radioButtons = await page.locator('input[type="radio"]').count();
    console.log(`Radio buttons: ${radioButtons}`);
    
    const checkboxes = await page.locator('input[type="checkbox"]').count();
    console.log(`Checkboxes: ${checkboxes}`);
    
    console.log('📍 Debug completed!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    await browser.close();
  }
}

debugActionPage().catch(console.error);
