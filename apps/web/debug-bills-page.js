// Debug script to see what's on the bills page
const { chromium } = require('playwright');

async function debugBillsPage() {
  console.log('🔍 Debugging bills page...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    console.log('📍 Navigating to bills page...');
    await page.goto('http://localhost:3001/bills');
    
    console.log('📍 Waiting for page to load...');
    await page.waitForTimeout(5000);
    
    console.log('📍 Taking screenshot...');
    await page.screenshot({ path: 'bills-page-debug.png', fullPage: true });
    
    console.log('📍 Getting page content...');
    const content = await page.content();
    console.log('Page title:', await page.title());
    
    // Check for specific elements
    const billCards = await page.locator('[data-testid="bill-card"]').count();
    console.log(`Found ${billCards} bill cards`);
    
    const takeActionButtons = await page.locator('button:has-text("Take Action")').count();
    console.log(`Found ${takeActionButtons} "Take Action" buttons`);
    
    const allButtons = await page.locator('button').count();
    console.log(`Found ${allButtons} total buttons`);
    
    // Get all button texts
    const buttonTexts = await page.locator('button').allTextContents();
    console.log('All button texts:', buttonTexts);
    
    // Check for error messages
    const errorMessages = await page.locator('text=Failed to load').count();
    console.log(`Found ${errorMessages} error messages`);
    
    // Check for loading states
    const loadingElements = await page.locator('text=Loading').count();
    console.log(`Found ${loadingElements} loading elements`);
    
    // Check if bills are actually displayed
    const billTitles = await page.locator('h3, h2, h1').allTextContents();
    console.log('Found titles:', billTitles.slice(0, 5)); // First 5 titles
    
    console.log('📍 Debug completed!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    // Keep browser open for manual inspection
    console.log('📍 Browser left open for manual inspection. Close manually when done.');
    // await browser.close();
  }
}

debugBillsPage().catch(console.error);
