// Find Take Action buttons
const { chromium } = require('playwright');

async function findTakeAction() {
  console.log('🔍 Finding Take Action buttons...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    console.log('📍 Navigating to bills page...');
    await page.goto('http://localhost:3001/bills');
    
    console.log('📍 Waiting for page to load...');
    await page.waitForTimeout(3000);
    
    console.log('📍 Getting all button texts...');
    const allButtons = await page.locator('button').all();
    
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      const text = await button.textContent();
      const isVisible = await button.isVisible();
      console.log(`Button ${i}: "${text}" (visible: ${isVisible})`);
    }
    
    console.log('📍 Looking for specific patterns...');
    
    // Try different selectors
    const takeActionCount = await page.locator('button:has-text("Take Action")').count();
    console.log(`"Take Action" buttons: ${takeActionCount}`);
    
    const actionCount = await page.locator('button:has-text("Action")').count();
    console.log(`"Action" buttons: ${actionCount}`);
    
    const learnMoreCount = await page.locator('button:has-text("Learn More")').count();
    console.log(`"Learn More" buttons: ${learnMoreCount}`);
    
    // Check for links instead of buttons
    const actionLinks = await page.locator('a:has-text("Take Action")').count();
    console.log(`"Take Action" links: ${actionLinks}`);
    
    // Check for any element with "action" text
    const anyAction = await page.locator('text=Action').count();
    console.log(`Any "Action" text: ${anyAction}`);
    
    console.log('📍 Checking bill card structure...');
    const billCards = await page.locator('[data-testid="bill-card"]').count();
    console.log(`Bill cards with data-testid: ${billCards}`);
    
    // Try to find bill cards by other means
    const cardLike = await page.locator('.card, [class*="card"], [class*="bill"]').count();
    console.log(`Card-like elements: ${cardLike}`);
    
    console.log('📍 Debug completed!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    await browser.close();
  }
}

findTakeAction().catch(console.error);
