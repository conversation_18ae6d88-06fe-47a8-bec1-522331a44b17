import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Disable standalone output for now to fix container issues
  // output: 'standalone',

  // Disable ESLint during build for deployment
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Disable TypeScript checking during build for deployment
  typescript: {
    ignoreBuildErrors: true,
  },

  // CRITICAL FIX: Downgraded to Tailwind CSS v3 to avoid lightningcss dependency
  // This eliminates the cross-platform build failure completely
  
  // Disable rewrites for static export
  // Note: API calls will be handled client-side using NEXT_PUBLIC_API_URL
  
  // Environment variables
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    INTERNAL_API_URL: process.env.INTERNAL_API_URL,
  },
  
  // Note: Headers removed for static export compatibility
  // Security headers would be configured at the CloudFront/S3 level
};

export default nextConfig;
