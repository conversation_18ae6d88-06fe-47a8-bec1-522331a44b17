// Simple debug script
const { chromium } = require('playwright');

async function simpleDebug() {
  console.log('🔍 Simple debug...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    console.log('📍 Navigating to bills page...');
    await page.goto('http://localhost:3001/bills');
    
    console.log('📍 Waiting for page to load...');
    await page.waitForTimeout(3000);
    
    console.log('📍 Getting page title...');
    const title = await page.title();
    console.log('Page title:', title);
    
    console.log('📍 Checking for bills...');
    const billElements = await page.locator('text=Lower Energy Costs Act').count();
    console.log(`Found ${billElements} bill elements`);
    
    console.log('📍 Checking for buttons...');
    const buttons = await page.locator('button').count();
    console.log(`Found ${buttons} buttons`);
    
    if (buttons > 0) {
      const buttonTexts = await page.locator('button').allTextContents();
      console.log('Button texts:', buttonTexts.slice(0, 10));
    }
    
    console.log('📍 Checking URL...');
    console.log('Current URL:', page.url());
    
    console.log('📍 Debug completed!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    await browser.close();
  }
}

simpleDebug().catch(console.error);
