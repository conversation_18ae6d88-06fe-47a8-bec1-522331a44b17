'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface ActionStats {
  total_actions: number;
  by_stance: { [key: string]: number };
  by_status: { [key: string]: number };
  recent_actions: Array<{
    id: string;
    bill_title: string;
    user_email: string;
    stance: string;
    created_at: string;
    selected_reasons: string[];
    custom_reason?: string;
  }>;
}

interface DetailedAction {
  id: string;
  bill_id: string;
  bill_title: string;
  user_id: string;
  user_email: string;
  user_name: string;
  stance: string;
  status: string;
  subject: string;
  message: string;
  created_at: string;
  sent_at?: string;
  delivered_at?: string;
  selected_reasons: Array<{
    id: string;
    reason_text: string;
  }>;
  custom_reason?: string;
  user_location: {
    zip_code?: string;
    city?: string;
    state?: string;
    address?: string;
  };
  representatives_contacted: Array<{
    name: string;
    title: string;
    party: string;
  }>;
  action_metadata?: any;
}

interface BillAnalytics {
  bill_id: string;
  bill_title: string;
  stats: {
    support: number;
    oppose: number;
    amend: number;
    total: number;
  };
  top_reasons: Array<{
    reason_text: string;
    count: number;
    stance: string;
  }>;
  custom_reasons: string[];
}

export default function AdminPage() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [billId, setBillId] = useState('hr9-118');
  const [results, setResults] = useState<any>(null);
  const [actionStats, setActionStats] = useState<ActionStats | null>(null);
  const [billAnalytics, setBillAnalytics] = useState<BillAnalytics[]>([]);
  const [selectedBillId, setSelectedBillId] = useState('bd9c4dfb-a7b7-406d-ac41-263f36548c50');
  const [loadingAnalytics, setLoadingAnalytics] = useState(false);
  const [detailedActions, setDetailedActions] = useState<DetailedAction[]>([]);
  const [showDetailedView, setShowDetailedView] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [selectedActionId, setSelectedActionId] = useState<string | null>(null);

  // Batch processing state
  const [batchProcessing, setBatchProcessing] = useState(false);
  const [batchResults, setBatchResults] = useState<any>(null);
  const [batchConfig, setBatchConfig] = useState({
    maxBills: 5  // Keep it simple like the original working version
  });

  // Load analytics data on component mount
  useEffect(() => {
    loadActionAnalytics();
  }, []);

  const loadActionAnalytics = async () => {
    setLoadingAnalytics(true);
    try {
      // Get overall action stats from admin overview
      const overviewResponse = await fetch('http://localhost:8000/api/v1/simple/admin/overview');
      
      // Get bill-specific analytics for the selected bill
      const billStatsResponse = await fetch(`http://localhost:8000/api/v1/simple/bills/${selectedBillId}/stats`);
      const topReasonsResponse = await fetch(`http://localhost:8000/api/v1/simple/bills/${selectedBillId}/top-reasons?stance=support&limit=5`);
      const customReasonsResponse = await fetch(`http://localhost:8000/api/v1/simple/bills/${selectedBillId}/custom-reasons?stance=support&limit=10`);
      
      if (overviewResponse.ok) {
        const overview = await overviewResponse.json();
        setActionStats({
          total_actions: overview.total_actions,
          by_stance: overview.by_stance,
          by_status: overview.by_status,
          recent_actions: overview.recent_actions
        });
      }
      
      if (billStatsResponse.ok && topReasonsResponse.ok && customReasonsResponse.ok) {
        const billStats = await billStatsResponse.json();
        const topReasons = await topReasonsResponse.json();
        const customReasons = await customReasonsResponse.json();
        
        setBillAnalytics([{
          bill_id: selectedBillId,
          bill_title: 'Affordable Housing Development Act', // This would come from the bill API
          stats: billStats.stats,
          top_reasons: topReasons.top_reasons || [],
          custom_reasons: customReasons.custom_reasons || []
        }]);
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
      // Don't show error toast for analytics - it's not critical for bill processing
      // toast.error('Failed to load analytics data');
    } finally {
      setLoadingAnalytics(false);
    }
  };

  const loadDetailedActions = async (billId?: string) => {
    setLoadingDetails(true);
    try {
      const url = billId 
        ? `http://localhost:8000/api/v1/simple/admin/actions/detailed?bill_id=${billId}&limit=50`
        : `http://localhost:8000/api/v1/simple/admin/actions/detailed?limit=50`;
      
      const response = await fetch(url);
      
      if (response.ok) {
        const data = await response.json();
        setDetailedActions(data.actions);
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error loading detailed actions:', error);
      toast.error('Failed to load detailed action data');
    } finally {
      setLoadingDetails(false);
    }
  };

  const handlePullBill = async () => {
    setIsProcessing(true);
    setResults(null);

    try {
      // Parse bill ID (e.g., "hr9-118" -> congress=118, bill_type=hr, bill_number=9)
      const match = billId.match(/^([a-z]+)(\d+)-(\d+)$/i);
      if (!match) {
        throw new Error('Invalid bill ID format. Use format like: hr9-118, s1234-118');
      }

      const [, billType, billNumber, congress] = match;

      const response = await fetch(`http://localhost:8000/api/v1/admin/process-and-save-bill?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setResults(result);
      toast.success('Bill processed successfully!');
    } catch (error) {
      console.error('Error processing bill:', error);
      toast.error('Failed to process bill');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTestPreview = async () => {
    setIsProcessing(true);
    
    try {
      const response = await fetch('http://localhost:8000/api/v1/actions/preview-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bill_id: results?.bill_id || '25177ae6-9916-456b-aec2-7d3b84f87647', // Use processed bill ID or fallback
          stance: 'support',
          selected_reasons: ['This bill improves safety standards'],
          custom_reasons: ['My custom reason for supporting this'],
          zip_code: '60302'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setResults(result);
      toast.success('Message preview generated successfully!');
    } catch (error) {
      console.error('Error generating preview:', error);
      toast.error('Failed to generate message preview');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBatchProcess = async () => {
    setBatchProcessing(true);
    setBatchResults(null);

    try {
      // Use the original working seed-real-bills endpoint
      const params = new URLSearchParams({
        limit: batchConfig.maxBills.toString()
      });

      const response = await fetch(`http://localhost:8000/api/v1/bills/admin/seed-real-bills?${params}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setBatchResults(result);

      if (result.success) {
        toast.success(`Successfully processed ${result.created_bills?.length || 0} bills!`);
      } else {
        toast.error('Batch processing completed with errors');
      }
    } catch (error) {
      console.error('Error in batch processing:', error);
      toast.error('Failed to process bills in batch');
    } finally {
      setBatchProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="mt-1 text-sm text-gray-600">
              Test bill processing and AI summarization
            </p>
          </div>

          <div className="p-6 space-y-6">
            {/* Bill ID Input */}
            <div>
              <label htmlFor="billId" className="block text-sm font-medium text-gray-700 mb-2">
                Bill ID
              </label>
              <input
                type="text"
                id="billId"
                value={billId}
                onChange={(e) => setBillId(e.target.value)}
                placeholder="hr9-118"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <button
                onClick={handlePullBill}
                disabled={isProcessing || !billId}
                className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                  isProcessing || !billId
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isProcessing ? 'Processing...' : 'Pull & Process Bill'}
              </button>

              <button
                onClick={handleTestPreview}
                disabled={isProcessing || !billId}
                className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                  isProcessing || !billId
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-green-600 hover:bg-green-700'
                }`}
              >
                {isProcessing ? 'Processing...' : 'Test Message Preview'}
              </button>
            </div>

            {/* Batch Bill Processing Section */}
            <div className="mt-8 p-6 bg-orange-50 rounded-lg border border-orange-200">
              <h3 className="text-lg font-medium text-orange-900 mb-4">Batch Bill Processing</h3>
              <p className="text-sm text-orange-800 mb-6">
                Fill your database with recent bills from Congress.gov using the proven working flow.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Configuration Panel */}
                <div className="space-y-4">
                  <h4 className="font-medium text-orange-900">Configuration</h4>

                  {/* Max Bills */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Number of Bills to Process
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="20"
                      value={batchConfig.maxBills}
                      onChange={(e) => setBatchConfig(prev => ({ ...prev, maxBills: parseInt(e.target.value) || 5 }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Fetches the most recent bills from Congress.gov (recommended: 5-10)
                    </p>
                  </div>
                </div>

                {/* Action Panel */}
                <div className="space-y-4">
                  <h4 className="font-medium text-orange-900">Actions</h4>

                  <button
                    onClick={handleBatchProcess}
                    disabled={batchProcessing}
                    className={`w-full px-4 py-3 text-sm font-medium text-white rounded-md ${
                      batchProcessing
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-orange-600 hover:bg-orange-700'
                    }`}
                  >
                    {batchProcessing ? 'Processing Bills...' : 'Start Batch Processing'}
                  </button>

                  <div className="text-xs text-gray-600">
                    <p>This will:</p>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Fetch recent bills from Congress.gov</li>
                      <li>Download full bill text</li>
                      <li>Generate AI summaries</li>
                      <li>Create campaigns for each bill</li>
                    </ul>
                  </div>

                  {/* Results Display */}
                  {batchResults && (
                    <div className="mt-4 p-4 bg-white rounded-lg border">
                      <h5 className="font-medium text-gray-900 mb-2">Processing Results</h5>
                      {batchResults.success ? (
                        <div className="text-green-700">
                          <p>✅ Successfully processed {batchResults.created_bills?.length || 0} bills</p>
                          <p>✅ Created {batchResults.created_campaigns?.length || 0} campaigns</p>
                          {batchResults.created_bills && batchResults.created_bills.length > 0 && (
                            <div className="mt-2">
                              <p className="font-medium">Bills created:</p>
                              <ul className="text-sm list-disc list-inside">
                                {batchResults.created_bills.slice(0, 5).map((bill: string, idx: number) => (
                                  <li key={idx}>{bill}</li>
                                ))}
                                {batchResults.created_bills.length > 5 && (
                                  <li>... and {batchResults.created_bills.length - 5} more</li>
                                )}
                              </ul>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-red-700">
                          <p>❌ Processing failed: {batchResults.error}</p>
                        </div>
                      )}

                      {batchResults.errors && batchResults.errors.length > 0 && (
                        <div className="mt-2">
                          <p className="font-medium text-red-700">Errors:</p>
                          <ul className="text-sm text-red-600 list-disc list-inside">
                            {batchResults.errors.slice(0, 3).map((error: string, idx: number) => (
                              <li key={idx}>{error}</li>
                            ))}
                            {batchResults.errors.length > 3 && (
                              <li>... and {batchResults.errors.length - 3} more errors</li>
                            )}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Values Analysis Review Section */}
            <div className="mt-8 p-6 bg-purple-50 rounded-lg border border-purple-200">
              <h3 className="text-lg font-medium text-purple-900 mb-4">Values Analysis Review</h3>
              <p className="text-sm text-purple-800 mb-4">
                Review AI-flagged bills and approve final tags and scores for the values analysis system.
              </p>
              <a
                href="/admin/review"
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700"
              >
                Open Review Queue
              </a>
            </div>

            {/* Action Tracking Analytics Section */}
            <div className="mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-blue-900">Action Tracking Analytics</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      setShowDetailedView(!showDetailedView);
                      if (!showDetailedView && detailedActions.length === 0) {
                        loadDetailedActions();
                      }
                    }}
                    className="px-3 py-1 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md"
                  >
                    {showDetailedView ? 'Show Analytics' : 'Show All Actions'}
                  </button>
                  <button
                    onClick={showDetailedView ? () => loadDetailedActions() : loadActionAnalytics}
                    disabled={loadingAnalytics || loadingDetails}
                    className={`px-3 py-1 text-sm font-medium text-white rounded-md ${
                      loadingAnalytics || loadingDetails
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {loadingAnalytics || loadingDetails ? 'Loading...' : 'Refresh Data'}
                  </button>
                </div>
              </div>
              
              <p className="text-sm text-blue-800 mb-6">
                View user action data, reasoning patterns, and engagement analytics for bills.
              </p>

              {/* Bill Selection */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-blue-900 mb-2">
                  Select Bill for Analysis
                </label>
                <select
                  value={selectedBillId}
                  onChange={(e) => {
                    setSelectedBillId(e.target.value);
                    // Refresh data when bill selection changes
                    setTimeout(() => loadActionAnalytics(), 100);
                  }}
                  className="block w-full max-w-md px-3 py-2 border border-blue-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="bd9c4dfb-a7b7-406d-ac41-263f36548c50">Affordable Housing Development Act (Test Bill)</option>
                </select>
              </div>

              {/* Overall Statistics */}
              {actionStats && (
                <div className="mb-8 bg-white rounded-lg p-6 shadow-sm">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Overall Platform Statistics</h4>
                  
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-blue-100 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-800">{actionStats.total_actions}</div>
                      <div className="text-sm text-blue-600">Total Actions</div>
                    </div>
                    <div className="bg-green-100 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-800">{actionStats.by_stance?.support || 0}</div>
                      <div className="text-sm text-green-600">Support Actions</div>
                    </div>
                    <div className="bg-purple-100 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-800">{actionStats.recent_actions.length}</div>
                      <div className="text-sm text-purple-600">Recent Activities</div>
                    </div>
                  </div>

                  {/* Recent Actions */}
                  {actionStats.recent_actions.length > 0 && (
                    <div>
                      <h5 className="text-md font-semibold text-gray-800 mb-3">Recent User Actions</h5>
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {actionStats.recent_actions.slice(0, 5).map((action, idx) => (
                          <div key={action.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900">{action.bill_title}</div>
                              <div className="text-xs text-gray-500">
                                {action.user_email} • {action.stance} • {new Date(action.created_at || '').toLocaleDateString()}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                action.stance === 'support' ? 'bg-green-100 text-green-800' :
                                action.stance === 'oppose' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {action.stance}
                              </span>
                              {action.selected_reasons && action.selected_reasons.length > 0 && (
                                <span className="text-xs text-gray-500">
                                  {action.selected_reasons.length} reasons
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Bill-Specific Analytics */}
              {billAnalytics.length > 0 && (
                <div className="space-y-6">
                  {billAnalytics.map((bill, index) => (
                    <div key={bill.bill_id} className="bg-white rounded-lg p-6 shadow-sm">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">{bill.bill_title}</h4>
                      
                      {/* Action Stats */}
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div className="bg-green-100 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-green-800">{bill.stats.support}</div>
                          <div className="text-sm text-green-600">Support Actions</div>
                        </div>
                        <div className="bg-red-100 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-red-800">{bill.stats.oppose}</div>
                          <div className="text-sm text-red-600">Oppose Actions</div>
                        </div>
                        <div className="bg-yellow-100 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-yellow-800">{bill.stats.amend}</div>
                          <div className="text-sm text-yellow-600">Amend Actions</div>
                        </div>
                        <div className="bg-blue-100 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-blue-800">{bill.stats.total}</div>
                          <div className="text-sm text-blue-600">Total Actions</div>
                        </div>
                      </div>

                      {/* Top Reasons */}
                      {bill.top_reasons.length > 0 && (
                        <div className="mb-6">
                          <h5 className="text-md font-semibold text-gray-800 mb-3">Top Reasons (Support)</h5>
                          <div className="space-y-2">
                            {bill.top_reasons.map((reason, idx) => (
                              <div key={idx} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span className="text-sm text-gray-700">{reason.reason_text}</span>
                                <span className="text-sm font-medium text-gray-900">{reason.count} users</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Custom Reasons */}
                      {bill.custom_reasons.length > 0 && (
                        <div>
                          <h5 className="text-md font-semibold text-gray-800 mb-3">Recent Custom Reasons</h5>
                          <div className="space-y-2 max-h-48 overflow-y-auto">
                            {bill.custom_reasons.map((reason, idx) => (
                              <div key={idx} className="p-3 bg-gray-50 rounded-lg">
                                <span className="text-sm text-gray-700 italic">"{reason}"</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* No data message */}
                      {bill.stats.total === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          <p>No action data available for this bill yet.</p>
                          <p className="text-sm mt-2">Actions will appear here once users start engaging with this bill.</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Loading State */}
              {loadingAnalytics && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-blue-800 mt-4">Loading analytics data...</p>
                </div>
              )}

              {/* No data state */}
              {!loadingAnalytics && !showDetailedView && billAnalytics.length === 0 && (
                <div className="text-center py-8 text-blue-800">
                  <p>No analytics data loaded. Click "Refresh Data" to load the latest metrics.</p>
                </div>
              )}

              {/* Detailed Actions View */}
              {showDetailedView && (
                <div className="mt-6">
                  {loadingDetails ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-blue-800 mt-4">Loading detailed actions...</p>
                    </div>
                  ) : (
                    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                      <div className="px-6 py-4 border-b border-gray-200">
                        <h4 className="text-lg font-semibold text-gray-900">All Action Details</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          Complete information for all user actions ({detailedActions.length} actions)
                        </p>
                      </div>
                      
                      {detailedActions.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {detailedActions.map((action) => (
                                <React.Fragment key={action.id}>
                                  <tr className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center">
                                      <span className={`px-2 py-1 text-xs rounded-full ${
                                        action.stance === 'support' ? 'bg-green-100 text-green-800' :
                                        action.stance === 'oppose' ? 'bg-red-100 text-red-800' :
                                        'bg-yellow-100 text-yellow-800'
                                      }`}>
                                        {action.stance}
                                      </span>
                                      <span className="ml-2 text-xs text-gray-500">
                                        {new Date(action.created_at || '').toLocaleDateString()}
                                      </span>
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">{action.user_name}</div>
                                    <div className="text-sm text-gray-500">{action.user_email}</div>
                                    <div className="text-xs text-gray-400">ID: {action.user_id?.substring(0, 8)}...</div>
                                  </td>
                                  <td className="px-6 py-4">
                                    <div className="text-sm font-medium text-gray-900 max-w-xs truncate">{action.bill_title}</div>
                                    <div className="text-xs text-gray-400">{action.bill_id?.substring(0, 8)}...</div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`px-2 py-1 text-xs rounded-full ${
                                      action.status === 'SENT' ? 'bg-green-100 text-green-800' :
                                      action.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                                      action.status === 'FAILED' ? 'bg-red-100 text-red-800' :
                                      'bg-gray-100 text-gray-800'
                                    }`}>
                                      {action.status}
                                    </span>
                                  </td>
                                  <td className="px-6 py-4">
                                    <button
                                      onClick={() => setSelectedActionId(selectedActionId === action.id ? null : action.id)}
                                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                    >
                                      {selectedActionId === action.id ? 'Hide Details' : 'View Details'}
                                    </button>
                                  </td>
                                </tr>
                                {selectedActionId === action.id && (
                                  <tr>
                                    <td colSpan={5} className="px-6 py-4 bg-gray-50">
                                      <div className="space-y-4">
                                        {/* Message Content */}
                                        <div>
                                          <h5 className="font-semibold text-gray-900 mb-2">Message Sent</h5>
                                          <div className="bg-white p-4 rounded-lg border">
                                            <div className="text-sm font-medium text-gray-900 mb-2">Subject: {action.subject}</div>
                                            <div className="text-sm text-gray-700 whitespace-pre-wrap max-h-48 overflow-y-auto">
                                              {action.message}
                                            </div>
                                          </div>
                                        </div>

                                        {/* User Reasoning */}
                                        {(action.selected_reasons.length > 0 || action.custom_reason) && (
                                          <div>
                                            <h5 className="font-semibold text-gray-900 mb-2">User's Reasoning</h5>
                                            <div className="bg-white p-4 rounded-lg border">
                                              {action.selected_reasons.length > 0 && (
                                                <div className="mb-3">
                                                  <div className="text-sm font-medium text-gray-700 mb-1">Selected Reasons:</div>
                                                  <ul className="list-disc list-inside space-y-1">
                                                    {action.selected_reasons.map((reason, idx) => (
                                                      <li key={idx} className="text-sm text-gray-600">{reason.reason_text}</li>
                                                    ))}
                                                  </ul>
                                                </div>
                                              )}
                                              {action.custom_reason && (
                                                <div>
                                                  <div className="text-sm font-medium text-gray-700 mb-1">Custom Reason:</div>
                                                  <div className="text-sm text-gray-600 italic">"{action.custom_reason}"</div>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        )}

                                        {/* Representatives Contacted */}
                                        {action.representatives_contacted.length > 0 && (
                                          <div>
                                            <h5 className="font-semibold text-gray-900 mb-2">Representatives Contacted</h5>
                                            <div className="bg-white p-4 rounded-lg border">
                                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                {action.representatives_contacted.map((rep, idx) => (
                                                  <div key={idx} className="text-sm">
                                                    <span className="font-medium">{rep.name}</span> ({rep.title}, {rep.party})
                                                  </div>
                                                ))}
                                              </div>
                                            </div>
                                          </div>
                                        )}

                                        {/* User Location */}
                                        {action.user_location && Object.keys(action.user_location).length > 0 && (
                                          <div>
                                            <h5 className="font-semibold text-gray-900 mb-2">User Location</h5>
                                            <div className="bg-white p-4 rounded-lg border">
                                              <div className="text-sm text-gray-600">
                                                {action.user_location.address && <div>Address: {action.user_location.address}</div>}
                                                <div>
                                                  {action.user_location.city}, {action.user_location.state} {action.user_location.zip_code}
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        )}

                                        {/* Technical Details */}
                                        <div>
                                          <h5 className="font-semibold text-gray-900 mb-2">Technical Details</h5>
                                          <div className="bg-white p-4 rounded-lg border">
                                            <div className="grid grid-cols-2 gap-4 text-sm">
                                              <div>
                                                <span className="font-medium">Action ID:</span> {action.id}
                                              </div>
                                              <div>
                                                <span className="font-medium">Delivery Method:</span> {action.delivery_method || 'N/A'}
                                              </div>
                                              <div>
                                                <span className="font-medium">Created:</span> {new Date(action.created_at || '').toLocaleString()}
                                              </div>
                                              <div>
                                                <span className="font-medium">Sent:</span> {action.sent_at ? new Date(action.sent_at).toLocaleString() : 'Not sent'}
                                              </div>
                                              {action.error_message && (
                                                <div className="col-span-2">
                                                  <span className="font-medium text-red-600">Error:</span> 
                                                  <span className="text-red-600 ml-1">{action.error_message}</span>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </td>
                                  </tr>
                                )}
                                </React.Fragment>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <p>No detailed actions found.</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Results Display */}
            {results && (
              <div className="mt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Results</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-auto max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="mt-8 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Instructions:</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• <strong>Pull & Process Bill:</strong> Fetches bill data from Congress.gov and generates AI summary</li>
                <li>• <strong>Batch Bill Processing:</strong> Fill your database with recent bills from Congress.gov (proven working flow)</li>
                <li>• <strong>Test Message Preview:</strong> Tests the message preview functionality with sample data</li>
                <li>• <strong>Action Tracking Analytics:</strong> View user engagement data, reasoning patterns, and action statistics</li>
                <li>• <strong>Values Analysis Review:</strong> Review AI-flagged bills and approve tags/scores</li>
                <li>• Use bill IDs like: hr9-118, s1-118, hr1234-118</li>
                <li>• Batch processing fetches the most recent bills and processes them with full AI enhancements</li>
                <li>• Start with 5-10 bills for testing, then increase as needed</li>
                <li>• Check the browser console for detailed error messages</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
