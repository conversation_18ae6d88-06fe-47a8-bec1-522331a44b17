'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

interface BillDetails {
  id: string;
  title: string;
  bill_number: string;
  bill_type: string;
  status: string;
  summary: string;
  tldr: string;
}

interface Analysis {
  id: string;
  democracy_threat_score: number;
  democracy_support_score: number;
  human_rights_threat_score: number;
  human_rights_support_score: number;
  environmental_threat_score: number;
  environmental_support_score: number;
  overall_threat_level: string;
  overall_support_level: string;
  confidence_score: number | null;
  analyzed_at: string;
  reasoning: any;
}

interface Tag {
  id: string;
  category: string;
  type: string;
  display_text: string;
  description: string;
  severity_level: number;
  color_theme: string;
  icon_name: string;
}

export default function BillReviewPage() {
  const params = useParams();
  const router = useRouter();
  const billId = params.billId as string;

  const [bill, setBill] = useState<BillDetails | null>(null);
  const [analysis, setAnalysis] = useState<Analysis | null>(null);
  const [suggestedTags, setSuggestedTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiKey] = useState('admin-dev-key-2024');

  // Form state
  const [finalScores, setFinalScores] = useState({
    democracy_threat: 0,
    democracy_support: 0,
    human_rights_threat: 0,
    human_rights_support: 0,
    environmental_threat: 0,
    environmental_support: 0,
  });
  const [reviewNotes, setReviewNotes] = useState('');
  const [approved, setApproved] = useState(true);

  useEffect(() => {
    if (billId) {
      fetchBillDetails();
      fetchAnalysisDetails();
    }
  }, [billId]);

  const fetchBillDetails = async () => {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/bills/${billId}`);
      if (!response.ok) throw new Error('Failed to fetch bill details');
      
      const result = await response.json();
      setBill(result);
    } catch (error) {
      console.error('Error fetching bill details:', error);
      toast.error('Failed to load bill details');
    }
  };

  const fetchAnalysisDetails = async () => {
    try {
      setIsLoading(true);
      
      // Get analysis details from the review queue endpoint
      const response = await fetch(`http://localhost:8000/api/v1/admin/review-queue?limit=100`, {
        headers: {
          'X-Admin-API-Key': apiKey,
        },
      });

      if (!response.ok) throw new Error('Failed to fetch analysis details');
      
      const result = await response.json();
      const billItem = result.data.items.find((item: any) => item.bill_id === billId);
      
      if (billItem) {
        setAnalysis(billItem.analysis);
        setSuggestedTags(billItem.suggested_tags);
        
        // Initialize form with current scores
        setFinalScores({
          democracy_threat: billItem.analysis.democracy_threat_score,
          democracy_support: billItem.analysis.democracy_support_score,
          human_rights_threat: billItem.analysis.human_rights_threat_score,
          human_rights_support: billItem.analysis.human_rights_support_score,
          environmental_threat: billItem.analysis.environmental_threat_score,
          environmental_support: billItem.analysis.environmental_support_score,
        });
      } else {
        throw new Error('Bill not found in review queue');
      }
    } catch (error) {
      console.error('Error fetching analysis details:', error);
      toast.error('Failed to load analysis details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitReview = async () => {
    try {
      setIsSubmitting(true);

      const reviewDecision = {
        bill_id: billId,
        reviewer_id: 'admin-user', // TODO: Replace with actual user ID
        final_scores: finalScores,
        final_tags: approved ? suggestedTags.map(tag => ({
          category: tag.category,
          type: tag.type,
          name: tag.display_text.toLowerCase().replace(/\s+/g, '_'),
          display_text: tag.display_text,
          description: tag.description,
          severity_level: tag.severity_level,
          display_priority: tag.severity_level,
          color_theme: tag.color_theme,
          icon_name: tag.icon_name
        })) : [],
        review_notes: reviewNotes,
        approved: approved
      };

      const response = await fetch('http://localhost:8000/api/v1/admin/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Admin-API-Key': apiKey,
        },
        body: JSON.stringify(reviewDecision),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('Review submitted successfully!');
        router.push('/admin/review');
      } else {
        throw new Error('Failed to submit review');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 7) return 'border-red-300 bg-red-50';
    if (score >= 4) return 'border-orange-300 bg-orange-50';
    if (score >= 1) return 'border-yellow-300 bg-yellow-50';
    return 'border-gray-300 bg-gray-50';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading bill review...</p>
        </div>
      </div>
    );
  }

  if (!bill || !analysis) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Bill or analysis not found</p>
          <Link
            href="/admin/review"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to Review Queue
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Review Bill Analysis</h1>
              <p className="mt-2 text-gray-600">
                {bill.bill_number} - {bill.title}
              </p>
            </div>
            <Link
              href="/admin/review"
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Back to Queue
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Bill Details */}
          <div className="space-y-6">
            {/* Bill Information */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Bill Information</h2>
              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-500">Number:</span>
                  <span className="ml-2 text-sm text-gray-900">{bill.bill_number}</span>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Type:</span>
                  <span className="ml-2 text-sm text-gray-900">{bill.bill_type.toUpperCase()}</span>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Status:</span>
                  <span className="ml-2 text-sm text-gray-900">{bill.status}</span>
                </div>
              </div>
            </div>

            {/* TL;DR */}
            {bill.tldr && (
              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">TL;DR</h2>
                <p className="text-sm text-gray-700">{bill.tldr}</p>
              </div>
            )}

            {/* Bill Summary */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Summary</h2>
              <p className="text-sm text-gray-700 whitespace-pre-wrap">{bill.summary}</p>
            </div>

            {/* AI Reasoning */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">AI Analysis Reasoning</h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-auto">
                  {JSON.stringify(analysis.reasoning, null, 2)}
                </pre>
              </div>
            </div>
          </div>

          {/* Right Column - Review Form */}
          <div className="space-y-6">
            {/* Current AI Scores */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">AI Analysis Scores</h2>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Democracy:</span>
                  <div className="mt-1">
                    <span className="text-red-600">Threat: {analysis.democracy_threat_score}</span>
                    <span className="ml-4 text-green-600">Support: {analysis.democracy_support_score}</span>
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Human Rights:</span>
                  <div className="mt-1">
                    <span className="text-red-600">Threat: {analysis.human_rights_threat_score}</span>
                    <span className="ml-4 text-green-600">Support: {analysis.human_rights_support_score}</span>
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Environment:</span>
                  <div className="mt-1">
                    <span className="text-red-600">Threat: {analysis.environmental_threat_score}</span>
                    <span className="ml-4 text-green-600">Support: {analysis.environmental_support_score}</span>
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Confidence:</span>
                  <div className="mt-1">
                    <span className="text-gray-600">
                      {analysis.confidence_score ? Math.round(analysis.confidence_score * 100) : 'N/A'}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Suggested Tags */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Suggested Tags</h2>
              <div className="space-y-2">
                {suggestedTags.map((tag) => (
                  <div key={tag.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <span className="font-medium text-gray-900">{tag.display_text}</span>
                      <p className="text-sm text-gray-600">{tag.description}</p>
                    </div>
                    <span className="text-sm text-gray-500">Severity: {tag.severity_level}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Review Form */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Your Review</h2>
              
              {/* Final Scores */}
              <div className="space-y-4 mb-6">
                <h3 className="text-md font-medium text-gray-700">Final Scores (1-10)</h3>
                
                {/* Democracy Scores */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Democracy Threat
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="10"
                      value={finalScores.democracy_threat}
                      onChange={(e) => setFinalScores(prev => ({
                        ...prev,
                        democracy_threat: parseInt(e.target.value) || 0
                      }))}
                      className={`block w-full px-3 py-2 border rounded-md text-sm ${getScoreColor(finalScores.democracy_threat)}`}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Democracy Support
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="10"
                      value={finalScores.democracy_support}
                      onChange={(e) => setFinalScores(prev => ({
                        ...prev,
                        democracy_support: parseInt(e.target.value) || 0
                      }))}
                      className={`block w-full px-3 py-2 border rounded-md text-sm ${getScoreColor(finalScores.democracy_support)}`}
                    />
                  </div>
                </div>

                {/* Human Rights Scores */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Human Rights Threat
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="10"
                      value={finalScores.human_rights_threat}
                      onChange={(e) => setFinalScores(prev => ({
                        ...prev,
                        human_rights_threat: parseInt(e.target.value) || 0
                      }))}
                      className={`block w-full px-3 py-2 border rounded-md text-sm ${getScoreColor(finalScores.human_rights_threat)}`}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Human Rights Support
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="10"
                      value={finalScores.human_rights_support}
                      onChange={(e) => setFinalScores(prev => ({
                        ...prev,
                        human_rights_support: parseInt(e.target.value) || 0
                      }))}
                      className={`block w-full px-3 py-2 border rounded-md text-sm ${getScoreColor(finalScores.human_rights_support)}`}
                    />
                  </div>
                </div>

                {/* Environmental Scores */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Environmental Threat
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="10"
                      value={finalScores.environmental_threat}
                      onChange={(e) => setFinalScores(prev => ({
                        ...prev,
                        environmental_threat: parseInt(e.target.value) || 0
                      }))}
                      className={`block w-full px-3 py-2 border rounded-md text-sm ${getScoreColor(finalScores.environmental_threat)}`}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Environmental Support
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="10"
                      value={finalScores.environmental_support}
                      onChange={(e) => setFinalScores(prev => ({
                        ...prev,
                        environmental_support: parseInt(e.target.value) || 0
                      }))}
                      className={`block w-full px-3 py-2 border rounded-md text-sm ${getScoreColor(finalScores.environmental_support)}`}
                    />
                  </div>
                </div>
              </div>

              {/* Approval Decision */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Review Decision
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="approved"
                      checked={approved}
                      onChange={() => setApproved(true)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Approve analysis and tags</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="approved"
                      checked={!approved}
                      onChange={() => setApproved(false)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Reject analysis (flag for further review)</span>
                  </label>
                </div>
              </div>

              {/* Review Notes */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Review Notes
                </label>
                <textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  rows={4}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Add any notes about your review decision..."
                />
              </div>

              {/* Submit Button */}
              <button
                onClick={handleSubmitReview}
                disabled={isSubmitting}
                className={`w-full px-4 py-2 text-sm font-medium text-white rounded-md ${
                  isSubmitting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isSubmitting ? 'Submitting Review...' : 'Submit Review'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
