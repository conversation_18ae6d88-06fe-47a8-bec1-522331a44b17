'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

interface ReviewItem {
  bill_id: string;
  bill_title: string;
  bill_number: string;
  bill_type: string;
  bill_status: string;
  analysis: {
    id: string;
    democracy_threat_score: number;
    democracy_support_score: number;
    human_rights_threat_score: number;
    human_rights_support_score: number;
    environmental_threat_score: number;
    environmental_support_score: number;
    overall_threat_level: string;
    overall_support_level: string;
    confidence_score: number | null;
    analyzed_at: string;
    ai_model_version: string;
    reasoning: any;
  };
  suggested_tags: Array<{
    id: string;
    category: string;
    type: string;
    display_text: string;
    description: string;
    severity_level: number;
    color_theme: string;
    icon_name: string;
  }>;
  flagged_reason: string;
}

interface ReviewStats {
  pending_reviews: number;
  completed_reviews: number;
  total_analyses: number;
  flagged_bills: number;
  review_completion_rate: number;
  threat_level_distribution: Record<string, number>;
}

export default function AdminReviewPage() {
  const [reviewItems, setReviewItems] = useState<ReviewItem[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [apiKey, setApiKey] = useState('admin-dev-key-2024'); // Default dev key

  useEffect(() => {
    fetchReviewQueue();
    fetchStats();
  }, []);

  const fetchReviewQueue = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('http://localhost:8000/api/v1/admin/review-queue', {
        headers: {
          'X-Admin-API-Key': apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setReviewItems(result.data.items);
      } else {
        throw new Error('Failed to fetch review queue');
      }
    } catch (error) {
      console.error('Error fetching review queue:', error);
      setError('Failed to load review queue');
      toast.error('Failed to load review queue');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/admin/review-stats', {
        headers: {
          'X-Admin-API-Key': apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setStats(result.data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 7) return 'text-red-600 bg-red-50';
    if (score >= 4) return 'text-orange-600 bg-orange-50';
    if (score >= 1) return 'text-yellow-600 bg-yellow-50';
    return 'text-gray-600 bg-gray-50';
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-700 bg-red-100';
      case 'high': return 'text-orange-700 bg-orange-100';
      case 'medium': return 'text-yellow-700 bg-yellow-100';
      case 'low': return 'text-blue-700 bg-blue-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading review queue...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => {
              setError(null);
              fetchReviewQueue();
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Values Analysis Review Queue</h1>
              <p className="mt-2 text-gray-600">
                Review AI-flagged bills and approve final tags and scores
              </p>
            </div>
            <div className="flex space-x-4">
              <Link
                href="/admin"
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Back to Admin
              </Link>
              <button
                onClick={() => {
                  fetchReviewQueue();
                  fetchStats();
                }}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <span className="text-orange-600 font-semibold">!</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Pending Reviews</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.pending_reviews}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-semibold">✓</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Completed</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.completed_reviews}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold">%</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Completion Rate</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.review_completion_rate}%</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <span className="text-red-600 font-semibold">🚩</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Flagged Bills</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.flagged_bills}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Review Queue */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Bills Requiring Review ({reviewItems.length})
            </h2>
          </div>

          {reviewItems.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-gray-500">No bills currently require review.</p>
              <p className="text-sm text-gray-400 mt-2">
                All AI analysis has been reviewed or no bills have been flagged.
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {reviewItems.map((item) => (
                <div key={item.bill_id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* Bill Info */}
                      <div className="mb-4">
                        <h3 className="text-lg font-medium text-gray-900">
                          {item.bill_number} - {item.bill_title}
                        </h3>
                        <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                          <span>Type: {item.bill_type.toUpperCase()}</span>
                          <span>Status: {item.bill_status}</span>
                          <span>Flagged: {item.flagged_reason}</span>
                        </div>
                      </div>

                      {/* Scores Grid */}
                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Democracy</p>
                          <div className="mt-1 flex justify-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getScoreColor(item.analysis.democracy_threat_score)}`}>
                              T: {item.analysis.democracy_threat_score}
                            </span>
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getScoreColor(item.analysis.democracy_support_score)}`}>
                              S: {item.analysis.democracy_support_score}
                            </span>
                          </div>
                        </div>

                        <div className="text-center">
                          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Human Rights</p>
                          <div className="mt-1 flex justify-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getScoreColor(item.analysis.human_rights_threat_score)}`}>
                              T: {item.analysis.human_rights_threat_score}
                            </span>
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getScoreColor(item.analysis.human_rights_support_score)}`}>
                              S: {item.analysis.human_rights_support_score}
                            </span>
                          </div>
                        </div>

                        <div className="text-center">
                          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Environment</p>
                          <div className="mt-1 flex justify-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getScoreColor(item.analysis.environmental_threat_score)}`}>
                              T: {item.analysis.environmental_threat_score}
                            </span>
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getScoreColor(item.analysis.environmental_support_score)}`}>
                              S: {item.analysis.environmental_support_score}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Overall Levels */}
                      <div className="flex items-center space-x-4 mb-4">
                        <span className={`px-3 py-1 text-sm font-medium rounded-full ${getLevelColor(item.analysis.overall_threat_level)}`}>
                          Threat: {item.analysis.overall_threat_level}
                        </span>
                        <span className={`px-3 py-1 text-sm font-medium rounded-full ${getLevelColor(item.analysis.overall_support_level)}`}>
                          Support: {item.analysis.overall_support_level}
                        </span>
                        {item.analysis.confidence_score && (
                          <span className="px-3 py-1 text-sm font-medium text-gray-600 bg-gray-100 rounded-full">
                            Confidence: {Math.round(item.analysis.confidence_score * 100)}%
                          </span>
                        )}
                      </div>

                      {/* Suggested Tags */}
                      {item.suggested_tags.length > 0 && (
                        <div className="mb-4">
                          <p className="text-sm font-medium text-gray-700 mb-2">Suggested Tags:</p>
                          <div className="flex flex-wrap gap-2">
                            {item.suggested_tags.map((tag) => (
                              <span
                                key={tag.id}
                                className={`px-2 py-1 text-xs font-medium rounded-full bg-${tag.color_theme}-100 text-${tag.color_theme}-800`}
                              >
                                {tag.display_text}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Action Button */}
                    <div className="ml-6 flex-shrink-0">
                      <Link
                        href={`/admin/review/${item.bill_id}`}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        Review Bill
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
