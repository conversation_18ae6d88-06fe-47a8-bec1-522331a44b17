'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Bill, BillStatus } from '../../types';
import { BillActionModal } from '../../components/shared';
import { EnhancedBillCard, FilterBar } from '../../components/bills';
import { billApi } from '../../services/apiClient';
import toast from 'react-hot-toast';

const BillsPage: React.FC = () => {
  const [bills, setBills] = useState<Bill[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<BillStatus | 'all'>('all');
  const [urgencyFilter, setUrgencyFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [sortBy, setSortBy] = useState<'priority' | 'date' | 'title' | 'action_count'>('priority');

  useEffect(() => {
    loadBills();
  }, []);

  const loadBills = async () => {
    try {
      setIsLoading(true);
      console.log('Loading bills from API...');

      // Load real bills from the API using the simple endpoint
      const billsData = await billApi.getBillsSimple({ limit: 50 });
      console.log('Bills data received:', billsData);

      // Transform the simple bill data to match the Bill interface
      const transformedBills = billsData.map((bill: any) => ({
        ...bill,
        bill_type: bill.chamber === 'house' ? 'house_bill' : 'senate_bill',
        session_year: 2024, // Default for now
        state: 'federal',
        description: bill.ai_summary || bill.title,
        summary: bill.ai_summary || '',
        simple_summary: bill.ai_summary || '',
        tldr: bill.ai_summary || '',
        sponsor_name: '',
        sponsor_party: '',
        sponsor_state: '',
        introduced_date: bill.created_at,
        last_action_date: bill.created_at,
        tags: [],
        categories: [],
        reasons_for_support: [],
        reasons_for_opposition: [],
        cosponsors: [],
        vote_history: []
      }));

      console.log('Transformed bills:', transformedBills);
      setBills(transformedBills);
    } catch (error) {
      console.error('Failed to load bills:', error);
      toast.error('Failed to load bills. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTakeAction = (bill: Bill) => {
    setSelectedBill(bill);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedBill(null);
  };

  const handleActionSubmit = (result: any) => {
    console.log('Action submitted:', result);
    // Could update UI to show action was taken
  };

  // Filter and sort bills
  const filteredAndSortedBills = useMemo(() => {
    let filtered = bills.filter(bill => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch =
          bill.title.toLowerCase().includes(query) ||
          bill.bill_number.toLowerCase().includes(query) ||
          bill.summary?.toLowerCase().includes(query) ||
          bill.summary_what_does?.content?.toLowerCase().includes(query);
        if (!matchesSearch) return false;
      }

      // Status filter
      if (statusFilter !== 'all' && bill.status !== statusFilter) {
        return false;
      }

      // Urgency filter (mock logic based on status)
      if (urgencyFilter !== 'all') {
        const billUrgency = bill.status === 'floor' ? 'high' :
                           bill.status === 'committee' ? 'medium' : 'low';
        if (billUrgency !== urgencyFilter) return false;
      }

      return true;
    });

    // Sort bills
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'action_count':
          // Mock action count based on priority score
          return b.priority_score - a.priority_score;
        case 'priority':
        default:
          return b.priority_score - a.priority_score;
      }
    });

    return filtered;
  }, [bills, searchQuery, statusFilter, urgencyFilter, sortBy]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span className="text-lg text-gray-600">Loading bills...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-4xl font-bold mb-2">Federal Bills</h1>
          <p className="text-blue-100 text-lg mb-6">Take action on legislation that matters to you</p>

          {/* Stats Row */}
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                {filteredAndSortedBills.length} of {bills.length} bills shown
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="bg-red-500 bg-opacity-80 px-3 py-1 rounded-full">
                {bills.filter(b => b.status === 'floor').length} urgent votes
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Filter Bar */}
        <FilterBar
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          statusFilter={statusFilter}
          onStatusFilterChange={setStatusFilter}
          urgencyFilter={urgencyFilter}
          onUrgencyFilterChange={setUrgencyFilter}
          sortBy={sortBy}
          onSortChange={setSortBy}
          totalCount={bills.length}
          filteredCount={filteredAndSortedBills.length}
        />

        {/* Bills Grid */}
        {bills.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No bills available at the moment.</p>
            <button
              onClick={loadBills}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Refresh
            </button>
          </div>
        ) : filteredAndSortedBills.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No bills match your current filters.</p>
            <button
              onClick={() => {
                setSearchQuery('');
                setStatusFilter('all');
                setUrgencyFilter('all');
              }}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Clear Filters
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 items-start">
            {filteredAndSortedBills.map((bill) => (
              <EnhancedBillCard
                key={bill.id}
                bill={bill}
                onTakeAction={handleTakeAction}
                usePageNavigation={true}
              />
            ))}
          </div>
        )}

        {/* Action Modal */}
        {selectedBill && (
          <BillActionModal
            isOpen={isModalOpen}
            onClose={handleModalClose}
            bill={selectedBill}
            onSubmit={handleActionSubmit}
          />
        )}


      </div>
    </div>
  );
};

export default BillsPage;
