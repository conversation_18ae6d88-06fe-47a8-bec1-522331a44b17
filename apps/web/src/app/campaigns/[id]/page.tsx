// Campaign detail page - displays full campaign information with modular components
import CampaignPageClient from './CampaignPageClient';

// Generate static params for build-time generation
export async function generateStaticParams() {
  // For emergency deployment, return empty array to skip pre-generation
  // In production, you would fetch all campaign IDs here
  return [];
}

export default function CampaignPage() {
  return <CampaignPageClient />;
}

// Note: Metadata generation removed for static export compatibility
// In a full implementation, you might use next/head or dynamic metadata
