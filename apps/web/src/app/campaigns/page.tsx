// Campaigns listing page - displays all campaigns with search and filtering
import Link from 'next/link';
import { campaignApi } from '../../services/apiClient';
import { Campaign, CampaignStatus, CampaignType } from '../../types';
import AuthNavigation from '../../components/auth/AuthNavigation';

interface CampaignsPageProps {
  searchParams: Promise<{
    status?: string;
    type?: string;
    featured?: string;
    page?: string;
  }>;
}

// Server-side data fetching for campaigns
async function getCampaigns(resolvedSearchParams: Awaited<CampaignsPageProps['searchParams']>): Promise<Campaign[]> {
  try {
    const params = {
      status: resolvedSearchParams.status as CampaignStatus,
      campaign_type: resolvedSearchParams.type as CampaignType,
      is_featured: resolvedSearchParams.featured === 'true' ? true : undefined,
      skip: resolvedSearchParams.page ? (parseInt(resolvedSearchParams.page) - 1) * 20 : 0,
      limit: 20
    };

    // Remove undefined values
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(([, value]) => value !== undefined)
    );

    if (Object.keys(cleanParams).length > 2) { // More than just skip/limit
      return await campaignApi.searchCampaigns(cleanParams);
    } else {
      return await campaignApi.getCampaigns(cleanParams);
    }
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    return [];
  }
}

export default async function CampaignsPage({ searchParams }: CampaignsPageProps) {
  const resolvedSearchParams = await searchParams;
  const campaigns = await getCampaigns(resolvedSearchParams);

  const getStatusColor = (status: CampaignStatus): string => {
    switch (status) {
      case CampaignStatus.ACTIVE:
        return 'bg-green-100 text-green-800';
      case CampaignStatus.DRAFT:
        return 'bg-gray-100 text-gray-800';
      case CampaignStatus.PAUSED:
        return 'bg-yellow-100 text-yellow-800';
      case CampaignStatus.COMPLETED:
        return 'bg-blue-100 text-blue-800';
      case CampaignStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: CampaignType): string => {
    switch (type) {
      case CampaignType.SUPPORT:
        return 'bg-green-100 text-green-800';
      case CampaignType.OPPOSE:
        return 'bg-red-100 text-red-800';
      case CampaignType.NEUTRAL:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-indigo-600">
                ModernAction
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/campaigns" className="text-indigo-600 hover:text-indigo-700 px-3 py-2 rounded-md text-sm font-medium">
                Campaigns
              </Link>
              <Link href="/about" className="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">
                About
              </Link>
              <AuthNavigation />
            </div>
          </div>
        </div>
      </nav>

      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h1 className="text-3xl font-bold text-gray-900">
                Active Campaigns
              </h1>
              <p className="mt-2 text-lg text-gray-600">
                Take action on important legislation affecting your community
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex flex-wrap gap-4">
            <Link
              href="/campaigns"
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                !resolvedSearchParams.status && !resolvedSearchParams.type && !resolvedSearchParams.featured
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All Campaigns
            </Link>
            
            <Link
              href="/campaigns?featured=true"
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                resolvedSearchParams.featured === 'true'
                  ? 'bg-purple-100 text-purple-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Featured
            </Link>
            
            <Link
              href="/campaigns?status=active"
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                resolvedSearchParams.status === 'active'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Active
            </Link>
            
            <Link
              href="/campaigns?type=support"
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                resolvedSearchParams.type === 'support'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Support
            </Link>
            
            <Link
              href="/campaigns?type=oppose"
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                resolvedSearchParams.type === 'oppose'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Oppose
            </Link>
          </div>
        </div>
      </div>

      {/* Campaigns Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        {campaigns.length === 0 ? (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No campaigns found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your filters or check back later for new campaigns.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {campaigns.map((campaign) => (
              <Link
                key={campaign.id}
                href={`/campaigns/${campaign.id}`}
                className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow overflow-hidden"
              >
                <div className="p-6">
                  {/* Badges */}
                  <div className="flex flex-wrap gap-2 mb-3">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`}>
                      {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                    </span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(campaign.campaign_type)}`}>
                      {campaign.campaign_type.charAt(0).toUpperCase() + campaign.campaign_type.slice(1)}
                    </span>
                    {campaign.is_featured && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        Featured
                      </span>
                    )}
                  </div>

                  {/* Title */}
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                    {campaign.title}
                  </h3>

                  {/* Description */}
                  {campaign.description && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {campaign.description}
                    </p>
                  )}

                  {/* Bill Info */}
                  <div className="mb-4 p-3 bg-gray-50 rounded-md">
                    <p className="text-sm font-medium text-gray-900 mb-1">
                      Related Bill: {campaign.bill.bill_number}
                    </p>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {campaign.bill.title}
                    </p>
                  </div>

                  {/* Progress */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-gray-700">Progress</span>
                      <span className="text-sm font-medium text-gray-900">
                        {Math.round(campaign.completion_percentage)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min(campaign.completion_percentage, 100)}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {(campaign.actual_actions || 0).toLocaleString()} of {(campaign.goal_actions || 0).toLocaleString()} actions
                    </p>
                  </div>

                  {/* Call to Action */}
                  <div className="text-center">
                    <span className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                      Take Action
                      <svg className="ml-2 -mr-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Generate metadata for the page
export async function generateMetadata({ searchParams }: CampaignsPageProps) {
  const resolvedSearchParams = await searchParams;
  let title = 'Campaigns | ModernAction.io';
  let description = 'Take action on important legislation affecting your community';

  if (resolvedSearchParams.featured === 'true') {
    title = 'Featured Campaigns | ModernAction.io';
    description = 'Featured campaigns for important legislative action';
  } else if (resolvedSearchParams.status) {
    title = `${resolvedSearchParams.status.charAt(0).toUpperCase() + resolvedSearchParams.status.slice(1)} Campaigns | ModernAction.io`;
  } else if (resolvedSearchParams.type) {
    title = `${resolvedSearchParams.type.charAt(0).toUpperCase() + resolvedSearchParams.type.slice(1)} Campaigns | ModernAction.io`;
  }

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    }
  };
}
