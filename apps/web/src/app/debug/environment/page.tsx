/**
 * Debug page to check environment variable availability
 * This helps diagnose Auth0 configuration issues
 */

export default function DebugEnv() {
  // Check server-side environment variables
  const serverEnvVars = {
    AUTH0_SECRET: process.env.AUTH0_SECRET ? '***SET***' : 'MISSING',
    AUTH0_BASE_URL: process.env.AUTH0_BASE_URL || 'MISSING',
    AUTH0_ISSUER_BASE_URL: process.env.AUTH0_ISSUER_BASE_URL || 'MISSING',
    AUTH0_CLIENT_ID: process.env.AUTH0_CLIENT_ID || 'MISSING',
    AUTH0_CLIENT_SECRET: process.env.AUTH0_CLIENT_SECRET ? '***SET***' : 'MISSING',
    AUTH0_AUDIENCE: process.env.AUTH0_AUDIENCE || 'MISSING',
    NODE_ENV: process.env.NODE_ENV || 'MISSING',
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'MISSING',
    INTERNAL_API_URL: process.env.INTERNAL_API_URL || 'MISSING',
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Environment Variables Debug
        </h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Server-Side Environment Variables
          </h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
            {JSON.stringify(serverEnvVars, null, 2)}
          </pre>
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Auth0 Configuration Status
          </h2>
          <div className="space-y-2">
            {Object.entries(serverEnvVars).map(([key, value]) => {
              if (!key.startsWith('AUTH0_')) return null;
              
              const isSet = value !== 'MISSING';
              return (
                <div key={key} className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${isSet ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="font-mono text-sm">{key}</span>
                  <span className={`text-sm ${isSet ? 'text-green-600' : 'text-red-600'}`}>
                    {isSet ? '✓ Set' : '✗ Missing'}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Diagnostic Information
          </h2>
          <div className="space-y-2 text-sm">
            <p><strong>Timestamp:</strong> {new Date().toISOString()}</p>
            <p><strong>Node.js Version:</strong> {process.version}</p>
            <p><strong>Platform:</strong> {process.platform}</p>
            <p><strong>Architecture:</strong> {process.arch}</p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-yellow-800 text-sm">
            <strong>Note:</strong> This page shows server-side environment variables only. 
            Client-side variables (NEXT_PUBLIC_*) would be visible in the browser&apos;s developer console.
          </p>
        </div>
      </div>
    </div>
  );
}
