@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Modern Civic Color Palette */
  --civic-primary: #2563EB;      /* Trustworthy blue */
  --civic-secondary: #7C3AED;    /* Civic purple */
  --civic-success: #10B981;      /* Success green */
  --civic-warning: #F59E0B;      /* Warning amber */
  --civic-error: #EF4444;        /* Error red */
  --civic-neutral: #6B7280;      /* Neutral gray */
  --civic-background: #F9FAFB;   /* Light background */
  
  /* Legacy variables for compatibility */
  --background: #ffffff;
  --foreground: #171717;
  
  /* Typography Scale */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  
  /* Spacing Scale */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  
  /* Border Radius */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --civic-background: #111827;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Classes */
.heading-1 {
  font-size: var(--text-2xl);
  line-height: 2rem;
  font-weight: 600;
}

.heading-2 {
  font-size: var(--text-xl);
  line-height: 1.75rem;
  font-weight: 600;
}

.heading-3 {
  font-size: var(--text-lg);
  line-height: 1.5rem;
  font-weight: 600;
}

.body-base {
  font-size: var(--text-base);
  line-height: 1.5rem;
  font-weight: 400;
}

.body-sm {
  font-size: var(--text-sm);
  line-height: 1.25rem;
  font-weight: 400;
}

.text-small {
  font-size: var(--text-xs);
  line-height: 1rem;
  font-weight: 400;
}

/* Modern Card Components */
.civic-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid #E5E7EB;
  transition: all 0.2s ease-in-out;
}

.civic-card:hover {
  box-shadow: var(--shadow-md);
  border-color: #D1D5DB;
}

.civic-card-selected {
  border-color: var(--civic-primary);
  box-shadow: 0 0 0 1px var(--civic-primary), var(--shadow-sm);
  background: rgb(37 99 235 / 0.02);
}

/* Button Components */
.btn-primary {
  background: var(--civic-primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-md);
  font-weight: 500;
  font-size: var(--text-sm);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.btn-primary:hover:not(:disabled) {
  background: #1D4ED8;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:disabled {
  background: #9CA3AF;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #F3F4F6;
  color: #374151;
  border: 1px solid #D1D5DB;
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-md);
  font-weight: 500;
  font-size: var(--text-sm);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.btn-secondary:hover:not(:disabled) {
  background: #E5E7EB;
  border-color: #9CA3AF;
}

/* Focus Styles for Accessibility */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid var(--civic-primary);
  outline-offset: 2px;
}

/* Form Input Styles */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-xl transition-all duration-200;
  @apply text-gray-900 placeholder-gray-500;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply bg-white;
}

.form-input:disabled {
  @apply bg-gray-50 text-gray-500 cursor-not-allowed;
}

.form-textarea {
  @apply w-full px-4 py-3 border border-gray-300 rounded-xl resize-none transition-all duration-200;
  @apply text-gray-900 placeholder-gray-500;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply bg-white;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-error {
  @apply mt-1 text-sm text-red-600;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(4px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-4px); }
  to { opacity: 1; transform: translateX(0); }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.2s ease-out;
}
