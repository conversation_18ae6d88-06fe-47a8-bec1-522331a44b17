'use client';

import { useState, useEffect } from 'react';
import { billApi } from '../../services/apiClient';

export default function TestApiPage() {
  const [apiConfig, setApiConfig] = useState<any>({});
  const [testResult, setTestResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Show API configuration
    setApiConfig({
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
      isClient: typeof window !== 'undefined',
      currentUrl: typeof window !== 'undefined' ? window.location.origin : 'server'
    });

    // Test API call
    const testApi = async () => {
      try {
        console.log('Testing API call...');
        const bills = await billApi.getBillsSimple({ limit: 3 });
        console.log('API response:', bills);
        setTestResult(bills);
      } catch (err: any) {
        console.error('API error:', err);
        setError(err.message || 'Unknown error');
      }
    };

    testApi();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">API Test Page</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">API Configuration</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(apiConfig, null, 2)}
          </pre>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <strong>Error:</strong> {error}
          </div>
        )}

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">API Test Result</h2>
          {testResult ? (
            <div>
              <p className="text-green-600 mb-4">✅ API call successful! Found {testResult.length} bills.</p>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(testResult, null, 2)}
              </pre>
            </div>
          ) : (
            <p className="text-gray-600">Loading...</p>
          )}
        </div>
      </div>
    </div>
  );
}
