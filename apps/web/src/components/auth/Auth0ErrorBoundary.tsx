'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class Auth0ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null 
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true, 
      error,
      errorInfo: null 
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to console for debugging
    console.error('Auth0ErrorBoundary caught an error:', error, errorInfo);
    
    // Update state with error info
    this.setState({
      error,
      errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-red-50">
          <div className="max-w-2xl mx-auto text-center p-8">
            <div className="mb-6">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            
            <h1 className="text-3xl font-bold text-red-600 mb-4">
              Authentication Configuration Error
            </h1>
            
            <p className="text-red-700 mb-6 text-lg">
              The application cannot start due to missing or invalid Auth0 configuration.
            </p>

            <div className="bg-red-100 border border-red-200 rounded-lg p-4 mb-6 text-left">
              <h3 className="font-semibold text-red-800 mb-2">Error Details:</h3>
              <pre className="text-sm text-red-700 whitespace-pre-wrap overflow-x-auto">
                {this.state.error?.message || 'Unknown error'}
              </pre>
              
              {this.state.errorInfo && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-red-800 font-medium">
                    Stack Trace (Click to expand)
                  </summary>
                  <pre className="text-xs text-red-600 mt-2 whitespace-pre-wrap overflow-x-auto">
                    {this.state.errorInfo.componentStack}
                  </pre>
                </details>
              )}
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 text-left">
              <h3 className="font-semibold text-yellow-800 mb-2">Troubleshooting Steps:</h3>
              <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
                <li>Check that all Auth0 environment variables are properly set</li>
                <li>Verify the Auth0 application configuration in the Auth0 dashboard</li>
                <li>Ensure the Auth0 domain and client ID are correct</li>
                <li>Check that the Auth0 client secret is properly configured</li>
                <li>Visit <a href="/debug/environment" className="underline text-blue-600">/debug/environment</a> to check environment variables</li>
              </ol>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
              
              <a
                href="/debug/environment"
                className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors inline-block"
              >
                Debug Environment
              </a>
            </div>

            <div className="mt-8 text-xs text-gray-500">
              <p>Error occurred at: {new Date().toISOString()}</p>
              <p>If this error persists, please contact the development team.</p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default Auth0ErrorBoundary;
