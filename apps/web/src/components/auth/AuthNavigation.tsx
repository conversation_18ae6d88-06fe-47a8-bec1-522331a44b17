'use client';

import React from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import LoginButton from './LoginButton';
import UserProfile from './UserProfile';

const AuthNavigation: React.FC = () => {
  const { user, isLoading } = useUser();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-4">
        <div className="animate-pulse bg-gray-200 h-8 w-20 rounded"></div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-4">
      {user ? <UserProfile /> : <LoginButton />}
    </div>
  );
};

export default AuthNavigation;
