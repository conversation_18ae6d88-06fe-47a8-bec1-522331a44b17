'use client';

import React from 'react';
import Link from 'next/link';

interface LoginButtonProps {
  className?: string;
  children?: React.ReactNode;
}

const LoginButton: React.FC<LoginButtonProps> = ({
  className = "bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",
  children = "Log In"
}) => {
  return (
    <Link
      href="/api/auth/login"
      className={className}
    >
      {children}
    </Link>
  );
};

export default LoginButton;
