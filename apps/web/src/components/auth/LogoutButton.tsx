'use client';

import React from 'react';
import Link from 'next/link';

interface LogoutButtonProps {
  className?: string;
  children?: React.ReactNode;
}

const LogoutButton: React.FC<LogoutButtonProps> = ({
  className = "bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",
  children = "Log Out"
}) => {
  return (
    <Link
      href="/api/auth/logout"
      className={className}
    >
      {children}
    </Link>
  );
};

export default LogoutButton;
