'use client';

import React from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import LogoutButton from './LogoutButton';

const UserProfile: React.FC = () => {
  const { user, isLoading } = useUser();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
        <span className="text-sm text-gray-600">Loading...</span>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="flex items-center space-x-4">
      <div className="flex items-center space-x-2">
        {user.picture && (
          /* eslint-disable-next-line @next/next/no-img-element */
          <img
            src={user.picture}
            alt={user.name || 'User'}
            className="h-8 w-8 rounded-full"
          />
        )}
        <span className="text-sm font-medium text-gray-700">
          {user.name || user.email}
        </span>
      </div>
      <LogoutButton />
    </div>
  );
};

export default UserProfile;
