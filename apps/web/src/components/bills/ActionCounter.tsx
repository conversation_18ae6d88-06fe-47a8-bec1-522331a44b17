import React from 'react';

interface ActionCounterProps {
  count?: number;
  trend?: number; // Percentage change
}

export const ActionCounter: React.FC<ActionCounterProps> = ({ count = 0, trend }) => {
  return (
    <div className="text-right">
      <div className="text-2xl font-bold text-blue-600">{count.toLocaleString()}</div>
      <div className="text-xs text-gray-500">actions taken</div>
      {trend && (
        <div className={`text-xs ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
          {trend > 0 ? '↗' : '↘'} {Math.abs(trend)}% this week
        </div>
      )}
    </div>
  );
};
