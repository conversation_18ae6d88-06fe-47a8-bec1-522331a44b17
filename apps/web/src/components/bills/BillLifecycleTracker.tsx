import React from 'react';
import { BillStatus } from '../../types';

interface BillLifecycleStep {
  key: BillStatus;
  label: string;
  description: string;
  icon: string;
  color: {
    bg: string;
    text: string;
    border: string;
    line: string;
  };
}

interface BillLifecycleTrackerProps {
  currentStatus: BillStatus;
  statusHistory?: Array<{
    status: BillStatus;
    date: string;
    notes?: string;
  }>;
  className?: string;
}

const LIFECYCLE_STEPS: BillLifecycleStep[] = [
  {
    key: 'introduced',
    label: 'Introduced',
    description: 'Bill introduced in chamber',
    icon: '📋',
    color: {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      border: 'border-blue-300',
      line: 'bg-blue-300'
    }
  },
  {
    key: 'committee',
    label: 'In Committee',
    description: 'Under committee review',
    icon: '🏛️',
    color: {
      bg: 'bg-yellow-100',
      text: 'text-yellow-800',
      border: 'border-yellow-300',
      line: 'bg-yellow-300'
    }
  },
  {
    key: 'floor',
    label: 'Floor Vote',
    description: 'Scheduled for floor vote',
    icon: '🗳️',
    color: {
      bg: 'bg-orange-100',
      text: 'text-orange-800',
      border: 'border-orange-300',
      line: 'bg-orange-300'
    }
  },
  {
    key: 'passed',
    label: 'Passed',
    description: 'Passed chamber vote',
    icon: '✅',
    color: {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-300',
      line: 'bg-green-300'
    }
  },
  {
    key: 'signed',
    label: 'Signed',
    description: 'Signed into law',
    icon: '🏛️',
    color: {
      bg: 'bg-emerald-100',
      text: 'text-emerald-800',
      border: 'border-emerald-300',
      line: 'bg-emerald-300'
    }
  }
];

// Handle alternative final states
const ALTERNATIVE_FINAL_STEPS: Record<BillStatus, BillLifecycleStep> = {
  'vetoed': {
    key: 'vetoed',
    label: 'Vetoed',
    description: 'Vetoed by executive',
    icon: '❌',
    color: {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-300',
      line: 'bg-red-300'
    }
  },
  'failed': {
    key: 'failed',
    label: 'Failed',
    description: 'Failed to pass',
    icon: '💔',
    color: {
      bg: 'bg-gray-100',
      text: 'text-gray-800',
      border: 'border-gray-300',
      line: 'bg-gray-300'
    }
  }
} as any;

export const BillLifecycleTracker: React.FC<BillLifecycleTrackerProps> = ({
  currentStatus,
  statusHistory = [],
  className = ''
}) => {
  // Determine which steps to show
  const getStepsToShow = (): BillLifecycleStep[] => {
    let steps = [...LIFECYCLE_STEPS];
    
    // If the bill was vetoed or failed, replace the final step
    if (currentStatus === 'vetoed' || currentStatus === 'failed') {
      steps = steps.slice(0, -1); // Remove 'signed'
      steps.push(ALTERNATIVE_FINAL_STEPS[currentStatus]);
    }
    
    return steps;
  };

  const steps = getStepsToShow();
  
  // Determine step states
  const getStepState = (step: BillLifecycleStep, index: number) => {
    const currentIndex = steps.findIndex(s => s.key === currentStatus);
    
    if (step.key === currentStatus) {
      return 'current';
    } else if (index < currentIndex) {
      return 'completed';
    } else {
      return 'upcoming';
    }
  };

  // Get date for a specific status from history
  const getStatusDate = (status: BillStatus): string | null => {
    const historyItem = statusHistory.find(h => h.status === status);
    return historyItem ? new Date(historyItem.date).toLocaleDateString() : null;
  };

  return (
    <div className={`bg-white rounded-xl border border-gray-200 p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Legislative Progress</h3>
        <p className="text-sm text-gray-600">Track this bill's journey through the legislative process</p>
      </div>

      <div className="relative">
        {/* Timeline line */}
        <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-200" />
        
        {/* Steps */}
        <div className="relative flex justify-between">
          {steps.map((step, index) => {
            const state = getStepState(step, index);
            const statusDate = getStatusDate(step.key);
            
            return (
              <div key={step.key} className="flex flex-col items-center relative">
                {/* Step circle */}
                <div className={`
                  relative z-10 w-12 h-12 rounded-full border-2 flex items-center justify-center
                  transition-all duration-300 shadow-sm
                  ${state === 'current' 
                    ? `${step.color.bg} ${step.color.border} ${step.color.text} ring-4 ring-opacity-20 ring-blue-500 scale-110` 
                    : state === 'completed'
                    ? `${step.color.bg} ${step.color.border} ${step.color.text}`
                    : 'bg-gray-50 border-gray-300 text-gray-400'
                  }
                `}>
                  <span className="text-lg">{step.icon}</span>
                </div>
                
                {/* Step label */}
                <div className="mt-3 text-center max-w-20">
                  <div className={`text-sm font-medium ${
                    state === 'current' ? step.color.text : 
                    state === 'completed' ? step.color.text : 'text-gray-500'
                  }`}>
                    {step.label}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {step.description}
                  </div>
                  {statusDate && (
                    <div className="text-xs text-gray-400 mt-1 font-mono">
                      {statusDate}
                    </div>
                  )}
                </div>
                
                {/* Progress line segment */}
                {index < steps.length - 1 && (
                  <div className={`
                    absolute top-6 left-6 w-full h-0.5 z-0
                    ${state === 'completed' ? step.color.line : 'bg-gray-200'}
                  `} />
                )}
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Current status indicator */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <span className="text-sm font-medium text-gray-700">Current Status:</span>
            <span className={`ml-2 px-3 py-1 rounded-full text-sm font-medium ${
              steps.find(s => s.key === currentStatus)?.color.bg || 'bg-gray-100'
            } ${
              steps.find(s => s.key === currentStatus)?.color.text || 'text-gray-800'
            }`}>
              {steps.find(s => s.key === currentStatus)?.label || currentStatus}
            </span>
          </div>
          {getStatusDate(currentStatus) && (
            <span className="text-sm text-gray-500">
              Updated {getStatusDate(currentStatus)}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};
