import React, { useState } from 'react';
import Link from 'next/link';
import { Bill } from '../../types';
import { StatusBadge } from './StatusBadge';
import { ActionCounter } from './ActionCounter';
import { QuickStat } from './QuickStat';
import { GroupPill } from './GroupPill';
import { ValuesAnalysisTags } from './ValuesAnalysisTags';

interface EnhancedBillCardProps {
  bill: Bill;
  onTakeAction: (bill: Bill) => void;
  onViewDetails?: (bill: Bill) => void;
  usePageNavigation?: boolean; // If true, use the new full page instead of modal
}

// Simple icons as SVG components
const ClockIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const ChevronDownIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);

const ChevronUpIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
  </svg>
);

const MegaphoneIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
  </svg>
);

const DollarSignIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
);

const UsersIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
  </svg>
);

// Helper functions for extracting key information
const extractTimeline = (timeline: any) => {
  if (!timeline) return 'TBD';

  const content = timeline.content?.toLowerCase() || '';
  if (content.includes('immediate')) return 'Immediate';
  if (content.includes('days')) {
    const match = content.match(/(\d+)\s*days?/);
    return match ? `${match[1]} days` : 'Soon';
  }
  if (content.includes('month')) return 'This month';
  if (content.includes('week')) return 'This week';
  return 'TBD';
};

const extractCostLevel = (costImpact: any) => {
  if (!costImpact) return 'TBD';

  const content = costImpact.content?.toLowerCase() || '';
  if (content.includes('billion')) return 'High';
  if (content.includes('million')) return 'Medium';
  if (content.includes('minimal') || content.includes('low')) return 'Low';
  if (content.includes('no cost') || content.includes('revenue')) return 'Neutral';
  return 'TBD';
};

export const EnhancedBillCard: React.FC<EnhancedBillCardProps> = ({ bill, onTakeAction, usePageNavigation = false }) => {
  // Extract key metrics from bill data using helper functions
  const getTimelineStatus = () => extractTimeline(bill.summary_timeline);
  const getCostLevel = () => extractCostLevel(bill.summary_cost_impact);
  const getAffectedGroupsCount = () => bill.summary_who_affects?.affected_groups?.length || 0;

  // Mock urgency and action count (these would come from backend in real implementation)
  const urgency = bill.status === 'floor' ? 'high' : 'medium';
  const actionCount = Math.floor(Math.random() * 1000) + 50; // Mock data

  return (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 overflow-hidden h-auto flex flex-col">
      {/* Header Section */}
      <div className="p-6 pb-4 flex-shrink-0">
        {/* Bill Number & Status */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">
              {bill.bill_number}
            </span>
            <StatusBadge status={bill.status} urgency={urgency} />
          </div>
          <ActionCounter count={actionCount} />
        </div>

        {/* Title - Allow natural height */}
        <h3 className="text-lg font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors leading-6">
          {bill.title}
        </h3>

        {/* Key Metrics Row - Fixed height */}
        <div className="flex items-center gap-3 mb-3 text-xs text-gray-600 h-5">
          <QuickStat
            icon={<ClockIcon />}
            label="Timeline"
            value={getTimelineStatus()}
          />
          <QuickStat
            icon={<DollarSignIcon />}
            label="Cost"
            value={getCostLevel()}
          />
          <QuickStat
            icon={<UsersIcon />}
            label="Affected"
            value={`${getAffectedGroupsCount()} groups`}
          />
        </div>
      </div>

      {/* Content Section - Flexible height with constraints */}
      <div className="px-6 flex-1 flex flex-col justify-between">
        {/* TL;DR or Simple Summary - Easy to understand */}
        <div className="mb-4">
          {bill.tldr ? (
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-semibold">TL;DR</span>
              </div>
              <p className="text-gray-800 text-sm leading-relaxed font-medium">
                {bill.tldr}
              </p>
            </div>
          ) : (
            <p className="text-gray-800 text-sm leading-relaxed font-medium">
              {bill.simple_summary || bill.summary_what_does?.content || bill.ai_summary || 'Summary not available'}
            </p>
          )}
        </div>

        {/* Affected Groups Tags */}
        <div className="mb-4">
          {bill.summary_who_affects?.affected_groups && bill.summary_who_affects.affected_groups.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {bill.summary_who_affects.affected_groups.slice(0, 2).map((group, index) => (
                <GroupPill key={index} group={group} />
              ))}
              {bill.summary_who_affects.affected_groups.length > 2 && (
                <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                  +{bill.summary_who_affects.affected_groups.length - 2}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Values Analysis Tags */}
        {bill.values_tags && bill.values_tags.length > 0 && (
          <div className="mb-4">
            <ValuesAnalysisTags
              tags={bill.values_tags}
              variant="compact"
              maxTags={3}
            />
          </div>
        )}
      </div>

      {/* Action Section - Fixed height */}
      <div className="px-6 pb-6 flex-shrink-0">
        {usePageNavigation ? (
          /* Link to full page */
          <Link 
            href={`/bills/${bill.id}/action`}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2 no-underline"
          >
            <MegaphoneIcon />
            Take Action
          </Link>
        ) : (
          /* Original modal button */
          <button
            onClick={() => onTakeAction(bill)}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            <MegaphoneIcon />
            Take Action
          </button>
        )}
      </div>


    </div>
  );
};


