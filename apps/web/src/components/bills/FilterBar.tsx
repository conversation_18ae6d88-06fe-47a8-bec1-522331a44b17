import React from 'react';
import { BillStatus } from '../../types';

interface FilterBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  statusFilter: BillStatus | 'all';
  onStatusFilterChange: (status: BillStatus | 'all') => void;
  urgencyFilter: 'all' | 'high' | 'medium' | 'low';
  onUrgencyFilterChange: (urgency: 'all' | 'high' | 'medium' | 'low') => void;
  sortBy: 'priority' | 'date' | 'title' | 'action_count';
  onSortChange: (sort: 'priority' | 'date' | 'title' | 'action_count') => void;
  totalCount: number;
  filteredCount: number;
}

const SearchIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const FilterIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
  </svg>
);

const SortIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
  </svg>
);

export const FilterBar: React.FC<FilterBarProps> = ({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  urgencyFilter,
  onUrgencyFilterChange,
  sortBy,
  onSortChange,
  totalCount,
  filteredCount
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      {/* Search Bar */}
      <div className="mb-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <SearchIcon />
          </div>
          <input
            type="text"
            placeholder="Search bills by title, description, or bill number..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Filters Row */}
      <div className="flex flex-wrap items-center gap-4 mb-4">
        {/* Status Filter */}
        <div className="flex items-center gap-2">
          <FilterIcon />
          <label className="text-sm font-medium text-gray-700">Status:</label>
          <select
            value={statusFilter}
            onChange={(e) => onStatusFilterChange(e.target.value as BillStatus | 'all')}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm text-gray-900 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value="active">Active</option>
            <option value="committee">In Committee</option>
            <option value="floor">Floor Vote</option>
            <option value="passed">Passed</option>
          </select>
        </div>

        {/* Urgency Filter */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Urgency:</span>
          <select
            value={urgencyFilter}
            onChange={(e) => onUrgencyFilterChange(e.target.value as 'all' | 'high' | 'medium' | 'low')}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm text-gray-900 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Levels</option>
            <option value="high">🔥 High</option>
            <option value="medium">⚡ Medium</option>
            <option value="low">📋 Low</option>
          </select>
        </div>

        {/* Sort */}
        <div className="flex items-center gap-2">
          <SortIcon />
          <label className="text-sm font-medium text-gray-700">Sort by:</label>
          <select
            value={sortBy}
            onChange={(e) => onSortChange(e.target.value as 'priority' | 'date' | 'title' | 'action_count')}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm text-gray-900 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="priority">Priority Score</option>
            <option value="date">Date Introduced</option>
            <option value="title">Title (A-Z)</option>
            <option value="action_count">Most Actions</option>
          </select>
        </div>
      </div>

      {/* Results Count */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <div>
          Showing <span className="font-medium">{filteredCount}</span> of <span className="font-medium">{totalCount}</span> bills
        </div>
        
        {/* Quick Filter Chips */}
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">Quick filters:</span>
          <button
            onClick={() => {
              onStatusFilterChange('floor');
              onUrgencyFilterChange('high');
            }}
            className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium hover:bg-red-200 transition-colors"
          >
            🔥 Urgent Votes
          </button>
          <button
            onClick={() => {
              onStatusFilterChange('active');
              onUrgencyFilterChange('all');
            }}
            className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium hover:bg-green-200 transition-colors"
          >
            📈 Active Bills
          </button>
        </div>
      </div>
    </div>
  );
};
