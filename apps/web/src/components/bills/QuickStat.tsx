import React from 'react';

interface QuickStatProps {
  icon: React.ReactNode;
  label: string;
  value: string;
}

export const QuickStat: React.FC<QuickStatProps> = ({ icon, label, value }) => {
  return (
    <div className="flex items-center gap-2 text-sm text-gray-600">
      <div className="w-4 h-4 text-gray-500">{icon}</div>
      <div>
        <div className="text-xs text-gray-500">{label}</div>
        <div className="font-medium text-gray-700">{value}</div>
      </div>
    </div>
  );
};
