import React from 'react';
import { Bill } from '../../types';

interface SimpleBillCardProps {
  bill: Bill;
  onTakeAction: (bill: Bill) => void;
}

export const SimpleBillCard: React.FC<SimpleBillCardProps> = ({ bill, onTakeAction }) => {
  return (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 overflow-hidden">
      {/* Header Section */}
      <div className="p-6">
        {/* Bill Number & Status */}
        <div className="flex items-center justify-between mb-3">
          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">
            {bill.bill_number}
          </span>
          <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
            {bill.status}
          </span>
        </div>

        {/* Title */}
        <h3 className="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
          {bill.title}
        </h3>

        {/* Summary */}
        <div className="mb-4">
          <p className="text-gray-700 leading-relaxed">
            {bill.summary_what_does?.content || bill.ai_summary || 'Summary not available'}
          </p>
        </div>

        {/* Action Button */}
        <button 
          onClick={() => onTakeAction(bill)} 
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors"
        >
          Take Action
        </button>
      </div>
    </div>
  );
};
