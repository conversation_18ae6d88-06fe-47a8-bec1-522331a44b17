import React from 'react';
import { BillStatus } from '../../types';

interface StatusBadgeProps {
  status: BillStatus;
  urgency?: 'high' | 'medium' | 'low';
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, urgency }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'committee':
        return {
          bg: 'bg-yellow-100',
          text: 'text-yellow-800',
          label: 'In Committee',
          icon: '📋'
        };
      case 'floor':
        return {
          bg: 'bg-red-100',
          text: 'text-red-800',
          label: 'Floor Vote Soon',
          icon: '🔥'
        };
      case 'passed':
        return {
          bg: 'bg-blue-100',
          text: 'text-blue-800',
          label: 'Passed House',
          icon: '✅'
        };
      case 'introduced':
        return {
          bg: 'bg-gray-100',
          text: 'text-gray-800',
          label: 'Introduced',
          icon: '📋'
        };
      case 'signed':
        return {
          bg: 'bg-green-100',
          text: 'text-green-800',
          label: 'Signed into Law',
          icon: '🏛️'
        };
      case 'vetoed':
        return {
          bg: 'bg-red-100',
          text: 'text-red-800',
          label: 'Vetoed',
          icon: '❌'
        };
      case 'failed':
        return {
          bg: 'bg-gray-100',
          text: 'text-gray-800',
          label: 'Failed',
          icon: '💔'
        };
      default:
        return {
          bg: 'bg-yellow-100',
          text: 'text-yellow-800',
          label: status || 'Unknown',
          icon: '⏳'
        };
    }
  };

  const config = getStatusConfig();
  
  return (
    <div className="flex items-center gap-2">
      <span className={`${config.bg} ${config.text} px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1`}>
        <span>{config.icon}</span>
        {config.label}
      </span>
      {urgency === 'high' && (
        <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse">
          URGENT
        </span>
      )}
    </div>
  );
};
