import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, ClockIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

interface SummaryVersion {
  version: number;
  date: string;
  reason: string;
  is_current: boolean;
  changes: {
    type: string;
    fields_changed?: string[];
    fields_added?: string[];
    fields_removed?: string[];
  };
  has_structured_data: boolean;
}

interface SummaryVersionTrackerProps {
  versions: SummaryVersion[];
  className?: string;
}

const CHANGE_REASON_LABELS: Record<string, { label: string; icon: string; color: string }> = {
  'manual_update': { label: 'Manual Update', icon: '✏️', color: 'text-blue-600' },
  'ai_update': { label: 'AI Enhancement', icon: '🤖', color: 'text-purple-600' },
  'bill_amended': { label: 'Bill Amendment', icon: '📝', color: 'text-orange-600' },
  'ai_improvement': { label: 'AI Improvement', icon: '✨', color: 'text-green-600' },
  'initial_version': { label: 'Initial Version', icon: '🆕', color: 'text-gray-600' }
};

const FIELD_LABELS: Record<string, string> = {
  'summary_what_does': 'What it does',
  'summary_who_affects': 'Who it affects',
  'summary_why_matters': 'Why it matters',
  'summary_key_provisions': 'Key provisions',
  'summary_timeline': 'Timeline',
  'summary_cost_impact': 'Cost impact',
  'ai_summary': 'AI summary',
  'simple_summary': 'Simple summary'
};

export const SummaryVersionTracker: React.FC<SummaryVersionTrackerProps> = ({
  versions,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<number | null>(null);

  if (!versions || versions.length === 0) {
    return (
      <div className={`bg-white rounded-xl border border-gray-200 p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <DocumentTextIcon className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">No summary versions available</p>
        </div>
      </div>
    );
  }

  const currentVersion = versions.find(v => v.is_current) || versions[0];
  const displayVersions = isExpanded ? versions : [currentVersion];

  const formatChangeDescription = (changes: SummaryVersion['changes']) => {
    if (changes.type === 'initial_version') {
      return 'Initial summary created';
    }

    const parts = [];
    if (changes.fields_added?.length) {
      parts.push(`Added ${changes.fields_added.length} section${changes.fields_added.length > 1 ? 's' : ''}`);
    }
    if (changes.fields_changed?.length) {
      parts.push(`Updated ${changes.fields_changed.length} section${changes.fields_changed.length > 1 ? 's' : ''}`);
    }
    if (changes.fields_removed?.length) {
      parts.push(`Removed ${changes.fields_removed.length} section${changes.fields_removed.length > 1 ? 's' : ''}`);
    }

    return parts.length > 0 ? parts.join(', ') : 'Summary updated';
  };

  const getFieldChanges = (changes: SummaryVersion['changes']) => {
    const allChanges = [
      ...(changes.fields_added || []).map(field => ({ field, type: 'added' })),
      ...(changes.fields_changed || []).map(field => ({ field, type: 'changed' })),
      ...(changes.fields_removed || []).map(field => ({ field, type: 'removed' }))
    ];
    return allChanges;
  };

  return (
    <div className={`bg-white rounded-xl border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">Summary Versions</h3>
          <p className="text-sm text-gray-600">Track how this bill's summary has evolved</p>
        </div>
        {versions.length > 1 && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
          >
            {isExpanded ? (
              <>
                <ChevronUpIcon className="w-4 h-4" />
                Show Less
              </>
            ) : (
              <>
                <ChevronDownIcon className="w-4 h-4" />
                Show All ({versions.length})
              </>
            )}
          </button>
        )}
      </div>

      <div className="space-y-4">
        {displayVersions.map((version, index) => {
          const reasonConfig = CHANGE_REASON_LABELS[version.reason] || CHANGE_REASON_LABELS['manual_update'];
          const isSelected = selectedVersion === version.version;
          const fieldChanges = getFieldChanges(version.changes);

          return (
            <div
              key={version.version}
              className={`border rounded-lg p-4 transition-all cursor-pointer ${
                version.is_current 
                  ? 'border-blue-200 bg-blue-50' 
                  : isSelected
                  ? 'border-gray-300 bg-gray-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedVersion(isSelected ? null : version.version)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <span className={`text-lg ${reasonConfig.color}`}>
                      {reasonConfig.icon}
                    </span>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">
                          Version {version.version}
                        </span>
                        {version.is_current && (
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                            Current
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <ClockIcon className="w-4 h-4" />
                        {new Date(version.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </div>
                  </div>

                  <div className="mb-2">
                    <span className={`text-sm font-medium ${reasonConfig.color}`}>
                      {reasonConfig.label}
                    </span>
                    <span className="text-sm text-gray-600 ml-2">
                      • {formatChangeDescription(version.changes)}
                    </span>
                  </div>

                  {isSelected && fieldChanges.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Changes in this version:</h4>
                      <div className="space-y-1">
                        {fieldChanges.map(({ field, type }, idx) => (
                          <div key={idx} className="flex items-center gap-2 text-sm">
                            <span className={`w-2 h-2 rounded-full ${
                              type === 'added' ? 'bg-green-400' :
                              type === 'changed' ? 'bg-blue-400' :
                              'bg-red-400'
                            }`} />
                            <span className="text-gray-600">
                              {FIELD_LABELS[field] || field}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              type === 'added' ? 'bg-green-100 text-green-700' :
                              type === 'changed' ? 'bg-blue-100 text-blue-700' :
                              'bg-red-100 text-red-700'
                            }`}>
                              {type}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2 ml-4">
                  {version.has_structured_data && (
                    <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                      Enhanced
                    </span>
                  )}
                  <ChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${
                    isSelected ? 'rotate-180' : ''
                  }`} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {!isExpanded && versions.length > 1 && (
        <div className="mt-4 text-center">
          <span className="text-sm text-gray-500">
            {versions.length - 1} earlier version{versions.length > 2 ? 's' : ''} available
          </span>
        </div>
      )}
    </div>
  );
};
