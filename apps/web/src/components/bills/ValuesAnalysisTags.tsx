import React from 'react';

// Values analysis interfaces
export interface ValuesTag {
  id: string;
  category: string;
  type: string;
  display_text: string;
  description?: string;
  severity_level: number;
  color_theme: string;
  icon_name: string;
  is_active: boolean;
}

export interface ValuesAnalysis {
  id: string;
  democracy_threat_score: number;
  democracy_support_score: number;
  human_rights_threat_score: number;
  human_rights_support_score: number;
  environmental_threat_score: number;
  environmental_support_score: number;
  overall_threat_level: string;
  overall_support_level: string;
  confidence_score?: number;
  requires_human_review: boolean;
  is_flagged: boolean;
  analyzed_at: string;
}

interface ValuesAnalysisTagsProps {
  tags: ValuesTag[];
  analysis?: ValuesAnalysis;
  variant?: 'compact' | 'full' | 'detailed';
  maxTags?: number;
}

// Icon components for different categories
const DemocracyIcon = () => (
  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
  </svg>
);

const HumanRightsIcon = () => (
  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
  </svg>
);

const EnvironmentIcon = () => (
  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
  </svg>
);

const InfoIcon = () => (
  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
  </svg>
);

// Get icon component based on category
const getIconForCategory = (category: string) => {
  switch (category) {
    case 'democracy':
      return <DemocracyIcon />;
    case 'human_rights':
      return <HumanRightsIcon />;
    case 'environment':
      return <EnvironmentIcon />;
    default:
      return <InfoIcon />;
  }
};

// Get color classes based on theme and severity
const getTagColors = (colorTheme: string, severityLevel: number) => {
  const baseColors = {
    blue: 'bg-blue-50 text-blue-700 border-blue-200',
    green: 'bg-green-50 text-green-700 border-green-200',
    orange: 'bg-orange-50 text-orange-700 border-orange-200',
    red: 'bg-red-50 text-red-700 border-red-200',
    purple: 'bg-purple-50 text-purple-700 border-purple-200',
    gray: 'bg-gray-50 text-gray-700 border-gray-200',
  };

  // Adjust intensity based on severity
  if (severityLevel >= 7) {
    return {
      blue: 'bg-blue-100 text-blue-800 border-blue-300',
      green: 'bg-green-100 text-green-800 border-green-300',
      orange: 'bg-orange-100 text-orange-800 border-orange-300',
      red: 'bg-red-100 text-red-800 border-red-300',
      purple: 'bg-purple-100 text-purple-800 border-purple-300',
      gray: 'bg-gray-100 text-gray-800 border-gray-300',
    }[colorTheme] || baseColors.gray;
  }

  return baseColors[colorTheme as keyof typeof baseColors] || baseColors.gray;
};

// Individual tag component
const ValuesTag: React.FC<{ tag: ValuesTag; variant: 'compact' | 'full' | 'detailed' }> = ({ tag, variant }) => {
  const colorClasses = getTagColors(tag.color_theme, tag.severity_level);
  const icon = getIconForCategory(tag.category);

  if (variant === 'compact') {
    return (
      <span
        className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${colorClasses}`}
        title={tag.description}
      >
        {icon}
        <span className="truncate max-w-20">{tag.display_text}</span>
      </span>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={`p-3 rounded-lg border ${colorClasses}`}>
        <div className="flex items-start gap-2">
          <div className="flex-shrink-0 mt-0.5">
            {icon}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{tag.display_text}</span>
              <span className="text-xs opacity-75">Level {tag.severity_level}</span>
            </div>
            {tag.description && (
              <p className="text-xs mt-1 opacity-90">{tag.description}</p>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Full variant (default)
  return (
    <span
      className={`inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium border ${colorClasses}`}
      title={tag.description}
    >
      {icon}
      <span>{tag.display_text}</span>
    </span>
  );
};

// Main component
export const ValuesAnalysisTags: React.FC<ValuesAnalysisTagsProps> = ({ 
  tags, 
  analysis, 
  variant = 'full', 
  maxTags 
}) => {
  if (!tags || tags.length === 0) {
    return null;
  }

  // Filter active tags and sort by priority
  const activeTags = tags
    .filter(tag => tag.is_active)
    .sort((a, b) => b.severity_level - a.severity_level);

  // Apply max tags limit if specified
  const displayTags = maxTags ? activeTags.slice(0, maxTags) : activeTags;
  const hiddenCount = maxTags && activeTags.length > maxTags ? activeTags.length - maxTags : 0;

  if (displayTags.length === 0) {
    return null;
  }

  return (
    <div className="values-analysis-tags">
      {variant === 'detailed' ? (
        <div className="space-y-2">
          {displayTags.map((tag) => (
            <ValuesTag key={tag.id} tag={tag} variant={variant} />
          ))}
          {hiddenCount > 0 && (
            <div className="text-xs text-gray-500 text-center py-1">
              +{hiddenCount} more tag{hiddenCount > 1 ? 's' : ''}
            </div>
          )}
        </div>
      ) : (
        <div className="flex flex-wrap gap-1.5">
          {displayTags.map((tag) => (
            <ValuesTag key={tag.id} tag={tag} variant={variant} />
          ))}
          {hiddenCount > 0 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
              +{hiddenCount}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

// Analysis summary component for detailed views
export const ValuesAnalysisSummary: React.FC<{ analysis: ValuesAnalysis }> = ({ analysis }) => {
  const getScoreColor = (score: number) => {
    if (score >= 7) return 'text-red-600';
    if (score >= 4) return 'text-orange-600';
    if (score >= 1) return 'text-yellow-600';
    return 'text-gray-500';
  };

  const getLevelBadge = (level: string) => {
    const colors = {
      critical: 'bg-red-100 text-red-800',
      high: 'bg-orange-100 text-orange-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-blue-100 text-blue-800',
      none: 'bg-gray-100 text-gray-800',
    };
    
    return colors[level as keyof typeof colors] || colors.none;
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4 space-y-3">
      <h4 className="text-sm font-medium text-gray-900">Values Analysis Summary</h4>
      
      <div className="grid grid-cols-3 gap-3 text-xs">
        <div className="text-center">
          <div className="text-gray-600 mb-1">Democracy</div>
          <div className="space-y-1">
            <div className={`font-medium ${getScoreColor(analysis.democracy_threat_score)}`}>
              T: {analysis.democracy_threat_score}
            </div>
            <div className={`font-medium ${getScoreColor(analysis.democracy_support_score)}`}>
              S: {analysis.democracy_support_score}
            </div>
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-gray-600 mb-1">Rights</div>
          <div className="space-y-1">
            <div className={`font-medium ${getScoreColor(analysis.human_rights_threat_score)}`}>
              T: {analysis.human_rights_threat_score}
            </div>
            <div className={`font-medium ${getScoreColor(analysis.human_rights_support_score)}`}>
              S: {analysis.human_rights_support_score}
            </div>
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-gray-600 mb-1">Environment</div>
          <div className="space-y-1">
            <div className={`font-medium ${getScoreColor(analysis.environmental_threat_score)}`}>
              T: {analysis.environmental_threat_score}
            </div>
            <div className={`font-medium ${getScoreColor(analysis.environmental_support_score)}`}>
              S: {analysis.environmental_support_score}
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex items-center justify-between pt-2 border-t border-gray-200">
        <div className="flex gap-2">
          {analysis.overall_threat_level !== 'none' && (
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelBadge(analysis.overall_threat_level)}`}>
              Threat: {analysis.overall_threat_level}
            </span>
          )}
          {analysis.overall_support_level !== 'none' && (
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelBadge(analysis.overall_support_level)}`}>
              Support: {analysis.overall_support_level}
            </span>
          )}
        </div>
        
        {analysis.confidence_score && (
          <span className="text-xs text-gray-500">
            {Math.round(analysis.confidence_score * 100)}% confidence
          </span>
        )}
      </div>
    </div>
  );
};

export default ValuesAnalysisTags;
