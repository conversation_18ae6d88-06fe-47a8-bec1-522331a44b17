'use client';

import React from 'react';
import { ValuesAnalysisTags, ValuesAnalysisSummary } from './ValuesAnalysisTags';
import type { ValuesTag, ValuesAnalysis } from '../../types';

// Mock data for testing
const mockValuesAnalysis: ValuesAnalysis = {
  id: 'test-analysis-1',
  created_at: '2024-01-15T10:00:00Z',
  updated_at: '2024-01-15T10:00:00Z',
  democracy_threat_score: 3,
  democracy_support_score: 7,
  human_rights_threat_score: 1,
  human_rights_support_score: 8,
  environmental_threat_score: 5,
  environmental_support_score: 2,
  overall_threat_level: 'medium',
  overall_support_level: 'high',
  confidence_score: 0.85,
  requires_human_review: false,
  is_flagged: false,
  analyzed_at: '2024-01-15T10:00:00Z',
  reviewed_by: 'admin-user',
  reviewed_at: '2024-01-15T11:00:00Z',
  review_notes: 'Approved after review'
};

const mockValuesTags: ValuesTag[] = [
  {
    id: 'tag-1',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    bill_id: 'test-bill-1',
    analysis_id: 'test-analysis-1',
    tag_category: 'democracy',
    tag_type: 'support',
    tag_name: 'voting_access',
    display_text: 'Voting Access',
    description: 'This bill improves access to voting for eligible citizens',
    severity_level: 7,
    display_priority: 7,
    color_theme: 'blue',
    icon_name: 'vote',
    is_active: true
  },
  {
    id: 'tag-2',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    bill_id: 'test-bill-1',
    analysis_id: 'test-analysis-1',
    tag_category: 'human_rights',
    tag_type: 'support',
    tag_name: 'civil_liberties',
    display_text: 'Civil Liberties',
    description: 'Strengthens protections for individual civil liberties',
    severity_level: 8,
    display_priority: 8,
    color_theme: 'green',
    icon_name: 'shield',
    is_active: true
  },
  {
    id: 'tag-3',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    bill_id: 'test-bill-1',
    analysis_id: 'test-analysis-1',
    tag_category: 'environment',
    tag_type: 'threat',
    tag_name: 'environmental_impact',
    display_text: 'Environmental Impact',
    description: 'May have moderate environmental implications',
    severity_level: 5,
    display_priority: 5,
    color_theme: 'orange',
    icon_name: 'warning',
    is_active: true
  }
];

export const ValuesAnalysisTest: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Values Analysis Components Test</h1>
        <p className="text-gray-600">Testing the display of values analysis tags and summaries</p>
      </div>

      {/* Compact Tags Test */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Compact Tags (for Bill Cards)</h2>
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">All Tags</h3>
            <ValuesAnalysisTags tags={mockValuesTags} variant="compact" />
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Limited to 2 Tags</h3>
            <ValuesAnalysisTags tags={mockValuesTags} variant="compact" maxTags={2} />
          </div>
        </div>
      </div>

      {/* Full Tags Test */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Full Tags (for Modals)</h2>
        <ValuesAnalysisTags tags={mockValuesTags} variant="full" />
      </div>

      {/* Detailed Tags Test */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Detailed Tags (for Detail Pages)</h2>
        <ValuesAnalysisTags tags={mockValuesTags} variant="detailed" />
      </div>

      {/* Analysis Summary Test */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Analysis Summary</h2>
        <ValuesAnalysisSummary analysis={mockValuesAnalysis} />
      </div>

      {/* Combined Test */}
      <div className="bg-purple-50 rounded-lg border border-purple-200 p-6">
        <h2 className="text-xl font-semibold text-purple-900 mb-4">Combined Display (as in Bill Detail)</h2>
        <div className="space-y-4">
          <p className="text-sm text-purple-700">
            This bill has been analyzed for its potential impact on democratic processes, 
            civil rights, and environmental considerations.
          </p>
          
          <ValuesAnalysisSummary analysis={mockValuesAnalysis} />
          
          <div>
            <h5 className="text-sm font-medium text-purple-800 mb-2">Key Considerations</h5>
            <ValuesAnalysisTags tags={mockValuesTags} variant="detailed" />
          </div>
          
          <p className="text-xs text-purple-600 italic">
            Analysis uses neutral, factual criteria to assess potential impacts. 
            All assessments are reviewed by our policy team.
          </p>
        </div>
      </div>

      {/* Empty State Test */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Empty State (No Tags)</h2>
        <div className="text-gray-500 text-sm">
          <ValuesAnalysisTags tags={[]} variant="full" />
          <p>No tags to display (component should render nothing)</p>
        </div>
      </div>

      {/* Color Theme Test */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Color Theme Test</h2>
        <div className="space-y-3">
          {['blue', 'green', 'orange', 'red', 'purple', 'gray'].map((color) => (
            <div key={color} className="flex items-center gap-4">
              <span className="w-16 text-sm font-medium text-gray-700 capitalize">{color}:</span>
              <ValuesAnalysisTags 
                tags={[{
                  ...mockValuesTags[0],
                  id: `test-${color}`,
                  display_text: `${color.charAt(0).toUpperCase() + color.slice(1)} Tag`,
                  color_theme: color,
                  severity_level: 5
                }]} 
                variant="full" 
              />
            </div>
          ))}
        </div>
      </div>

      {/* Severity Level Test */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Severity Level Test</h2>
        <div className="space-y-3">
          {[1, 3, 5, 7, 9].map((severity) => (
            <div key={severity} className="flex items-center gap-4">
              <span className="w-16 text-sm font-medium text-gray-700">Level {severity}:</span>
              <ValuesAnalysisTags 
                tags={[{
                  ...mockValuesTags[0],
                  id: `test-severity-${severity}`,
                  display_text: `Severity ${severity}`,
                  severity_level: severity
                }]} 
                variant="full" 
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ValuesAnalysisTest;
