// BillSummaryCard component - displays bill information related to the campaign
import React from 'react';
import { Bill, BillStatus, BillType, Chamber } from '../../types';

interface BillSummaryCardProps {
  bill: Bill;
}

const BillSummaryCard: React.FC<BillSummaryCardProps> = ({ bill }) => {
  const getStatusColor = (status: BillStatus): string => {
    switch (status) {
      case BillStatus.INTRODUCED:
        return 'bg-blue-100 text-blue-800';
      case BillStatus.COMMITTEE:
        return 'bg-yellow-100 text-yellow-800';
      case BillStatus.FLOOR:
        return 'bg-orange-100 text-orange-800';
      case BillStatus.PASSED:
        return 'bg-green-100 text-green-800';
      case BillStatus.SIGNED:
        return 'bg-green-100 text-green-800';
      case BillStatus.VETOED:
        return 'bg-red-100 text-red-800';
      case BillStatus.FAILED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatBillType = (type: BillType): string => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatChamber = (chamber: Chamber): string => {
    switch (chamber) {
      case Chamber.HOUSE:
        return 'House of Representatives';
      case Chamber.SENATE:
        return 'Senate';
      case Chamber.UNICAMERAL:
        return 'Legislature';
      default:
        return chamber;
    }
  };

  return (
    <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Related Bill
          </h3>
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
              {bill.bill_number}
            </span>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(bill.status)}`}>
              {bill.status.charAt(0).toUpperCase() + bill.status.slice(1)}
            </span>
          </div>
        </div>
        {bill.is_featured && (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            Featured
          </span>
        )}
      </div>

      {/* Bill Title */}
      <h4 className="text-xl font-semibold text-gray-900 mb-3">
        {bill.title}
      </h4>

      {/* Bill Description */}
      {bill.description && (
        <p className="text-gray-600 mb-4">
          {bill.description}
        </p>
      )}

      {/* Bill Summary */}
      {bill.summary && (
        <div className="mb-4">
          <h5 className="text-sm font-medium text-gray-900 mb-2">Summary</h5>
          <p className="text-sm text-gray-600">
            {bill.summary}
          </p>
        </div>
      )}

      {/* Bill Metadata */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <span className="font-medium text-gray-900">Type:</span>
          <p className="text-gray-600">{formatBillType(bill.bill_type)}</p>
        </div>
        
        <div>
          <span className="font-medium text-gray-900">Chamber:</span>
          <p className="text-gray-600">{formatChamber(bill.chamber)}</p>
        </div>

        <div>
          <span className="font-medium text-gray-900">Session:</span>
          <p className="text-gray-600">{bill.session_year}</p>
        </div>

        <div>
          <span className="font-medium text-gray-900">State:</span>
          <p className="text-gray-600">
            {bill.state === 'federal' ? 'Federal' : bill.state.toUpperCase()}
          </p>
        </div>

        {bill.sponsor_name && (
          <div className="md:col-span-2">
            <span className="font-medium text-gray-900">Sponsor:</span>
            <p className="text-gray-600">
              {bill.sponsor_name}
              {bill.sponsor_party && ` (${bill.sponsor_party})`}
              {bill.sponsor_state && ` - ${bill.sponsor_state}`}
            </p>
          </div>
        )}

        {bill.committee && (
          <div className="md:col-span-2">
            <span className="font-medium text-gray-900">Committee:</span>
            <p className="text-gray-600">{bill.committee}</p>
          </div>
        )}
      </div>

      {/* Tags and Categories */}
      {((bill.tags && bill.tags.length > 0) || (bill.categories && bill.categories.length > 0)) && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          {bill.tags && bill.tags.length > 0 && (
            <div className="mb-2">
              <span className="text-sm font-medium text-gray-900 mr-2">Tags:</span>
              <div className="inline-flex flex-wrap gap-1">
                {bill.tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {bill.categories && bill.categories.length > 0 && (
            <div>
              <span className="text-sm font-medium text-gray-900 mr-2">Categories:</span>
              <div className="inline-flex flex-wrap gap-1">
                {bill.categories.map((category, index) => (
                  <span 
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-700"
                  >
                    {category}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* External Links */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex flex-wrap gap-2">
          {bill.full_text_url && (
            <a 
              href={bill.full_text_url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              Read Full Text
              <svg className="ml-1 w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          )}
          
          {bill.external_congress_id && (
            <a 
              href={`https://www.congress.gov/bill/${bill.session_year}/${bill.external_congress_id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              Congress.gov
              <svg className="ml-1 w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          )}
        </div>
      </div>
    </div>
  );
};

export default BillSummaryCard;
