'use client';

import { useUser } from '@auth0/nextjs-auth0/client';
import { useEffect } from 'react';
import { setAuthToken } from '../services/apiClient';

/**
 * Hook to manage Auth0 tokens and sync them with the API client
 */
export const useAuthToken = () => {
  const { user, isLoading } = useUser();

  useEffect(() => {
    const updateToken = async () => {
      if (user && !isLoading) {
        try {
          // Get access token from the Next.js Auth0 API route
          const response = await fetch('/api/auth/token');
          if (response.ok) {
            const { accessToken } = await response.json();
            setAuthToken(accessToken);
          } else {
            console.error('Failed to get access token from API route');
            setAuthToken(null);
          }
        } catch (error) {
          console.error('Failed to get access token:', error);
          setAuthToken(null);
        }
      } else {
        setAuthToken(null);
      }
    };

    updateToken();
  }, [user, isLoading]);

  return {
    isAuthenticated: !!user,
    isLoading
  };
};
