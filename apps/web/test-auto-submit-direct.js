// Test auto-submit fix by going directly to action page
const { chromium } = require('playwright');

async function testAutoSubmitDirect() {
  console.log('🚀 Starting direct auto-submit fix test...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console logs to capture our debugging messages
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🚨') || text.includes('🎯') || text.includes('📍')) {
      console.log(`BROWSER: ${text}`);
    }
  });
  
  try {
    // Go directly to a bill's action page (using the ID from the API response)
    const billId = 'bd9c4dfb-a7b7-406d-ac41-263f36548c50'; // Affordable Housing Act
    console.log('📍 Navigating directly to action page...');
    await page.goto(`http://localhost:3001/bills/${billId}/action`);
    
    console.log('📍 Waiting for action page to load...');
    await page.waitForTimeout(3000);
    
    console.log('📍 Looking for stance buttons...');
    const allButtons = await page.locator('button').all();
    
    for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
      const button = allButtons[i];
      const text = await button.textContent();
      const isVisible = await button.isVisible();
      console.log(`Button ${i}: "${text}" (visible: ${isVisible})`);
    }
    
    // Try to find stance selection buttons
    const supportButton = await page.locator('button:has-text("support")').count();
    const opposeButton = await page.locator('button:has-text("oppose")').count();
    const amendButton = await page.locator('button:has-text("amend")').count();
    
    console.log(`Support buttons: ${supportButton}`);
    console.log(`Oppose buttons: ${opposeButton}`);
    console.log(`Amend buttons: ${amendButton}`);
    
    // Try different stance button patterns
    const stanceButtons = await page.locator('button[data-stance], button[value*="support"], button[value*="oppose"], button[value*="amend"]').count();
    console.log(`Stance buttons with data attributes: ${stanceButtons}`);
    
    // Look for radio buttons
    const radioButtons = await page.locator('input[type="radio"]').count();
    console.log(`Radio buttons: ${radioButtons}`);
    
    if (radioButtons > 0) {
      console.log('📍 Found radio buttons, trying to select one...');
      await page.click('input[type="radio"]');
      await page.click('button:has-text("Next")');
      
      console.log('📍 Moving to next step...');
      await page.waitForTimeout(2000);
      
      // Continue with the flow...
      console.log('📍 Test completed - check browser for current state');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    // Keep browser open for inspection
    console.log('📍 Browser left open for inspection. Close manually when done.');
  }
}

testAutoSubmitDirect().catch(console.error);
