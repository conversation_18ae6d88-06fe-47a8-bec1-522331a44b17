// Simple test to verify auto-submit fix
// Run this with: node test-auto-submit-fix.js

const { chromium } = require('playwright');

async function testAutoSubmitFix() {
  console.log('🚀 Starting auto-submit fix test...');

  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();

  // Listen for console logs to capture our debugging messages
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🚨') || text.includes('🎯') || text.includes('📍')) {
      console.log(`BROWSER: ${text}`);
    }
  });

  try {
    console.log('📍 Navigating to bills page...');
    await page.goto('http://localhost:3001/bills');

    console.log('📍 Waiting for bills to load...');
    // Use a more flexible selector since we know bills are loading
    await page.waitForSelector('text=Lower Energy Costs Act', { timeout: 15000 });

    console.log('📍 Clicking on first bill...');
    // Click on the first "Take Action" link (not button)
    await page.click('a:has-text("Take Action")');

    console.log('📍 Waiting for action page to load...');
    await page.waitForURL('**/action', { timeout: 10000 });

    console.log('📍 Going through the flow...');

    // Step 1: Stance
    await page.click('button:has-text("I want to amend this bill")');
    await page.click('button:has-text("Next")');

    // Step 2: Reasons
    await page.click('input[type="checkbox"]');
    await page.click('button:has-text("Next")');

    // Step 3: Contact
    await page.fill('input[name="zip_code"]', '19146');
    await page.click('button:has-text("Next")');

    console.log('📍 Waiting for AI generation...');
    await page.waitForSelector('text=Edit & Send', { timeout: 30000 });

    console.log('📍 Reached edit_and_send step - checking for auto-submit prevention...');

    // Wait for the blocking period to complete
    await page.waitForTimeout(1000);

    // Check if the button is enabled and shows correct text
    const submitButton = page.locator('button[type="submit"]:has-text("Send Messages")');
    const isEnabled = await submitButton.isEnabled();
    const buttonText = await submitButton.textContent();

    console.log(`📍 Submit button enabled: ${isEnabled}`);
    console.log(`📍 Submit button text: "${buttonText}"`);

    if (buttonText.includes('Loading...')) {
      console.log('✅ GOOD: Button shows "Loading..." during blocking period');
    } else if (buttonText.includes('Send Messages')) {
      console.log('✅ GOOD: Button shows "Send Messages" after blocking period');
    } else {
      console.log('❌ UNEXPECTED: Button text is unexpected');
    }

    // Wait a bit more to see if auto-submit happens
    console.log('📍 Waiting 3 seconds to see if auto-submit occurs...');
    await page.waitForTimeout(3000);

    // Check if we're still on the action page (no auto-submit)
    const currentUrl = page.url();
    if (currentUrl.includes('/action')) {
      console.log('✅ SUCCESS: No auto-submit detected! Still on action page.');
    } else {
      console.log('❌ FAILURE: Auto-submit occurred! Redirected to:', currentUrl);
    }

    console.log('📍 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

testAutoSubmitFix().catch(console.error);
