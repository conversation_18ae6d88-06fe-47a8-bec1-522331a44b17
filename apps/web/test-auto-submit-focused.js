// Focused auto-submit test - directly test the fix mechanism
const { chromium } = require('playwright');

async function testAutoSubmitFix() {
  console.log('🚀 Starting focused auto-submit test...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console logs to capture our debugging messages
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🚨') || text.includes('🎯') || text.includes('📍')) {
      console.log(`BROWSER: ${text}`);
    }
  });
  
  try {
    // Go directly to a bill's action page
    const billId = 'bd9c4dfb-a7b7-406d-ac41-263f36548c50'; // Affordable Housing Act
    console.log('📍 Navigating directly to action page...');
    await page.goto(`http://localhost:3000/bills/${billId}/action`);
    
    console.log('📍 Waiting for action page to load...');
    await page.waitForTimeout(3000);
    
    // Navigate through the first few steps quickly
    console.log('📍 Selecting stance (Support)...');
    await page.click('button:has-text("Support")');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Skip reasons step...');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Filling contact information...');
    await page.fill('input[name="first_name"]', 'Test');
    await page.fill('input[name="last_name"]', 'User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="address"]', '123 Main Street');
    await page.fill('input[name="city"]', 'Philadelphia');
    await page.fill('input[name="state"]', 'PA');
    await page.fill('input[name="zip_code"]', '19146');
    
    console.log('📍 Clicking Continue to start AI generation...');
    await page.click('button:has-text("Continue")');
    
    // Instead of waiting for AI generation, let's inject a custom message directly
    console.log('📍 🚨 SIMULATING DIRECT STEP TRANSITION TO EDIT_AND_SEND...');
    
    // Wait for the AI generation step to start
    await page.waitForTimeout(5000);
    
    // Inject JavaScript to manually set the step to edit_and_send and trigger the scenario
    await page.evaluate(() => {
      console.log('🚨 MANUAL TEST: Injecting edit_and_send step transition...');
      
      // Find the form and trigger setValue to simulate the problematic scenario
      const customMessageTextarea = document.querySelector('textarea[name="custom_message"]');
      if (customMessageTextarea) {
        // Simulate React Hook Form setValue call
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, 'value').set;
        nativeInputValueSetter.call(customMessageTextarea, 'Test message for auto-submit detection');
        
        // Trigger the events that React Hook Form would trigger
        customMessageTextarea.dispatchEvent(new Event('input', { bubbles: true }));
        customMessageTextarea.dispatchEvent(new Event('change', { bubbles: true }));
      }
    });
    
    console.log('📍 🚨 MONITORING FOR AUTO-SUBMIT BEHAVIOR...');
    
    // Monitor the page for 10 seconds to see if auto-submit occurs
    let autoSubmitDetected = false;
    const startUrl = page.url();
    
    for (let i = 1; i <= 10; i++) {
      await page.waitForTimeout(1000);
      const currentUrl = page.url();
      const stillOnAction = currentUrl.includes('/action');
      
      console.log(`📍 Second ${i}: Still on action page: ${stillOnAction}, URL: ${currentUrl}`);
      
      if (!stillOnAction && currentUrl !== startUrl) {
        console.log('❌❌❌ AUTO-SUBMIT DETECTED! Redirected to:', currentUrl);
        autoSubmitDetected = true;
        break;
      }
    }
    
    if (!autoSubmitDetected) {
      console.log('✅✅✅ SUCCESS: No auto-submit detected! Fix is working!');
      
      // Check if we can manually verify the blocking mechanism
      try {
        const result = await page.evaluate(() => {
          const form = document.querySelector('form');
          if (form) {
            // Check if there's evidence of the blocking mechanism
            return {
              hasForm: true,
              formAction: form.action,
              submitButtons: document.querySelectorAll('button[type="submit"]').length
            };
          }
          return { hasForm: false };
        });
        
        console.log('📍 Form state:', result);
      } catch (e) {
        console.log('📍 Could not check form state:', e.message);
      }
    }
    
    console.log('📍 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    console.log('📍 Browser left open for manual inspection.');
    // Don't close browser so we can inspect
  }
}

testAutoSubmitFix().catch(console.error);