// Complete test of the auto-submit fix
const { chromium } = require('playwright');

async function testCompleteFlow() {
  console.log('🚀 Starting complete auto-submit fix test...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console logs to capture our debugging messages
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🚨') || text.includes('🎯') || text.includes('📍')) {
      console.log(`BROWSER: ${text}`);
    }
  });
  
  try {
    // Go directly to a bill's action page
    const billId = 'bd9c4dfb-a7b7-406d-ac41-263f36548c50'; // Affordable Housing Act
    console.log('📍 Navigating directly to action page...');
    await page.goto(`http://localhost:3001/bills/${billId}/action`);
    
    console.log('📍 Waiting for action page to load...');
    await page.waitForTimeout(3000);
    
    console.log('📍 Step 1: Selecting stance (Needs Changes)...');
    await page.click('button:has-text("Needs Changes")');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Step 2: Waiting for reasons step...');
    await page.waitForTimeout(2000);
    
    // Select a reason checkbox
    console.log('📍 Step 2: Selecting a reason...');
    await page.click('input[type="checkbox"]');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Step 3: Waiting for contact step...');
    await page.waitForTimeout(2000);
    
    // Fill in zip code
    console.log('📍 Step 3: Filling zip code...');
    await page.fill('input[name="zip_code"]', '19146');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Step 4: Waiting for AI generation...');
    await page.waitForTimeout(5000);
    
    // Wait for the edit_and_send step
    console.log('📍 Waiting for Edit & Send step...');
    await page.waitForSelector('text=Edit & Send', { timeout: 60000 });
    
    console.log('📍 🚨 CRITICAL: Now at edit_and_send step - watching for auto-submit...');
    
    // Wait for the blocking period and monitor for auto-submit
    console.log('📍 Waiting 2 seconds to see if auto-submit occurs...');
    await page.waitForTimeout(2000);
    
    // Check if we're still on the action page (no auto-submit)
    const currentUrl = page.url();
    console.log(`📍 Current URL: ${currentUrl}`);
    
    if (currentUrl.includes('/action')) {
      console.log('✅ SUCCESS: No auto-submit detected! Still on action page.');
      
      // Check the submit button state
      const submitButton = page.locator('button[type="submit"]:has-text("Send Messages")');
      const isEnabled = await submitButton.isEnabled();
      const buttonText = await submitButton.textContent();
      
      console.log(`📍 Submit button enabled: ${isEnabled}`);
      console.log(`📍 Submit button text: "${buttonText}"`);
      
      if (buttonText.includes('Loading...')) {
        console.log('✅ GOOD: Button shows "Loading..." during blocking period');
      } else if (buttonText.includes('Send Messages')) {
        console.log('✅ GOOD: Button shows "Send Messages" after blocking period');
      } else {
        console.log('❌ UNEXPECTED: Button text is unexpected');
      }
      
      // Wait a bit more to ensure no delayed auto-submit
      console.log('📍 Waiting 3 more seconds to ensure no delayed auto-submit...');
      await page.waitForTimeout(3000);
      
      const finalUrl = page.url();
      if (finalUrl.includes('/action')) {
        console.log('✅ FINAL SUCCESS: No auto-submit occurred! Fix is working!');
      } else {
        console.log('❌ DELAYED FAILURE: Auto-submit occurred after delay!');
      }
      
    } else {
      console.log('❌ FAILURE: Auto-submit occurred! Redirected to:', currentUrl);
    }
    
    console.log('📍 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    // Keep browser open for inspection
    console.log('📍 Browser left open for inspection. Close manually when done.');
  }
}

testCompleteFlow().catch(console.error);
