// Quick test to verify the explicit click fix
const { chromium } = require('playwright');

async function testExplicitClickFix() {
  console.log('🚀 Testing explicit click fix...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console logs to capture our debugging messages
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🚨') || text.includes('🎯') || text.includes('📍')) {
      console.log(`BROWSER: ${text}`);
    }
  });
  
  try {
    // Go directly to a bill's action page
    const billId = 'bd9c4dfb-a7b7-406d-ac41-263f36548c50';
    console.log('📍 Navigating to action page...');
    await page.goto(`http://localhost:3000/bills/${billId}/action`);
    
    console.log('📍 Waiting for page load...');
    await page.waitForTimeout(2000);
    
    // Quick navigation to edit_and_send step
    console.log('📍 Support...');
    await page.click('button:has-text("Support")');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Skip reasons...');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Fill contact info...');
    await page.fill('input[name="first_name"]', 'Test');
    await page.fill('input[name="last_name"]', 'User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="address"]', '123 Main St');
    await page.fill('input[name="city"]', 'New York');
    await page.fill('input[name="state"]', 'NY');
    await page.fill('input[name="zip_code"]', '10002');
    
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Waiting for edit_and_send step...');
    await page.waitForSelector('text=Edit & Send', { timeout: 90000 });
    
    console.log('📍 🚨🚨🚨 NOW AT EDIT_AND_SEND - MONITORING FOR AUTO-SUBMIT...');
    
    // Wait 5 seconds and check if form auto-submits
    let autoSubmitDetected = false;
    for (let i = 1; i <= 5; i++) {
      await page.waitForTimeout(1000);
      const currentUrl = page.url();
      const stillOnAction = currentUrl.includes('/action');
      
      console.log(`📍 Second ${i}: Still on action page: ${stillOnAction}`);
      
      if (!stillOnAction) {
        console.log('❌❌❌ AUTO-SUBMIT DETECTED!');
        autoSubmitDetected = true;
        break;
      }
    }
    
    if (!autoSubmitDetected) {
      console.log('✅✅✅ SUCCESS: No auto-submit detected for 5 seconds!');
      console.log('📍 Now testing manual click...');
      
      // Try to click the submit button manually
      await page.click('button[type="submit"]');
      
      // Wait a moment to see if it processes
      await page.waitForTimeout(2000);
      
      console.log('📍 Manual click test completed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

testExplicitClickFix().catch(console.error);