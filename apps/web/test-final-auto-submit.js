// Final auto-submit test - simplified approach
const { chromium } = require('playwright');

async function testFinalAutoSubmit() {
  console.log('🚀 Starting final auto-submit test...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console logs to capture our debugging messages
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🚨') || text.includes('🎯') || text.includes('📍')) {
      console.log(`BROWSER: ${text}`);
    }
  });
  
  try {
    // Go directly to a bill's action page
    const billId = 'bd9c4dfb-a7b7-406d-ac41-263f36548c50'; // Affordable Housing Act
    console.log('📍 Navigating directly to action page...');
    await page.goto(`http://localhost:3000/bills/${billId}/action`);
    
    console.log('📍 Waiting for action page to load...');
    await page.waitForTimeout(3000);
    
    console.log('📍 Selecting stance (Needs Changes)...');
    await page.click('button:has-text("Needs Changes")');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Waiting for contact form...');
    await page.waitForTimeout(2000);
    
    console.log('📍 Filling contact information...');
    await page.fill('input[name="first_name"]', 'Test');
    await page.fill('input[name="last_name"]', 'User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="address"]', '123 Main Street');
    await page.fill('input[name="city"]', 'Philadelphia');
    await page.fill('input[name="state"]', 'PA');
    await page.fill('input[name="zip_code"]', '19146');
    
    console.log('📍 Clicking Continue to start AI generation...');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 🚨 WAITING FOR AI GENERATION AND EDIT_AND_SEND STEP...');
    console.log('📍 This may take 30-90 seconds...');
    
    // Wait for the edit_and_send step
    await page.waitForSelector('text=Edit & Send', { timeout: 120000 });
    
    console.log('📍 🚨🚨🚨 CRITICAL: NOW AT EDIT_AND_SEND STEP! 🚨🚨🚨');
    console.log('📍 🚨 MONITORING FOR AUTO-SUBMIT IN NEXT 10 SECONDS...');
    
    // Monitor for auto-submit every second for 10 seconds
    let autoSubmitDetected = false;
    for (let i = 1; i <= 10; i++) {
      await page.waitForTimeout(1000);
      const currentUrl = page.url();
      const stillOnAction = currentUrl.includes('/action');
      
      console.log(`📍 Second ${i}: Still on action page: ${stillOnAction}`);
      
      if (!stillOnAction) {
        console.log('❌❌❌ AUTO-SUBMIT DETECTED! Redirected to:', currentUrl);
        autoSubmitDetected = true;
        break;
      }
    }
    
    if (!autoSubmitDetected) {
      console.log('✅✅✅ SUCCESS: No auto-submit detected! Fix is working!');
      
      // Check button state
      try {
        const submitButton = page.locator('button[type="submit"]');
        const buttonText = await submitButton.textContent();
        const isEnabled = await submitButton.isEnabled();
        
        console.log(`📍 Submit button: "${buttonText}" enabled=${isEnabled}`);
        
        if (buttonText.includes('Send Messages') && isEnabled) {
          console.log('✅ PERFECT: Button is ready for user interaction!');
        }
      } catch (e) {
        console.log('📍 Could not check button state:', e.message);
      }
    }
    
    console.log('📍 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    console.log('📍 Browser left open for manual inspection.');
    // Don't close browser so we can inspect
  }
}

testFinalAutoSubmit().catch(console.error);
