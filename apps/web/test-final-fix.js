// Test the final fix - form submission completely blocked
const { chromium } = require('playwright');

async function testFinalFix() {
  console.log('🚀 Testing FINAL fix - form submission should be completely blocked...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console logs
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🚨') || text.includes('🎯') || text.includes('📍')) {
      console.log(`BROWSER: ${text}`);
    }
  });
  
  try {
    console.log('📍 Navigating to bills page to get a real bill ID...');
    await page.goto('http://localhost:3000/bills');
    
    // Wait for bills to load and get a real bill
    await page.waitForTimeout(5000);
    
    // Look for "Take Action" buttons
    const actionButtons = await page.$$('text=Take Action');
    if (actionButtons.length === 0) {
      console.log('❌ No bills loaded or no Take Action buttons found');
      return;
    }
    
    console.log(`📍 Found ${actionButtons.length} Take Action buttons`);
    console.log('📍 Clicking first Take Action button...');
    
    await actionButtons[0].click();
    
    console.log('📍 Waiting for action page to load...');
    await page.waitForTimeout(3000);
    
    // Check if we're on an action page
    const currentUrl = page.url();
    console.log('📍 Current URL:', currentUrl);
    
    if (!currentUrl.includes('/action')) {
      console.log('❌ Not on action page');
      return;
    }
    
    console.log('📍 ✅ On action page! Monitoring for auto-submit during step transitions...');
    
    // Monitor for 10 seconds to see if any auto-submit happens
    let autoSubmitDetected = false;
    for (let i = 1; i <= 10; i++) {
      await page.waitForTimeout(1000);
      const stillOnAction = page.url().includes('/action');
      
      console.log(`📍 Second ${i}: Still on action page: ${stillOnAction}`);
      
      if (!stillOnAction) {
        console.log('❌❌❌ AUTO-SUBMIT DETECTED!');
        autoSubmitDetected = true;
        break;
      }
    }
    
    if (!autoSubmitDetected) {
      console.log('✅✅✅ SUCCESS: No auto-submit detected for 10 seconds!');
      console.log('📍 Fix appears to be working!');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

testFinalFix().catch(console.error);