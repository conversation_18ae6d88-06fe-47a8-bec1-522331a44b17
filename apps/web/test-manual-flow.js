// Manual test - just get to edit_and_send step and observe
const { chromium } = require('playwright');

async function testManualFlow() {
  console.log('🚀 Starting manual flow test...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console logs to capture our debugging messages
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('🚨') || text.includes('🎯') || text.includes('📍')) {
      console.log(`BROWSER: ${text}`);
    }
  });
  
  try {
    // Go directly to a bill's action page
    const billId = 'bd9c4dfb-a7b7-406d-ac41-263f36548c50'; // Affordable Housing Act
    console.log('📍 Navigating directly to action page...');
    await page.goto(`http://localhost:3001/bills/${billId}/action`);
    
    console.log('📍 Waiting for action page to load...');
    await page.waitForTimeout(3000);
    
    console.log('📍 Step 1: Selecting stance (Needs Changes)...');
    await page.click('button:has-text("Needs Changes")');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Step 2: Waiting for reasons step...');
    await page.waitForTimeout(3000);
    
    // Check what's available on the reasons step
    console.log('📍 Checking reasons step content...');
    const allInputs = await page.locator('input').all();
    console.log(`Found ${allInputs.length} input elements`);
    
    for (let i = 0; i < allInputs.length; i++) {
      const input = allInputs[i];
      const type = await input.getAttribute('type');
      const name = await input.getAttribute('name');
      const isVisible = await input.isVisible();
      console.log(`Input ${i}: type="${type}" name="${name}" visible=${isVisible}`);
    }
    
    // Try to find any clickable element to proceed
    const buttons = await page.locator('button').all();
    console.log(`Found ${buttons.length} buttons on reasons step`);
    
    for (let i = 0; i < buttons.length; i++) {
      const button = buttons[i];
      const text = await button.textContent();
      const isVisible = await button.isVisible();
      console.log(`Button ${i}: "${text}" visible=${isVisible}`);
    }
    
    // Try to click Continue without selecting anything
    console.log('📍 Trying to click Continue...');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Step 3: Waiting for contact step...');
    await page.waitForTimeout(3000);
    
    // Fill in zip code
    console.log('📍 Step 3: Filling zip code...');
    await page.fill('input[name="zip_code"]', '19146');
    await page.click('button:has-text("Continue")');
    
    console.log('📍 Step 4: Waiting for AI generation...');
    console.log('📍 This may take 30-60 seconds...');
    
    // Wait for the edit_and_send step
    console.log('📍 Waiting for Edit & Send step...');
    await page.waitForSelector('text=Edit & Send', { timeout: 90000 });
    
    console.log('📍 🚨 CRITICAL: Now at edit_and_send step!');
    console.log('📍 🚨 WATCHING FOR AUTO-SUBMIT IN NEXT 5 SECONDS...');
    
    // Monitor for auto-submit
    for (let i = 1; i <= 5; i++) {
      await page.waitForTimeout(1000);
      const currentUrl = page.url();
      console.log(`📍 Second ${i}: Still on action page: ${currentUrl.includes('/action')}`);
      
      if (!currentUrl.includes('/action')) {
        console.log('❌ AUTO-SUBMIT DETECTED! Redirected to:', currentUrl);
        break;
      }
    }
    
    const finalUrl = page.url();
    if (finalUrl.includes('/action')) {
      console.log('✅ SUCCESS: No auto-submit detected! Fix is working!');
      
      // Check button state
      const submitButton = page.locator('button[type="submit"]');
      const buttonText = await submitButton.textContent();
      const isEnabled = await submitButton.isEnabled();
      
      console.log(`📍 Submit button: "${buttonText}" enabled=${isEnabled}`);
    } else {
      console.log('❌ FAILURE: Auto-submit occurred!');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    console.log('📍 Test completed. Browser left open for inspection.');
  }
}

testManualFlow().catch(console.error);
