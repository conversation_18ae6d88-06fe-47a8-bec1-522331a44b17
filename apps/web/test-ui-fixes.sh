#!/bin/bash

# Script to test UI fixes for bill cards and modal

echo "🔧 Testing UI fixes for ModernAction.io"
echo "======================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Not in the web app directory. Please run from apps/web/"
    exit 1
fi

echo "✅ In correct directory: $(pwd)"

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "❌ Error: node_modules not found. Please run 'npm install' first."
    exit 1
fi

echo "✅ Dependencies installed"

# Start the development server
echo "🚀 Starting development server..."
echo "   This will start the Next.js dev server on http://localhost:3000"
echo "   Navigate to /bills to test the bill cards"
echo ""
echo "🔍 What to test:"
echo "   1. Bill cards should not be cut off - content should be fully visible"
echo "   2. Click 'Take Action' on any bill card"
echo "   3. ✨ NEW: Bill details should be visible immediately (no dropdown needed)"
echo "   4. 🔥 CRITICAL: Scroll down in the modal - you should see yellow test content"
echo "   5. 🔥 CRITICAL: Support/Oppose/Amend buttons should stay at bottom while scrolling"
echo "   6. Look for yellow 'Test Scrolling Content' section at bottom"
echo "   7. Verify the modal layout: [Header] [Scrollable Content] [Fixed Buttons]"
echo ""
echo "Press Ctrl+C to stop the server when done testing"
echo ""

# Start the dev server
npm run dev
