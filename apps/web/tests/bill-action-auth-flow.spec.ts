import { test, expect } from '@playwright/test';

test.describe('Bill Action Modal - Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should show optional authentication for non-authenticated users', async ({ page }) => {
    // Find and click on a bill to open the action modal
    const billCard = page.locator('[data-testid="bill-card"]').first();
    await billCard.click();

    // Wait for modal to open
    await expect(page.locator('[data-testid="bill-action-modal"]')).toBeVisible();

    // Step 1: Choose stance
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Next")').click();

    // Step 2: Add reasons (can skip)
    await page.locator('button:has-text("Next")').click();

    // Step 3: Should show OPTIONAL authentication
    await expect(page.locator('h4:has-text("Sign in for a better experience (Optional)")')).toBeVisible();
    await expect(page.locator('text=Sign in to save your preferences')).toBeVisible();
    await expect(page.locator('button:has-text("Sign In / Sign Up")')).toBeVisible();
    await expect(page.locator('button:has-text("Continue as Guest")')).toBeVisible();

    // Should not show AI generation yet
    await expect(page.locator('text=Writing your personalized letter')).not.toBeVisible();
  });

  test('should proceed directly to AI generation for authenticated users', async ({ page }) => {
    // Mock authenticated user
    await page.addInitScript(() => {
      // Mock Auth0 user
      window.__AUTH0_USER__ = {
        email: '<EMAIL>',
        user_metadata: {
          zip_code: '90210'
        }
      };
    });
    
    // Find and click on a bill to open the action modal
    const billCard = page.locator('[data-testid="bill-card"]').first();
    await billCard.click();
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="bill-action-modal"]')).toBeVisible();
    
    // Step 1: Choose stance
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Next")').click();
    
    // Step 2: Add reasons (can skip)
    await page.locator('button:has-text("Next")').click();
    
    // Step 3: Should go directly to AI generation (skip auth step)
    await expect(page.locator('h4:has-text("Writing your personalized letter")')).toBeVisible();
    await expect(page.locator('text=Your Address')).toBeVisible();
    await expect(page.locator('text=ZIP Code: 90210')).toBeVisible();
    await expect(page.locator('button:has-text("Change Address")')).toBeVisible();
    
    // Should not show authentication step
    await expect(page.locator('text=Sign in to continue')).not.toBeVisible();
  });

  test('should show AI progress and allow address editing during generation', async ({ page }) => {
    // Mock authenticated user
    await page.addInitScript(() => {
      window.__AUTH0_USER__ = {
        email: '<EMAIL>',
        user_metadata: {
          zip_code: '90210'
        }
      };
    });
    
    // Mock slow API response to test loading state
    await page.route('**/api/v1/actions/preview', async route => {
      // Delay response to test loading state
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'Test personalized message',
          representatives: [
            { name: 'Rep. Test', title: 'Representative', party: 'D' },
            { name: 'Sen. Example', title: 'Senator', party: 'R' }
          ]
        })
      });
    });
    
    // Navigate through steps to AI generation
    const billCard = page.locator('[data-testid="bill-card"]').first();
    await billCard.click();
    
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Next")').click();
    await page.locator('button:has-text("Next")').click();
    
    // Should show AI progress
    await expect(page.locator('text=AI is writing your personalized letter')).toBeVisible();
    await expect(page.locator('[data-testid="ai-progress-bar"]')).toBeVisible();
    
    // Should still be able to change address during loading
    await expect(page.locator('button:has-text("Change Address")')).toBeVisible();
    await expect(page.locator('button:has-text("Change Address")')).toBeEnabled();
    
    // Test address change functionality
    await page.locator('button:has-text("Change Address")').click();
    // Note: This would trigger a browser prompt in real usage
    // In tests, we'd need to mock this or use a different UI pattern
  });

  test('should show success message only after AI completes', async ({ page }) => {
    // Mock authenticated user
    await page.addInitScript(() => {
      window.__AUTH0_USER__ = {
        email: '<EMAIL>',
        user_metadata: {
          zip_code: '90210'
        }
      };
    });
    
    // Mock API response
    await page.route('**/api/v1/actions/preview', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'Test personalized message',
          representatives: [
            { name: 'Rep. Test', title: 'Representative', party: 'D' },
            { name: 'Sen. Example', title: 'Senator', party: 'R' }
          ]
        })
      });
    });
    
    // Navigate through steps
    const billCard = page.locator('[data-testid="bill-card"]').first();
    await billCard.click();
    
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Next")').click();
    await page.locator('button:has-text("Next")').click();
    
    // Wait for AI to complete
    await expect(page.locator('text=Letter complete! Found 2 representatives')).toBeVisible();
    await expect(page.locator('text=Your personalized message is ready for review')).toBeVisible();
    
    // Should be able to proceed to edit step
    await expect(page.locator('button:has-text("Review Letter")')).toBeEnabled();
    await page.locator('button:has-text("Review Letter")').click();
    
    // Should be in edit step
    await expect(page.locator('h4:has-text("Review and edit your letter")')).toBeVisible();
    await expect(page.locator('text=Your Representatives')).toBeVisible();
    await expect(page.locator('text=Rep. Test')).toBeVisible();
    await expect(page.locator('text=Sen. Example')).toBeVisible();
  });

  test('should include email and zip code fields in edit step', async ({ page }) => {
    // Mock complete flow to edit step
    await page.addInitScript(() => {
      window.__AUTH0_USER__ = {
        email: '<EMAIL>',
        user_metadata: {
          zip_code: '90210'
        }
      };
    });
    
    await page.route('**/api/v1/actions/preview', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'Test personalized message',
          representatives: [
            { name: 'Rep. Test', title: 'Representative', party: 'D' }
          ]
        })
      });
    });
    
    // Navigate to edit step
    const billCard = page.locator('[data-testid="bill-card"]').first();
    await billCard.click();
    
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Next")').click();
    await page.locator('button:has-text("Next")').click();
    
    // Wait for AI completion and proceed to edit
    await page.locator('button:has-text("Review Letter")').click();
    
    // Should have email field with user's email pre-filled
    await expect(page.locator('label:has-text("Your Email Address")')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toHaveValue('<EMAIL>');
    
    // Should have ZIP code field
    await expect(page.locator('label:has-text("ZIP Code")')).toBeVisible();
    await expect(page.locator('input[name="zip_code"]')).toHaveValue('90210');
    await expect(page.locator('button:has-text("Update Reps")')).toBeVisible();
    
    // Should have editable message
    await expect(page.locator('label:has-text("Your Letter")')).toBeVisible();
    await expect(page.locator('textarea[name="custom_message"]')).toBeVisible();
  });

  test('should allow guest users to continue without signing in', async ({ page }) => {
    // Mock API response for guest user flow
    await page.route('**/api/v1/actions/preview', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'Test personalized message for guest user',
          representatives: [
            { name: 'Rep. Test', title: 'Representative', party: 'D' },
            { name: 'Sen. Example', title: 'Senator', party: 'R' }
          ]
        })
      });
    });

    // Start as non-authenticated user
    const billCard = page.locator('[data-testid="bill-card"]').first();
    await billCard.click();

    // Fill out stance and reasons
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Next")').click();
    await page.locator('button:has-text("Next")').click();

    // Should be at optional auth step - choose to continue as guest
    await expect(page.locator('button:has-text("Continue as Guest")')).toBeVisible();
    await page.locator('button:has-text("Continue as Guest")').click();

    // Should now be at AI generation step with guest form
    await expect(page.locator('h4:has-text("Writing your personalized letter")')).toBeVisible();
    await expect(page.locator('label:has-text("Email Address")')).toBeVisible();
    await expect(page.locator('label:has-text("ZIP Code")')).toBeVisible();

    // Fill out email and ZIP to trigger AI
    await page.locator('input[type="email"]').fill('<EMAIL>');
    await page.locator('input[name="zip_code"]').fill('90210');

    // Should show AI progress and additional address fields
    await expect(page.locator('text=✓ Valid ZIP code - AI is writing your letter!')).toBeVisible();
    await expect(page.locator('h5:has-text("Complete Your Address")')).toBeVisible();
    await expect(page.locator('text=While we\'re writing your personalized letter')).toBeVisible();

    // Should show additional address fields
    await expect(page.locator('label:has-text("Street Address")')).toBeVisible();
    await expect(page.locator('label:has-text("City")')).toBeVisible();
    await expect(page.locator('label:has-text("State")')).toBeVisible();

    // Fill out complete address
    await page.locator('input[name="address"]').fill('123 Main St');
    await page.locator('input[name="city"]').fill('Beverly Hills');
    await page.locator('input[name="state"]').fill('CA');

    // Should be able to proceed once AI completes and address is filled
    await expect(page.locator('button:has-text("Review Letter")')).toBeEnabled();
  });

  test('should show parallel experience: AI generation + address completion for guests', async ({ page }) => {
    // Mock slow API to test parallel experience
    await page.route('**/api/v1/actions/preview', async route => {
      await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'Test personalized message',
          representatives: [
            { name: 'Rep. Test', title: 'Representative', party: 'D' }
          ]
        })
      });
    });

    const billCard = page.locator('[data-testid="bill-card"]').first();
    await billCard.click();

    // Navigate to guest flow
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Next")').click();
    await page.locator('button:has-text("Next")').click();
    await page.locator('button:has-text("Continue as Guest")').click();

    // Enter email and ZIP to start AI
    await page.locator('input[type="email"]').fill('<EMAIL>');
    await page.locator('input[name="zip_code"]').fill('90210');

    // Should immediately show AI progress AND address form
    await expect(page.locator('text=AI is writing your personalized letter')).toBeVisible();
    await expect(page.locator('h5:has-text("Complete Your Address")')).toBeVisible();

    // User can fill address while AI is working
    await page.locator('input[name="address"]').fill('123 Main St');
    await page.locator('input[name="city"]').fill('Beverly Hills');
    await page.locator('input[name="state"]').fill('CA');

    // Button should show "Complete Address Below" until both AI and address are done
    await expect(page.locator('button:has-text("Complete Address Below")')).toBeVisible();

    // Once AI completes, should be able to proceed
    await expect(page.locator('button:has-text("Review Letter")')).toBeEnabled({ timeout: 5000 });
  });
});
