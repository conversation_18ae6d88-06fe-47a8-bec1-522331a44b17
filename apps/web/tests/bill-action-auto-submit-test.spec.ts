import { test, expect } from '@playwright/test';

test.describe('Bill Action Auto-Submit Investigation', () => {
  test.beforeEach(async ({ page }) => {
    // Enable console logging to capture our debug messages
    page.on('console', msg => {
      console.log(`[${msg.type()}] ${msg.text()}`);
    });
    
    // Navigate to the bills page
    await page.goto('http://localhost:3000/bills');
  });

  test('should complete bill action flow without auto-submit', async ({ page }) => {
    // Wait for bills to load and check if we get the error
    try {
      await page.waitForSelector('.grid', { timeout: 10000 });
    } catch (error) {
      console.log('Bills page failed to load, checking for error message');
      const errorMessage = await page.locator('text=Failed to load bills').first();
      if (await errorMessage.isVisible()) {
        console.log('❌ Bills page showing "Failed to load bills" error');
        // Try to click refresh button
        const refreshButton = page.locator('button:has-text("Refresh")');
        if (await refreshButton.isVisible()) {
          await refreshButton.click();
          await page.waitForTimeout(3000);
        }
      }
    }

    // Look for any bill card to click on
    const billCards = page.locator('[data-testid="bill-card"], .bg-white:has(button:has-text("Take Action"))');
    await expect(billCards.first()).toBeVisible({ timeout: 15000 });
    
    // Click on the first bill's "Take Action" button
    const takeActionButton = billCards.first().locator('button:has-text("Take Action")');
    await takeActionButton.click();

    // Wait for the bill action page to load
    await expect(page.locator('h1:has-text("Take Action"), h2:has-text("Take Action")')).toBeVisible();
    
    // Step 1: Select stance
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Continue")').click();

    // Step 2: Select reasons
    // Click on at least one reason checkbox
    const reasonCheckboxes = page.locator('input[type="checkbox"]');
    if (await reasonCheckboxes.count() > 0) {
      await reasonCheckboxes.first().click();
    }
    await page.locator('button:has-text("Continue")').click();

    // Step 3: Fill contact information (as guest user)
    await page.fill('input[name="first_name"]', 'Test');
    await page.fill('input[name="last_name"]', 'User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="zip_code"]', '12345');
    await page.fill('input[name="address"]', '123 Test St');
    await page.fill('input[name="city"]', 'Test City');
    await page.fill('input[name="state"]', 'NY');
    
    await page.locator('button:has-text("Continue")').click();

    // Step 4: AI Generation step
    console.log('🎯 Reached AI Generation Step');
    
    // Wait for AI generation to start
    await expect(page.locator('text=Crafting Your Message')).toBeVisible();
    
    // Monitor for any unexpected form submissions during AI generation
    let formSubmittedDuringAI = false;
    page.on('response', response => {
      if (response.url().includes('/api/v1/actions/submit') && response.request().method() === 'POST') {
        console.log('🚨 DETECTED: Form submitted during AI generation!');
        formSubmittedDuringAI = true;
      }
    });

    // Wait for AI generation to complete (look for completion indicators)
    try {
      // Wait for either the edit step to appear OR a success message (which would indicate auto-submit)
      await Promise.race([
        page.waitForSelector('h2:has-text("Review & Send Your Message")', { timeout: 30000 }),
        page.waitForSelector('text=Successfully sent your message', { timeout: 30000 }),
        page.waitForSelector('text=Message ready!', { timeout: 30000 })
      ]);
      
      // Check if we were auto-redirected (indicating auto-submit occurred)
      const currentUrl = page.url();
      if (currentUrl.includes('/bills') && !currentUrl.includes('/action')) {
        console.log('🚨 AUTO-SUBMIT DETECTED: Redirected back to bills page without user action');
        expect(false, 'Form was auto-submitted during AI generation').toBe(true);
      }
      
      // Check if success message appeared (indicating auto-submit)
      const successMessage = page.locator('text=Successfully sent your message');
      if (await successMessage.isVisible()) {
        console.log('🚨 AUTO-SUBMIT DETECTED: Success message appeared without user clicking send');
        expect(false, 'Form was auto-submitted during AI generation').toBe(true);
      }
      
    } catch (error) {
      console.log('AI generation timed out or failed:', error);
    }

    // If we made it here without auto-submit, check if we're on the edit step
    const editStepTitle = page.locator('h2:has-text("Review & Send Your Message")');
    if (await editStepTitle.isVisible()) {
      console.log('✅ Successfully reached edit step without auto-submit');
      
      // Step 5: Test the edit step for auto-submit
      console.log('🎯 Testing edit step for auto-submit behavior');
      
      // Wait a few seconds to see if auto-submit occurs
      await page.waitForTimeout(5000);
      
      // Check if we're still on the edit step (not redirected)
      const stillOnEditStep = await editStepTitle.isVisible();
      if (!stillOnEditStep) {
        console.log('🚨 AUTO-SUBMIT DETECTED: Left edit step without user action');
        expect(false, 'Form was auto-submitted from edit step').toBe(true);
      }
      
      // Check if success message appeared without clicking send
      const successMessage = page.locator('text=Successfully sent your message');
      if (await successMessage.isVisible()) {
        console.log('🚨 AUTO-SUBMIT DETECTED: Success message appeared from edit step without user action');
        expect(false, 'Form was auto-submitted from edit step').toBe(true);
      }
      
      console.log('✅ Edit step passed auto-submit test - no automatic submission detected');
      
      // Now test manual submission
      const sendButton = page.locator('button:has-text("Send Messages")');
      await expect(sendButton).toBeVisible();
      
      console.log('🎯 Testing manual form submission');
      await sendButton.click();
      
      // Wait for success message or redirect
      await Promise.race([
        page.waitForSelector('text=Successfully sent your message', { timeout: 10000 }),
        page.waitForURL(/\/bills$/, { timeout: 10000 })
      ]);
      
      console.log('✅ Manual form submission successful');
      
    } else {
      console.log('❌ Did not reach edit step - possible auto-submit or other issue');
    }
  });

  test('API health check - verify bills endpoint works', async ({ page }) => {
    // Test the bills API directly
    const response = await page.request.get('http://localhost:8000/api/v1/bills?limit=5');
    console.log('Bills API status:', response.status());
    
    if (response.status() !== 200) {
      console.log('❌ Bills API is not working');
      const text = await response.text();
      console.log('Response:', text);
    } else {
      console.log('✅ Bills API is working');
      const data = await response.json();
      console.log(`Found ${data.length} bills`);
    }
  });

  test('Frontend bills page - detailed error diagnosis', async ({ page }) => {
    // Navigate to bills page and capture all network requests
    const requests = [];
    const responses = [];
    
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method()
      });
    });
    
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status(),
        statusText: response.statusText()
      });
    });
    
    await page.goto('http://localhost:3000/bills');
    
    // Wait for page to load
    await page.waitForTimeout(5000);
    
    console.log('=== Network Requests ===');
    requests.forEach(req => {
      console.log(`${req.method} ${req.url}`);
    });
    
    console.log('=== Network Responses ===');
    responses.forEach(res => {
      if (res.status >= 400) {
        console.log(`❌ ${res.status} ${res.statusText} - ${res.url}`);
      } else {
        console.log(`✅ ${res.status} - ${res.url}`);
      }
    });
    
    // Check what's actually displayed on the page
    const loadingText = page.locator('text=Loading bills');
    const errorText = page.locator('text=Failed to load bills');
    const noBillsText = page.locator('text=No bills available');
    const billCards = page.locator('.grid .bg-white');
    
    if (await loadingText.isVisible()) {
      console.log('📍 Page shows: Loading bills...');
    } else if (await errorText.isVisible()) {
      console.log('❌ Page shows: Failed to load bills');
    } else if (await noBillsText.isVisible()) {
      console.log('📍 Page shows: No bills available');
    } else if (await billCards.count() > 0) {
      const count = await billCards.count();
      console.log(`✅ Page shows: ${count} bill cards`);
    } else {
      console.log('❓ Page shows: Unknown state');
      const pageContent = await page.textContent('body');
      console.log('Page content preview:', pageContent.substring(0, 500));
    }
  });
});