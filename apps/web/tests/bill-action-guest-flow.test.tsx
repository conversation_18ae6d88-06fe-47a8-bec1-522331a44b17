import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BillActionModal } from '../src/components/shared/BillActionModal';
import { useUser } from '@auth0/nextjs-auth0/client';

// Mock Auth0
jest.mock('@auth0/nextjs-auth0/client');
const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

// Mock API calls
global.fetch = jest.fn();

const mockBill = {
  id: 'test-bill-1',
  title: 'Test Bill',
  summary: 'A test bill for testing',
  status: 'active',
  chamber: 'house'
};

describe('BillActionModal - Guest User Flow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default to non-authenticated user
    mockUseUser.mockReturnValue({
      user: null,
      error: null,
      isLoading: false
    });
  });

  test('should show optional authentication step for guest users', () => {
    render(
      <BillActionModal 
        isOpen={true} 
        onClose={() => {}} 
        bill={mockBill} 
      />
    );

    // Should show stance selection initially
    expect(screen.getByText('What is your position on this bill?')).toBeInTheDocument();
  });

  test('should allow guest users to continue without signing in', async () => {
    render(
      <BillActionModal 
        isOpen={true} 
        onClose={() => {}} 
        bill={mockBill} 
      />
    );

    // Navigate through steps
    fireEvent.click(screen.getByText('Support'));
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Next'));

    // Should show optional auth
    await waitFor(() => {
      expect(screen.getByText(/Sign in for a better experience \(Optional\)/)).toBeInTheDocument();
    });

    // Should have "Continue as Guest" button
    expect(screen.getByText('Continue as Guest')).toBeInTheDocument();
    
    // Click continue as guest
    fireEvent.click(screen.getByText('Continue as Guest'));

    // Should show AI generation step with guest form
    await waitFor(() => {
      expect(screen.getByText('Writing your personalized letter')).toBeInTheDocument();
      expect(screen.getByLabelText(/Email Address/)).toBeInTheDocument();
      expect(screen.getByLabelText(/ZIP Code/)).toBeInTheDocument();
    });
  });

  test('should show address fields after AI starts for guest users', async () => {
    // Mock API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        message: 'Test message',
        representatives: [{ name: 'Rep. Test', title: 'Representative', party: 'D' }]
      })
    });

    render(
      <BillActionModal 
        isOpen={true} 
        onClose={() => {}} 
        bill={mockBill} 
      />
    );

    // Navigate to guest AI generation
    fireEvent.click(screen.getByText('Support'));
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Continue as Guest'));

    // Fill email and ZIP to trigger AI
    const emailInput = screen.getByLabelText(/Email Address/);
    const zipInput = screen.getByLabelText(/ZIP Code/);
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(zipInput, { target: { value: '90210' } });

    // Should show additional address fields after AI starts
    await waitFor(() => {
      expect(screen.getByText('Complete Your Address')).toBeInTheDocument();
      expect(screen.getByText(/While we're writing your personalized letter/)).toBeInTheDocument();
    });

    // Should show street address, city, state fields
    expect(screen.getByLabelText(/Street Address/)).toBeInTheDocument();
    expect(screen.getByLabelText(/City/)).toBeInTheDocument();
    expect(screen.getByLabelText(/State/)).toBeInTheDocument();
  });

  test('should require complete address before proceeding for guest users', async () => {
    // Mock API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        message: 'Test message',
        representatives: [{ name: 'Rep. Test', title: 'Representative', party: 'D' }]
      })
    });

    render(
      <BillActionModal 
        isOpen={true} 
        onClose={() => {}} 
        bill={mockBill} 
      />
    );

    // Navigate to guest AI generation and fill initial fields
    fireEvent.click(screen.getByText('Support'));
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Continue as Guest'));

    const emailInput = screen.getByLabelText(/Email Address/);
    const zipInput = screen.getByLabelText(/ZIP Code/);
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(zipInput, { target: { value: '90210' } });

    // Wait for AI to complete and address fields to appear
    await waitFor(() => {
      expect(screen.getByText('Complete Your Address')).toBeInTheDocument();
    });

    // Button should be disabled until address is complete
    const reviewButton = screen.getByRole('button', { name: /Complete Address Below|Review Letter/ });
    expect(reviewButton).toBeDisabled();

    // Fill complete address
    fireEvent.change(screen.getByLabelText(/Street Address/), { target: { value: '123 Main St' } });
    fireEvent.change(screen.getByLabelText(/City/), { target: { value: 'Beverly Hills' } });
    fireEvent.change(screen.getByLabelText(/State/), { target: { value: 'CA' } });

    // Button should now be enabled
    await waitFor(() => {
      expect(screen.getByText('Review Letter')).toBeEnabled();
    });
  });

  test('should work differently for authenticated users', () => {
    // Mock authenticated user
    mockUseUser.mockReturnValue({
      user: {
        email: '<EMAIL>',
        user_metadata: {
          zip_code: '90210'
        }
      },
      error: null,
      isLoading: false
    });

    render(
      <BillActionModal 
        isOpen={true} 
        onClose={() => {}} 
        bill={mockBill} 
      />
    );

    // Navigate through steps
    fireEvent.click(screen.getByText('Support'));
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Next'));

    // Should skip auth step and go directly to AI generation
    expect(screen.getByText('Writing your personalized letter')).toBeInTheDocument();
    
    // Should show saved address, not form fields
    expect(screen.getByText('Your Address')).toBeInTheDocument();
    expect(screen.getByText('ZIP Code: 90210')).toBeInTheDocument();
    expect(screen.getByText('Change Address')).toBeInTheDocument();
    
    // Should NOT show email/ZIP input fields
    expect(screen.queryByLabelText(/Email Address/)).not.toBeInTheDocument();
    expect(screen.queryByLabelText(/ZIP Code \*/)).not.toBeInTheDocument();
  });
});
