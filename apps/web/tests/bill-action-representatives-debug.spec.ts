import { test, expect, Page } from '@playwright/test';

test.describe('Bill Action Representatives Debug', () => {
  test('Debug representative lookup and AI generation flow', async ({ page }) => {
    // Enable console logging
    page.on('console', msg => console.log(`Browser: ${msg.text()}`));
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log(`API Response: ${response.status()} ${response.url()}`);
      }
    });
    page.on('requestfailed', request => {
      console.log(`Failed request: ${request.url()} - ${request.failure()?.errorText}`);
    });

    // Navigate to bills page
    await page.goto('http://localhost:3000/bills');
    await page.waitForLoadState('networkidle');

    // Find and click on the first bill's "Take Action" button
    const takeActionButton = page.locator('a[href*="/bills/"][href*="/action"]').first();
    await expect(takeActionButton).toBeVisible();
    await takeActionButton.click();

    // Wait for the bill action page to load
    await page.waitForLoadState('networkidle');
    await expect(page.locator('h1:has-text("Take Action")')).toBeVisible();

    // Step 1: Select a stance
    console.log('Step 1: Selecting stance...');
    const supportButton = page.locator('button:has-text("Support")');
    await expect(supportButton).toBeVisible();
    await supportButton.click();

    // Step 2: Continue to reasons (or skip)
    console.log('Step 2: Moving to reasons...');
    const continueButton = page.locator('button:has-text("Continue")');
    await continueButton.click();
    await page.waitForTimeout(500);

    // Step 3: Continue to contact info
    console.log('Step 3: Moving to contact...');
    await continueButton.click();
    await page.waitForTimeout(500);

    // Step 4: Fill out contact information
    console.log('Step 4: Filling contact info...');
    await page.fill('input[name="first_name"]', 'Test');
    await page.fill('input[name="last_name"]', 'User'); 
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="zip_code"]', '60302');
    await page.fill('input[name="address"]', '123 Test St');
    await page.fill('input[name="city"]', 'Oak Park');
    await page.fill('input[name="state"]', 'IL');

    // Step 5: Continue to AI generation
    console.log('Step 5: Moving to AI generation...');
    await continueButton.click();

    // Wait for AI generation to start
    await page.waitForTimeout(1000);

    // Monitor network requests during AI generation
    const previewPromise = page.waitForResponse(response => 
      response.url().includes('/api/v1/actions/preview-message') && response.status() === 200
    );

    // Also check for direct officials lookup
    const officialsPromise = page.waitForResponse(response => 
      response.url().includes('/api/v1/officials/lookup') && response.status() === 200
    );

    // Wait for AI generation to complete or fail
    try {
      console.log('Waiting for preview message API call...');
      const previewResponse = await Promise.race([
        previewPromise,
        page.waitForTimeout(30000).then(() => null)
      ]);

      if (previewResponse) {
        const previewData = await previewResponse.json();
        console.log('Preview message response:', JSON.stringify(previewData, null, 2));
        
        // Check if we got real representatives
        if (previewData.representatives) {
          console.log('Representatives found:');
          previewData.representatives.forEach((rep: any, index: number) => {
            console.log(`  ${index + 1}. ${rep.full_name || rep.name} (${rep.party}) - ${rep.title}`);
          });
          
          // Check if we're still getting dummy data
          const isDummyData = previewData.representatives.some((rep: any) => 
            (rep.full_name || rep.name || '').includes('IL One') || 
            (rep.full_name || rep.name || '').includes('IL Two') ||
            (rep.full_name || rep.name || '').includes('IL District')
          );
          
          if (isDummyData) {
            console.error('❌ Still getting dummy representative data!');
          } else {
            console.log('✅ Got real representative data!');
          }
        }
      } else {
        console.error('❌ Preview message API call timed out');
      }

      // Also check officials lookup directly
      try {
        console.log('Testing direct officials lookup...');
        const officialsResponse = await page.request.get('http://localhost:8000/api/v1/officials/lookup?zip_code=60302');
        const officialsData = await officialsResponse.json();
        console.log('Direct officials lookup response:', JSON.stringify(officialsData, null, 2));
      } catch (error) {
        console.error('Direct officials lookup failed:', error);
      }

      // Test database officials endpoint
      try {
        console.log('Testing database officials endpoint...');
        const dbOfficialsResponse = await page.request.get('http://localhost:8000/api/v1/officials/by-zip/60302');
        const dbOfficialsData = await dbOfficialsResponse.json();
        console.log('Database officials response:', JSON.stringify(dbOfficialsData, null, 2));
      } catch (error) {
        console.error('Database officials lookup failed:', error);
      }

    } catch (error) {
      console.error('Error during AI generation:', error);
    }

    // Wait for the UI to update
    await page.waitForTimeout(5000);

    // Check if we reached the edit and send step
    const editStep = page.locator('h2:has-text("Review & Send Your Message")');
    if (await editStep.isVisible()) {
      console.log('✅ Reached edit and send step');
      
      // Check the representatives shown in the UI
      const repCards = page.locator('[data-testid="representative-card"], .bg-white.rounded-lg.p-4');
      const repCount = await repCards.count();
      console.log(`Found ${repCount} representative cards in UI`);
      
      for (let i = 0; i < repCount; i++) {
        const repCard = repCards.nth(i);
        const repName = await repCard.locator('p.font-medium').textContent();
        const repInfo = await repCard.locator('p.text-sm').textContent();
        console.log(`Representative ${i + 1}: ${repName} - ${repInfo}`);
      }
    } else {
      console.log('❌ Did not reach edit and send step');
      
      // Check for error messages
      const errorMessages = await page.locator('[role="alert"], .text-red-600, .bg-red-50').allTextContents();
      if (errorMessages.length > 0) {
        console.log('Error messages found:', errorMessages);
      }
    }

    // Take a screenshot for debugging
    await page.screenshot({ path: 'debug-representatives.png', fullPage: true });
    console.log('Screenshot saved as debug-representatives.png');
  });

  test('Test API endpoints directly', async ({ page }) => {
    // Test the API endpoints directly to see what they return
    
    console.log('=== Testing API Endpoints Directly ===');
    
    // Test 1: Officials lookup endpoint
    try {
      const lookupResponse = await page.request.get('http://localhost:8000/api/v1/officials/lookup?zip_code=60302');
      console.log(`Officials lookup status: ${lookupResponse.status()}`);
      if (lookupResponse.ok()) {
        const lookupData = await lookupResponse.json();
        console.log('Officials lookup data:', JSON.stringify(lookupData, null, 2));
      } else {
        const errorText = await lookupResponse.text();
        console.log('Officials lookup error:', errorText);
      }
    } catch (error) {
      console.error('Officials lookup request failed:', error);
    }

    // Test 2: Database officials endpoint  
    try {
      const dbResponse = await page.request.get('http://localhost:8000/api/v1/officials/by-zip/60302');
      console.log(`Database officials status: ${dbResponse.status()}`);
      if (dbResponse.ok()) {
        const dbData = await dbResponse.json();
        console.log('Database officials data:', JSON.stringify(dbData, null, 2));
      } else {
        const errorText = await dbResponse.text();
        console.log('Database officials error:', errorText);
      }
    } catch (error) {
      console.error('Database officials request failed:', error);
    }

    // Test 3: Preview message endpoint with sample data
    try {
      const previewData = {
        bill_id: "test-bill-id",
        stance: "support", 
        selected_reasons: ["Economic benefits"],
        custom_reasons: [],
        personal_stories: "",
        first_name: "Test",
        last_name: "User",
        zip_code: "60302"
      };

      console.log('Testing preview message with data:', previewData);
      
      const previewResponse = await page.request.post('http://localhost:8000/api/v1/actions/preview-message', {
        data: previewData,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`Preview message status: ${previewResponse.status()}`);
      if (previewResponse.ok()) {
        const responseData = await previewResponse.json();
        console.log('Preview message data:', JSON.stringify(responseData, null, 2));
      } else {
        const errorText = await previewResponse.text();
        console.log('Preview message error:', errorText);
      }
    } catch (error) {
      console.error('Preview message request failed:', error);
    }
  });
});