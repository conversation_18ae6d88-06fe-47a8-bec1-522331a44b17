import { test, expect } from '@playwright/test';

test.describe('Bill Cards UI Issues', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to bills page
    await page.goto('/bills');
    
    // Wait for bills to load
    await page.waitForSelector('[data-testid="bill-card"], .bg-white.rounded-xl', { timeout: 10000 });
  });

  test('Bill cards should not be cut off and display full content', async ({ page }) => {
    // Get all bill cards
    const billCards = page.locator('.bg-white.rounded-xl').filter({ hasText: 'Take Action' });
    
    // Wait for at least one card to be visible
    await expect(billCards.first()).toBeVisible();
    
    const cardCount = await billCards.count();
    console.log(`Found ${cardCount} bill cards`);
    
    // Check each card for cutoff issues
    for (let i = 0; i < Math.min(cardCount, 5); i++) {
      const card = billCards.nth(i);
      
      // Check if card is visible
      await expect(card).toBeVisible();
      
      // Get card dimensions
      const cardBox = await card.boundingBox();
      console.log(`Card ${i} dimensions:`, cardBox);
      
      // Check if summary text is visible and not cut off
      const summaryText = card.locator('p').filter({ hasText: /summary|bill|act/i }).first();
      if (await summaryText.count() > 0) {
        await expect(summaryText).toBeVisible();
        
        // Check if text is not truncated by looking for overflow
        const textBox = await summaryText.boundingBox();
        const isOverflowing = await summaryText.evaluate((el) => {
          return el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth;
        });
        
        console.log(`Card ${i} summary text overflow:`, isOverflowing);
        console.log(`Card ${i} summary text box:`, textBox);
        
        // Take screenshot of the card for visual inspection
        await card.screenshot({ path: `test-results/card-${i}-screenshot.png` });
      }
      
      // Check if Take Action button is visible
      const actionButton = card.locator('button', { hasText: 'Take Action' });
      await expect(actionButton).toBeVisible();
    }
  });

  test('Bill action modal should contain detailed bill information', async ({ page }) => {
    // Click on the first Take Action button
    const firstCard = page.locator('.bg-white.rounded-xl').filter({ hasText: 'Take Action' }).first();
    await expect(firstCard).toBeVisible();
    
    const takeActionButton = firstCard.locator('button', { hasText: 'Take Action' });
    await takeActionButton.click();
    
    // Wait for modal to open
    await page.waitForSelector('[role="dialog"], .fixed.inset-0', { timeout: 5000 });
    
    // Check if modal is visible
    const modal = page.locator('[role="dialog"], .fixed.inset-0').first();
    await expect(modal).toBeVisible();
    
    // Take screenshot of the modal
    await modal.screenshot({ path: 'test-results/modal-screenshot.png' });
    
    // Check for bill details section
    const billDetailsButton = modal.locator('button', { hasText: /bill details|analysis/i });
    
    if (await billDetailsButton.count() > 0) {
      console.log('Found bill details button');
      
      // Click to expand bill details
      await billDetailsButton.click();
      
      // Wait a moment for expansion
      await page.waitForTimeout(1000);
      
      // Check for detailed bill information
      const detailsContent = modal.locator('text=/what.*does|who.*affects|why.*matters|cost.*impact/i');
      const detailsCount = await detailsContent.count();
      
      console.log(`Found ${detailsCount} detail sections`);
      
      if (detailsCount === 0) {
        console.log('❌ NO BILL DETAILS FOUND IN MODAL!');
        
        // Log all text content in modal for debugging
        const modalText = await modal.textContent();
        console.log('Modal content:', modalText);
      } else {
        console.log('✅ Bill details found in modal');
      }
      
      // Take screenshot after expanding details
      await modal.screenshot({ path: 'test-results/modal-expanded-screenshot.png' });
    } else {
      console.log('❌ NO BILL DETAILS BUTTON FOUND!');
      
      // Check if there's any bill information visible
      const billInfo = modal.locator('text=/summary|bill|act|congress/i');
      const infoCount = await billInfo.count();
      console.log(`Found ${infoCount} bill info elements`);
      
      // Log modal content for debugging
      const modalText = await modal.textContent();
      console.log('Modal content:', modalText);
    }
    
    // Check if action buttons (Support/Oppose) are at the bottom
    const actionButtons = modal.locator('button', { hasText: /support|oppose|amend/i });
    const buttonCount = await actionButtons.count();
    console.log(`Found ${buttonCount} action buttons`);
    
    if (buttonCount > 0) {
      const firstActionButton = actionButtons.first();
      const buttonBox = await firstActionButton.boundingBox();
      const modalBox = await modal.boundingBox();
      
      if (buttonBox && modalBox) {
        const isNearBottom = (buttonBox.y + buttonBox.height) > (modalBox.y + modalBox.height * 0.8);
        console.log(`Action buttons near bottom: ${isNearBottom}`);
      }
    }
  });

  test('Check for specific UI issues mentioned by user', async ({ page }) => {
    console.log('🔍 Checking specific UI issues...');
    
    // 1. Check bill cards for cutoff
    const cards = page.locator('.bg-white.rounded-xl').filter({ hasText: 'Take Action' });
    const cardCount = await cards.count();
    
    for (let i = 0; i < Math.min(cardCount, 3); i++) {
      const card = cards.nth(i);
      const cardHeight = await card.evaluate(el => el.scrollHeight);
      const visibleHeight = await card.evaluate(el => el.clientHeight);
      
      console.log(`Card ${i}: scrollHeight=${cardHeight}, clientHeight=${visibleHeight}`);
      
      if (cardHeight > visibleHeight) {
        console.log(`❌ Card ${i} is cut off! Content height (${cardHeight}) > visible height (${visibleHeight})`);
      }
    }
    
    // 2. Check modal for bill details
    const firstTakeAction = cards.first().locator('button', { hasText: 'Take Action' });
    await firstTakeAction.click();
    
    await page.waitForSelector('[role="dialog"]', { timeout: 5000 });
    const modal = page.locator('[role="dialog"]').first();
    
    // Look for any bill details content
    const hasWhatDoes = await modal.locator('text=/what.*does/i').count() > 0;
    const hasWhoAffects = await modal.locator('text=/who.*affects/i').count() > 0;
    const hasWhyMatters = await modal.locator('text=/why.*matters/i').count() > 0;
    const hasCostImpact = await modal.locator('text=/cost.*impact/i').count() > 0;
    const hasTimeline = await modal.locator('text=/timeline/i').count() > 0;
    
    console.log('Bill details check:');
    console.log(`- What Does: ${hasWhatDoes}`);
    console.log(`- Who Affects: ${hasWhoAffects}`);
    console.log(`- Why Matters: ${hasWhyMatters}`);
    console.log(`- Cost Impact: ${hasCostImpact}`);
    console.log(`- Timeline: ${hasTimeline}`);
    
    const totalDetailsFound = [hasWhatDoes, hasWhoAffects, hasWhyMatters, hasCostImpact, hasTimeline].filter(Boolean).length;
    
    if (totalDetailsFound === 0) {
      console.log('❌ NO BILL DETAILS FOUND IN MODAL AT ALL!');
    } else {
      console.log(`✅ Found ${totalDetailsFound} types of bill details`);
    }
  });
});
