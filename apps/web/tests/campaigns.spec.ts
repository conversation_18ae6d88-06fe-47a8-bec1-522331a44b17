// Playwright tests for campaign pages
import { test, expect } from '@playwright/test';

test.describe('Campaign Pages', () => {
  test('campaigns listing page loads correctly', async ({ page }) => {
    // Navigate to campaigns page
    await page.goto('/campaigns');

    // Check that the page loads
    await expect(page).toHaveTitle(/Campaigns.*ModernAction\.io/);

    // Check for main heading
    await expect(page.locator('h1')).toContainText('Active Campaigns');

    // Check for description
    await expect(page.locator('text=Take action on important legislation')).toBeVisible();

    // Check for filter buttons using more specific selectors
    await expect(page.locator('a[href="/campaigns"]')).toContainText('All Campaigns');
    await expect(page.locator('a[href="/campaigns?featured=true"]')).toContainText('Featured');
    await expect(page.locator('a[href="/campaigns?status=active"]')).toContainText('Active');
    await expect(page.locator('a[href="/campaigns?type=support"]')).toContainText('Support');
    await expect(page.locator('a[href="/campaigns?type=oppose"]')).toContainText('Oppose');

    // Since there's no backend data, check for empty state
    await expect(page.locator('text=No campaigns found')).toBeVisible();
    await expect(page.locator('text=Try adjusting your filters')).toBeVisible();
  });

  test('campaign detail page shows 404 for non-existent campaign', async ({ page }) => {
    // Navigate to a non-existent campaign
    await page.goto('/campaigns/non-existent-id');

    // Wait for page to load and check for 404 content
    await page.waitForLoadState('networkidle');

    // Check for 404 page elements - might be in different heading levels
    const heading = page.locator('h1, h2, h3').filter({ hasText: 'Campaign Not Found' });
    await expect(heading).toBeVisible();

    await expect(page.locator('text=doesn\'t exist or may have been removed')).toBeVisible();

    // Check for navigation buttons
    await expect(page.locator('text=View All Campaigns')).toBeVisible();
    await expect(page.locator('text=Go Home')).toBeVisible();

    // Test navigation buttons work
    const viewAllButton = page.locator('a').filter({ hasText: 'View All Campaigns' });
    await expect(viewAllButton).toHaveAttribute('href', '/campaigns');

    const goHomeButton = page.locator('a').filter({ hasText: 'Go Home' });
    await expect(goHomeButton).toHaveAttribute('href', '/');
  });

  test('filter buttons work correctly', async ({ page }) => {
    await page.goto('/campaigns');

    // Test Featured filter using specific link selector
    await page.click('a[href="/campaigns?featured=true"]');
    await expect(page).toHaveURL(/featured=true/);

    // Test Active filter
    await page.click('a[href="/campaigns?status=active"]');
    await expect(page).toHaveURL(/status=active/);

    // Test Support filter
    await page.click('a[href="/campaigns?type=support"]');
    await expect(page).toHaveURL(/type=support/);

    // Test Oppose filter
    await page.click('a[href="/campaigns?type=oppose"]');
    await expect(page).toHaveURL(/type=oppose/);

    // Test All Campaigns (reset)
    await page.click('a[href="/campaigns"]');
    await expect(page).toHaveURL(/\/campaigns$/);
  });

  test('page is responsive and accessible', async ({ page }) => {
    await page.goto('/campaigns');

    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('a[href="/campaigns"]')).toBeVisible();

    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('h1')).toBeVisible();

    // Test desktop viewport
    await page.setViewportSize({ width: 1200, height: 800 });
    await expect(page.locator('h1')).toBeVisible();

    // Basic accessibility checks
    await expect(page.locator('h1')).toHaveAttribute('class', /text-3xl/);
    // Check for semantic structure instead of specific main tag
    await expect(page.locator('h1')).toBeVisible();
  });

  test('basic page functionality works', async ({ page }) => {
    // Simple test to verify the page loads and basic elements are present
    await page.goto('/campaigns');

    // Should load the page structure
    await expect(page.locator('h1')).toContainText('Active Campaigns');

    // Should show empty state (since no backend data)
    await expect(page.locator('text=No campaigns found')).toBeVisible();

    // Should have filter navigation
    await expect(page.locator('a[href="/campaigns"]')).toBeVisible();
  });
});
