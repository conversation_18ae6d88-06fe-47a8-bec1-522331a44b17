import { test, expect } from '@playwright/test';

test.describe('CORS Debug', () => {
  test('Direct API call from browser', async ({ page }) => {
    // Navigate to a simple page first
    await page.goto('http://localhost:3000');
    
    // Try making the API call directly from the browser console
    const result = await page.evaluate(async () => {
      try {
        const response = await fetch('http://localhost:8000/api/v1/bills/?limit=3');
        console.log('Response status:', response.status);
        console.log('Response headers:', [...response.headers.entries()]);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        return {
          success: true,
          status: response.status,
          dataLength: data.length,
          firstBillTitle: data[0]?.title
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          name: error.name
        };
      }
    });
    
    console.log('Direct fetch result:', result);
    
    if (!result.success) {
      console.log('❌ Direct fetch failed:', result.error);
    } else {
      console.log('✅ Direct fetch succeeded:', result);
    }
  });

  test('Check axios request from React app', async ({ page }) => {
    // Enable console logging
    page.on('console', msg => {
      console.log(`[Browser Console] ${msg.text()}`);
    });
    
    // Navigate to bills page and wait for network activity
    await page.goto('http://localhost:3000/bills');
    
    // Wait a bit for the page to try loading bills
    await page.waitForTimeout(5000);
    
    // Check what's displayed
    const pageText = await page.textContent('body');
    if (pageText.includes('Failed to load bills')) {
      console.log('❌ Bills page shows error');
    } else if (pageText.includes('No bills available')) {
      console.log('📍 Bills page shows no bills');
    } else if (pageText.includes('Loading bills')) {
      console.log('⏳ Bills page still loading');
    } else {
      console.log('❓ Bills page in unknown state');
    }
  });

  test('Test with axios directly in browser', async ({ page }) => {
    // Navigate to a page that has axios loaded
    await page.goto('http://localhost:3000/bills');
    
    // Wait for the page to load
    await page.waitForTimeout(2000);
    
    // Try making the same request that the app makes
    const result = await page.evaluate(async () => {
      try {
        // Import axios from the already loaded modules
        const axios = window.axios || (await import('axios')).default;
        
        const response = await axios.get('http://localhost:8000/api/v1/bills/?limit=3');
        
        return {
          success: true,
          status: response.status,
          dataLength: response.data.length,
          firstBillTitle: response.data[0]?.title
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          code: error.code,
          response: error.response ? {
            status: error.response.status,
            statusText: error.response.statusText,
            headers: error.response.headers
          } : null
        };
      }
    });
    
    console.log('Axios result:', JSON.stringify(result, null, 2));
    
    if (!result.success) {
      console.log('❌ Axios request failed:', result.error);
      if (result.response) {
        console.log('Response details:', result.response);
      }
    } else {
      console.log('✅ Axios request succeeded');
    }
  });
});