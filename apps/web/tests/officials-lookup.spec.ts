// Playwright E2E tests for the Officials Lookup service with OpenStates integration
import { test, expect } from '@playwright/test';

test.describe('Officials Lookup - OpenStates Integration', () => {
  // Test the API endpoints directly since we have a working backend
  test('API returns officials for zip code 90210', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/v1/officials/by-zip/90210');
    
    expect(response.status()).toBe(200);
    
    const officials = await response.json();
    expect(Array.isArray(officials)).toBe(true);
    expect(officials.length).toBeGreaterThan(0);
    
    // Verify the structure of returned officials (OfficialContact format)
    const firstOfficial = officials[0];
    expect(firstOfficial).toHaveProperty('id');
    expect(firstOfficial).toHaveProperty('name');
    expect(firstOfficial).toHaveProperty('title');
    expect(firstOfficial).toHaveProperty('email');
    expect(firstOfficial).toHaveProperty('phone');
    expect(firstOfficial).toHaveProperty('website');
    expect(firstOfficial).toHaveProperty('office_address');
    
    // Verify we get real officials data (not hardcoded fallback)
    const officialNames = officials.map(o => o.name);
    expect(officialNames).toContain('Adam Schiff');
    expect(officialNames).toContain('Alex Padilla');
  });

  test('API returns officials for zip code 10001', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/v1/officials/by-zip/10001');
    
    expect(response.status()).toBe(200);
    
    const officials = await response.json();
    expect(Array.isArray(officials)).toBe(true);
    expect(officials.length).toBeGreaterThan(0);
    
    // Verify we get New York officials
    const officialNames = officials.map(o => o.name);
    expect(officialNames).toContain('Chuck Schumer');
    expect(officialNames).toContain('Kirsten Gillibrand');
    expect(officialNames).toContain('Jerry Nadler');
  });

  test('API handles invalid zip codes gracefully', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/v1/officials/by-zip/00000');
    
    // Should still return 200 but with empty array or appropriate response
    expect(response.status()).toBe(200);
    
    const officials = await response.json();
    expect(Array.isArray(officials)).toBe(true);
  });

  test('API validates zip code format', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/v1/officials/by-zip/123');
    
    // Should return validation error
    expect(response.status()).toBe(400);
  });

  test('API returns consistent data structure', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/v1/officials/by-zip/90210');
    const officials = await response.json();
    
    // Test that all officials have consistent structure (OfficialContact format)
    officials.forEach(official => {
      expect(typeof official.id).toBe('string');
      expect(typeof official.name).toBe('string');
      expect(typeof official.title).toBe('string');
      expect(['string', 'object']).toContain(typeof official.email); // Can be null
      expect(['string', 'object']).toContain(typeof official.phone); // Can be null
      expect(['string', 'object']).toContain(typeof official.website); // Can be null
      expect(['string', 'object']).toContain(typeof official.office_address); // Can be null
      expect(typeof official.preferred_contact_method).toBe('string');
    });
  });

  test('API caching works correctly', async ({ request }) => {
    // First request
    const start1 = Date.now();
    const response1 = await request.get('http://localhost:8000/api/v1/officials/by-zip/90210');
    const duration1 = Date.now() - start1;
    
    expect(response1.status()).toBe(200);
    const officials1 = await response1.json();
    
    // Second request (should be faster due to caching)
    const start2 = Date.now();
    const response2 = await request.get('http://localhost:8000/api/v1/officials/by-zip/90210');
    const duration2 = Date.now() - start2;
    
    expect(response2.status()).toBe(200);
    const officials2 = await response2.json();
    
    // Results should be identical
    expect(officials1).toEqual(officials2);
    
    // Second request should be faster (cached)
    expect(duration2).toBeLessThan(duration1);
  });

  test('API health check works', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/v1/health');
    
    expect(response.status()).toBe(200);
    const health = await response.json();
    expect(health).toHaveProperty('status');
    expect(health.status).toBe('ok');
  });
});

test.describe('Officials Lookup - Frontend Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the officials API to return consistent test data
    await page.route('**/api/v1/officials/search*', async (route) => {
      const url = route.request().url();
      const zipCode = new URL(url).searchParams.get('zip_code');
      
      let mockData = [];
      if (zipCode === '90210') {
        mockData = [
          {
            id: 'test-official-1',
            name: 'Adam Schiff',
            title: 'Senator',
            party: 'Democratic',
            level: 'state',
            state: '',
            district: 'California',
            email: null,
            phone: null
          },
          {
            id: 'test-official-2',
            name: 'Alex Padilla',
            title: 'Senator',
            party: 'Democratic',
            level: 'state',
            state: '',
            district: 'California',
            email: null,
            phone: null
          }
        ];
      } else if (zipCode === '10001') {
        mockData = [
          {
            id: 'test-official-3',
            name: 'Chuck Schumer',
            title: 'Senator',
            party: 'Democratic',
            level: 'state',
            state: '',
            district: 'New York',
            email: null,
            phone: null
          }
        ];
      }
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockData)
      });
    });
  });

  test('officials lookup component works with zip code input', async ({ page }) => {
    // Create a test page with officials lookup functionality
    await page.setContent(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Officials Lookup Test</title>
        <style>
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .form-group { margin-bottom: 15px; }
          .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
          .form-group input { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
          .button { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; background: #3b82f6; color: white; }
          .official-card { border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 8px; }
          .loading { opacity: 0.6; }
          .error { color: red; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>Find Your Representatives</h1>
          <form id="lookupForm">
            <div class="form-group">
              <label for="zipCode">Enter your ZIP code:</label>
              <input type="text" id="zipCode" data-testid="zip-code-input" placeholder="e.g., 90210" maxlength="5" pattern="[0-9]{5}">
            </div>
            <button type="submit" data-testid="lookup-button" class="button">Find My Representatives</button>
          </form>
          
          <div id="loading" data-testid="loading-indicator" style="display: none;">
            Loading your representatives...
          </div>
          
          <div id="error" data-testid="error-message" class="error" style="display: none;"></div>
          
          <div id="results" data-testid="officials-results"></div>
        </div>
        
        <script>
          document.getElementById('lookupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const zipCode = document.getElementById('zipCode').value;
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const results = document.getElementById('results');
            
            // Clear previous results
            error.style.display = 'none';
            results.innerHTML = '';
            
            // Validate zip code
            if (!/^[0-9]{5}$/.test(zipCode)) {
              error.textContent = 'Please enter a valid 5-digit ZIP code';
              error.style.display = 'block';
              return;
            }
            
            // Show loading
            loading.style.display = 'block';
            
            try {
              const response = await fetch(\`/api/v1/officials/search?zip_code=\${zipCode}\`);
              
              if (!response.ok) {
                throw new Error('Failed to fetch officials');
              }
              
              const officials = await response.json();
              
              if (officials.length === 0) {
                results.innerHTML = '<p data-testid="no-results">No representatives found for this ZIP code.</p>';
              } else {
                results.innerHTML = officials.map(official => \`
                  <div class="official-card" data-testid="official-card">
                    <h3 data-testid="official-name">\${official.name}</h3>
                    <p><strong>Title:</strong> \${official.title}</p>
                    <p><strong>Party:</strong> \${official.party || 'N/A'}</p>
                    <p><strong>Level:</strong> \${official.level}</p>
                    <p><strong>District:</strong> \${official.district || 'N/A'}</p>
                  </div>
                \`).join('');
              }
            } catch (err) {
              error.textContent = 'Error loading representatives. Please try again.';
              error.style.display = 'block';
            } finally {
              loading.style.display = 'none';
            }
          });
        </script>
      </body>
      </html>
    `);

    await page.waitForLoadState('domcontentloaded');

    // Test with Beverly Hills zip code
    await page.fill('[data-testid="zip-code-input"]', '90210');
    await page.click('[data-testid="lookup-button"]');

    // Wait for loading to appear and disappear
    await expect(page.locator('[data-testid="loading-indicator"]')).toBeVisible();
    await expect(page.locator('[data-testid="loading-indicator"]')).toBeHidden();

    // Check that officials are displayed
    await expect(page.locator('[data-testid="official-card"]')).toHaveCount(2);
    await expect(page.locator('[data-testid="official-name"]').first()).toContainText('Adam Schiff');
    await expect(page.locator('[data-testid="official-name"]').nth(1)).toContainText('Alex Padilla');
  });

  test('officials lookup handles different zip codes', async ({ page }) => {
    await page.setContent(`
      <!DOCTYPE html>
      <html>
      <head><title>Test</title></head>
      <body>
        <input data-testid="zip-code-input" type="text">
        <button data-testid="lookup-button">Search</button>
        <div data-testid="results"></div>
        <script>
          document.querySelector('[data-testid="lookup-button"]').addEventListener('click', async function() {
            const zipCode = document.querySelector('[data-testid="zip-code-input"]').value;
            const response = await fetch(\`/api/v1/officials/search?zip_code=\${zipCode}\`);
            const officials = await response.json();
            document.querySelector('[data-testid="results"]').innerHTML = officials.map(o => \`<div data-testid="official">\${o.name}</div>\`).join('');
          });
        </script>
      </body>
      </html>
    `);

    // Test New York zip code
    await page.fill('[data-testid="zip-code-input"]', '10001');
    await page.click('[data-testid="lookup-button"]');
    
    await page.waitForSelector('[data-testid="official"]');
    await expect(page.locator('[data-testid="official"]')).toContainText('Chuck Schumer');
  });

  test('officials lookup validates zip code format', async ({ page }) => {
    await page.setContent(`
      <!DOCTYPE html>
      <html>
      <head><title>Test</title></head>
      <body>
        <input data-testid="zip-code-input" type="text">
        <button data-testid="lookup-button">Search</button>
        <div data-testid="error" style="display: none;"></div>
        <script>
          document.querySelector('[data-testid="lookup-button"]').addEventListener('click', function() {
            const zipCode = document.querySelector('[data-testid="zip-code-input"]').value;
            const error = document.querySelector('[data-testid="error"]');
            
            if (!/^[0-9]{5}$/.test(zipCode)) {
              error.textContent = 'Invalid ZIP code';
              error.style.display = 'block';
            } else {
              error.style.display = 'none';
            }
          });
        </script>
      </body>
      </html>
    `);

    // Test invalid zip code
    await page.fill('[data-testid="zip-code-input"]', 'invalid');
    await page.click('[data-testid="lookup-button"]');
    
    await expect(page.locator('[data-testid="error"]')).toBeVisible();
    await expect(page.locator('[data-testid="error"]')).toContainText('Invalid ZIP code');
  });
});
