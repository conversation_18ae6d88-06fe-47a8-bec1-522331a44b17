#!/bin/bash

# Build and Deploy Script for ModernAction Web
# This script builds the Docker image with our SSR bug fixes and deploys it

set -e

echo "=== ModernAction Web Build and Deploy ==="
echo "Building clean Docker image with SSR bug fixes..."

# Set variables
ECR_REGISTRY="308755113449.dkr.ecr.us-east-1.amazonaws.com"
ECR_REPOSITORY="modernaction-web-staging"
IMAGE_TAG="final-fix-amd64"
FULL_IMAGE_NAME="${ECR_REGISTRY}/${ECR_REPOSITORY}:${IMAGE_TAG}"

# Navigate to web app directory
cd apps/web

# Clean any existing build artifacts
echo "Cleaning .next directory for fresh build..."
rm -rf .next

# Login to ECR
echo "Logging in to Amazon ECR..."
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $ECR_REGISTRY

# Build Docker image with no cache
echo "Building Docker image: $FULL_IMAGE_NAME"
docker build --no-cache -t $FULL_IMAGE_NAME .

# Push to ECR
echo "Pushing image to ECR..."
docker push $FULL_IMAGE_NAME

echo "✅ Build and push completed successfully!"
echo "Image: $FULL_IMAGE_NAME"

# Return to root directory
cd ../..

echo "=== Next Steps ==="
echo "1. Update ECS Task Definition to use: $FULL_IMAGE_NAME"
echo "2. Force new deployment of modernaction-web-staging service"
echo "3. Test campaigns page at staging URL"
