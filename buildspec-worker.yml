version: 0.2

env:
  variables:
    ENVIRONMENT: staging
    AWS_ACCOUNT_ID: "************"
    AWS_DEFAULT_REGION: "us-east-1"
    ECR_REGISTRY: "************.dkr.ecr.us-east-1.amazonaws.com"
    IMAGE_TAG: "latest"

phases:
  pre_build:
    commands:
      - echo "Starting pre-build phase..."
      - echo "Logging in to Amazon ECR..."
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
      - echo "Build will use image tag $IMAGE_TAG"

  build:
    commands:
      - echo "Starting build phase..."
      - echo "Building Worker container with ML dependencies..."
      - cd apps/api
      - echo "Building Docker worker image..."
      - docker build --no-cache --platform linux/amd64 -f Dockerfile.worker -t modernaction-worker:$IMAGE_TAG .
      - docker tag modernaction-worker:$IMAGE_TAG $ECR_REGISTRY/modernaction-worker:$IMAGE_TAG
      - cd ../..

  post_build:
    commands:
      - echo "Starting post-build phase..."
      - echo "Pushing Worker image to ECR..."
      - docker push $ECR_REGISTRY/modernaction-worker:$IMAGE_TAG
      - echo "Worker image build and push completed successfully!"

artifacts:
  files:
    - '**/*'
  name: modernaction-worker-build
