version: 0.2

env:
  variables:
    ENVIRONMENT: staging
    AWS_ACCOUNT_ID: "************"
    AWS_DEFAULT_REGION: "us-east-1"
    ECR_REGISTRY: "************.dkr.ecr.us-east-1.amazonaws.com"
    ECS_CLUSTER: "modernaction-staging"
    IMAGE_TAG: "debug-tools-final"

phases:
  pre_build:
    commands:
      - echo "Starting pre-build phase..."
      - echo "Logging in to Amazon ECR..."
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
      - echo "Build will use image tag debug-tools-final"

  build:
    commands:
      - echo "Starting build phase..."
      - echo "Building Web container with Auth0 debugging tools..."
      - cd apps/web
      - echo "Cleaning .next directory to ensure fresh build..."
      - rm -rf .next
      - echo "Building Docker image with debugging tools..."
      - docker build --no-cache --platform linux/amd64 -t modernaction-web:debug-tools-final .
      - docker tag modernaction-web:debug-tools-final $ECR_REGISTRY/modernaction-web-$ENVIRONMENT:debug-tools-final
      - docker tag modernaction-web:debug-tools-final $ECR_REGISTRY/modernaction-web-$ENVIRONMENT:latest
      - cd ../..

  post_build:
    commands:
      - echo "Starting post-build phase..."
      - echo "Pushing Web image to ECR..."
      - docker push $ECR_REGISTRY/modernaction-web-$ENVIRONMENT:debug-tools-final
      - docker push $ECR_REGISTRY/modernaction-web-$ENVIRONMENT:latest
      - echo "Updating ECS web service..."
      - aws ecs update-service --cluster $ECS_CLUSTER --service modernaction-web-$ENVIRONMENT --force-new-deployment
      - echo "Waiting for web service to stabilize..."
      - aws ecs wait services-stable --cluster $ECS_CLUSTER --services modernaction-web-$ENVIRONMENT --max-attempts 20
      - echo "Build and deployment completed successfully!"
      - echo "Auth0 debugging tools deployed!"

artifacts:
  files:
    - '**/*'
  name: modernaction-debug-build
