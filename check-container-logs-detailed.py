#!/usr/bin/env python3
"""
Check container logs for Auth0 and secret injection issues
"""

import boto3
import json
from datetime import datetime, timedelta

def main():
    ecs = boto3.client('ecs', region_name='us-east-1')
    logs = boto3.client('logs', region_name='us-east-1')
    
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-web-staging'
    
    print("🔍 Getting running tasks...")
    
    # Get running tasks
    tasks_response = ecs.list_tasks(
        cluster=cluster_name,
        serviceName=service_name,
        desiredStatus='RUNNING'
    )
    
    if not tasks_response['taskArns']:
        print("❌ No running tasks found!")
        return
    
    task_arn = tasks_response['taskArns'][0]
    task_id = task_arn.split('/')[-1]
    
    print(f"📋 Task ID: {task_id}")
    
    # Get task details
    task_details = ecs.describe_tasks(
        cluster=cluster_name,
        tasks=[task_arn]
    )
    
    task = task_details['tasks'][0]
    print(f"🏷️  Task Status: {task['lastStatus']}")
    print(f"📅 Created: {task['createdAt']}")
    
    # Check for stopped reason (if any)
    if 'stoppedReason' in task:
        print(f"🛑 Stopped Reason: {task['stoppedReason']}")
    
    # Get container logs
    log_group = '/ecs/modernaction-staging-web'
    log_stream = f"ecs/WebContainer/{task_id}"
    
    print(f"\n📜 Checking logs in {log_group}/{log_stream}")
    
    try:
        # Get logs from the last hour
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=1)
        
        response = logs.get_log_events(
            logGroupName=log_group,
            logStreamName=log_stream,
            startTime=int(start_time.timestamp() * 1000),
            endTime=int(end_time.timestamp() * 1000)
        )
        
        events = response['events']
        
        if not events:
            print("📭 No log events found")
            return
        
        print(f"📊 Found {len(events)} log events")
        print("\n🔍 Recent log entries:")
        print("=" * 80)
        
        # Show last 20 log entries
        for event in events[-20:]:
            timestamp = datetime.fromtimestamp(event['timestamp'] / 1000)
            message = event['message'].strip()
            print(f"[{timestamp}] {message}")
        
        print("=" * 80)
        
        # Look for specific Auth0 and secret-related errors
        print("\n🔍 Searching for Auth0 and secret-related issues:")
        
        auth0_keywords = [
            'auth0', 'AUTH0', 'secret', 'SECRET', 'environment', 'ENV',
            'missing', 'undefined', 'null', 'error', 'ERROR', 'failed', 'FAILED'
        ]
        
        relevant_logs = []
        for event in events:
            message = event['message'].lower()
            if any(keyword.lower() in message for keyword in auth0_keywords):
                relevant_logs.append(event)
        
        if relevant_logs:
            print(f"🚨 Found {len(relevant_logs)} potentially relevant log entries:")
            for event in relevant_logs[-10:]:  # Show last 10 relevant entries
                timestamp = datetime.fromtimestamp(event['timestamp'] / 1000)
                message = event['message'].strip()
                print(f"  [{timestamp}] {message}")
        else:
            print("✅ No obvious Auth0 or secret-related errors found")
            
    except Exception as e:
        print(f"❌ Error reading logs: {e}")
        
        # Try to list available log streams
        try:
            streams_response = logs.describe_log_streams(
                logGroupName=log_group,
                orderBy='LastEventTime',
                descending=True,
                limit=10
            )
            
            print(f"\n📋 Available log streams in {log_group}:")
            for stream in streams_response['logStreams']:
                print(f"  - {stream['logStreamName']} (last event: {stream.get('lastEventTime', 'N/A')})")
                
        except Exception as e2:
            print(f"❌ Error listing log streams: {e2}")

if __name__ == '__main__':
    main()
