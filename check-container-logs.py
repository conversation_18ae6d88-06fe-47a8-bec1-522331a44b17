#!/usr/bin/env python3
"""
Check container logs to see what's happening with Auth0
"""

import boto3
from datetime import datetime, timedelta

def main():
    ecs = boto3.client('ecs', region_name='us-east-1')
    logs = boto3.client('logs', region_name='us-east-1')
    
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-web-staging'
    
    print("📋 Getting running task...")
    
    # Get running tasks
    tasks_response = ecs.list_tasks(
        cluster=cluster_name,
        serviceName=service_name,
        desiredStatus='RUNNING'
    )
    
    if not tasks_response['taskArns']:
        print("❌ No running tasks found!")
        return
    
    # Get task details
    task_details = ecs.describe_tasks(
        cluster=cluster_name,
        tasks=[tasks_response['taskArns'][0]]
    )
    
    task = task_details['tasks'][0]
    task_id = task['taskArn'].split('/')[-1]
    
    print(f"Task ID: {task_id}")
    print(f"Task Definition: {task['taskDefinitionArn'].split('/')[-1]}")
    
    # Check container logs
    log_group = '/ecs/ModernActionstagingWebTaskDefinition6EB55C12'
    log_stream = f'web/{task_id}'
    
    print(f"\n📝 Checking logs for stream: {log_stream}")
    
    try:
        # Get recent logs (last 10 minutes)
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=10)
        
        response = logs.get_log_events(
            logGroupName=log_group,
            logStreamName=log_stream,
            startTime=int(start_time.timestamp() * 1000),
            endTime=int(end_time.timestamp() * 1000)
        )
        
        print("Recent log entries:")
        for event in response['events'][-20:]:  # Last 20 log entries
            timestamp = datetime.fromtimestamp(event['timestamp'] / 1000)
            message = event['message'].strip()
            print(f"  {timestamp}: {message}")
            
    except Exception as e:
        print(f"Error reading logs: {e}")
        
        # Try to list available log streams
        try:
            streams_response = logs.describe_log_streams(
                logGroupName=log_group,
                orderBy='LastEventTime',
                descending=True,
                limit=10
            )
            
            print("\nAvailable log streams:")
            for stream in streams_response['logStreams']:
                print(f"  {stream['logStreamName']} (last event: {stream.get('lastEventTime', 'N/A')})")
                
        except Exception as e2:
            print(f"Error listing log streams: {e2}")

if __name__ == '__main__':
    main()
