#!/usr/bin/env python3
"""
Check ECS deployment status
"""

import boto3
import json

def main():
    ecs = boto3.client('ecs', region_name='us-east-1')
    
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-web-staging'
    
    print("🔍 Checking ECS service status...")
    
    # Get service details
    response = ecs.describe_services(
        cluster=cluster_name,
        services=[service_name]
    )
    
    if not response['services']:
        print("❌ Service not found!")
        return
    
    service = response['services'][0]
    
    print(f"Service Status: {service['status']}")
    print(f"Running Count: {service['runningCount']}")
    print(f"Pending Count: {service['pendingCount']}")
    print(f"Desired Count: {service['desiredCount']}")
    print(f"Task Definition: {service['taskDefinition']}")
    
    # Check deployments
    print("\n📋 Deployments:")
    for deployment in service['deployments']:
        print(f"  Status: {deployment['status']}")
        print(f"  Task Definition: {deployment['taskDefinition']}")
        print(f"  Running Count: {deployment['runningCount']}")
        print(f"  Pending Count: {deployment['pendingCount']}")
        print(f"  Desired Count: {deployment['desiredCount']}")
        print(f"  Created: {deployment['createdAt']}")
        print("  ---")
    
    # Check tasks
    print("\n🏃 Running Tasks:")
    tasks_response = ecs.list_tasks(
        cluster=cluster_name,
        serviceName=service_name
    )
    
    if tasks_response['taskArns']:
        task_details = ecs.describe_tasks(
            cluster=cluster_name,
            tasks=tasks_response['taskArns']
        )
        
        for task in task_details['tasks']:
            print(f"  Task: {task['taskArn'].split('/')[-1]}")
            print(f"  Status: {task['lastStatus']}")
            print(f"  Health: {task.get('healthStatus', 'N/A')}")
            print(f"  Task Definition: {task['taskDefinitionArn'].split('/')[-1]}")
            print("  ---")
    else:
        print("  No running tasks found")

if __name__ == '__main__':
    main()
