#!/usr/bin/env python3
"""
Check the current task definition environment variables and secrets
"""

import boto3
import json

def main():
    ecs = boto3.client('ecs', region_name='us-east-1')
    
    print("🔍 Checking current task definition configuration...")
    
    # Get the current task definition
    task_def_response = ecs.describe_task_definition(
        taskDefinition='arn:aws:ecs:us-east-1:308755113449:task-definition/ModernActionstagingWebTaskDefinition6EB55C12:28'
    )
    
    task_def = task_def_response['taskDefinition']
    
    print(f"📋 Task Definition: {task_def['family']}:{task_def['revision']}")
    print(f"🏷️  Status: {task_def['status']}")
    
    # Find the WebContainer
    web_container = None
    for container in task_def['containerDefinitions']:
        if container['name'] == 'WebContainer':
            web_container = container
            break
    
    if not web_container:
        print("❌ WebContainer not found!")
        return
    
    print(f"\n🐳 WebContainer Configuration:")
    print(f"📦 Image: {web_container['image']}")
    
    # Check environment variables
    print(f"\n🌍 Environment Variables:")
    if 'environment' in web_container:
        for env_var in web_container['environment']:
            name = env_var['name']
            value = env_var['value']
            if 'SECRET' in name or 'PASSWORD' in name:
                value = '***REDACTED***'
            print(f"  {name} = {value}")
    else:
        print("  No environment variables found")
    
    # Check secrets
    print(f"\n🔐 Secrets:")
    if 'secrets' in web_container:
        for secret in web_container['secrets']:
            name = secret['name']
            value_from = secret['valueFrom']
            print(f"  {name} = {value_from}")
    else:
        print("  No secrets found")
    
    # Check if Auth0 variables are present
    print(f"\n✅ Auth0 Configuration Check:")
    
    auth0_env_vars = [
        'AUTH0_BASE_URL',
        'AUTH0_ISSUER_BASE_URL', 
        'AUTH0_CLIENT_ID',
        'AUTH0_AUDIENCE',
        'NEXT_PUBLIC_AUTH0_DOMAIN',
        'NEXT_PUBLIC_AUTH0_CLIENT_ID',
        'NEXT_PUBLIC_AUTH0_AUDIENCE'
    ]
    
    auth0_secrets = [
        'AUTH0_SECRET',
        'AUTH0_CLIENT_SECRET'
    ]
    
    # Check environment variables
    env_vars = {env['name']: env['value'] for env in web_container.get('environment', [])}
    for var in auth0_env_vars:
        if var in env_vars:
            print(f"  ✅ {var}: {env_vars[var]}")
        else:
            print(f"  ❌ {var}: MISSING")
    
    # Check secrets
    secrets = {secret['name']: secret['valueFrom'] for secret in web_container.get('secrets', [])}
    for var in auth0_secrets:
        if var in secrets:
            print(f"  ✅ {var}: {secrets[var]}")
        else:
            print(f"  ❌ {var}: MISSING")

if __name__ == '__main__':
    main()
