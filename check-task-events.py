#!/usr/bin/env python3
"""
Check ECS service events to see what happened with the deployment
"""

import boto3
from datetime import datetime, timedelta

def main():
    ecs = boto3.client('ecs', region_name='us-east-1')
    
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-web-staging'
    
    print("📋 Checking ECS service events...")
    
    # Get service events
    response = ecs.describe_services(
        cluster=cluster_name,
        services=[service_name]
    )
    
    if not response['services']:
        print("❌ Service not found!")
        return
    
    service = response['services'][0]
    
    # Show recent events (last 2 hours)
    cutoff_time = datetime.now() - timedelta(hours=2)
    
    print("\n🕐 Recent Service Events:")
    for event in service['events'][:20]:  # Show last 20 events
        event_time = event['createdAt'].replace(tzinfo=None)
        if event_time > cutoff_time:
            print(f"  {event['createdAt']}: {event['message']}")
    
    # Also check for stopped tasks
    print("\n🛑 Recent Stopped Tasks:")
    stopped_tasks = ecs.list_tasks(
        cluster=cluster_name,
        serviceName=service_name,
        desiredStatus='STOPPED'
    )
    
    if stopped_tasks['taskArns']:
        # Get details of recent stopped tasks
        recent_stopped = stopped_tasks['taskArns'][:5]  # Last 5 stopped tasks
        task_details = ecs.describe_tasks(
            cluster=cluster_name,
            tasks=recent_stopped
        )
        
        for task in task_details['tasks']:
            task_id = task['taskArn'].split('/')[-1]
            stopped_reason = task.get('stoppedReason', 'Unknown')
            stopped_at = task.get('stoppedAt', 'Unknown')
            task_def = task['taskDefinitionArn'].split('/')[-1]
            
            print(f"  Task: {task_id}")
            print(f"  Task Definition: {task_def}")
            print(f"  Stopped At: {stopped_at}")
            print(f"  Stopped Reason: {stopped_reason}")
            
            # Check container exit codes
            for container in task.get('containers', []):
                if 'exitCode' in container:
                    print(f"  Container {container['name']}: Exit Code {container['exitCode']}")
                    if 'reason' in container:
                        print(f"    Reason: {container['reason']}")
            print("  ---")

if __name__ == '__main__':
    main()
