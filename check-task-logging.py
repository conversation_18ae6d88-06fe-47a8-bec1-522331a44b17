#!/usr/bin/env python3
"""
Check task definition logging configuration
"""

import boto3
import json

def main():
    ecs = boto3.client('ecs', region_name='us-east-1')
    
    print("🔍 Checking task definition logging configuration...")
    
    # Get the current task definition
    task_def_response = ecs.describe_task_definition(
        taskDefinition='arn:aws:ecs:us-east-1:308755113449:task-definition/ModernActionstagingWebTaskDefinition6EB55C12:28'
    )
    
    task_def = task_def_response['taskDefinition']
    
    # Find the WebContainer
    web_container = None
    for container in task_def['containerDefinitions']:
        if container['name'] == 'WebContainer':
            web_container = container
            break
    
    if not web_container:
        print("❌ WebContainer not found!")
        return
    
    print(f"🐳 WebContainer Logging Configuration:")
    
    if 'logConfiguration' in web_container:
        log_config = web_container['logConfiguration']
        print(f"  📋 Log Driver: {log_config.get('logDriver', 'N/A')}")
        
        if 'options' in log_config:
            print(f"  🔧 Log Options:")
            for key, value in log_config['options'].items():
                print(f"    {key}: {value}")
        else:
            print(f"  ❌ No log options configured")
    else:
        print(f"  ❌ No logging configuration found!")
    
    # Also check if the container is actually running
    print(f"\n🏃 Checking container status...")
    
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-web-staging'
    
    # Get running tasks
    tasks_response = ecs.list_tasks(
        cluster=cluster_name,
        serviceName=service_name,
        desiredStatus='RUNNING'
    )
    
    if not tasks_response['taskArns']:
        print("❌ No running tasks found!")
        return
    
    task_arn = tasks_response['taskArns'][0]
    
    # Get task details
    task_details = ecs.describe_tasks(
        cluster=cluster_name,
        tasks=[task_arn]
    )
    
    task = task_details['tasks'][0]
    
    print(f"📋 Task Status: {task['lastStatus']}")
    print(f"🏥 Health Status: {task.get('healthStatus', 'UNKNOWN')}")
    
    # Check container statuses
    print(f"\n🐳 Container Statuses:")
    for container in task.get('containers', []):
        name = container['name']
        status = container.get('lastStatus', 'UNKNOWN')
        health = container.get('healthStatus', 'UNKNOWN')
        
        print(f"  {name}: {status} (health: {health})")
        
        if 'reason' in container:
            print(f"    Reason: {container['reason']}")
        
        if 'exitCode' in container:
            print(f"    Exit Code: {container['exitCode']}")

if __name__ == '__main__':
    main()
