#!/usr/bin/env python3
"""
Check web container logs for Auth0 configuration
"""

import boto3
from datetime import datetime, timedelta

def main():
    logs = boto3.client('logs', region_name='us-east-1')
    
    # Try the most recent staging web log groups
    log_groups = [
        'modernaction-staging-WebTaskDefinitionWebContainerLogGroup9BD817D4-g0P3dC8LggmF',
        'modernaction-staging-WebTaskDefinitionWebContainerLogGroup9BD817D4-dbnKcJhdTKM5',
        'modernaction-staging-WebTaskDefinitionWebContainerLogGroup9BD817D4-c1vofgce9qoo',
        'modernaction-staging-WebTaskDefinitionWebContainerLogGroup9BD817D4-SPeJyKC2rqbZ',
        'modernaction-staging-WebTaskDefinitionWebContainerLogGroup9BD817D4-C6n7pfCDWlcW'
    ]
    
    for log_group in log_groups:
        print(f"\n🔍 Checking log group: {log_group}")
        
        try:
            # Get recent log streams
            streams_response = logs.describe_log_streams(
                logGroupName=log_group,
                orderBy='LastEventTime',
                descending=True,
                limit=5
            )
            
            if not streams_response['logStreams']:
                print("  No log streams found")
                continue
                
            # Check the most recent stream
            latest_stream = streams_response['logStreams'][0]
            stream_name = latest_stream['logStreamName']
            
            print(f"  Latest stream: {stream_name}")
            print(f"  Last event: {latest_stream.get('lastEventTime', 'N/A')}")
            
            # Get recent logs
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=30)
            
            response = logs.get_log_events(
                logGroupName=log_group,
                logStreamName=stream_name,
                startTime=int(start_time.timestamp() * 1000),
                endTime=int(end_time.timestamp() * 1000)
            )
            
            if response['events']:
                print("  Recent log entries:")
                for event in response['events'][-10:]:  # Last 10 entries
                    timestamp = datetime.fromtimestamp(event['timestamp'] / 1000)
                    message = event['message'].strip()
                    print(f"    {timestamp}: {message}")
                break  # Found logs, stop checking other groups
            else:
                print("  No recent log entries")
                
        except Exception as e:
            print(f"  Error: {e}")

if __name__ == '__main__':
    main()
