#!/usr/bin/env python3
"""
Simple check of what columns actually exist in staging database
"""

import psycopg2
import os

# Load secrets from .env.local
def load_env_vars():
    env_path = os.path.join(os.path.dirname(__file__), '.env.local')
    with open(env_path, 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value

if __name__ == "__main__":
    load_env_vars()
    
    print("🔍 Checking staging database columns...")
    
    # Connect to database
    conn = psycopg2.connect(
        host=os.environ['DB_HOST'],
        port=os.environ['DB_PORT'],
        database=os.environ['DB_NAME'],
        user=os.environ['DB_USERNAME'],
        password=os.environ['DB_PASSWORD']
    )
    
    cursor = conn.cursor()
    
    # Check bills table columns
    cursor.execute("""
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'bills' 
        ORDER BY ordinal_position
    """)
    
    print("\n📋 Bills table columns:")
    bills_columns = []
    for row in cursor.fetchall():
        bills_columns.append(row[0])
        print(f"  • {row[0]} ({row[1]}) - {'NULL' if row[2] == 'YES' else 'NOT NULL'}")
    
    # Check if the problematic columns exist
    if 'reasons_for_support' not in bills_columns:
        print("\n❌ Missing column: reasons_for_support")
    if 'reasons_for_opposition' not in bills_columns:
        print("\n❌ Missing column: reasons_for_opposition")
    
    cursor.close()
    conn.close()