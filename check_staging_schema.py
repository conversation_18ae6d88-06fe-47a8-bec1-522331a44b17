#!/usr/bin/env python3
"""
Check the actual staging database schema to see what's missing
"""

import os
import sys
from pathlib import Path

# Add the API app to Python path
api_path = Path(__file__).parent / "apps" / "api"
sys.path.insert(0, str(api_path))

# Set environment to use our local .env file
os.environ.setdefault("ENV_FILE", ".env.local")

from sqlalchemy import create_engine, text
from app.core.config import settings

def check_schema():
    """Check what columns actually exist in the bills table"""
    print("🔍 Checking staging database schema...")
    
    engine = create_engine(settings.database_url, echo=False)
    
    with engine.connect() as conn:
        # Check bills table columns
        result = conn.execute(text("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'bills' 
            ORDER BY ordinal_position
        """))
        
        print("\n📋 Bills table columns:")
        bills_columns = []
        for row in result:
            bills_columns.append(row[0])
            print(f"  • {row[0]} ({row[1]}) - {'NULL' if row[2] == 'YES' else 'NOT NULL'}")
        
        # Check if the problematic columns exist
        if 'reasons_for_support' not in bills_columns:
            print("\n❌ Missing column: reasons_for_support")
        if 'reasons_for_opposition' not in bills_columns:
            print("\n❌ Missing column: reasons_for_opposition")
        
        # Check campaigns table
        result = conn.execute(text("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'campaigns'
            ORDER BY ordinal_position
        """))
        
        print(f"\n📋 Campaigns table columns:")
        for row in result:
            print(f"  • {row[0]} ({row[1]})")
        
        # Check if we have data
        result = conn.execute(text("SELECT COUNT(*) FROM bills"))
        bill_count = result.fetchone()[0]
        
        result = conn.execute(text("SELECT COUNT(*) FROM campaigns"))
        campaign_count = result.fetchone()[0]
        
        print(f"\n📊 Data counts:")
        print(f"  • Bills: {bill_count}")
        print(f"  • Campaigns: {campaign_count}")
        
        if campaign_count > 0:
            result = conn.execute(text("SELECT title, actual_actions, goal_actions FROM campaigns LIMIT 3"))
            print(f"\n🏛️ Sample campaigns:")
            for row in result:
                print(f"  • {row[0]} - {row[1]}/{row[2]} actions")

if __name__ == "__main__":
    print("🔍 ModernAction.io Database Schema Check")
    print("=" * 42)
    check_schema()