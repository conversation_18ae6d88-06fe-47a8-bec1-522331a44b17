{"name": "modernaction-web-debug-build", "description": "Build ModernAction web application with Auth0 debugging tools", "source": {"type": "GITHUB", "location": "https://github.com/columj9/modern-action-2.0.git", "buildspec": "buildspec.yml"}, "artifacts": {"type": "NO_ARTIFACTS"}, "environment": {"type": "LINUX_CONTAINER", "image": "aws/codebuild/standard:7.0", "computeType": "BUILD_GENERAL1_MEDIUM", "privilegedMode": true, "environmentVariables": [{"name": "AWS_DEFAULT_REGION", "value": "us-east-1"}, {"name": "AWS_ACCOUNT_ID", "value": "************"}, {"name": "ENVIRONMENT", "value": "staging"}]}, "serviceRole": "arn:aws:iam::************:role/codebuild-service-role", "timeoutInMinutes": 60, "queuedTimeoutInMinutes": 480, "tags": [{"key": "Project", "value": "ModernAction"}, {"key": "Environment", "value": "staging"}, {"key": "Purpose", "value": "Auth0-Debug-Build"}]}