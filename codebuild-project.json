{"name": "modernaction-web-final-build", "description": "Build final ModernAction web image with entrypoint fix", "source": {"type": "GITHUB", "location": "https://github.com/columj9/modern-action-2.0.git", "buildspec": "version: 0.2\nphases:\n  pre_build:\n    commands:\n      - echo Logging in to Amazon ECR...\n      - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 308755113449.dkr.ecr.us-east-1.amazonaws.com\n  build:\n    commands:\n      - echo Build started on `date`\n      - echo Building the Docker image...\n      - cd apps/web\n      - docker build -t modernaction-web-staging:final-launch-candidate .\n      - docker tag modernaction-web-staging:final-launch-candidate 308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:final-launch-candidate\n  post_build:\n    commands:\n      - echo Build completed on `date`\n      - echo Pushing the Docker image...\n      - docker push 308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:final-launch-candidate\n"}, "artifacts": {"type": "NO_ARTIFACTS"}, "environment": {"type": "LINUX_CONTAINER", "image": "aws/codebuild/standard:7.0", "computeType": "BUILD_GENERAL1_MEDIUM", "privilegedMode": true}, "serviceRole": "arn:aws:iam::308755113449:role/codebuild-service-role"}