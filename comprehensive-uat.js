#!/usr/bin/env node

const { chromium } = require('playwright');

async function comprehensiveUAT() {
    console.log('🧪 COMPREHENSIVE USER ACCEPTANCE TEST');
    console.log('=====================================');
    
    const browser = await chromium.launch({ 
        headless: false,  // Show browser for visibility
        slowMo: 1000     // Slow down for better observation
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    let testResults = {
        homepage: false,
        authBypassGone: false,
        loginButtonFound: false,
        auth0Redirect: false,
        loginFormFound: false,
        campaignsPage: false,
        campaignModal: false,
        coreActions: false
    };
    
    try {
        // ===== TEST 1: HOMEPAGE LOADS =====
        console.log('\n📍 TEST 1: Homepage Loading');
        await page.goto('https://staging.modernaction.io', { waitUntil: 'networkidle' });
        await page.screenshot({ path: 'uat-1-homepage.png' });
        
        const title = await page.title();
        console.log(`✅ Page title: ${title}`);
        testResults.homepage = title.includes('ModernAction');
        
        // ===== TEST 2: AUTH0 BYPASS MODE GONE =====
        console.log('\n🔍 TEST 2: Verify Auth0 Bypass Mode is Gone');
        const pageContent = await page.textContent('body');
        const bypassModePresent = pageContent.includes('Auth0 Bypass Mode');
        
        if (bypassModePresent) {
            console.log('❌ Auth0 Bypass Mode still detected!');
            testResults.authBypassGone = false;
        } else {
            console.log('✅ Auth0 Bypass Mode is gone');
            testResults.authBypassGone = true;
        }
        
        // ===== TEST 3: LOGIN BUTTON PRESENT =====
        console.log('\n🔍 TEST 3: Login Button Detection');
        const loginButton = page.locator('text=Log in').first();
        const loginVisible = await loginButton.isVisible();
        
        if (loginVisible) {
            console.log('✅ Login button found and visible');
            testResults.loginButtonFound = true;
        } else {
            console.log('❌ Login button not found');
            testResults.loginButtonFound = false;
        }
        
        // ===== TEST 4: AUTH0 LOGIN FLOW =====
        console.log('\n🖱️  TEST 4: Auth0 Login Flow');

        if (loginVisible) {
            // Set up response monitoring
            let auth0RedirectDetected = false;

            page.on('response', response => {
                if (response.url().includes('auth0.com') ||
                    response.url().includes('/api/auth/login')) {
                    console.log(`🔄 Auth0 response: ${response.status()} ${response.url()}`);
                    if (response.url().includes('auth0.com')) {
                        auth0RedirectDetected = true;
                    }
                }
            });

            // Handle potential overlays/modals
            try {
                // Check for and close any modals that might be blocking
                const modalCloseButtons = page.locator('[aria-label="Close"], button:has-text("×"), button:has-text("Close"), .modal-close');
                const modalCount = await modalCloseButtons.count();
                if (modalCount > 0) {
                    console.log(`🔄 Found ${modalCount} potential modal close buttons, attempting to close`);
                    await modalCloseButtons.first().click();
                    await page.waitForTimeout(1000);
                }
            } catch (e) {
                console.log('ℹ️  No modals to close');
            }

            // Try multiple click strategies
            let clickSuccessful = false;

            // Strategy 1: Direct click
            try {
                await loginButton.click({ timeout: 5000 });
                clickSuccessful = true;
                console.log('✅ Direct click successful');
            } catch (e) {
                console.log('⚠️  Direct click failed, trying force click');

                // Strategy 2: Force click
                try {
                    await loginButton.click({ force: true, timeout: 5000 });
                    clickSuccessful = true;
                    console.log('✅ Force click successful');
                } catch (e2) {
                    console.log('⚠️  Force click failed, trying navigation');

                    // Strategy 3: Direct navigation
                    try {
                        await page.goto('https://staging.modernaction.io/api/auth/login');
                        clickSuccessful = true;
                        console.log('✅ Direct navigation successful');
                    } catch (e3) {
                        console.log('❌ All click strategies failed');
                    }
                }
            }

            if (clickSuccessful) {
                // Wait for navigation or Auth0 redirect
                try {
                    await page.waitForURL(url => url.includes('auth0.com'), { timeout: 10000 });
                    console.log('✅ Successfully redirected to Auth0');
                    testResults.auth0Redirect = true;
                
                // Take screenshot of Auth0 page
                await page.screenshot({ path: 'uat-4-auth0-page.png' });
                
                // Check for Auth0 login form elements
                const loginFormPresent = await page.locator('input[type="email"], input[name="username"], input[name="email"]').isVisible();
                const passwordFieldPresent = await page.locator('input[type="password"], input[name="password"]').isVisible();
                
                if (loginFormPresent && passwordFieldPresent) {
                    console.log('✅ Auth0 login form detected');
                    testResults.loginFormFound = true;
                } else {
                    console.log('⚠️  Auth0 page loaded but login form not clearly visible');
                    testResults.loginFormFound = false;
                }
                
            } catch (e) {
                console.log(`⚠️  Auth0 redirect timeout: ${e.message}`);
                testResults.auth0Redirect = auth0RedirectDetected;
                
                // Check current URL and page content
                const currentUrl = page.url();
                console.log(`📍 Current URL: ${currentUrl}`);
                
                if (currentUrl.includes('auth0.com')) {
                    console.log('✅ On Auth0 domain (redirect successful)');
                    testResults.auth0Redirect = true;
                }
                }
            } else {
                console.log('❌ Unable to click login button');
            }
        }
        
        // ===== TEST 5: RETURN TO APP AND TEST CAMPAIGNS =====
        console.log('\n🏠 TEST 5: Return to App and Test Campaigns');
        
        // Navigate back to main app
        await page.goto('https://staging.modernaction.io', { waitUntil: 'networkidle' });
        
        // Test campaigns page
        const campaignsLink = page.locator('text=Campaigns').first();
        if (await campaignsLink.isVisible()) {
            await campaignsLink.click();
            await page.waitForURL('**/campaigns', { timeout: 5000 });
            
            console.log('✅ Successfully navigated to campaigns page');
            testResults.campaignsPage = true;
            
            await page.screenshot({ path: 'uat-5-campaigns-page.png' });
            
            // Look for campaign cards
            const campaignCards = page.locator('[data-testid="campaign-card"], .campaign-card, .card').first();
            if (await campaignCards.isVisible()) {
                console.log('✅ Campaign cards detected');
                
                // Try to click on a campaign
                await campaignCards.click();
                await page.waitForTimeout(2000);
                
                // Check for modal or campaign details
                const modalPresent = await page.locator('.modal, [role="dialog"], .campaign-modal').isVisible();
                if (modalPresent) {
                    console.log('✅ Campaign modal opened');
                    testResults.campaignModal = true;
                    
                    await page.screenshot({ path: 'uat-5-campaign-modal.png' });
                    
                    // Look for action buttons (email, twitter, etc.)
                    const actionButtons = page.locator('button:has-text("Send"), button:has-text("Tweet"), button:has-text("Email"), button:has-text("Action")');
                    const actionButtonCount = await actionButtons.count();
                    
                    if (actionButtonCount > 0) {
                        console.log(`✅ Found ${actionButtonCount} action buttons`);
                        testResults.coreActions = true;
                    } else {
                        console.log('⚠️  No action buttons found in campaign modal');
                    }
                } else {
                    console.log('⚠️  Campaign modal did not open');
                }
            } else {
                console.log('⚠️  No campaign cards found');
            }
        } else {
            console.log('❌ Campaigns link not found');
        }
        
    } catch (error) {
        console.log(`❌ UAT ERROR: ${error.message}`);
        await page.screenshot({ path: 'uat-error.png' });
        
    } finally {
        await browser.close();
    }
    
    // ===== FINAL RESULTS =====
    console.log('\n🏆 UAT RESULTS SUMMARY');
    console.log('======================');
    
    const tests = [
        { name: 'Homepage Loads', result: testResults.homepage },
        { name: 'Auth0 Bypass Gone', result: testResults.authBypassGone },
        { name: 'Login Button Found', result: testResults.loginButtonFound },
        { name: 'Auth0 Redirect Works', result: testResults.auth0Redirect },
        { name: 'Auth0 Login Form', result: testResults.loginFormFound },
        { name: 'Campaigns Page', result: testResults.campaignsPage },
        { name: 'Campaign Modal', result: testResults.campaignModal },
        { name: 'Core Actions Available', result: testResults.coreActions }
    ];
    
    let passedTests = 0;
    tests.forEach(test => {
        const status = test.result ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} - ${test.name}`);
        if (test.result) passedTests++;
    });
    
    const successRate = (passedTests / tests.length * 100).toFixed(1);
    console.log(`\n📊 Overall Success Rate: ${successRate}% (${passedTests}/${tests.length})`);
    
    if (successRate >= 80) {
        console.log('🎉 UAT PASSED - Application ready for production!');
        return true;
    } else {
        console.log('💥 UAT FAILED - Issues need to be addressed');
        return false;
    }
}

// Run the comprehensive UAT
comprehensiveUAT().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('💥 UAT crashed:', error);
    process.exit(1);
});
