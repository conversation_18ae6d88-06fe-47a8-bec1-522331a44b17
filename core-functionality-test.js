#!/usr/bin/env node

const { chromium } = require('playwright');

async function testCoreFunctionality() {
    console.log('🧪 COMPREHENSIVE CORE FUNCTIONALITY TEST');
    console.log('========================================');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    const testResults = {
        billsAPI: false,
        billSummaries: false,
        campaignInteraction: false,
        representativeFinding: false,
        emailGeneration: false,
        tweetGeneration: false,
        userPersonalization: false,
        actionSubmission: false
    };
    
    try {
        // ===== TEST 1: VERIFY BILLS API AND DATA =====
        console.log('\n📋 TEST 1: Bills API and Data Verification');
        
        await page.goto('https://staging.modernaction.io/api/v1/bills');
        const billsContent = await page.textContent('body');
        
        try {
            const billsData = JSON.parse(billsContent);
            testResults.billsAPI = Array.isArray(billsData) && billsData.length > 0;
            
            if (testResults.billsAPI) {
                console.log(`✅ Bills API working - Found ${billsData.length} bills`);
                
                // Check for bill summaries
                const billsWithSummaries = billsData.filter(bill => 
                    bill.summary && bill.summary.length > 10
                );
                testResults.billSummaries = billsWithSummaries.length > 0;
                console.log(`✅ Bill summaries present: ${billsWithSummaries.length}/${billsData.length} bills have summaries`);
                
                // Log sample bill data
                if (billsData.length > 0) {
                    const sampleBill = billsData[0];
                    console.log(`📄 Sample bill: ${sampleBill.title} (${sampleBill.bill_number})`);
                    if (sampleBill.summary) {
                        console.log(`📝 Summary preview: ${sampleBill.summary.substring(0, 100)}...`);
                    }
                }
            } else {
                console.log('❌ No bills found in API response');
            }
        } catch (e) {
            console.log(`❌ Bills API failed: ${e.message}`);
        }
        
        // ===== TEST 2: CAMPAIGNS WITH BILL DATA =====
        console.log('\n🎯 TEST 2: Campaigns with Bill Integration');
        
        await page.goto('https://staging.modernaction.io/api/v1/campaigns');
        const campaignsContent = await page.textContent('body');
        
        try {
            const campaignsData = JSON.parse(campaignsContent);
            const campaignsWithBills = campaignsData.filter(campaign => 
                campaign.bill && campaign.bill.title
            );
            
            testResults.campaignInteraction = campaignsWithBills.length > 0;
            console.log(`✅ Campaigns with bills: ${campaignsWithBills.length}/${campaignsData.length}`);
            
            if (campaignsWithBills.length > 0) {
                const sampleCampaign = campaignsWithBills[0];
                console.log(`📋 Sample campaign: "${sampleCampaign.title}"`);
                console.log(`📄 Associated bill: ${sampleCampaign.bill.title} (${sampleCampaign.bill.bill_number})`);
                console.log(`📧 Has email template: ${!!sampleCampaign.email_template}`);
                console.log(`🐦 Has social message: ${!!sampleCampaign.social_media_message}`);
            }
        } catch (e) {
            console.log(`❌ Campaigns API failed: ${e.message}`);
        }
        
        // ===== TEST 3: REPRESENTATIVE FINDING =====
        console.log('\n🏛️  TEST 3: Representative Finding Functionality');
        
        // Test with a sample ZIP code
        const testZipCode = '10001'; // NYC zip code
        await page.goto(`https://staging.modernaction.io/api/v1/representatives?zip_code=${testZipCode}`);
        const repsContent = await page.textContent('body');
        
        try {
            const repsData = JSON.parse(repsContent);
            testResults.representativeFinding = Array.isArray(repsData) && repsData.length > 0;
            
            if (testResults.representativeFinding) {
                console.log(`✅ Representatives found for ${testZipCode}: ${repsData.length} representatives`);
                
                repsData.forEach((rep, index) => {
                    console.log(`  ${index + 1}. ${rep.name} (${rep.party}) - ${rep.office}`);
                    if (rep.email) console.log(`     📧 Email: ${rep.email}`);
                });
            } else {
                console.log(`❌ No representatives found for ${testZipCode}`);
            }
        } catch (e) {
            console.log(`❌ Representatives API failed: ${e.message}`);
        }
        
        // ===== TEST 4: FRONTEND CAMPAIGN INTERACTION =====
        console.log('\n🖥️  TEST 4: Frontend Campaign Interaction');
        
        await page.goto('https://staging.modernaction.io/campaigns', { waitUntil: 'networkidle' });
        await page.screenshot({ path: 'core-test-campaigns-page.png' });
        
        // Look for campaign cards or elements
        const campaignElements = page.locator('[data-testid*="campaign"], .campaign, .card, article');
        const campaignCount = await campaignElements.count();
        
        if (campaignCount > 0) {
            console.log(`✅ Found ${campaignCount} campaign elements on page`);
            
            try {
                // Try to click on the first campaign
                await campaignElements.first().click();
                await page.waitForTimeout(2000);
                
                // Look for campaign modal or details
                const modalVisible = await page.locator('.modal, [role="dialog"], .campaign-modal, .overlay').isVisible();
                
                if (modalVisible) {
                    console.log('✅ Campaign modal opened successfully');
                    await page.screenshot({ path: 'core-test-campaign-modal.png' });
                    
                    // Look for action elements
                    const emailToggle = page.locator('input[type="checkbox"]:near(:text("email")), input[type="checkbox"]:near(:text("Email"))').first();
                    const tweetToggle = page.locator('input[type="checkbox"]:near(:text("tweet")), input[type="checkbox"]:near(:text("Twitter"))').first();
                    const sendButton = page.locator('button:has-text("Send"), button:has-text("Submit"), button:has-text("Take Action")').first();
                    
                    const emailToggleVisible = await emailToggle.isVisible();
                    const tweetToggleVisible = await tweetToggle.isVisible();
                    const sendButtonVisible = await sendButton.isVisible();
                    
                    console.log(`📧 Email toggle visible: ${emailToggleVisible}`);
                    console.log(`🐦 Tweet toggle visible: ${tweetToggleVisible}`);
                    console.log(`📤 Send button visible: ${sendButtonVisible}`);
                    
                    // Test email personalization
                    if (emailToggleVisible) {
                        await emailToggle.check();
                        console.log('✅ Email option enabled');
                        testResults.emailGeneration = true;
                    }
                    
                    // Test tweet personalization
                    if (tweetToggleVisible) {
                        await tweetToggle.check();
                        console.log('✅ Tweet option enabled');
                        testResults.tweetGeneration = true;
                    }
                    
                    // Look for personalization fields
                    const nameField = page.locator('input[name*="name"], input[placeholder*="name"], input[placeholder*="Name"]').first();
                    const zipField = page.locator('input[name*="zip"], input[placeholder*="zip"], input[placeholder*="ZIP"]').first();
                    const messageField = page.locator('textarea, input[name*="message"], input[placeholder*="message"]').first();
                    
                    const nameFieldVisible = await nameField.isVisible();
                    const zipFieldVisible = await zipField.isVisible();
                    const messageFieldVisible = await messageField.isVisible();
                    
                    console.log(`👤 Name field visible: ${nameFieldVisible}`);
                    console.log(`📍 ZIP field visible: ${zipFieldVisible}`);
                    console.log(`💬 Message field visible: ${messageFieldVisible}`);
                    
                    // Test personalization
                    if (nameFieldVisible || zipFieldVisible || messageFieldVisible) {
                        testResults.userPersonalization = true;
                        console.log('✅ User personalization fields available');
                        
                        // Fill in test data
                        if (nameFieldVisible) {
                            await nameField.fill('Test User');
                            console.log('📝 Filled name field');
                        }
                        
                        if (zipFieldVisible) {
                            await zipField.fill('10001');
                            console.log('📝 Filled ZIP code');
                        }
                        
                        if (messageFieldVisible) {
                            await messageField.fill('This is a test personalized message for the campaign.');
                            console.log('📝 Filled personalized message');
                        }
                        
                        await page.screenshot({ path: 'core-test-personalization.png' });
                    }
                    
                    // Test action submission (but don't actually submit)
                    if (sendButtonVisible) {
                        testResults.actionSubmission = true;
                        console.log('✅ Action submission button available');
                        console.log('ℹ️  Skipping actual submission to avoid sending test emails/tweets');
                    }
                    
                } else {
                    console.log('⚠️  Campaign modal did not open');
                }
                
            } catch (e) {
                console.log(`⚠️  Campaign interaction failed: ${e.message}`);
            }
        } else {
            console.log('❌ No campaign elements found on page');
        }
        
    } catch (error) {
        console.log(`❌ Core functionality test error: ${error.message}`);
        await page.screenshot({ path: 'core-test-error.png' });
        
    } finally {
        await browser.close();
    }
    
    // ===== FINAL RESULTS =====
    console.log('\n🏆 CORE FUNCTIONALITY TEST RESULTS');
    console.log('===================================');
    
    const coreTests = [
        { name: 'Bills API Working', result: testResults.billsAPI, critical: true },
        { name: 'Bill Summaries Present', result: testResults.billSummaries, critical: true },
        { name: 'Campaign-Bill Integration', result: testResults.campaignInteraction, critical: true },
        { name: 'Representative Finding', result: testResults.representativeFinding, critical: true },
        { name: 'Email Generation Available', result: testResults.emailGeneration, critical: true },
        { name: 'Tweet Generation Available', result: testResults.tweetGeneration, critical: true },
        { name: 'User Personalization', result: testResults.userPersonalization, critical: true },
        { name: 'Action Submission Ready', result: testResults.actionSubmission, critical: true }
    ];
    
    let passedTests = 0;
    let criticalPassed = 0;
    let criticalTotal = 0;
    
    coreTests.forEach(test => {
        const status = test.result ? '✅ PASS' : '❌ FAIL';
        const priority = test.critical ? '[CRITICAL]' : '[OPTIONAL]';
        console.log(`${status} ${priority} - ${test.name}`);
        
        if (test.result) passedTests++;
        if (test.critical) {
            criticalTotal++;
            if (test.result) criticalPassed++;
        }
    });
    
    const overallSuccess = (passedTests / coreTests.length * 100).toFixed(1);
    const criticalSuccess = (criticalPassed / criticalTotal * 100).toFixed(1);
    
    console.log(`\n📊 Overall Success Rate: ${overallSuccess}% (${passedTests}/${coreTests.length})`);
    console.log(`🎯 Critical Features Success: ${criticalSuccess}% (${criticalPassed}/${criticalTotal})`);
    
    if (criticalSuccess >= 100) {
        console.log('\n🎉 🚀 CORE FUNCTIONALITY FULLY OPERATIONAL! 🚀 🎉');
        console.log('All critical features are working correctly.');
        return true;
    } else if (criticalSuccess >= 75) {
        console.log('\n⚠️  MOSTLY FUNCTIONAL - Some issues detected');
        console.log('Core features mostly working but some improvements needed.');
        return true;
    } else {
        console.log('\n❌ CORE FUNCTIONALITY ISSUES - Critical features not working');
        return false;
    }
}

// Execute core functionality test
testCoreFunctionality().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('💥 Core functionality test crashed:', error);
    process.exit(1);
});
