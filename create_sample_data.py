#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to populate the staging database with sample data for testing
"""
import psycopg2
import json
from datetime import datetime, date

# Database connection details from staging environment
DB_CONFIG = {
    'host': 'modernaction-staging-modernactiondatabasefdd241de-giohrnseld4l.csdoaiogadw5.us-east-1.rds.amazonaws.com',
    'port': '5432',
    'database': 'modernaction',
    'user': 'modernaction_admin',
    # Password will need to be retrieved from AWS Secrets Manager
}

def create_sample_data():
    try:
        # Note: This script requires the database password from AWS Secrets Manager
        # For security, we won't hardcode it here
        print("To run this script, you need to:")
        print("1. Get the database password from AWS Secrets Manager")
        print("2. Set the DB_PASSWORD environment variable")
        print("3. Run this script from an environment with database access")
        
        # Sample SQL that would create test data
        sample_data_sql = '''
        -- Insert a sample bill
        INSERT INTO bills (
            title, description, bill_number, bill_type, status, session_year,
            chamber, state, summary, ai_summary, openstates_id, introduced_date,
            sponsor_name, sponsor_party, is_featured, priority_score,
            created_at, updated_at
        ) VALUES (
            'Climate Action Now Act',
            'A bill to require the President to develop and update annually a plan for the United States to meet its nationally determined contribution under the Paris Agreement on climate change.',
            'HR-9',
            'bill',
            'active',
            2023,
            'house',
            'federal',
            'This bill requires the President to develop a plan for meeting US climate commitments under the Paris Agreement.',
            'This important climate legislation would establish a framework for the United States to meet its international climate commitments by requiring annual planning and progress reporting.',
            'hr-9-2023',
            '2023-01-15',
            'Rep. Kathy Castor',
            'Democratic',
            true,
            85,
            NOW(),
            NOW()
        ) ON CONFLICT (openstates_id) DO NOTHING;
        
        -- Insert a sample campaign
        INSERT INTO campaigns (
            title, description, short_description, campaign_type, status,
            call_to_action, target_audience, geographic_scope, start_date,
            end_date, is_featured, is_public, goal_actions, actual_actions,
            bill_id, created_at, updated_at
        ) VALUES (
            'Support Climate Action Now',
            'Join thousands of Americans in urging Congress to pass the Climate Action Now Act. This critical legislation will help the United States meet its climate commitments and lead the global fight against climate change.',
            'Urge your representatives to support climate action legislation',
            'advocacy',
            'active',
            'Email your representatives today!',
            'climate-conscious citizens',
            ARRAY['national'],
            '2023-06-01',
            '2024-06-01',
            true,
            true,
            10000,
            2341,
            (SELECT id FROM bills WHERE openstates_id = 'hr-9-2023' LIMIT 1),
            NOW(),
            NOW()
        );
        
        -- Check if data exists
        SELECT 'Bills:' as table_name, COUNT(*) as count FROM bills
        UNION ALL
        SELECT 'Campaigns:', COUNT(*) FROM campaigns;
        '''
        
        print("\nSample SQL to create test data:")
        print(sample_data_sql)
        
        return sample_data_sql
        
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    print("🔧 Creating sample data for ModernAction staging database...")
    create_sample_data()