#!/usr/bin/env python3
"""
Simple script to create a test bill for testing personal stories integration
"""

import os
import sys
import psycopg2
import uuid
from datetime import datetime

def get_database_url():
    """Get database URL from environment or construct from components."""
    database_url = os.environ.get('DATABASE_URL')
    if database_url:
        return database_url
    
    # Try to construct from individual components
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'modernaction')
    db_username = os.environ.get('DB_USERNAME', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'password')
    
    return f"postgresql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}"

def create_test_bill():
    """Create a simple test bill for testing."""
    
    database_url = get_database_url()
    print(f"Connecting to database...")
    
    try:
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        # Generate a unique bill ID
        bill_id = str(uuid.uuid4())
        
        # Create a test bill
        bill_data = {
            'id': bill_id,
            'bill_number': 'H.R.TEST-123',
            'title': 'Climate Action Test Bill',
            'summary': 'A test bill for climate action to test personal stories integration',
            'status': 'introduced',
            'congress_session': 118,
            'support_reasons': ['Addresses climate change', 'Creates green jobs', 'Protects future generations'],
            'oppose_reasons': ['Economic concerns', 'Implementation challenges'],
            'amend_reasons': ['Needs stronger provisions', 'Timeline too aggressive'],
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        # Insert the bill
        insert_query = """
        INSERT INTO bills (
            id, bill_number, title, summary, status, congress_session,
            support_reasons, oppose_reasons, amend_reasons, created_at, updated_at
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) ON CONFLICT (bill_number) DO UPDATE SET
            title = EXCLUDED.title,
            summary = EXCLUDED.summary,
            updated_at = EXCLUDED.updated_at
        RETURNING id;
        """
        
        cur.execute(insert_query, (
            bill_data['id'],
            bill_data['bill_number'],
            bill_data['title'],
            bill_data['summary'],
            bill_data['status'],
            bill_data['congress_session'],
            bill_data['support_reasons'],
            bill_data['oppose_reasons'],
            bill_data['amend_reasons'],
            bill_data['created_at'],
            bill_data['updated_at']
        ))
        
        result = cur.fetchone()
        if result:
            created_bill_id = result[0]
            print(f"✅ Created test bill with ID: {created_bill_id}")
            print(f"   Bill Number: {bill_data['bill_number']}")
            print(f"   Title: {bill_data['title']}")
        
        # Commit the transaction
        conn.commit()
        
        # Return the bill ID for testing
        return created_bill_id
        
    except Exception as e:
        print(f"❌ Error creating test bill: {e}")
        if 'conn' in locals():
            conn.rollback()
        return None
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()

def verify_bill_exists(bill_id):
    """Verify the bill was created successfully."""
    database_url = get_database_url()
    
    try:
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        cur.execute("SELECT id, bill_number, title FROM bills WHERE id = %s", (bill_id,))
        result = cur.fetchone()
        
        if result:
            print(f"✅ Bill verification successful:")
            print(f"   ID: {result[0]}")
            print(f"   Number: {result[1]}")
            print(f"   Title: {result[2]}")
            return True
        else:
            print(f"❌ Bill not found in database")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying bill: {e}")
        return False
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🧪 Creating Test Bill for Personal Stories Integration")
    print("=" * 55)
    
    bill_id = create_test_bill()
    
    if bill_id:
        print("\n🔍 Verifying bill creation...")
        if verify_bill_exists(bill_id):
            print(f"\n🎉 SUCCESS! Test bill created successfully.")
            print(f"📋 Use this bill ID for testing: {bill_id}")
            print(f"🧪 You can now test the personal stories API with this bill.")
        else:
            print(f"\n❌ FAILED! Bill creation verification failed.")
            sys.exit(1)
    else:
        print(f"\n❌ FAILED! Could not create test bill.")
        sys.exit(1)
