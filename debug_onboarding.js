#!/usr/bin/env node
const { chromium } = require('playwright');

async function debugOnboarding() {
  console.log('🔍 Debugging Onboarding Setup...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Listen for console logs
  page.on('console', msg => console.log('BROWSER LOG:', msg.text()));
  page.on('pageerror', error => console.log('BROWSER ERROR:', error.message));
  
  try {
    // Clear localStorage and go to page
    await page.goto('http://localhost:3000');
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    
    console.log('📄 Page loaded, localStorage cleared');
    
    // Reload to trigger onboarding
    await page.reload();
    await page.waitForTimeout(3000);
    
    // Check what's actually on the page
    const title = await page.title();
    console.log('📝 Page title:', title);
    
    // Look for any elements with data-testid
    const testIds = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('[data-testid]'));
      return elements.map(el => el.getAttribute('data-testid'));
    });
    console.log('🎯 Found test IDs:', testIds);
    
    // Check if modal exists (even if not visible)
    const modalExists = await page.$('[data-testid="onboarding-modal"]');
    console.log('🔍 Modal exists:', !!modalExists);
    
    if (modalExists) {
      const isVisible = await modalExists.isVisible();
      console.log('👁️ Modal visible:', isVisible);
    }
    
    // Check for Dialog or any modal-like elements
    const dialogElements = await page.$$('div[role="dialog"]');
    console.log('💬 Dialog elements found:', dialogElements.length);
    
    // Look for text content that might indicate onboarding
    const welcomeText = await page.textContent('body');
    const hasWelcome = welcomeText.includes('Welcome to ModernAction');
    console.log('👋 Has welcome text:', hasWelcome);
    
    // Check localStorage
    const localStorage = await page.evaluate(() => {
      const data = localStorage.getItem('modernaction-onboarding');
      return data ? JSON.parse(data) : null;
    });
    console.log('💾 localStorage data:', localStorage);
    
    // Check if Zustand store is working
    const zustandStore = await page.evaluate(() => {
      return window.__zustandDebug || 'Zustand debug not available';
    });
    console.log('🗃️ Zustand store:', zustandStore);
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'debug-onboarding.png', fullPage: true });
    console.log('📸 Screenshot saved as debug-onboarding.png');
    
    // Keep browser open for manual inspection
    console.log('🔍 Browser will stay open for 30 seconds for manual inspection...');
    await page.waitForTimeout(30000);
    
  } catch (error) {
    console.log('❌ Debug failed:', error.message);
  } finally {
    await browser.close();
  }
}

debugOnboarding();