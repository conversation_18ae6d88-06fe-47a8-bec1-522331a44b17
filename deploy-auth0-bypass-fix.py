#!/usr/bin/env python3
"""
Deploy the Auth0 bypass fix using the new Docker image
"""

import boto3
import json
import time

def main():
    ecs = boto3.client('ecs', region_name='us-east-1')
    
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-web-staging'
    secret_arn = 'arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0ConfigSecret5DCB30FC-unX0jBWDsgB7-I7b74d'
    new_image = '308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:final-auth0-working'
    
    print("🚀 Deploying Auth0 bypass fix...")
    
    # Get current working task definition
    print("📋 Getting current working task definition...")
    service_response = ecs.describe_services(
        cluster=cluster_name,
        services=[service_name]
    )
    
    if not service_response['services']:
        print("❌ Service not found!")
        return
    
    # Get the task definition that's actually working (from running tasks)
    tasks_response = ecs.list_tasks(
        cluster=cluster_name,
        serviceName=service_name,
        desiredStatus='RUNNING'
    )
    
    if not tasks_response['taskArns']:
        print("❌ No running tasks found!")
        return
    
    # Get details of the running task
    task_details = ecs.describe_tasks(
        cluster=cluster_name,
        tasks=[tasks_response['taskArns'][0]]
    )
    
    working_task_def_arn = task_details['tasks'][0]['taskDefinitionArn']
    print(f"Working task definition: {working_task_def_arn}")
    
    # Get the working task definition
    task_def_response = ecs.describe_task_definition(
        taskDefinition=working_task_def_arn
    )
    
    task_def = task_def_response['taskDefinition']
    
    # Update the web container with new image and Auth0 secrets
    print("🔧 Updating task definition with new image and Auth0 secrets...")
    
    # Find the web container
    web_container = None
    for container in task_def['containerDefinitions']:
        if container['name'] == 'WebContainer':
            web_container = container
            break
    
    if not web_container:
        print("❌ WebContainer not found in task definition!")
        return
    
    print(f"Current image: {web_container['image']}")
    print(f"New image: {new_image}")
    
    # Update the image
    web_container['image'] = new_image
    
    # Add secrets to the web container
    if 'secrets' not in web_container:
        web_container['secrets'] = []

    # Add Auth0 secrets
    auth0_secrets = [
        {
            'name': 'AUTH0_SECRET',
            'valueFrom': f'{secret_arn}:AUTH0_SECRET::'
        },
        {
            'name': 'AUTH0_CLIENT_SECRET',
            'valueFrom': f'{secret_arn}:AUTH0_CLIENT_SECRET::'
        }
    ]

    # Remove existing Auth0 secrets if any
    web_container['secrets'] = [s for s in web_container['secrets']
                               if s['name'] not in ['AUTH0_SECRET', 'AUTH0_CLIENT_SECRET']]

    # Add new Auth0 secrets
    web_container['secrets'].extend(auth0_secrets)

    # Add public Auth0 environment variables for client-side
    if 'environment' not in web_container:
        web_container['environment'] = []

    # Remove existing public Auth0 env vars if any
    web_container['environment'] = [e for e in web_container['environment']
                                   if e['name'] not in ['NEXT_PUBLIC_AUTH0_DOMAIN', 'NEXT_PUBLIC_AUTH0_CLIENT_ID', 'NEXT_PUBLIC_AUTH0_AUDIENCE']]

    # Add new public Auth0 environment variables
    public_auth0_env_vars = [
        {
            'name': 'NEXT_PUBLIC_AUTH0_DOMAIN',
            'value': 'dev-vvwd64m28nwqm871.us.auth0.com'
        },
        {
            'name': 'NEXT_PUBLIC_AUTH0_CLIENT_ID',
            'value': 'DSImZkGjtsDp2ZoJWdRVN57YW37cQxY4'
        },
        {
            'name': 'NEXT_PUBLIC_AUTH0_AUDIENCE',
            'value': 'https://api.modernaction.io'
        }
    ]

    web_container['environment'].extend(public_auth0_env_vars)

    print(f"✅ Updated image, added {len(auth0_secrets)} Auth0 secrets, and {len(public_auth0_env_vars)} public Auth0 env vars to WebContainer")
    
    # Register new task definition
    print("📝 Registering new task definition...")
    
    # Remove fields that shouldn't be in the registration request
    task_def_for_registration = {
        'family': task_def['family'],
        'taskRoleArn': task_def['taskRoleArn'],
        'executionRoleArn': task_def['executionRoleArn'],
        'networkMode': task_def['networkMode'],
        'containerDefinitions': task_def['containerDefinitions'],
        'requiresCompatibilities': task_def['requiresCompatibilities'],
        'cpu': task_def['cpu'],
        'memory': task_def['memory']
    }
    
    new_task_def_response = ecs.register_task_definition(**task_def_for_registration)
    new_task_def_arn = new_task_def_response['taskDefinition']['taskDefinitionArn']
    
    print(f"✅ New task definition registered: {new_task_def_arn}")
    
    # Update service to use new task definition
    print("🔄 Updating ECS service...")
    
    ecs.update_service(
        cluster=cluster_name,
        service=service_name,
        taskDefinition=new_task_def_arn,
        forceNewDeployment=True
    )
    
    print("✅ Service update initiated!")
    print("⏳ Waiting for deployment to complete...")
    
    # Wait for deployment to complete
    waiter = ecs.get_waiter('services_stable')
    try:
        waiter.wait(
            cluster=cluster_name,
            services=[service_name],
            WaiterConfig={
                'delay': 15,
                'maxAttempts': 20  # 5 minutes max
            }
        )
        print("🎉 Deployment completed successfully!")
        
    except Exception as e:
        print(f"⚠️  Deployment may still be in progress: {e}")
    
    print("🔗 Test the application at: https://staging.modernaction.io")
    print("🎯 Expected result: Application should load without Auth0 configuration errors")

if __name__ == '__main__':
    main()
