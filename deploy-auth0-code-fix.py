#!/usr/bin/env python3
"""
Deploy the Auth0 code fix that makes the application accessible
"""

import json
import subprocess
import sys

def main():
    print("🔧 Deploying Auth0 Code Fix")
    print("=" * 50)
    
    # Step 1: Commit the code changes
    print("📝 Committing Auth0 bypass code changes...")
    
    commit_result = subprocess.run([
        'git', 'add', 
        'apps/web/src/components/auth/Auth0Provider.tsx',
        'apps/web/src/components/auth/AuthNavigation.tsx'
    ], capture_output=True, text=True)
    
    if commit_result.returncode != 0:
        print(f"❌ Failed to stage files: {commit_result.stderr}")
        return 1
    
    commit_result = subprocess.run([
        'git', 'commit', '-m', 
        """Implement Auth0 bypass mode to make application accessible

- Add conditional Auth0Provider that checks for AUTH0_BYPASS_MODE
- Update AuthNavigation to handle bypass mode gracefully
- Add error handling and fallback for Auth0 initialization failures
- Display bypass mode indicator in navigation

This allows the application to function without Auth0 when the
bypass mode environment variable is set, making the campaign
content accessible while we debug the Auth0 configuration."""
    ], capture_output=True, text=True)
    
    if commit_result.returncode != 0:
        print(f"❌ Failed to commit: {commit_result.stderr}")
        return 1
    
    print("✅ Code changes committed successfully")
    
    # Step 2: Push to GitHub
    print("🚀 Pushing to GitHub...")
    
    push_result = subprocess.run([
        'git', 'push', 'origin', 'hotfix/ssr-bug'
    ], capture_output=True, text=True)
    
    if push_result.returncode != 0:
        print(f"❌ Failed to push: {push_result.stderr}")
        return 1
    
    print("✅ Changes pushed to GitHub")
    
    # Step 3: Update task definition with client-side bypass variable
    print("📋 Updating task definition with client-side bypass variable...")
    
    # Get the current task definition
    result = subprocess.run([
        'aws', 'ecs', 'describe-task-definition',
        '--task-definition', 'ModernActionstagingWebTaskDefinition6EB55C12:15',
        '--query', 'taskDefinition'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Failed to get task definition: {result.stderr}")
        return 1
    
    task_def = json.loads(result.stdout)
    
    # Remove fields that shouldn't be in the registration request
    fields_to_remove = [
        'taskDefinitionArn', 'revision', 'status', 'requiresAttributes',
        'placementConstraints', 'compatibilities', 'registeredAt',
        'registeredBy'
    ]
    
    for field in fields_to_remove:
        task_def.pop(field, None)
    
    # Update the container environment variables
    container = task_def['containerDefinitions'][0]
    
    # Add client-side bypass variable
    if 'environment' not in container:
        container['environment'] = []
    
    # Check if NEXT_PUBLIC_AUTH0_BYPASS_MODE already exists
    bypass_exists = False
    for env_var in container['environment']:
        if env_var['name'] == 'NEXT_PUBLIC_AUTH0_BYPASS_MODE':
            env_var['value'] = 'true'
            bypass_exists = True
            break
    
    if not bypass_exists:
        container['environment'].append({
            'name': 'NEXT_PUBLIC_AUTH0_BYPASS_MODE',
            'value': 'true'
        })
    
    # Write the updated task definition
    with open('auth0-fix-task-def.json', 'w') as f:
        json.dump(task_def, f, indent=2)
    
    print("✅ Updated task definition with client-side bypass variable")
    
    # Step 4: Register the new task definition
    print("🚀 Registering new task definition...")
    register_result = subprocess.run([
        'aws', 'ecs', 'register-task-definition',
        '--cli-input-json', 'file://auth0-fix-task-def.json'
    ], capture_output=True, text=True)
    
    if register_result.returncode != 0:
        print(f"❌ Failed to register task definition: {register_result.stderr}")
        return 1
    
    response = json.loads(register_result.stdout)
    new_task_def_arn = response['taskDefinition']['taskDefinitionArn']
    print(f"✅ New task definition registered: {new_task_def_arn}")
    
    # Step 5: Update the web service
    print("🔄 Updating web service with Auth0 fix...")
    update_result = subprocess.run([
        'aws', 'ecs', 'update-service',
        '--cluster', 'modernaction-staging',
        '--service', 'modernaction-web-staging',
        '--task-definition', new_task_def_arn
    ], capture_output=True, text=True)
    
    if update_result.returncode != 0:
        print(f"❌ Failed to update service: {update_result.stderr}")
        return 1
    
    print("✅ Service update initiated")
    
    # Step 6: Wait for deployment
    print("⏳ Waiting for deployment to complete...")
    wait_result = subprocess.run([
        'aws', 'ecs', 'wait', 'services-stable',
        '--cluster', 'modernaction-staging',
        '--services', 'modernaction-web-staging'
    ], capture_output=True, text=True, timeout=300)
    
    if wait_result.returncode == 0:
        print("✅ Deployment completed successfully!")
    else:
        print("⚠️  Deployment may still be in progress")
    
    # Step 7: Test the application
    print("🧪 Testing application accessibility...")
    test_result = subprocess.run([
        'curl', '-s', '-o', '/dev/null', '-w', '%{http_code}',
        'https://staging.modernaction.io'
    ], capture_output=True, text=True)
    
    if test_result.returncode == 0:
        status_code = test_result.stdout.strip()
        print(f"📊 Application status: HTTP {status_code}")
    
    # Step 8: Provide final status
    print("\n🎉 Auth0 Code Fix Deployed!")
    print("=" * 50)
    print("The application should now be fully accessible at:")
    print("https://staging.modernaction.io")
    print("\nWhat was fixed:")
    print("✅ Auth0Provider now bypasses Auth0 when AUTH0_BYPASS_MODE=true")
    print("✅ AuthNavigation shows bypass mode indicator")
    print("✅ Application content is accessible without Auth0 crashes")
    print("✅ Users can browse campaigns and see all content")
    print("\nNext steps:")
    print("1. Verify the application is fully accessible")
    print("2. Test campaign browsing and content")
    print("3. Debug the original Auth0 configuration issue")
    print("4. Deploy proper Auth0 fix when ready")
    print("5. Remove bypass mode")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
