#!/usr/bin/env python3
"""
Deploy Auth0 debugging fixes to prevent complete application crash
"""

import json
import subprocess
import sys
import time

def main():
    print("🔧 Deploying Auth0 Debug Fixes")
    print("=" * 50)
    
    # Step 1: Commit the debugging changes
    print("📝 Committing debugging changes...")
    
    commit_result = subprocess.run([
        'git', 'add',
        'apps/web/src/app/layout.tsx',
        'apps/web/src/components/auth/Auth0ErrorBoundary.tsx',
        'apps/web/src/app/debug/environment/page.tsx',
        'docs/CRITICAL_AUTH0_ISSUE_ANALYSIS.md'
    ], capture_output=True, text=True)
    
    if commit_result.returncode != 0:
        print(f"❌ Failed to stage files: {commit_result.stderr}")
        return 1
    
    commit_result = subprocess.run([
        'git', 'commit', '-m', 
        """Add Auth0 debugging tools and error boundary

- Add Auth0ErrorBoundary to prevent complete app crash
- Add /debug/env page to check environment variables
- Update layout.tsx to use error boundary
- Add comprehensive Auth0 issue analysis documentation

This prevents the "Auth0 configuration error" from making
the entire application unusable while we debug the issue."""
    ], capture_output=True, text=True)
    
    if commit_result.returncode != 0:
        print(f"❌ Failed to commit: {commit_result.stderr}")
        return 1
    
    print("✅ Changes committed successfully")
    
    # Step 2: Push to GitHub
    print("🚀 Pushing to GitHub...")
    
    push_result = subprocess.run([
        'git', 'push', 'origin', 'hotfix/ssr-bug'
    ], capture_output=True, text=True)
    
    if push_result.returncode != 0:
        print(f"❌ Failed to push: {push_result.stderr}")
        return 1
    
    print("✅ Changes pushed to GitHub")
    
    # Step 3: Trigger CodeBuild
    print("🏗️  Starting CodeBuild...")
    
    build_result = subprocess.run([
        'aws', 'codebuild', 'start-build',
        '--project-name', 'modernaction-web-final-build',
        '--source-version', 'hotfix/ssr-bug'
    ], capture_output=True, text=True)
    
    if build_result.returncode != 0:
        print(f"❌ Failed to start build: {build_result.stderr}")
        return 1
    
    build_response = json.loads(build_result.stdout)
    build_id = build_response['build']['id']
    print(f"✅ Build started: {build_id}")
    
    # Step 4: Monitor build progress
    print("⏳ Monitoring build progress...")
    
    max_wait_time = 600  # 10 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        status_result = subprocess.run([
            'aws', 'codebuild', 'batch-get-builds',
            '--ids', build_id,
            '--query', 'builds[0].{buildStatus: buildStatus, currentPhase: currentPhase}'
        ], capture_output=True, text=True)
        
        if status_result.returncode == 0:
            status = json.loads(status_result.stdout)
            build_status = status.get('buildStatus', 'UNKNOWN')
            current_phase = status.get('currentPhase', 'UNKNOWN')
            
            print(f"📊 Build Status: {build_status}, Phase: {current_phase}")
            
            if build_status == 'SUCCEEDED':
                print("✅ Build completed successfully!")
                break
            elif build_status in ['FAILED', 'FAULT', 'STOPPED', 'TIMED_OUT']:
                print(f"❌ Build failed with status: {build_status}")
                return 1
        
        time.sleep(30)  # Wait 30 seconds before checking again
    
    else:
        print("⚠️  Build is taking longer than expected, continuing with deployment...")
    
    # Step 5: Update ECS service (force new deployment)
    print("🔄 Forcing ECS service update...")
    
    # Get the current task definition
    task_def_result = subprocess.run([
        'aws', 'ecs', 'describe-services',
        '--cluster', 'modernaction-staging',
        '--services', 'modernaction-web-staging',
        '--query', 'services[0].taskDefinition'
    ], capture_output=True, text=True)
    
    if task_def_result.returncode != 0:
        print(f"❌ Failed to get current task definition: {task_def_result.stderr}")
        return 1
    
    current_task_def = json.loads(task_def_result.stdout)
    
    # Force new deployment with the same task definition
    update_result = subprocess.run([
        'aws', 'ecs', 'update-service',
        '--cluster', 'modernaction-staging',
        '--service', 'modernaction-web-staging',
        '--task-definition', current_task_def,
        '--force-new-deployment'
    ], capture_output=True, text=True)
    
    if update_result.returncode != 0:
        print(f"❌ Failed to update service: {update_result.stderr}")
        return 1
    
    print("✅ Service update initiated")
    
    # Step 6: Wait for deployment to stabilize
    print("⏳ Waiting for deployment to complete...")
    
    wait_result = subprocess.run([
        'aws', 'ecs', 'wait', 'services-stable',
        '--cluster', 'modernaction-staging',
        '--services', 'modernaction-web-staging'
    ], capture_output=True, text=True, timeout=600)  # 10 minute timeout
    
    if wait_result.returncode == 0:
        print("✅ Deployment completed successfully!")
    else:
        print("⚠️  Deployment may still be in progress")
    
    # Step 7: Provide next steps
    print("\n🎉 Auth0 Debug Fixes Deployed!")
    print("=" * 50)
    print("Next steps:")
    print("1. Visit https://staging.modernaction.io to see if the error boundary works")
    print("2. Visit https://staging.modernaction.io/debug/environment to check environment variables")
    print("3. Check the error boundary message for specific Auth0 configuration issues")
    print("4. Use the debugging information to fix the root cause")
    print("\nThe application should now show a proper error message instead of crashing completely.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
