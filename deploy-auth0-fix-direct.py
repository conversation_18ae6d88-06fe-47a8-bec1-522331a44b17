#!/usr/bin/env python3
"""
Direct ECS deployment script to fix Auth0 configuration
This bypasses CloudFormation and updates the ECS task definition directly
"""

import boto3
import json
import time

def main():
    # AWS clients
    ecs = boto3.client('ecs', region_name='us-east-1')
    
    # Configuration
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-web-staging'
    secret_arn = 'arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0ConfigSecret5DCB30FC-unX0jBWDsgB7-I7b74d'
    
    print("🚀 Starting direct Auth0 fix deployment...")
    
    # Step 1: Get current task definition
    print("📋 Getting current task definition...")
    service_response = ecs.describe_services(
        cluster=cluster_name,
        services=[service_name]
    )
    
    if not service_response['services']:
        print("❌ Service not found!")
        return
    
    current_task_def_arn = service_response['services'][0]['taskDefinition']
    print(f"Current task definition: {current_task_def_arn}")
    
    # Get task definition details
    task_def_response = ecs.describe_task_definition(
        taskDefinition=current_task_def_arn
    )
    
    task_def = task_def_response['taskDefinition']
    
    # Step 2: Modify task definition to add Auth0 secrets
    print("🔧 Adding Auth0 secrets to task definition...")
    
    # Find the web container
    web_container = None
    for container in task_def['containerDefinitions']:
        if container['name'] == 'WebContainer':
            web_container = container
            break
    
    if not web_container:
        print("❌ WebContainer not found in task definition!")
        return
    
    # Add secrets to the web container
    if 'secrets' not in web_container:
        web_container['secrets'] = []
    
    # Add Auth0 secrets
    auth0_secrets = [
        {
            'name': 'AUTH0_SECRET',
            'valueFrom': f'{secret_arn}:AUTH0_SECRET::'
        },
        {
            'name': 'AUTH0_CLIENT_SECRET',
            'valueFrom': f'{secret_arn}:AUTH0_CLIENT_SECRET::'
        }
    ]
    
    # Remove existing Auth0 secrets if any
    web_container['secrets'] = [s for s in web_container['secrets'] 
                               if s['name'] not in ['AUTH0_SECRET', 'AUTH0_CLIENT_SECRET']]
    
    # Add new Auth0 secrets
    web_container['secrets'].extend(auth0_secrets)
    
    print(f"✅ Added {len(auth0_secrets)} Auth0 secrets to WebContainer")
    
    # Step 3: Register new task definition
    print("📝 Registering new task definition...")
    
    # Remove fields that shouldn't be in the registration request
    task_def_for_registration = {
        'family': task_def['family'],
        'taskRoleArn': task_def['taskRoleArn'],
        'executionRoleArn': task_def['executionRoleArn'],
        'networkMode': task_def['networkMode'],
        'containerDefinitions': task_def['containerDefinitions'],
        'requiresCompatibilities': task_def['requiresCompatibilities'],
        'cpu': task_def['cpu'],
        'memory': task_def['memory']
    }
    
    new_task_def_response = ecs.register_task_definition(**task_def_for_registration)
    new_task_def_arn = new_task_def_response['taskDefinition']['taskDefinitionArn']
    
    print(f"✅ New task definition registered: {new_task_def_arn}")
    
    # Step 4: Update service to use new task definition
    print("🔄 Updating ECS service...")
    
    ecs.update_service(
        cluster=cluster_name,
        service=service_name,
        taskDefinition=new_task_def_arn,
        forceNewDeployment=True
    )
    
    print("✅ Service update initiated!")
    print("⏳ Waiting for deployment to complete...")
    
    # Wait for deployment to complete
    waiter = ecs.get_waiter('services_stable')
    try:
        waiter.wait(
            cluster=cluster_name,
            services=[service_name],
            WaiterConfig={
                'delay': 15,
                'maxAttempts': 40  # 10 minutes max
            }
        )
        print("🎉 Deployment completed successfully!")
        print("🔗 Test the application at: https://staging.modernaction.io")
        
    except Exception as e:
        print(f"⚠️  Deployment may still be in progress: {e}")
        print("🔗 Check the application at: https://staging.modernaction.io")

if __name__ == '__main__':
    main()
