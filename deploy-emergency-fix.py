#!/usr/bin/env python3

import boto3
import json
import time
import sys

def deploy_emergency_fix():
    """Deploy emergency fix by forcing new deployment with existing image"""
    
    # Initialize AWS clients
    ecs = boto3.client('ecs', region_name='us-east-1')
    
    print("🚨 DEPLOYING EMERGENCY SQLALCHEMY FIX")
    print("=" * 50)
    
    # Since we can't build a new image easily, let's force a new deployment
    # The fix is already in the codebase, we just need to restart the containers
    
    print("🔄 Step 1: Force new deployment to pick up code changes...")
    
    try:
        # Force new deployment - this will restart containers with latest code
        ecs.update_service(
            cluster='modernaction-staging',
            service='modernaction-api-staging',
            forceNewDeployment=True
        )
        
        print("✅ ECS service update initiated with force new deployment")
        
        # Wait for deployment to complete
        print("⏳ Waiting for deployment to complete...")
        waiter = ecs.get_waiter('services_stable')
        waiter.wait(
            cluster='modernaction-staging',
            services=['modernaction-api-staging'],
            WaiterConfig={
                'delay': 15,
                'maxAttempts': 40  # 10 minutes
            }
        )
        
        print("✅ Deployment completed successfully!")
        
    except Exception as e:
        print(f"❌ Service update failed: {e}")
        return False
    
    # Step 2: Verify the fix
    print("\n🔍 Step 2: Verifying the fix...")
    
    import requests
    
    # Wait a bit for the service to be fully ready
    print("⏳ Waiting 30 seconds for service to be ready...")
    time.sleep(30)
    
    try:
        # Test the campaigns API endpoint
        response = requests.get('https://staging.modernaction.io/api/v1/campaigns', timeout=30)
        
        if response.status_code == 200:
            campaigns = response.json()
            if isinstance(campaigns, list) and len(campaigns) > 0:
                print(f"✅ SUCCESS! API returned {len(campaigns)} campaigns")
                print("🎉 THE FINAL BUG IS FIXED!")
                print("🚀 ModernAction.io is ready for launch!")
                return True
            else:
                print(f"⚠️  API returned empty list: {campaigns}")
                return False
        else:
            print(f"❌ API returned status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API verification failed: {e}")
        return False

if __name__ == "__main__":
    success = deploy_emergency_fix()
    sys.exit(0 if success else 1)
