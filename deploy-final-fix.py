#!/usr/bin/env python3

import boto3
import json
import time
import sys

def deploy_final_fix():
    """Deploy the final SQLAlchemy fix to resolve the campaigns API issue"""
    
    # Initialize AWS clients
    ecs = boto3.client('ecs', region_name='us-east-1')
    codebuild = boto3.client('codebuild', region_name='us-east-1')
    
    print("🚀 DEPLOYING FINAL SQLALCHEMY FIX")
    print("=" * 50)
    
    # Step 1: Trigger CodeBuild to build and push the fixed API image
    print("📦 Step 1: Building fixed API image via CodeBuild...")
    
    try:
        build_response = codebuild.start_build(
            projectName='modernaction-build-project',
            environmentVariablesOverride=[
                {
                    'name': 'IMAGE_TAG',
                    'value': 'final-launch-candidate-v4'
                }
            ]
        )
        
        build_id = build_response['build']['id']
        print(f"✅ CodeBuild started: {build_id}")
        
        # Wait for build to complete
        print("⏳ Waiting for build to complete...")
        while True:
            build_status = codebuild.batch_get_builds(ids=[build_id])
            status = build_status['builds'][0]['buildStatus']
            
            if status == 'SUCCEEDED':
                print("✅ Build completed successfully!")
                break
            elif status in ['FAILED', 'FAULT', 'STOPPED', 'TIMED_OUT']:
                print(f"❌ Build failed with status: {status}")
                return False
            else:
                print(f"⏳ Build status: {status}")
                time.sleep(30)
        
    except Exception as e:
        print(f"❌ CodeBuild failed: {e}")
        return False
    
    # Step 2: Update ECS task definition with new image
    print("\n🔄 Step 2: Updating ECS task definition...")
    
    try:
        # Get current task definition
        current_task_def = ecs.describe_task_definition(
            taskDefinition='modernaction-api-staging'
        )
        
        # Create new task definition with updated image
        task_def = current_task_def['taskDefinition']
        
        # Update the API container image
        for container in task_def['containerDefinitions']:
            if container['name'] == 'web':
                old_image = container['image']
                container['image'] = '891377308049.dkr.ecr.us-east-1.amazonaws.com/modernaction-api:final-launch-candidate-v4'
                print(f"📝 Updated image: {old_image} -> {container['image']}")
        
        # Remove fields that can't be included in register_task_definition
        fields_to_remove = [
            'taskDefinitionArn', 'revision', 'status', 'requiresAttributes',
            'placementConstraints', 'compatibilities', 'registeredAt', 'registeredBy'
        ]
        
        for field in fields_to_remove:
            task_def.pop(field, None)
        
        # Register new task definition
        new_task_def = ecs.register_task_definition(**task_def)
        new_task_def_arn = new_task_def['taskDefinition']['taskDefinitionArn']
        
        print(f"✅ New task definition registered: {new_task_def_arn}")
        
    except Exception as e:
        print(f"❌ Task definition update failed: {e}")
        return False
    
    # Step 3: Update ECS service
    print("\n🔄 Step 3: Updating ECS service...")
    
    try:
        ecs.update_service(
            cluster='modernaction-staging',
            service='modernaction-api-staging',
            taskDefinition=new_task_def_arn,
            forceNewDeployment=True
        )
        
        print("✅ ECS service update initiated")
        
        # Wait for deployment to complete
        print("⏳ Waiting for deployment to complete...")
        waiter = ecs.get_waiter('services_stable')
        waiter.wait(
            cluster='modernaction-staging',
            services=['modernaction-api-staging'],
            WaiterConfig={
                'delay': 15,
                'maxAttempts': 40  # 10 minutes
            }
        )
        
        print("✅ Deployment completed successfully!")
        
    except Exception as e:
        print(f"❌ Service update failed: {e}")
        return False
    
    # Step 4: Verify the fix
    print("\n🔍 Step 4: Verifying the fix...")
    
    import requests
    
    try:
        # Test the campaigns API endpoint
        response = requests.get('https://staging.modernaction.io/api/v1/campaigns', timeout=30)
        
        if response.status_code == 200:
            campaigns = response.json()
            if isinstance(campaigns, list) and len(campaigns) > 0:
                print(f"✅ SUCCESS! API returned {len(campaigns)} campaigns")
                print("🎉 THE FINAL BUG IS FIXED!")
                print("🚀 ModernAction.io is ready for launch!")
                return True
            else:
                print(f"⚠️  API returned empty list: {campaigns}")
                return False
        else:
            print(f"❌ API returned status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API verification failed: {e}")
        return False

if __name__ == "__main__":
    success = deploy_final_fix()
    sys.exit(0 if success else 1)
