#!/bin/bash

# Deploy sample data to staging environment
# This script retrieves the database password from AWS Secrets Manager and runs the seeding

echo "🔐 Retrieving database password from AWS Secrets Manager..."

# Get the database password from AWS Secrets Manager
DB_PASSWORD=$(aws secretsmanager get-secret-value \
    --secret-id "DatabaseCredentials8547B3E7-7zaDeXDnFrx0" \
    --query 'SecretString' \
    --output text | jq -r '.password')

if [ -z "$DB_PASSWORD" ]; then
    echo "❌ Failed to retrieve database password from AWS Secrets Manager"
    echo "Make sure you have AWS CLI configured and access to the secret"
    exit 1
fi

echo "✅ Database password retrieved successfully"
echo "🌱 Running data seeding script..."

# Export the password and run the seeding script
export DB_PASSWORD="$DB_PASSWORD"
python seed_staging_data.py

echo "🎉 Sample data deployment complete!"
echo "🌐 Visit https://staging.modernaction.io to see the populated content"