version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: modernaction
      POSTGRES_USER: modernaction_user
      POSTGRES_PASSWORD: modernaction_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./apps/api/scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U modernaction_user -d modernaction"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and background tasks
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # FastAPI Backend
  api:
    build:
      context: ./apps/api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: ************************************************************/modernaction
      DB_HOST: db
      DB_PORT: 5432
      DB_NAME: modernaction
      DB_USERNAME: modernaction_user
      DB_PASSWORD: modernaction_password
      REDIS_URL: redis://redis:6379/0
      ENVIRONMENT: development
      SECRET_KEY: development-secret-key
      JWT_SECRET: development-jwt-secret
      CORS_ORIGINS: http://localhost:3000,http://localhost:3001,http://localhost:3002
      # External API Keys
      OPENSTATES_API_KEY: 6080864a-b9e2-4570-b67e-0abdfcdee059
      GOOGLE_CIVIC_INFO_API_KEY: AIzaSyAA5ShGbL9sifQFSBdLKn2fGOa2JHvQ4To
      CONGRESS_GOV_API_KEY: T1RKX9f8kEspd5nynWFRGqg18S4wd1aH3TFPh3HW
      HUGGING_FACE_API_KEY: *************************************
      OPENAI_API_KEY: ********************************************************************************************************************************************************************
      ACTION_NETWORK_API_KEY: 5f1b67417939be842570bf40917661ac
    volumes:
      - ./apps/api:/app
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js Frontend
  web:
    build:
      context: ./apps/web
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000/api/v1
      AUTH0_SECRET: FClGARHv7jAop8IPRCgGFVxdxnplWigb
      AUTH0_BASE_URL: http://localhost:3000
      AUTH0_ISSUER_BASE_URL: https://dev-vvwd64m28nwqm871.us.auth0.com
      AUTH0_CLIENT_ID: Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC
      AUTH0_CLIENT_SECRET: ****************************************************************
      AUTH0_AUDIENCE: https://api.modernaction.io
      NODE_ENV: development
    volumes:
      - ./apps/web:/app
      - /app/node_modules
    depends_on:
      - api

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - api
      - web

volumes:
  postgres_data:
  redis_data: