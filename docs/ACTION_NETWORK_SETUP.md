# Action Network Integration Setup Guide

This guide walks you through setting up Action Network integration for ModernAction.io to enable real message delivery to elected officials.

## Overview

Action Network is a digital organizing platform that provides APIs for sending messages to elected officials. Our integration uses the Action Network API v2 with OSDI (Open Supporter Data Interface) format for professional political communications.

## Prerequisites

- Action Network account (free tier available)
- AWS CLI configured (for production deployment)
- Access to AWS Secrets Manager (for production)

## Step 1: Create Action Network Account

1. **Sign Up**
   - Go to [https://actionnetwork.org](https://actionnetwork.org)
   - Click "Sign Up" and create a free account
   - Verify your email address

2. **Complete Profile**
   - Fill out your organization information
   - Add contact details and organization description
   - This information will be associated with messages sent through the API

## Step 2: Generate API Key

1. **Access API Settings**
   - Log into your Action Network dashboard
   - Navigate to **Settings** → **API & Sync**

2. **Generate API Key**
   - Click "Generate New API Key"
   - Copy the API key immediately (it won't be shown again)
   - Store it securely

3. **Note API Limits**
   - Free tier: 1,000 API calls per month
   - Paid tiers: Higher limits available
   - Monitor usage in the API & Sync section

## Step 3: Configure API Key

### Local Development

Add the API key to your environment:

```bash
# Add to your .env file
echo "ACTION_NETWORK_API_KEY=your-api-key-here" >> apps/api/.env

# Or export directly
export ACTION_NETWORK_API_KEY="your-api-key-here"
```

### Production (AWS Secrets Manager)

Store the API key securely in AWS Secrets Manager:

```bash
# Create the secret
aws secretsmanager create-secret \
  --name "action-network-api-key" \
  --description "Action Network API key for ModernAction.io" \
  --secret-string "your-api-key-here"

# Update CDK configuration to reference this secret
# (This should already be configured in your infrastructure)
```

## Step 4: Test the Integration

Run the test script to verify everything is working:

```bash
cd apps/api
python scripts/test_action_network.py
```

Expected output:
```
🧪 Action Network Integration Test
========================================
✅ API Key configured (ends with: ...xyz)
🔍 Testing Action Network API health...
✅ Action Network API is healthy and accessible

📤 Testing Action Network message submission...
✅ Action Network message submission test successful
   Total targets: 1
   Successful submissions: 1
   Failed submissions: 0

🎉 All Action Network tests passed!
   Your Action Network integration is ready for production.
```

## Step 5: Verify in Action Network Dashboard

1. **Check Messages**
   - Go to your Action Network dashboard
   - Navigate to **Messages** section
   - Look for test messages from the integration

2. **Monitor API Usage**
   - Go to **Settings** → **API & Sync**
   - Check API call usage and remaining quota
   - Set up alerts if approaching limits

## Step 6: Production Deployment

1. **Update Infrastructure**
   - Ensure AWS Secrets Manager secret is created
   - Verify ECS task definition references the secret
   - Deploy updated infrastructure

2. **Test Production Environment**
   - Run the test script in production environment
   - Verify logs in CloudWatch
   - Test with real user flow

## Troubleshooting

### Common Issues

**API Key Not Working**
- Verify the API key is correct (no extra spaces)
- Check if the key has expired
- Ensure your Action Network account is active

**Permission Errors**
- Verify your Action Network account has API access enabled
- Check if you've exceeded API rate limits
- Ensure your organization profile is complete

**Network Issues**
- Verify outbound HTTPS access to actionnetwork.org
- Check security group rules in AWS
- Test DNS resolution

### Debug Commands

```bash
# Test API key manually
curl -H "OSDI-API-Token: your-api-key" \
     https://actionnetwork.org/api/v2/

# Check environment variables
echo $ACTION_NETWORK_API_KEY

# View service logs
docker logs your-api-container
```

## API Documentation

- **Action Network API**: https://actionnetwork.org/docs
- **OSDI Specification**: http://opensupporter.github.io/osdi-docs/
- **Rate Limits**: https://actionnetwork.org/docs/v2/#rate-limiting

## Security Considerations

1. **API Key Protection**
   - Never commit API keys to version control
   - Use AWS Secrets Manager for production
   - Rotate keys regularly

2. **Data Privacy**
   - Action Network stores user data according to their privacy policy
   - Ensure compliance with your privacy policy
   - Consider data retention policies

3. **Rate Limiting**
   - Implement proper error handling for rate limits
   - Monitor API usage to avoid hitting limits
   - Consider upgrading to paid tier for higher limits

## Support

- **Action Network Support**: <EMAIL>
- **API Documentation**: https://actionnetwork.org/docs
- **ModernAction.io Issues**: Create GitHub issue in repository

## Next Steps

After successful setup:

1. Test with real user data in staging environment
2. Monitor message delivery rates and success metrics
3. Set up alerts for API failures or rate limit warnings
4. Consider upgrading to paid Action Network tier for higher limits
5. Implement additional error handling and retry logic as needed
