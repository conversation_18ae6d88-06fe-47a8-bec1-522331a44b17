# Auth0 Setup Guide for ModernAction.io

This guide provides step-by-step instructions for setting up Auth0 authentication for the ModernAction.io application.

## Prerequisites

- Auth0 account (free tier is sufficient for development/staging)
- AWS CLI configured with appropriate permissions
- Access to the ModernAction.io AWS account

## Step 1: Create Auth0 Application

1. **Log in to Auth0 Dashboard**
   - Go to https://auth0.com and log in to your account
   - If you don't have an account, create one (free tier available)

2. **Create a New Application**
   - Click "Applications" in the left sidebar
   - Click "Create Application"
   - Name: "ModernAction.io"
   - Application Type: "Regular Web Applications"
   - Click "Create"

3. **Configure Application Settings**
   - Go to the "Settings" tab of your new application
   - Note down the following values (you'll need them later):
     - **Domain**: `your-tenant.auth0.com`
     - **Client ID**: `abc123...`
     - **Client Secret**: `xyz789...` (keep this secure!)

4. **Configure Allowed URLs**
   - **Allowed Callback URLs**:
     ```
     http://localhost:3000/api/auth/callback,
     https://staging.modernaction.io/api/auth/callback,
     https://modernaction.io/api/auth/callback
     ```
   - **Allowed Logout URLs**:
     ```
     http://localhost:3000,
     https://staging.modernaction.io,
     https://modernaction.io
     ```
   - **Allowed Web Origins**:
     ```
     http://localhost:3000,
     https://staging.modernaction.io,
     https://modernaction.io
     ```

## Step 2: Create Auth0 API

1. **Create API for Backend Authentication**
   - Click "APIs" in the left sidebar
   - Click "Create API"
   - Name: "ModernAction.io API"
   - Identifier: `https://api.modernaction.io`
   - Signing Algorithm: RS256
   - Click "Create"

2. **Note the API Identifier**
   - This will be used as the `AUTH0_AUDIENCE` value

## Step 3: Update AWS Secrets Manager

1. **Generate Auth0 Secret**
   ```bash
   # Generate a 32-character random string for AUTH0_SECRET
   openssl rand -base64 32
   ```

2. **Update Auth0 Secrets in AWS**
   ```bash
   # Get the secret ARN (replace with your environment)
   aws secretsmanager describe-secret --secret-id "ModernActionstagingAuth0Secret" --region us-east-1

   # Update the secret with your Auth0 values
   aws secretsmanager update-secret --secret-id "ModernActionstagingAuth0Secret" \
     --secret-string '{
       "AUTH0_SECRET": "your-32-character-random-string",
       "AUTH0_CLIENT_SECRET": "your-auth0-client-secret"
     }' --region us-east-1
   ```

## Step 4: Update CDK Configuration

1. **Update the CDK Stack**
   - Open `infrastructure/modernaction/modernaction_stack.py`
   - Replace the placeholder values in the web container environment:

   ```python
   "AUTH0_ISSUER_BASE_URL": "https://your-tenant.auth0.com",
   "AUTH0_CLIENT_ID": "your-auth0-client-id",
   "AUTH0_AUDIENCE": "https://api.modernaction.io",
   ```

2. **Deploy the Updated Infrastructure**
   ```bash
   cd infrastructure
   export ENVIRONMENT=staging
   cdk deploy
   ```

## Step 5: Update and Deploy Application

1. **Install the Correct Auth0 Package**
   ```bash
   cd apps/web
   npm uninstall @auth0/auth0-react
   npm install @auth0/nextjs-auth0
   ```

2. **Build and Deploy the Web Application**
   ```bash
   # Build the Docker image
   docker build --no-cache --platform linux/amd64 -t modernaction-web-staging .

   # Tag for ECR
   docker tag modernaction-web-staging:latest 308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:auth0-fix

   # Push to ECR
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 308755113449.dkr.ecr.us-east-1.amazonaws.com
   docker push 308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:auth0-fix

   # Update ECS service
   aws ecs update-service --cluster modernaction-staging --service modernaction-web-staging --force-new-deployment
   ```

## Step 6: Test Authentication

1. **Access the Application**
   - Go to https://staging.modernaction.io
   - Click the "Log In" button
   - You should be redirected to Auth0 login page

2. **Create Test User**
   - In Auth0 Dashboard, go to "User Management" > "Users"
   - Click "Create User"
   - Enter email and password
   - Click "Create"

3. **Test Login Flow**
   - Try logging in with your test user
   - After successful login, you should be redirected back to the application
   - You should see your user profile in the navigation

## Step 7: Fix Campaign Action Counts

1. **Trigger Action Count Recalculation**
   ```bash
   # Call the admin API to fix action counts
   curl -X POST https://staging.modernaction.io/api/v1/campaigns/admin/recalculate-action-counts
   ```

2. **Verify the Fix**
   ```bash
   # Check campaigns now show correct action counts
   curl https://staging.modernaction.io/api/v1/campaigns | jq '.[0] | {title, actual_actions, goal_actions}'
   ```

## Troubleshooting

### "Auth0 configuration error" Message

- **Cause**: Missing or incorrect Auth0 environment variables
- **Solution**: Verify all environment variables are set correctly in the ECS task definition

### Login Button Not Working

- **Cause**: Incorrect callback URLs in Auth0 application settings
- **Solution**: Ensure callback URLs match exactly (including protocol and path)

### "Invalid audience" Error

- **Cause**: Mismatch between AUTH0_AUDIENCE and Auth0 API identifier
- **Solution**: Verify the API identifier in Auth0 matches the AUTH0_AUDIENCE value

### Deployment Issues

- **Cause**: Package installation or build failures
- **Solution**: Ensure Node.js and npm are available, clean Docker cache if needed

## Security Notes

- **Never commit Auth0 secrets to git**
- **Use AWS Secrets Manager for all sensitive values**
- **Rotate Auth0 client secret regularly**
- **Use HTTPS in production**
- **Enable Auth0 security features like anomaly detection**

## Environment Variables Reference

### Required Environment Variables (Web Container)

```bash
# Public variables (safe to expose)
AUTH0_BASE_URL=https://staging.modernaction.io
AUTH0_ISSUER_BASE_URL=https://your-tenant.auth0.com
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_AUDIENCE=https://api.modernaction.io

# Secret variables (stored in AWS Secrets Manager)
AUTH0_SECRET=your-32-character-random-string
AUTH0_CLIENT_SECRET=your-auth0-client-secret
```

### Local Development (.env.local)

```bash
AUTH0_SECRET=your-32-character-random-string
AUTH0_BASE_URL=http://localhost:3000
AUTH0_ISSUER_BASE_URL=https://your-tenant.auth0.com
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_CLIENT_SECRET=your-auth0-client-secret
AUTH0_AUDIENCE=https://api.modernaction.io
```

## Next Steps

After completing this setup:

1. **Test the complete user journey** (login, browse campaigns, take actions)
2. **Set up monitoring** for authentication errors
3. **Configure Auth0 rules** for user metadata if needed
4. **Set up automated CI/CD** to replace manual deployment process
5. **Prepare for production** by creating production Auth0 application
