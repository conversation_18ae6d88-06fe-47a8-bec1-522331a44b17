# CI/CD Setup Guide for ModernAction.io

This guide provides instructions for setting up automated CI/CD pipelines for ModernAction.io using either AWS CodeBuild or GitHub Actions.

## Overview

The CI/CD pipeline automates:
- Building Docker images for both web and API services
- Running tests
- Pushing images to Amazon ECR
- Deploying to Amazon ECS
- Running health checks
- Fixing campaign action counts

## Option 1: AWS CodeBuild (Recommended)

### Prerequisites

- AWS CLI configured with appropriate permissions
- GitHub repository connected to AWS CodeBuild
- CDK deployed with CodeBuild project

### Setup Steps

1. **Deploy the CodeBuild Project**
   ```bash
   cd infrastructure
   export ENVIRONMENT=staging
   cdk deploy
   ```

2. **Connect GitHub Repository**
   - Go to AWS CodeBuild console
   - Find the project: `modernaction-cicd-staging`
   - Go to "Source" settings
   - Click "Connect to GitHub" if not already connected
   - Authorize AWS CodeBuild to access your repository

3. **Configure Webhook**
   - The CDK automatically sets up a webhook for pushes to `main` branch
   - Verify in CodeBuild console under "Webhooks"

4. **Test the Pipeline**
   ```bash
   # Make a small change and push to main
   git add .
   git commit -m "Test CI/CD pipeline"
   git push origin main
   ```

5. **Monitor Build**
   - Go to CodeBuild console
   - Watch the build progress
   - Check CloudWatch logs for detailed output

### CodeBuild Features

- **Automatic Triggers**: Builds on every push to `main` branch
- **Docker Layer Caching**: Faster builds using cached layers
- **Health Checks**: Automatic verification after deployment
- **Rollback**: Manual rollback capability through ECS console

## Option 2: GitHub Actions

### Prerequisites

- GitHub repository with Actions enabled
- AWS credentials stored as GitHub secrets

### Setup Steps

1. **Configure GitHub Secrets**
   Go to your GitHub repository → Settings → Secrets and variables → Actions
   
   Add these secrets:
   ```
   AWS_ACCESS_KEY_ID: your-aws-access-key
   AWS_SECRET_ACCESS_KEY: your-aws-secret-key
   ```

2. **Enable GitHub Actions**
   - The workflow file is already in `.github/workflows/deploy.yml`
   - It will automatically trigger on pushes to `main` branch

3. **Test the Pipeline**
   ```bash
   # Make a change and push to main
   git add .
   git commit -m "Test GitHub Actions pipeline"
   git push origin main
   ```

4. **Monitor Build**
   - Go to GitHub repository → Actions tab
   - Watch the workflow progress
   - Check logs for any issues

### GitHub Actions Features

- **Test First**: Runs tests before deployment
- **Matrix Builds**: Can test multiple environments
- **Artifact Storage**: Stores build artifacts
- **Pull Request Checks**: Can run on PRs for validation

## Pipeline Stages

### 1. Pre-Build
- Login to Amazon ECR
- Set up build environment
- Generate unique image tags

### 2. Build
- Build API Docker image (FastAPI)
- Build Web Docker image (Next.js)
- Tag images with commit hash and `latest`

### 3. Test (GitHub Actions only)
- Run frontend tests (`npm test`)
- Run backend tests (`pytest`)

### 4. Deploy
- Push images to ECR
- Update ECS services
- Wait for deployment to stabilize

### 5. Post-Deploy
- Run health checks
- Fix campaign action counts
- Notify of success/failure

## Monitoring and Troubleshooting

### Build Logs

**CodeBuild:**
- AWS Console → CodeBuild → Build projects → modernaction-cicd-staging
- Click on build run → Logs tab

**GitHub Actions:**
- GitHub repository → Actions tab → Click on workflow run

### Common Issues

1. **Docker Build Failures**
   - Check Dockerfile syntax
   - Verify base image availability
   - Check for dependency installation issues

2. **ECR Push Failures**
   - Verify AWS credentials
   - Check ECR repository exists
   - Ensure proper IAM permissions

3. **ECS Deployment Failures**
   - Check ECS service configuration
   - Verify task definition is valid
   - Check ALB health checks

4. **Health Check Failures**
   - Verify application is starting correctly
   - Check CloudWatch logs for errors
   - Ensure ALB target groups are healthy

### Manual Rollback

If deployment fails:

```bash
# Get previous task definition
aws ecs describe-services --cluster modernaction-staging --services modernaction-web-staging

# Rollback to previous revision
aws ecs update-service \
  --cluster modernaction-staging \
  --service modernaction-web-staging \
  --task-definition ModernActionstagingWebTaskDefinition6EB55C12:9 \
  --force-new-deployment
```

## Security Considerations

### AWS CodeBuild
- Uses IAM roles (no long-term credentials)
- Builds run in isolated containers
- Secrets managed through AWS Secrets Manager

### GitHub Actions
- Requires AWS credentials as secrets
- Use least-privilege IAM policies
- Consider using OIDC for credential-less authentication

## Performance Optimization

### Build Speed
- Docker layer caching enabled
- Parallel builds for web and API
- Optimized Dockerfile layers

### Deployment Speed
- Rolling deployments (zero downtime)
- Health checks prevent bad deployments
- Fast rollback capability

## Next Steps

1. **Set up staging/production environments**
   - Create separate pipelines for each environment
   - Use environment-specific configurations

2. **Add more comprehensive testing**
   - Integration tests
   - E2E tests with Playwright
   - Load testing with k6

3. **Implement blue/green deployments**
   - Zero-downtime deployments
   - Automatic rollback on failure

4. **Add monitoring and alerting**
   - CloudWatch alarms
   - Slack/email notifications
   - Performance monitoring

## Troubleshooting Commands

```bash
# Check CodeBuild project status
aws codebuild list-projects
aws codebuild batch-get-projects --names modernaction-cicd-staging

# Check ECS service status
aws ecs describe-services --cluster modernaction-staging --services modernaction-web-staging

# Check ECR repositories
aws ecr describe-repositories

# View recent builds
aws codebuild list-builds-for-project --project-name modernaction-cicd-staging

# Get build logs
aws logs filter-log-events --log-group-name /aws/codebuild/modernaction-cicd-staging
```
