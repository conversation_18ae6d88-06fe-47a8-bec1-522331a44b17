# Part 2: Action Network Integration - Implementation Documentation

## Overview

Part 2 of the ModernAction.io implementation focuses on integrating Action Network for message delivery to representatives. This part builds upon the AI Bill Summarization System from Part 1 and adds the capability for users to take action on bills by sending personalized messages to their representatives.

## Completed Components

### 1. Action Network Service (`app/services/action_network_service.py`)

**Purpose**: Handles message submission to representatives via Action Network's API.

**Key Features**:
- Person record creation/update in Action Network
- Message submission with retry logic using tenacity
- Comprehensive error handling and logging
- Health check functionality

**API Integration**:
- Uses Action Network's REST API v2
- Handles authentication via API key
- Manages person records and message submissions
- Implements exponential backoff for failed requests

**Usage Example**:
```python
action_network_service = get_action_network_service()
message_data = {
    'person': user_info,
    'targets': representatives,
    'subject': 'Regarding HR5',
    'body': 'Personalized message content...',
    'bill_number': 'HR5',
    'position': 'support'
}
result = await action_network_service.submit_message(message_data)
```

### 2. Officials Lookup Service (`app/services/officials_service.py`)

**Purpose**: Enhanced representative lookup using OpenStates API as the primary data source.

**Key Features**:
- ZIP code to congressional district mapping
- Federal senator and representative lookup
- Geographic information resolution
- Fallback mechanisms for data reliability

**Data Sources**:
- **Primary**: OpenStates API (preferred for federal representatives)
- **Geographic**: ZIP code to district mapping
- **Structured Output**: Consistent representative data format

**Usage Example**:
```python
officials_service = get_officials_service()
result = await officials_service.lookup_representatives_by_zip("90210")
# Returns: senators, representative, district info
```

### 3. Message Personalization Service (`app/services/message_personalization_service.py`)

**Purpose**: AI-powered creation of personalized messages using OpenAI GPT-4.

**Key Features**:
- Bill-specific message generation
- Position-aware content (support/oppose/amend)
- Representative-specific personalization
- Talking points extraction
- User context integration

**AI Integration**:
- Uses OpenAI GPT-4 for message generation
- Structured prompts for consistent output
- JSON response parsing for structured data
- Error handling for API failures

**Usage Example**:
```python
message_service = get_message_personalization_service()
personalization_data = {
    'bill': bill_object,
    'position': 'support',
    'user_info': user_data,
    'representatives': rep_list,
    'custom_message': optional_custom_text
}
result = await message_service.create_personalized_messages(personalization_data)
```

### 4. Enhanced Bill Service (`app/services/enhanced_bill_service.py`)

**Purpose**: Complete async service for bill processing with comprehensive AI analysis.

**Key Features**:
- Congress.gov API integration for bill data
- Full text extraction and cleaning
- Comprehensive AI analysis using OpenAI GPT-4
- Database persistence with JSONB storage
- Async/await patterns throughout

**AI Processing**:
- Bill summarization
- Reason generation (support/oppose/amend)
- Message template creation
- Tag extraction
- Structured JSONB storage

### 5. Enhanced Actions API (`app/api/v1/endpoints/enhanced_actions.py`)

**Purpose**: REST API endpoints for the complete bill action workflow.

**Endpoints**:

#### `POST /lookup-representatives`
- Look up federal representatives by ZIP code
- Returns senators and house representative
- Uses OpenStates API for accurate data

#### `POST /take-action`
- Complete bill action workflow
- Steps: validate bill → lookup reps → personalize messages → send via Action Network
- Returns comprehensive action results

#### `GET /preview-message/{bill_id}`
- Preview personalized message without sending
- Allows users to see message content before committing
- Uses sample user data for preview

#### `GET /health`
- Health check for all action-related services
- Verifies Action Network, Officials, and Message services

### 6. Enhanced Database Schema

**New Action Fields** (Migration: `f791c0c650e3_add_enhanced_action_tracking_fields`):
- `bill_id`: Link actions to specific bills
- `position`: User's position (support/oppose/amend)
- `action_network_id`: Action Network message ID for tracking
- `personalized_content`: JSONB field for AI-generated message content
- `representative_info`: JSONB field for representative details
- `user_location`: JSONB field for user's address/ZIP context

**Enhanced Bill Model**:
- Fixed JSONB column types for AI-generated content
- Added `actions` relationship for bill-to-action tracking
- Proper PostgreSQL JSONB support with GIN indexes

### 7. Enhanced Seeding Tool (`enhanced_seed.py`)

**Purpose**: Command-line tool for processing individual bills with comprehensive AI analysis.

**Features**:
- Bill processing with full AI analysis
- Environment variable validation
- Async processing support
- Comprehensive error handling

**Usage**:
```bash
python3 enhanced_seed.py --bill HR5 --session 118
```

## Dependencies Added

Added to `requirements.txt`:
- `aiohttp>=3.8.0,<4.0.0` - Async HTTP client for API calls
- `openai>=1.0.0,<2.0.0` - OpenAI API client
- `tenacity>=8.0.0,<9.0.0` - Retry logic with exponential backoff

## Environment Variables Required

For full functionality, the following environment variables must be configured:

```bash
# Congress.gov API (for bill data)
CONGRESS_GOV_API_KEY=your_congress_gov_api_key

# OpenAI API (for AI processing)
OPENAI_API_KEY=your_openai_api_key

# OpenStates API (for representative lookup)
OPENSTATES_API_KEY=your_openstates_api_key

# Action Network API (for message delivery)
ACTION_NETWORK_API_KEY=your_action_network_api_key
```

## Testing

### Unit Testing
Each service includes comprehensive error handling and can be tested independently:

```python
# Test officials lookup
officials_service = get_officials_service()
result = await officials_service.lookup_representatives_by_zip("90210")

# Test message personalization
message_service = get_message_personalization_service()
# ... test with sample data

# Test Action Network integration
action_network_service = get_action_network_service()
# ... test with sample message data
```

### Integration Testing
The enhanced actions endpoint provides end-to-end testing:

```bash
# Test representative lookup
curl -X POST "http://localhost:8000/api/v1/enhanced-actions/lookup-representatives" \
  -H "Content-Type: application/json" \
  -d '{"zip_code": "90210"}'

# Test message preview
curl "http://localhost:8000/api/v1/enhanced-actions/preview-message/1?position=support&zip_code=90210"
```

## Architecture Patterns

### Async/Await Throughout
All services use async/await patterns for:
- Database operations
- External API calls
- AI processing
- Message delivery

### Service Layer Pattern
Clean separation of concerns:
- **Services**: Business logic and external integrations
- **Models**: Database schema and relationships
- **Endpoints**: HTTP request/response handling
- **Schemas**: Data validation and serialization

### Error Handling
Comprehensive error handling with:
- Retry logic for external APIs
- Structured error responses
- Logging for debugging
- Graceful degradation

### JSONB Storage
PostgreSQL JSONB columns for:
- AI-generated content (reasons, templates, tags)
- Representative information
- User location data
- Personalized message content

## Next Steps

Part 2 is now complete. The next phase (Part 3: Frontend Integration) will include:

1. **Frontend Components**: React components for bill action interface
2. **ZIP Code Input**: User interface for representative lookup
3. **Message Customization**: Interface for reviewing and customizing messages
4. **Action Confirmation**: User confirmation and tracking interface
5. **Integration Testing**: End-to-end testing of the complete workflow

## Security Considerations

- API keys stored as environment variables
- User data validation through Pydantic schemas
- SQL injection prevention through SQLAlchemy ORM
- Rate limiting considerations for external APIs
- Secure handling of user contact information

## Performance Considerations

- Async operations for concurrent processing
- Database connection pooling
- Retry logic with exponential backoff
- JSONB indexing for efficient queries
- Caching strategies for representative data

This completes Part 2 of the Implementation-2.MD plan, providing a robust foundation for civic engagement through AI-powered bill analysis and automated representative outreach.
