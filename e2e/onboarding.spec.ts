/**
 * End-to-End Tests for User Onboarding Flow
 * 
 * This test suite validates the complete onboarding experience:
 * 1. First-time user sees onboarding modal
 * 2. User can select issues and save preferences  
 * 3. Preferences are stored in localStorage
 * 4. Returning users don't see the modal again
 * 
 * These tests ensure the onboarding flow works correctly in the live application
 * context with proper localStorage integration.
 */

import { test, expect } from '@playwright/test';

test.describe('User Onboarding Flow', () => {
  
  test.beforeEach(async ({ page }) => {
    // Clear localStorage and cookies before each test to ensure clean state
    await page.context().clearCookies();
    // Navigate to the page first, then clear localStorage
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => {
      try {
        localStorage.clear();
        sessionStorage.clear();
      } catch (error) {
        console.log('localStorage not available in beforeEach');
      }
    });
  });

  test('First-time user experience: modal appears and saves preferences', async ({ page }) => {
    console.log('🧪 Testing first-time user onboarding experience...');
    
    // Ensure localStorage is completely clear for first-time user
    await page.evaluate(() => {
      try {
        localStorage.clear();
        sessionStorage.clear();
      } catch (error) {
        console.log('localStorage clear failed');
      }
    });
    
    // Reload to trigger onboarding for first-time user
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Assert that the onboarding modal is visible
    const onboardingModal = page.getByTestId('onboarding-modal');
    await expect(onboardingModal).toBeVisible();
    
    console.log('✅ Onboarding modal is visible for first-time user');
    
    // Verify modal content
    await expect(page.getByText('Welcome to ModernAction')).toBeVisible();
    await expect(page.getByText('What issues matter most to you?')).toBeVisible();
    
    // Verify issue buttons are present (based on actual ISSUE_CATEGORIES)
    const environmentButton = page.getByTestId('issue-environment');
    const healthcareButton = page.getByTestId('issue-healthcare');
    const economicJusticeButton = page.getByTestId('issue-economic-justice');
    
    await expect(environmentButton).toBeVisible();
    await expect(healthcareButton).toBeVisible();
    await expect(economicJusticeButton).toBeVisible();
    
    console.log('✅ All issue selection buttons are visible');
    
    // Select multiple issues
    await environmentButton.click();
    await healthcareButton.click();
    await economicJusticeButton.click();
    
    // Verify selected state (buttons should have selected styling)
    await expect(environmentButton).toHaveClass(/border-blue-500|bg-blue-50/);
    await expect(healthcareButton).toHaveClass(/border-blue-500|bg-blue-50/);
    await expect(economicJusticeButton).toHaveClass(/border-blue-500|bg-blue-50/);
    
    console.log('✅ Issues selected successfully with visual feedback');
    
    // Click the Save button
    const saveButton = page.getByTestId('onboarding-save-button');
    await expect(saveButton).toBeVisible();
    await saveButton.click();
    
    // Wait for modal to disappear
    await expect(onboardingModal).not.toBeVisible();
    
    console.log('✅ Modal disappeared after saving preferences');
    
    // Verify localStorage contains the correct state
    const localStorageData = await page.evaluate(() => {
      try {
        const data = localStorage.getItem('modernaction-onboarding');
        return data ? JSON.parse(data) : null;
      } catch (error) {
        return { error: 'localStorage not accessible' };
      }
    });
    
    // Validate localStorage structure and values (Zustand persist format)
    if (localStorageData && !localStorageData.error) {
      expect(localStorageData).toBeTruthy();
      expect(localStorageData.state).toBeTruthy();
      expect(localStorageData.state.hasCompletedOnboarding).toBe(true);
      expect(localStorageData.state.issuePreferences).toContain('Environment');
      expect(localStorageData.state.issuePreferences).toContain('Healthcare');
      expect(localStorageData.state.issuePreferences).toContain('Economic Justice');
    } else {
      console.log('ℹ️ localStorage not accessible in test environment, continuing test...');
    }
    
    console.log('✅ localStorage contains correct onboarding data:', localStorageData);
    
    // Navigate away and back to ensure state persists
    await page.goto('/about'); // Assuming an about page exists
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify modal does not appear again
    await expect(onboardingModal).not.toBeVisible();
    
    console.log('✅ Modal correctly hidden on return visit');
  });

  test('Skip onboarding functionality', async ({ page }) => {
    console.log('🧪 Testing skip onboarding functionality...');
    
    // Page already loaded from beforeEach, just reload
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Verify onboarding modal is visible
    const onboardingModal = page.getByTestId('onboarding-modal');
    await expect(onboardingModal).toBeVisible();
    
    // Click the Skip button
    const skipButton = page.getByTestId('onboarding-skip-button');
    await expect(skipButton).toBeVisible();
    await skipButton.click();
    
    // Verify modal disappears
    await expect(onboardingModal).not.toBeVisible();
    
    console.log('✅ Modal disappeared after skipping');
    
    // Verify localStorage reflects skipped state
    const localStorageData = await page.evaluate(() => {
      try {
        const data = localStorage.getItem('modernaction-onboarding');
        return data ? JSON.parse(data) : null;
      } catch (error) {
        return { error: 'localStorage not accessible' };
      }
    });
    
    if (localStorageData && !localStorageData.error) {
      expect(localStorageData).toBeTruthy();
      expect(localStorageData.state).toBeTruthy();
      expect(localStorageData.state.hasCompletedOnboarding).toBe(true);
      expect(localStorageData.state.issuePreferences).toEqual([]);
    } else {
      console.log('ℹ️ localStorage not accessible in test environment');
    }
    
    console.log('✅ localStorage correctly shows skipped onboarding:', localStorageData);
  });

  test('Returning user experience: modal does not appear', async ({ page }) => {
    console.log('🧪 Testing returning user experience...');
    
    // Pre-populate localStorage to simulate returning user (Zustand format)
    await page.evaluate(() => {
      try {
        const onboardingData = {
          state: {
            hasCompletedOnboarding: true,
            issuePreferences: ['Environment', 'Healthcare']
          },
          version: 0
        };
        localStorage.setItem('modernaction-onboarding', JSON.stringify(onboardingData));
      } catch (error) {
        console.log('localStorage not available for setup');
      }
    });
    
    // Navigate to homepage
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify onboarding modal is NOT visible
    const onboardingModal = page.getByTestId('onboarding-modal');
    await expect(onboardingModal).not.toBeVisible();
    
    console.log('✅ Onboarding modal correctly hidden for returning user');
    
    // Verify localStorage data persists
    const localStorageData = await page.evaluate(() => {
      try {
        const data = localStorage.getItem('modernaction-onboarding');
        return data ? JSON.parse(data) : null;
      } catch (error) {
        return { error: 'localStorage not accessible' };
      }
    });
    
    if (localStorageData && !localStorageData.error) {
      expect(localStorageData.state.hasCompletedOnboarding).toBe(true);
      expect(localStorageData.state.issuePreferences).toContain('Environment');
      expect(localStorageData.state.issuePreferences).toContain('Healthcare');
    }
    
    console.log('✅ localStorage data persisted correctly for returning user');
  });

  test('Modal closes when clicking outside (if implemented)', async ({ page }) => {
    console.log('🧪 Testing modal close behavior...');
    
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const onboardingModal = page.getByTestId('onboarding-modal');
    await expect(onboardingModal).toBeVisible();
    
    // Try clicking outside the modal (on backdrop)
    const modalBackdrop = page.getByTestId('onboarding-modal-backdrop');
    if (await modalBackdrop.isVisible()) {
      await modalBackdrop.click();
      
      // Check if modal closes (this behavior may vary based on UX requirements)
      const isModalStillVisible = await onboardingModal.isVisible();
      
      if (!isModalStillVisible) {
        console.log('✅ Modal closes when clicking outside');
        
        // Verify localStorage is updated appropriately
        const localStorageData = await page.evaluate(() => {
          try {
            const data = localStorage.getItem('modernaction-onboarding');
            return data ? JSON.parse(data) : null;
          } catch (error) {
            return { error: 'localStorage not accessible' };
          }
        });
        
        // Modal should either be marked as skipped or not update localStorage
        if (localStorageData && !localStorageData.error) {
          expect(localStorageData?.state?.hasCompletedOnboarding).toBeTruthy();
        }
      } else {
        console.log('ℹ️ Modal stays open when clicking outside (as designed)');
      }
    } else {
      console.log('ℹ️ No backdrop click handler implemented');
    }
  });

  test('Issue selection state management', async ({ page }) => {
    console.log('🧪 Testing issue selection state management...');
    
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const onboardingModal = page.getByTestId('onboarding-modal');
    await expect(onboardingModal).toBeVisible();
    
    const environmentButton = page.getByTestId('issue-environment');
    const healthcareButton = page.getByTestId('issue-healthcare');
    
    // Select environment
    await environmentButton.click();
    await expect(environmentButton).toHaveClass(/selected|bg-blue|border-blue/);
    
    // Select healthcare
    await healthcareButton.click();
    await expect(healthcareButton).toHaveClass(/selected|bg-blue|border-blue/);
    
    // Deselect environment
    await environmentButton.click();
    await expect(environmentButton).not.toHaveClass(/selected|bg-blue|border-blue/);
    
    // Healthcare should still be selected
    await expect(healthcareButton).toHaveClass(/selected|bg-blue|border-blue/);
    
    console.log('✅ Issue selection toggling works correctly');
    
    // Save with only healthcare selected
    await page.getByTestId('onboarding-save-button').click();
    
    // Verify localStorage
    const localStorageData = await page.evaluate(() => {
      try {
        const data = localStorage.getItem('modernaction-onboarding');
        return data ? JSON.parse(data) : null;
      } catch (error) {
        return { error: 'localStorage not accessible' };
      }
    });
    
    if (localStorageData && !localStorageData.error) {
      expect(localStorageData.state.issuePreferences).toEqual(['Healthcare']);
      expect(localStorageData.state.issuePreferences).not.toContain('Environment');
    }
    
    console.log('✅ Only selected issues saved to localStorage');
  });

  test('Onboarding data structure validation', async ({ page }) => {
    console.log('🧪 Testing onboarding data structure...');
    
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Select some issues and save
    await page.getByTestId('issue-environment').click();
    await page.getByTestId('issue-healthcare').click();
    await page.getByTestId('onboarding-save-button').click();
    
    // Validate complete data structure
    const localStorageData = await page.evaluate(() => {
      try {
        const data = localStorage.getItem('modernaction-onboarding');
        return data ? JSON.parse(data) : null;
      } catch (error) {
        return { error: 'localStorage not accessible' };
      }
    });
    
    if (localStorageData && !localStorageData.error) {
      // Verify all required fields are present (Zustand format)
      expect(localStorageData).toHaveProperty('state');
      expect(localStorageData).toHaveProperty('version');
      expect(localStorageData.state).toHaveProperty('hasCompletedOnboarding');
      expect(localStorageData.state).toHaveProperty('issuePreferences');
      
      // Verify data types
      expect(typeof localStorageData.state.hasCompletedOnboarding).toBe('boolean');
      expect(Array.isArray(localStorageData.state.issuePreferences)).toBe(true);
      expect(typeof localStorageData.version).toBe('number');
      
      // Verify values
      expect(localStorageData.state.hasCompletedOnboarding).toBe(true);
      expect(localStorageData.version).toBe(0);
    }
    
    console.log('✅ Onboarding data structure is valid and complete');
  });

  test('Multiple page navigation maintains state', async ({ page }) => {
    console.log('🧪 Testing state persistence across navigation...');
    
    // Complete onboarding
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    await page.getByTestId('issue-environment').click();
    await page.getByTestId('onboarding-save-button').click();
    
    // Navigate to different pages
    const testRoutes = ['/campaigns', '/about', '/contact', '/'];
    
    for (const route of testRoutes) {
      try {
        await page.goto(route);
        await page.waitForTimeout(500);
        
        // Verify modal doesn't appear
        const modal = page.getByTestId('onboarding-modal');
        await expect(modal).not.toBeVisible();
        
        // Verify localStorage persists
        const data = await page.evaluate(() => {
          try {
            const stored = localStorage.getItem('modernaction-onboarding');
            return stored ? JSON.parse(stored) : null;
          } catch (error) {
            return { error: 'localStorage not accessible' };
          }
        });
        
        if (data && !data.error) {
          expect(data?.state?.hasCompletedOnboarding).toBe(true);
        }
        
      } catch (error) {
        // Route might not exist, continue with test
        console.log(`ℹ️ Route ${route} not accessible, skipping...`);
      }
    }
    
    console.log('✅ Onboarding state persists across all page navigation');
  });

});

test.describe('Onboarding Integration with Global State', () => {
  
  test('Zustand store reflects localStorage data', async ({ page }) => {
    console.log('🧪 Testing Zustand store integration...');
    
    // Pre-populate localStorage (Zustand format)
    await page.evaluate(() => {
      try {
        const onboardingData = {
          state: {
            hasCompletedOnboarding: true,
            issuePreferences: ['Environment', 'Healthcare', 'Economic Justice']
          },
          version: 0
        };
        localStorage.setItem('modernaction-onboarding', JSON.stringify(onboardingData));
      } catch (error) {
        console.log('localStorage not available for setup');
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check if Zustand store has loaded the data (if accessible via window object)
    const zustandState = await page.evaluate(() => {
      // This assumes the Zustand store is exposed for testing
      // In a real app, you might need to add a test helper
      return (window as any).__ZUSTAND_STORE_STATE__ || null;
    });
    
    if (zustandState) {
      expect(zustandState.hasCompletedOnboarding).toBe(true);
      expect(zustandState.issuePreferences).toEqual(['Environment', 'Healthcare', 'Economic Justice']);
      console.log('✅ Zustand store correctly reflects localStorage data');
    } else {
      console.log('ℹ️ Zustand store not exposed for testing (this is normal in production)');
    }
  });

});

/**
 * Performance and Accessibility Tests
 */
test.describe('Onboarding Performance and Accessibility', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the page first, then clear localStorage
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => {
      try {
        localStorage.clear();
        sessionStorage.clear();
      } catch (error) {
        console.log('localStorage not available in beforeEach');
      }
    });
  });
  
  test('Modal renders quickly and is accessible', async ({ page }) => {
    console.log('🧪 Testing onboarding performance and accessibility...');
    
    const startTime = Date.now();
    // Reload to trigger onboarding for first-time user
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Wait for modal to appear and measure time
    const modal = page.getByTestId('onboarding-modal');
    await expect(modal).toBeVisible();
    const renderTime = Date.now() - startTime;
    
    // Modal should render within reasonable time (5 seconds for E2E)
    expect(renderTime).toBeLessThan(5000);
    console.log(`✅ Modal rendered in ${renderTime}ms`);
    
    // Check accessibility attributes - wait for them to be present
    await expect(modal).toHaveAttribute('role', 'dialog');
    await expect(modal).toHaveAttribute('aria-modal', 'true');
    
    // Focus should be trapped in modal
    await page.keyboard.press('Tab');
    const activeElement = await page.evaluate(() => document.activeElement?.getAttribute('data-testid'));
    expect(['issue-environment', 'issue-healthcare', 'issue-education', 'onboarding-save-button', 'onboarding-skip-button']).toContain(activeElement);
    
    console.log('✅ Modal has proper accessibility attributes and focus management');
  });

});