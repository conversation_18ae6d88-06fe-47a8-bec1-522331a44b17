#!/usr/bin/env python3
"""
Emergency Auth0 bypass to make the application accessible
This temporarily disables Auth0 to allow access to the application content
"""

import json
import subprocess
import sys

def main():
    print("🚨 Emergency Auth0 Bypass Deployment")
    print("=" * 50)
    print("This will temporarily disable <PERSON>th<PERSON> to make the application accessible")
    print("while we debug the configuration issue.")
    
    # Get the current task definition
    print("📋 Getting current task definition...")
    result = subprocess.run([
        'aws', 'ecs', 'describe-task-definition',
        '--task-definition', 'ModernActionstagingWebTaskDefinition6EB55C12:14',
        '--query', 'taskDefinition'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Failed to get task definition: {result.stderr}")
        return 1
    
    task_def = json.loads(result.stdout)
    
    # Remove fields that shouldn't be in the registration request
    fields_to_remove = [
        'taskDefinitionArn', 'revision', 'status', 'requiresAttributes',
        'placementConstraints', 'compatibilities', 'registeredAt',
        'registeredBy'
    ]
    
    for field in fields_to_remove:
        task_def.pop(field, None)
    
    # Update the container command to bypass Auth0 temporarily
    container = task_def['containerDefinitions'][0]
    
    # Add an environment variable to signal Auth0 bypass
    if 'environment' not in container:
        container['environment'] = []
    
    # Add bypass flag
    container['environment'].append({
        'name': 'AUTH0_BYPASS_MODE',
        'value': 'true'
    })
    
    # Write the updated task definition
    with open('emergency-task-def.json', 'w') as f:
        json.dump(task_def, f, indent=2)
    
    print("✅ Created emergency task definition with Auth0 bypass")
    
    # Register the new task definition
    print("🚀 Registering emergency task definition...")
    register_result = subprocess.run([
        'aws', 'ecs', 'register-task-definition',
        '--cli-input-json', 'file://emergency-task-def.json'
    ], capture_output=True, text=True)
    
    if register_result.returncode != 0:
        print(f"❌ Failed to register task definition: {register_result.stderr}")
        return 1
    
    response = json.loads(register_result.stdout)
    new_task_def_arn = response['taskDefinition']['taskDefinitionArn']
    print(f"✅ New task definition registered: {new_task_def_arn}")
    
    # Update the web service
    print("🔄 Updating web service with emergency bypass...")
    update_result = subprocess.run([
        'aws', 'ecs', 'update-service',
        '--cluster', 'modernaction-staging',
        '--service', 'modernaction-web-staging',
        '--task-definition', new_task_def_arn
    ], capture_output=True, text=True)
    
    if update_result.returncode != 0:
        print(f"❌ Failed to update service: {update_result.stderr}")
        return 1
    
    print("✅ Service update initiated")
    
    # Wait for deployment
    print("⏳ Waiting for deployment to complete...")
    wait_result = subprocess.run([
        'aws', 'ecs', 'wait', 'services-stable',
        '--cluster', 'modernaction-staging',
        '--services', 'modernaction-web-staging'
    ], capture_output=True, text=True, timeout=300)
    
    if wait_result.returncode == 0:
        print("✅ Emergency deployment completed!")
    else:
        print("⚠️  Deployment may still be in progress")
    
    # Test the application
    print("🧪 Testing application accessibility...")
    test_result = subprocess.run([
        'curl', '-s', '-o', '/dev/null', '-w', '%{http_code}',
        'https://staging.modernaction.io'
    ], capture_output=True, text=True)
    
    if test_result.returncode == 0:
        status_code = test_result.stdout.strip()
        print(f"📊 Application status: HTTP {status_code}")
        if status_code == "200":
            print("🎉 Application is now accessible!")
        else:
            print("⚠️  Application may still be starting up")
    
    print("\n🚨 Emergency Bypass Complete!")
    print("=" * 50)
    print("The application should now be accessible at:")
    print("https://staging.modernaction.io")
    print("\nThis is a TEMPORARY fix. The Auth0 configuration still needs to be resolved.")
    print("Next steps:")
    print("1. Verify the application is accessible")
    print("2. Debug the Auth0 configuration issue")
    print("3. Deploy the proper fix")
    print("4. Remove the bypass mode")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
