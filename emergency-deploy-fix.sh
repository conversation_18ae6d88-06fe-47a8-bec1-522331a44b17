#!/bin/bash

# Emergency Deployment Fix for ModernAction.io
# This script fixes the ECS deployment issues and deploys the corrected configuration

set -e

echo "🚨 EMERGENCY DEPLOYMENT FIX STARTING..."

# Configuration
AWS_REGION="us-east-1"
ECR_REPOSITORY="modernaction-api-staging"
ECS_CLUSTER="modernaction-staging"
ECS_SERVICE="modernaction-api-staging"
TASK_DEFINITION_FILE="task-definition.json"

# Get AWS account ID
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
ECR_REGISTRY="${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

echo "📋 Configuration:"
echo "  AWS Region: $AWS_REGION"
echo "  ECR Repository: $ECR_REPOSITORY"
echo "  ECS Cluster: $ECS_CLUSTER"
echo "  ECS Service: $ECS_SERVICE"
echo "  Account ID: $ACCOUNT_ID"

# Step 1: Stop the current deployment thrashing
echo "🛑 Step 1: Stopping current deployment..."
aws ecs update-service \
    --cluster $ECS_CLUSTER \
    --service $ECS_SERVICE \
    --desired-count 0 \
    --region $AWS_REGION

echo "⏳ Waiting for service to scale down..."
aws ecs wait services-stable \
    --cluster $ECS_CLUSTER \
    --services $ECS_SERVICE \
    --region $AWS_REGION

# Step 2: Build and push new image
echo "🔨 Step 2: Building and pushing new image..."
cd apps/api

# Build the Docker image
IMAGE_TAG=$(git rev-parse --short HEAD)
FULL_IMAGE_URI="${ECR_REGISTRY}/${ECR_REPOSITORY}:${IMAGE_TAG}"

echo "Building image: $FULL_IMAGE_URI"
docker build -t $FULL_IMAGE_URI .

# Login to ECR
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY

# Push the image
docker push $FULL_IMAGE_URI

cd ../..

# Step 3: Update task definition with new image
echo "📝 Step 3: Updating task definition..."
sed -i.bak "s|\"image\": \".*\"|\"image\": \"$FULL_IMAGE_URI\"|g" $TASK_DEFINITION_FILE

echo "Updated task definition:"
cat $TASK_DEFINITION_FILE

# Step 4: Register new task definition
echo "📋 Step 4: Registering new task definition..."
TASK_DEFINITION_ARN=$(aws ecs register-task-definition \
    --cli-input-json file://$TASK_DEFINITION_FILE \
    --region $AWS_REGION \
    --query 'taskDefinition.taskDefinitionArn' \
    --output text)

echo "Registered task definition: $TASK_DEFINITION_ARN"

# Step 5: Update service with new task definition
echo "🚀 Step 5: Updating ECS service..."
aws ecs update-service \
    --cluster $ECS_CLUSTER \
    --service $ECS_SERVICE \
    --task-definition $TASK_DEFINITION_ARN \
    --desired-count 1 \
    --region $AWS_REGION

# Step 6: Wait for deployment to complete
echo "⏳ Step 6: Waiting for service to stabilize..."
aws ecs wait services-stable \
    --cluster $ECS_CLUSTER \
    --services $ECS_SERVICE \
    --region $AWS_REGION \
    --cli-read-timeout 600 \
    --cli-connect-timeout 600

# Step 7: Verify deployment
echo "✅ Step 7: Verifying deployment..."
SERVICE_STATUS=$(aws ecs describe-services \
    --cluster $ECS_CLUSTER \
    --services $ECS_SERVICE \
    --region $AWS_REGION \
    --query 'services[0].status' \
    --output text)

RUNNING_COUNT=$(aws ecs describe-services \
    --cluster $ECS_CLUSTER \
    --services $ECS_SERVICE \
    --region $AWS_REGION \
    --query 'services[0].runningCount' \
    --output text)

echo "Service Status: $SERVICE_STATUS"
echo "Running Count: $RUNNING_COUNT"

if [ "$SERVICE_STATUS" = "ACTIVE" ] && [ "$RUNNING_COUNT" = "1" ]; then
    echo "🎉 DEPLOYMENT SUCCESSFUL!"
    echo "Service is running with the updated task definition."
    
    # Get load balancer URL
    LB_DNS=$(aws ecs describe-services \
        --cluster $ECS_CLUSTER \
        --services $ECS_SERVICE \
        --region $AWS_REGION \
        --query 'services[0].loadBalancers[0].targetGroupArn' \
        --output text | xargs -I {} aws elbv2 describe-target-groups \
        --target-group-arns {} \
        --query 'TargetGroups[0].LoadBalancerArns[0]' \
        --output text | xargs -I {} aws elbv2 describe-load-balancers \
        --load-balancer-arns {} \
        --query 'LoadBalancers[0].DNSName' \
        --output text)
    
    echo "🌐 Application URL: http://$LB_DNS"
    echo "🔍 Health Check: http://$LB_DNS/api/v1/health"
    
    # Test health endpoint
    echo "🏥 Testing health endpoint..."
    if curl -f "http://$LB_DNS/api/v1/health" > /dev/null 2>&1; then
        echo "✅ Health check passed!"
    else
        echo "⚠️  Health check failed - service may still be starting up"
    fi
else
    echo "❌ DEPLOYMENT FAILED!"
    echo "Service Status: $SERVICE_STATUS"
    echo "Running Count: $RUNNING_COUNT"
    exit 1
fi

echo "🎯 EMERGENCY DEPLOYMENT FIX COMPLETED!"