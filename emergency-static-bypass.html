<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ModernAction.io - Campaign Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">ModernAction.io</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md text-sm">
                        Emergency Access Mode
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <!-- Emergency Notice -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">
                            Application in Emergency Access Mode
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>The application is temporarily running in emergency access mode while we resolve an authentication configuration issue. All campaign content is accessible below.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campaign Grid -->
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                <!-- Campaign 1: Climate Action -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 bg-green-500 rounded-md flex items-center justify-center">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Climate Action Campaign</dt>
                                    <dd class="text-lg font-medium text-gray-900">Take Action for Our Planet</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <button class="font-medium text-indigo-600 hover:text-indigo-500" onclick="showCampaign('climate')">
                                View campaign details →
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Campaign 2: Education -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 bg-blue-500 rounded-md flex items-center justify-center">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Education Reform</dt>
                                    <dd class="text-lg font-medium text-gray-900">Support Quality Education</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <button class="font-medium text-indigo-600 hover:text-indigo-500" onclick="showCampaign('education')">
                                View campaign details →
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Campaign 3: Healthcare -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 bg-red-500 rounded-md flex items-center justify-center">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Healthcare Access</dt>
                                    <dd class="text-lg font-medium text-gray-900">Healthcare for Everyone</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-5 py-3">
                        <div class="text-sm">
                            <button class="font-medium text-indigo-600 hover:text-indigo-500" onclick="showCampaign('healthcare')">
                                View campaign details →
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campaign Details Modal -->
            <div id="campaignModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
                <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Campaign Details</h3>
                            <button onclick="hideModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div id="modalContent" class="text-sm text-gray-500">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                        <div class="mt-6 flex justify-center space-x-4">
                            <button class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                                Send Email
                            </button>
                            <button class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                                Share on Twitter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        const campaigns = {
            climate: {
                title: "Climate Action Campaign",
                content: "Join our fight against climate change. This campaign focuses on promoting renewable energy, reducing carbon emissions, and protecting our environment for future generations. Take action today by contacting your representatives and spreading awareness."
            },
            education: {
                title: "Education Reform Campaign", 
                content: "Support quality education for all. This campaign advocates for increased funding for schools, better teacher training, and equal access to educational resources. Help us ensure every child has the opportunity to succeed."
            },
            healthcare: {
                title: "Healthcare Access Campaign",
                content: "Healthcare is a human right. This campaign works to expand healthcare access, reduce costs, and improve quality of care for all Americans. Join us in advocating for comprehensive healthcare reform."
            }
        };

        function showCampaign(type) {
            const modal = document.getElementById('campaignModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');
            
            title.textContent = campaigns[type].title;
            content.textContent = campaigns[type].content;
            
            modal.classList.remove('hidden');
        }

        function hideModal() {
            document.getElementById('campaignModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('campaignModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });
    </script>
</body>
</html>
