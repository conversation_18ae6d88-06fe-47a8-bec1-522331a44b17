#!/usr/bin/env python3
"""
Final deployment script for Auth0 debugging tools
This script monitors CodeBuild, deploys the new image, and provides next steps
"""

import json
import subprocess
import sys
import time

def run_command(command, description):
    """Run a command and return the result"""
    print(f"🔄 {description}...")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print(f"✅ {description} completed successfully")
        return result.stdout.strip()
    else:
        print(f"❌ {description} failed: {result.stderr}")
        return None

def wait_for_build(project_name, build_id=None):
    """Wait for the latest build to complete"""
    if not build_id:
        # Get the latest build
        result = run_command(
            f"aws codebuild list-builds-for-project --project-name {project_name} --sort-order DESCENDING --max-items 1 --query 'ids[0]' --output text",
            "Getting latest build ID"
        )
        if not result or result == "None":
            print("❌ No builds found for project")
            return False
        build_id = result
    
    print(f"📊 Monitoring build: {build_id}")
    
    max_wait_time = 1200  # 20 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        status_result = run_command(
            f"aws codebuild batch-get-builds --ids {build_id} --query 'builds[0].{{buildStatus: buildStatus, currentPhase: currentPhase}}' --output json",
            "Checking build status"
        )
        
        if status_result:
            try:
                status = json.loads(status_result)
                build_status = status.get('buildStatus', 'UNKNOWN')
                current_phase = status.get('currentPhase', 'UNKNOWN')
                
                print(f"📊 Build Status: {build_status}, Phase: {current_phase}")
                
                if build_status == 'SUCCEEDED':
                    print("🎉 Build completed successfully!")
                    return True
                elif build_status in ['FAILED', 'FAULT', 'STOPPED', 'TIMED_OUT']:
                    print(f"❌ Build failed with status: {build_status}")
                    return False
            except json.JSONDecodeError:
                print("⚠️  Could not parse build status")
        
        time.sleep(30)  # Wait 30 seconds before checking again
    
    print("⚠️  Build is taking longer than expected")
    return False

def update_ecs_service():
    """Update the ECS service with the new image"""
    print("🚀 Updating ECS service with new image...")
    
    # The buildspec.yml already handles the ECS service update
    # But we can force a new deployment to ensure it picks up the latest image
    result = run_command(
        "aws ecs update-service --cluster modernaction-staging --service modernaction-web-staging --force-new-deployment",
        "Forcing ECS service update"
    )
    
    if result:
        print("⏳ Waiting for service to stabilize...")
        stabilize_result = run_command(
            "aws ecs wait services-stable --cluster modernaction-staging --services modernaction-web-staging",
            "Waiting for service stabilization"
        )
        return stabilize_result is not None
    
    return False

def test_application():
    """Test the application to see if debugging tools are working"""
    print("🧪 Testing application...")
    
    # Test main application
    main_status = run_command(
        'curl -s -o /dev/null -w "%{http_code}" https://staging.modernaction.io',
        "Testing main application"
    )
    
    # Test debug endpoint
    debug_status = run_command(
        'curl -s -o /dev/null -w "%{http_code}" https://staging.modernaction.io/debug/environment',
        "Testing debug endpoint"
    )
    
    print(f"📊 Main app status: {main_status}")
    print(f"📊 Debug endpoint status: {debug_status}")
    
    return main_status == "200" and debug_status == "200"

def main():
    print("🚀 Final Deployment Script for Auth0 Debugging Tools")
    print("=" * 60)
    
    project_name = "modernaction-web-debug-build"
    
    # Step 1: Check if there's a recent build or start a new one
    print("Step 1: Checking for recent builds...")
    
    # Get the latest build
    latest_build = run_command(
        f"aws codebuild list-builds-for-project --project-name {project_name} --sort-order DESCENDING --max-items 1 --query 'ids[0]' --output text",
        "Getting latest build"
    )
    
    if latest_build and latest_build != "None":
        print(f"Found recent build: {latest_build}")
        
        # Check if it's still running
        status_result = run_command(
            f"aws codebuild batch-get-builds --ids {latest_build} --query 'builds[0].buildStatus' --output text",
            "Checking build status"
        )
        
        if status_result == "IN_PROGRESS":
            print("Build is already in progress, monitoring...")
            build_success = wait_for_build(project_name, latest_build)
        elif status_result == "SUCCEEDED":
            print("Recent build already succeeded, proceeding to deployment...")
            build_success = True
        else:
            print("Starting new build...")
            new_build = run_command(
                f"aws codebuild start-build --project-name {project_name} --source-version hotfix/ssr-bug --query 'build.id' --output text",
                "Starting new build"
            )
            if new_build:
                build_success = wait_for_build(project_name, new_build)
            else:
                build_success = False
    else:
        print("No recent builds found, starting new build...")
        new_build = run_command(
            f"aws codebuild start-build --project-name {project_name} --source-version hotfix/ssr-bug --query 'build.id' --output text",
            "Starting new build"
        )
        if new_build:
            build_success = wait_for_build(project_name, new_build)
        else:
            build_success = False
    
    if not build_success:
        print("❌ Build failed or timed out")
        return 1
    
    # Step 2: Update ECS service
    print("\nStep 2: Updating ECS service...")
    if not update_ecs_service():
        print("❌ ECS service update failed")
        return 1
    
    # Step 3: Test the application
    print("\nStep 3: Testing application...")
    if test_application():
        print("✅ Application is responding correctly")
    else:
        print("⚠️  Application may still be starting up")
    
    # Step 4: Provide next steps
    print("\n🎉 Deployment Complete!")
    print("=" * 60)
    print("Next steps:")
    print("1. Visit https://staging.modernaction.io")
    print("   - Should show error boundary instead of complete crash")
    print("2. Visit https://staging.modernaction.io/debug/environment")
    print("   - Check what environment variables are available")
    print("3. Use the debugging information to identify the Auth0 configuration issue")
    print("4. Apply the final fix based on the debug findings")
    print("\nThe application now has proper error handling and debugging tools!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
