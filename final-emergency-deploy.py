#!/usr/bin/env python3

import boto3
import subprocess
import time
import sys

def final_emergency_deploy():
    """Final emergency deployment - remove actions import and deploy"""
    
    print("🚨 FINAL EMERGENCY DEPLOYMENT")
    print("=" * 50)
    print("Fix: Remove back_populates from Action model relationships")
    print("Fix: Re-enable actions endpoint with corrected relationships")
    print("Fix: Remove action_types column that doesn't exist in database")
    print("Fix: Comment out actions relationship in Official model")
    print()
    
    # Configuration
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-api-staging'
    ecr_registry = '308755113449.dkr.ecr.us-east-1.amazonaws.com'
    repository_name = 'modernaction-api-staging'
    image_tag = 'final-fix-official-relationship'
    
    try:
        # Step 1: Build the fixed API image
        print("🔨 Step 1: Building final fixed API image...")
        
        build_cmd = [
            'docker', 'build',
            '--platform', 'linux/amd64',
            '-t', f'{ecr_registry}/{repository_name}:{image_tag}',
            '-f', 'apps/api/Dockerfile',
            'apps/api'
        ]
        
        result = subprocess.run(build_cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ Docker build failed: {result.stderr}")
            return False
        
        print("✅ API image built successfully")
        
        # Step 2: Push image to ECR
        print("\n🔐 Step 2: Pushing image to ECR...")
        
        # Get ECR login token
        login_cmd = ['aws', 'ecr', 'get-login-password', '--region', 'us-east-1']
        login_result = subprocess.run(login_cmd, capture_output=True, text=True)
        
        if login_result.returncode != 0:
            print(f"❌ ECR login failed: {login_result.stderr}")
            return False
        
        # Docker login to ECR
        docker_login_cmd = ['docker', 'login', '--username', 'AWS', '--password-stdin', ecr_registry]
        docker_login_result = subprocess.run(docker_login_cmd, input=login_result.stdout, text=True)
        
        if docker_login_result.returncode != 0:
            print("❌ Docker login to ECR failed")
            return False
        
        # Push image
        push_cmd = ['docker', 'push', f'{ecr_registry}/{repository_name}:{image_tag}']
        push_result = subprocess.run(push_cmd, capture_output=True, text=True)
        
        if push_result.returncode != 0:
            print(f"❌ Docker push failed: {push_result.stderr}")
            return False
        
        print("✅ Image pushed to ECR successfully")
        
        # Step 3: Update ECS task definition
        print("\n📝 Step 3: Updating ECS task definition...")
        
        ecs = boto3.client('ecs', region_name='us-east-1')
        
        # Get current task definition
        current_task_def = ecs.describe_task_definition(
            taskDefinition='ModernActionstagingApiServiceTaskDef63E102B3'
        )
        
        # Create new task definition with updated image
        task_def = current_task_def['taskDefinition']
        
        # Update the API container image
        for container in task_def['containerDefinitions']:
            if container['name'] == 'web':  # The API container is named 'web'
                old_image = container['image']
                container['image'] = f'{ecr_registry}/{repository_name}:{image_tag}'
                print(f"📝 Updated image: {old_image} -> {container['image']}")
        
        # Remove fields that can't be included in register_task_definition
        fields_to_remove = [
            'taskDefinitionArn', 'revision', 'status', 'requiresAttributes',
            'placementConstraints', 'compatibilities', 'registeredAt', 'registeredBy'
        ]
        
        for field in fields_to_remove:
            task_def.pop(field, None)
        
        # Register new task definition
        new_task_def = ecs.register_task_definition(**task_def)
        new_task_def_arn = new_task_def['taskDefinition']['taskDefinitionArn']
        
        print(f"✅ New task definition registered: {new_task_def_arn}")
        
        # Step 4: Update ECS service
        print("\n🔄 Step 4: Updating ECS service...")
        
        ecs.update_service(
            cluster=cluster_name,
            service=service_name,
            taskDefinition=new_task_def_arn,
            forceNewDeployment=True
        )
        
        print("✅ ECS service update initiated")
        
        # Step 5: Wait for deployment to complete
        print("\n⏳ Step 5: Waiting for deployment to complete...")
        
        waiter = ecs.get_waiter('services_stable')
        waiter.wait(
            cluster=cluster_name,
            services=[service_name],
            WaiterConfig={
                'delay': 15,
                'maxAttempts': 40  # 10 minutes
            }
        )
        
        print("✅ Deployment completed successfully!")
        
        # Step 6: Test the fix
        print("\n🔍 Step 6: Testing the campaigns API...")
        
        import requests
        
        # Wait a bit for the service to be fully ready
        time.sleep(30)
        
        try:
            response = requests.get('https://staging.modernaction.io/api/v1/campaigns', timeout=30)
            
            if response.status_code == 200:
                campaigns = response.json()
                if isinstance(campaigns, list):
                    print(f"✅ SUCCESS! API returned {len(campaigns)} campaigns")
                    print("🎉 THE FINAL BUG IS FIXED!")
                    print("🚀 ModernAction.io campaigns API is working!")
                    
                    # Test health endpoint too
                    health_response = requests.get('https://staging.modernaction.io/api/v1/health', timeout=10)
                    if health_response.status_code == 200:
                        print("✅ Health endpoint also working!")
                    
                    return True
                else:
                    print(f"⚠️  API returned non-list response: {campaigns}")
                    return False
            else:
                print(f"❌ API returned status {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ API test failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Final emergency deployment failed: {e}")
        return False

if __name__ == "__main__":
    success = final_emergency_deploy()
    if success:
        print("\n🎉 FINAL EMERGENCY DEPLOYMENT SUCCESSFUL!")
        print("✅ All SQLAlchemy and import issues fixed")
        print("✅ Campaigns API working")
        print("✅ ModernAction.io ready for launch!")
    else:
        print("\n❌ FINAL EMERGENCY DEPLOYMENT FAILED!")
        print("Manual intervention required")
    
    sys.exit(0 if success else 1)
