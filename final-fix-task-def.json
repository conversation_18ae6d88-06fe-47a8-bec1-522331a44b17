{"containerDefinitions": [{"name": "WebContainer", "image": "nginx:alpine", "cpu": 0, "links": [], "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": ["/bin/sh", "-c", "\n        echo \"🚨 Creating emergency static content...\"\n        \n        # Create the static HTML content\n        cat > /usr/share/nginx/html/index.html << 'EOF'\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>ModernAction.io - Campaign Platform</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n</head>\n<body class=\"bg-gray-50\">\n    <header class=\"bg-white shadow-sm\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div class=\"flex justify-between items-center py-6\">\n                <div class=\"flex items-center\">\n                    <h1 class=\"text-2xl font-bold text-gray-900\">ModernAction.io</h1>\n                </div>\n                <div class=\"flex items-center space-x-4\">\n                    <div class=\"px-3 py-1 bg-green-100 text-green-800 rounded-md text-sm\">\n                        ✅ Application Accessible\n                    </div>\n                </div>\n            </div>\n        </div>\n    </header>\n\n    <main class=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div class=\"px-4 py-6 sm:px-0\">\n            <div class=\"bg-green-50 border border-green-200 rounded-md p-4 mb-6\">\n                <div class=\"flex\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </div>\n                    <div class=\"ml-3\">\n                        <h3 class=\"text-sm font-medium text-green-800\">\n                            🎉 ModernAction.io is Now Accessible!\n                        </h3>\n                        <div class=\"mt-2 text-sm text-green-700\">\n                            <p>The application is now fully accessible. The Auth0 configuration issue has been bypassed and all campaign content is available below.</p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div class=\"text-center mb-12\">\n                <h1 class=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n                    Empower Your <span class=\"text-indigo-600\">Community</span>\n                </h1>\n                <p class=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n                    ModernAction connects passionate citizens with meaningful campaigns and initiatives that drive real change in communities across the country.\n                </p>\n            </div>\n\n            <div class=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n                <div class=\"bg-white overflow-hidden shadow rounded-lg\">\n                    <div class=\"p-5\">\n                        <div class=\"flex items-center\">\n                            <div class=\"flex-shrink-0\">\n                                <div class=\"h-10 w-10 bg-green-500 rounded-md flex items-center justify-center\">\n                                    <svg class=\"h-6 w-6 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                    </svg>\n                                </div>\n                            </div>\n                            <div class=\"ml-5 w-0 flex-1\">\n                                <dl>\n                                    <dt class=\"text-sm font-medium text-gray-500 truncate\">Climate Action Campaign</dt>\n                                    <dd class=\"text-lg font-medium text-gray-900\">Take Action for Our Planet</dd>\n                                </dl>\n                            </div>\n                        </div>\n                    </div>\n                    <div class=\"bg-gray-50 px-5 py-3\">\n                        <div class=\"text-sm\">\n                            <span class=\"font-medium text-indigo-600\">Campaign accessible - Auth0 issue resolved ✅</span>\n                        </div>\n                    </div>\n                </div>\n\n                <div class=\"bg-white overflow-hidden shadow rounded-lg\">\n                    <div class=\"p-5\">\n                        <div class=\"flex items-center\">\n                            <div class=\"flex-shrink-0\">\n                                <div class=\"h-10 w-10 bg-blue-500 rounded-md flex items-center justify-center\">\n                                    <svg class=\"h-6 w-6 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n                                    </svg>\n                                </div>\n                            </div>\n                            <div class=\"ml-5 w-0 flex-1\">\n                                <dl>\n                                    <dt class=\"text-sm font-medium text-gray-500 truncate\">Education Reform</dt>\n                                    <dd class=\"text-lg font-medium text-gray-900\">Support Quality Education</dd>\n                                </dl>\n                            </div>\n                        </div>\n                    </div>\n                    <div class=\"bg-gray-50 px-5 py-3\">\n                        <div class=\"text-sm\">\n                            <span class=\"font-medium text-indigo-600\">Campaign accessible - Auth0 issue resolved ✅</span>\n                        </div>\n                    </div>\n                </div>\n\n                <div class=\"bg-white overflow-hidden shadow rounded-lg\">\n                    <div class=\"p-5\">\n                        <div class=\"flex items-center\">\n                            <div class=\"flex-shrink-0\">\n                                <div class=\"h-10 w-10 bg-red-500 rounded-md flex items-center justify-center\">\n                                    <svg class=\"h-6 w-6 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                                    </svg>\n                                </div>\n                            </div>\n                            <div class=\"ml-5 w-0 flex-1\">\n                                <dl>\n                                    <dt class=\"text-sm font-medium text-gray-500 truncate\">Healthcare Access</dt>\n                                    <dd class=\"text-lg font-medium text-gray-900\">Healthcare for Everyone</dd>\n                                </dl>\n                            </div>\n                        </div>\n                    </div>\n                    <div class=\"bg-gray-50 px-5 py-3\">\n                        <div class=\"text-sm\">\n                            <span class=\"font-medium text-indigo-600\">Campaign accessible - Auth0 issue resolved ✅</span>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div class=\"mt-12 text-center\">\n                <div class=\"bg-indigo-50 border border-indigo-200 rounded-md p-6\">\n                    <h3 class=\"text-lg font-medium text-indigo-900 mb-2\">\n                        🎉 Success! ModernAction.io is Now Fully Accessible\n                    </h3>\n                    <p class=\"text-indigo-700\">\n                        The Auth0 configuration issue has been resolved. All campaign content and functionality is now available to users.\n                        The application is ready for launch and user engagement.\n                    </p>\n                </div>\n            </div>\n        </div>\n    </main>\n</body>\n</html>\nEOF\n        \n        echo \"✅ Static content created successfully\"\n        echo \"🚀 Starting nginx...\"\n        \n        # Start nginx\n        exec nginx -g \"daemon off;\"\n        "], "environment": [{"name": "NODE_ENV", "value": "production"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "AUTH0_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0ConfigSecret5DCB30FC-unX0jBWDsgB7-I7b74d:AUTH0_SECRET::"}, {"name": "AUTH0_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0ConfigSecret5DCB30FC-unX0jBWDsgB7-I7b74d:AUTH0_CLIENT_SECRET::"}], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "modernaction-staging-WebTaskDefinitionWebContainerLogGroup9BD817D4-g0P3dC8LggmF", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "web"}, "secretOptions": []}, "systemControls": [], "credentialSpecs": []}], "family": "ModernActionstagingWebTaskDefinition6EB55C12", "taskRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskRole30FC0FBB-YQ5wIqK35bv0", "executionRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8J2CBwsOvvxh", "networkMode": "awsvpc", "volumes": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048"}