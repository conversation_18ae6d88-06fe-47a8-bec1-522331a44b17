#!/usr/bin/env node

const { chromium } = require('playwright');

async function finalProductionUAT() {
    console.log('🚀 FINAL PRODUCTION READINESS UAT');
    console.log('=================================');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 300
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    const results = {
        homepage: false,
        auth0Integration: false,
        campaignsAPI: false,
        campaignsPage: false,
        apiHealth: false,
        databaseSeeded: false,
        noBypassMode: false
    };
    
    try {
        console.log('\n🏠 Testing Homepage...');
        await page.goto('https://staging.modernaction.io', { waitUntil: 'networkidle' });
        
        const title = await page.title();
        results.homepage = title.includes('ModernAction');
        console.log(`✅ Homepage loads: ${results.homepage}`);
        
        const content = await page.textContent('body');
        results.noBypassMode = !content.includes('Auth0 Bypass Mode');
        console.log(`✅ No Auth0 bypass mode: ${results.noBypassMode}`);
        
        console.log('\n🔐 Testing Auth0 Integration...');
        try {
            const authResponse = await page.goto('https://staging.modernaction.io/api/auth/login');
            results.auth0Integration = authResponse.status() === 302 || page.url().includes('auth0.com');
            console.log(`✅ Auth0 redirect working: ${results.auth0Integration}`);
        } catch (e) {
            console.log(`❌ Auth0 test failed: ${e.message}`);
        }
        
        console.log('\n🌐 Testing API Health...');
        try {
            await page.goto('https://staging.modernaction.io/api/v1/health');
            const healthContent = await page.textContent('body');
            results.apiHealth = healthContent.includes('healthy') || healthContent.includes('ok');
            console.log(`✅ API health: ${results.apiHealth}`);
        } catch (e) {
            console.log(`❌ API health failed: ${e.message}`);
        }
        
        console.log('\n📋 Testing Campaigns API...');
        try {
            await page.goto('https://staging.modernaction.io/api/v1/campaigns');
            const campaignsContent = await page.textContent('body');
            const campaignsData = JSON.parse(campaignsContent);
            results.campaignsAPI = Array.isArray(campaignsData) && campaignsData.length > 0;
            results.databaseSeeded = campaignsData.length >= 5;
            console.log(`✅ Campaigns API working: ${results.campaignsAPI}`);
            console.log(`✅ Database seeded (${campaignsData.length} campaigns): ${results.databaseSeeded}`);
        } catch (e) {
            console.log(`❌ Campaigns API failed: ${e.message}`);
        }
        
        console.log('\n📄 Testing Campaigns Page...');
        try {
            await page.goto('https://staging.modernaction.io/campaigns', { waitUntil: 'networkidle' });
            const campaignsPageContent = await page.textContent('body');
            results.campaignsPage = page.url().includes('/campaigns') && 
                                   (campaignsPageContent.includes('campaign') || 
                                    campaignsPageContent.includes('Campaign'));
            console.log(`✅ Campaigns page accessible: ${results.campaignsPage}`);
        } catch (e) {
            console.log(`❌ Campaigns page failed: ${e.message}`);
        }
        
        await page.screenshot({ path: 'final-uat-complete.png' });
        
    } catch (error) {
        console.log(`❌ UAT Error: ${error.message}`);
        await page.screenshot({ path: 'final-uat-error.png' });
        
    } finally {
        await browser.close();
    }
    
    console.log('\n🏆 FINAL PRODUCTION READINESS ASSESSMENT');
    console.log('========================================');
    
    const testSuite = [
        { name: 'Homepage Loads', result: results.homepage, critical: true },
        { name: 'No Auth0 Bypass Mode', result: results.noBypassMode, critical: true },
        { name: 'Auth0 Integration Working', result: results.auth0Integration, critical: true },
        { name: 'API Health Check', result: results.apiHealth, critical: true },
        { name: 'Campaigns API Working', result: results.campaignsAPI, critical: true },
        { name: 'Database Properly Seeded', result: results.databaseSeeded, critical: true },
        { name: 'Campaigns Page Accessible', result: results.campaignsPage, critical: false }
    ];
    
    let criticalPassed = 0;
    let criticalTotal = 0;
    let totalPassed = 0;
    
    testSuite.forEach(test => {
        const status = test.result ? '✅ PASS' : '❌ FAIL';
        const priority = test.critical ? '[CRITICAL]' : '[OPTIONAL]';
        console.log(`${status} ${priority} - ${test.name}`);
        
        if (test.result) totalPassed++;
        if (test.critical) {
            criticalTotal++;
            if (test.result) criticalPassed++;
        }
    });
    
    const overallSuccess = (totalPassed / testSuite.length * 100).toFixed(1);
    const criticalSuccess = (criticalPassed / criticalTotal * 100).toFixed(1);
    
    console.log(`\n📊 Overall Success Rate: ${overallSuccess}% (${totalPassed}/${testSuite.length})`);
    console.log(`🎯 Critical Systems Success: ${criticalSuccess}% (${criticalPassed}/${criticalTotal})`);
    
    if (criticalSuccess >= 100) {
        console.log('\n🎉 🚀 PRODUCTION READY! 🚀 🎉');
        console.log('All critical systems are functioning correctly.');
        console.log('The application is ready for production deployment and user access.');
        return true;
    } else if (criticalSuccess >= 80) {
        console.log('\n⚠️  MOSTLY READY - Minor issues detected');
        console.log('Core functionality working but some non-critical issues remain.');
        return true;
    } else {
        console.log('\n❌ NOT READY - Critical issues need resolution');
        console.log('Critical systems are not functioning properly.');
        return false;
    }
}

// Execute final UAT
finalProductionUAT().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('💥 Final UAT crashed:', error);
    process.exit(1);
});
