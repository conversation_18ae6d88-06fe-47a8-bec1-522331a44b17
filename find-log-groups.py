#!/usr/bin/env python3
"""
Find the correct log group for the ECS service
"""

import boto3

def main():
    logs = boto3.client('logs', region_name='us-east-1')
    
    print("🔍 Finding log groups...")
    
    try:
        # List log groups that might be related to our service
        response = logs.describe_log_groups(
            logGroupNamePrefix='/ecs/'
        )
        
        print("ECS Log Groups:")
        for group in response['logGroups']:
            print(f"  {group['logGroupName']}")
            
        # Also check for modernaction related groups
        response2 = logs.describe_log_groups(
            logGroupNamePrefix='/aws/ecs/'
        )
        
        print("\nAWS ECS Log Groups:")
        for group in response2['logGroups']:
            print(f"  {group['logGroupName']}")
            
        # Check for any groups with 'modern' in the name
        all_groups = logs.describe_log_groups()
        modern_groups = [g for g in all_groups['logGroups'] 
                        if 'modern' in g['logGroupName'].lower()]
        
        if modern_groups:
            print("\nModernAction related log groups:")
            for group in modern_groups:
                print(f"  {group['logGroupName']}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    main()
