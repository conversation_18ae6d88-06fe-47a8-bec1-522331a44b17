#!/usr/bin/env python3
"""
Fix Auth0 API route to bypass Auth0 configuration
"""

import boto3
import json
import time

def main():
    ecs = boto3.client('ecs', region_name='us-east-1')
    
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-web-staging'
    
    print("🚀 Fixing Auth0 API route...")
    
    # Get running task
    tasks_response = ecs.list_tasks(
        cluster=cluster_name,
        serviceName=service_name,
        desiredStatus='RUNNING'
    )
    
    if not tasks_response['taskArns']:
        print("❌ No running tasks found!")
        return
    
    task_details = ecs.describe_tasks(
        cluster=cluster_name,
        tasks=[tasks_response['taskArns'][0]]
    )
    
    task = task_details['tasks'][0]
    task_id = task['taskArn'].split('/')[-1]
    
    print(f"Task ID: {task_id}")
    
    # Execute command to fix the Auth0 API route
    print("🔧 Fixing Auth0 API route in running container...")
    
    # Create bypass Auth0 API route
    bypass_route_content = '''import { NextRequest, NextResponse } from 'next/server';

// EMERGENCY BYPASS: Auth0 API routes disabled until configuration is fixed
export async function GET(request: NextRequest) {
  console.log('🚨 Auth0 API route bypassed - authentication disabled');
  
  const { pathname } = new URL(request.url);
  
  // Handle different Auth0 routes
  if (pathname.includes('/login')) {
    return NextResponse.redirect(new URL('/', request.url));
  }
  
  if (pathname.includes('/logout')) {
    return NextResponse.redirect(new URL('/', request.url));
  }
  
  if (pathname.includes('/callback')) {
    return NextResponse.redirect(new URL('/', request.url));
  }
  
  if (pathname.includes('/me')) {
    return NextResponse.json({ 
      error: 'Auth0 bypassed',
      message: 'Authentication is currently disabled'
    });
  }
  
  // Default response
  return NextResponse.json({ 
    status: 'Auth0 bypassed',
    message: 'Authentication routes are currently disabled'
  });
}

export async function POST(request: NextRequest) {
  return GET(request);
}
'''
    
    # Execute command to update the file
    exec_response = ecs.execute_command(
        cluster=cluster_name,
        task=task_id,
        container='WebContainer',
        interactive=False,
        command=[
            'sh', '-c', 
            f'''cat > /app/src/app/api/auth/[auth0]/route.ts << 'EOF'
{bypass_route_content}
EOF'''
        ]
    )
    
    print("✅ Auth0 API route updated")
    
    # Restart the Next.js process to pick up the changes
    print("🔄 Restarting Next.js process...")
    
    restart_response = ecs.execute_command(
        cluster=cluster_name,
        task=task_id,
        container='WebContainer',
        interactive=False,
        command=['sh', '-c', 'pkill -f "next" && sleep 2 && npm run start &']
    )
    
    print("✅ Next.js process restarted")
    print("⏳ Waiting for application to restart...")
    time.sleep(30)
    
    print("🎉 Auth0 API route fix completed!")
    print("🔗 Test the application at: https://staging.modernaction.io")

if __name__ == '__main__':
    main()
