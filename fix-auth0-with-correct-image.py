#!/usr/bin/env python3
"""
Fix Auth0 configuration with the correct Docker image
"""

import boto3
import json

def main():
    # AWS clients
    ecs = boto3.client('ecs', region_name='us-east-1')
    ecr = boto3.client('ecr', region_name='us-east-1')
    
    # Configuration
    cluster_name = 'modernaction-staging'
    service_name = 'modernaction-web-staging'
    secret_arn = 'arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0ConfigSecret5DCB30FC-unX0jBWDsgB7-I7b74d'
    
    print("🚀 Starting Auth0 fix with correct Docker image...")
    
    # Step 1: Check available ECR images
    print("🐳 Checking available Docker images...")
    try:
        ecr_response = ecr.describe_images(
            repositoryName='modernaction-web-staging'
        )
        
        print("Available images:")
        for image in ecr_response['imageDetails'][:10]:  # Show last 10 images
            tags = image.get('imageTags', ['<untagged>'])
            pushed_at = image.get('imagePushedAt', 'Unknown')
            print(f"  Tags: {tags}, Pushed: {pushed_at}")
            
    except Exception as e:
        print(f"Error checking ECR: {e}")
    
    # Step 2: Get current working task definition (the one that's actually running)
    print("\n📋 Getting current working task definition...")
    service_response = ecs.describe_services(
        cluster=cluster_name,
        services=[service_name]
    )
    
    if not service_response['services']:
        print("❌ Service not found!")
        return
    
    # Get the task definition that's actually working (from running tasks)
    tasks_response = ecs.list_tasks(
        cluster=cluster_name,
        serviceName=service_name,
        desiredStatus='RUNNING'
    )
    
    if not tasks_response['taskArns']:
        print("❌ No running tasks found!")
        return
    
    # Get details of the running task
    task_details = ecs.describe_tasks(
        cluster=cluster_name,
        tasks=[tasks_response['taskArns'][0]]
    )
    
    working_task_def_arn = task_details['tasks'][0]['taskDefinitionArn']
    print(f"Working task definition: {working_task_def_arn}")
    
    # Get the working task definition
    task_def_response = ecs.describe_task_definition(
        taskDefinition=working_task_def_arn
    )
    
    task_def = task_def_response['taskDefinition']
    
    # Step 3: Add Auth0 secrets to the working task definition
    print("🔧 Adding Auth0 secrets to working task definition...")
    
    # Find the web container
    web_container = None
    for container in task_def['containerDefinitions']:
        if container['name'] == 'WebContainer':
            web_container = container
            break
    
    if not web_container:
        print("❌ WebContainer not found in task definition!")
        return
    
    print(f"Current image: {web_container['image']}")
    
    # Add secrets to the web container
    if 'secrets' not in web_container:
        web_container['secrets'] = []
    
    # Add Auth0 secrets
    auth0_secrets = [
        {
            'name': 'AUTH0_SECRET',
            'valueFrom': f'{secret_arn}:AUTH0_SECRET::'
        },
        {
            'name': 'AUTH0_CLIENT_SECRET',
            'valueFrom': f'{secret_arn}:AUTH0_CLIENT_SECRET::'
        }
    ]
    
    # Remove existing Auth0 secrets if any
    web_container['secrets'] = [s for s in web_container['secrets'] 
                               if s['name'] not in ['AUTH0_SECRET', 'AUTH0_CLIENT_SECRET']]
    
    # Add new Auth0 secrets
    web_container['secrets'].extend(auth0_secrets)
    
    print(f"✅ Added {len(auth0_secrets)} Auth0 secrets to WebContainer")
    
    # Step 4: Register new task definition
    print("📝 Registering new task definition...")
    
    # Remove fields that shouldn't be in the registration request
    task_def_for_registration = {
        'family': task_def['family'],
        'taskRoleArn': task_def['taskRoleArn'],
        'executionRoleArn': task_def['executionRoleArn'],
        'networkMode': task_def['networkMode'],
        'containerDefinitions': task_def['containerDefinitions'],
        'requiresCompatibilities': task_def['requiresCompatibilities'],
        'cpu': task_def['cpu'],
        'memory': task_def['memory']
    }
    
    new_task_def_response = ecs.register_task_definition(**task_def_for_registration)
    new_task_def_arn = new_task_def_response['taskDefinition']['taskDefinitionArn']
    
    print(f"✅ New task definition registered: {new_task_def_arn}")
    
    # Step 5: Update service to use new task definition
    print("🔄 Updating ECS service...")
    
    ecs.update_service(
        cluster=cluster_name,
        service=service_name,
        taskDefinition=new_task_def_arn,
        forceNewDeployment=True
    )
    
    print("✅ Service update initiated!")
    print("⏳ Deployment will take a few minutes...")
    print("🔗 Test the application at: https://staging.modernaction.io")

if __name__ == '__main__':
    main()
