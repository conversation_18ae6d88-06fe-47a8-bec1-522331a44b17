#!/usr/bin/env python3

import boto3
import subprocess
import time
import sys

def fix_container_directly():
    """Fix the SQLAlchemy issue directly in the running container"""
    
    print("🚨 FIXING SQLALCHEMY ISSUE DIRECTLY IN CONTAINER")
    print("=" * 50)
    
    # Get the current running task
    ecs = boto3.client('ecs', region_name='us-east-1')
    
    try:
        # Get running tasks
        tasks = ecs.list_tasks(
            cluster='modernaction-staging',
            serviceName='modernaction-api-staging'
        )
        
        if not tasks['taskArns']:
            print("❌ No running tasks found")
            return False
        
        task_arn = tasks['taskArns'][0]
        task_id = task_arn.split('/')[-1]
        
        print(f"📋 Found running task: {task_id}")
        
        # Step 1: Fix the User model directly in the container
        print("\n🔧 Step 1: Fixing User model in container...")
        
        fix_command = """
        # Remove the broken relationship line
        sed -i 's/actions = relationship("Action", back_populates="user", cascade="all, delete-orphan")/# Note: Action model relationship removed as Action model doesn'\''t exist/' /app/app/models/user.py
        
        # Verify the fix
        echo "✅ Fixed User model:"
        grep -A 2 -B 2 "Action model relationship removed" /app/app/models/user.py
        """
        
        # Execute the fix in the container
        exec_command = [
            'aws', 'ecs', 'execute-command',
            '--cluster', 'modernaction-staging',
            '--task', task_id,
            '--container', 'web',
            '--interactive',
            '--command', f'/bin/bash -c "{fix_command}"',
            '--region', 'us-east-1'
        ]
        
        try:
            result = subprocess.run(exec_command, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print("✅ User model fixed in container")
                print(result.stdout)
            else:
                print(f"❌ Failed to fix User model: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Failed to execute command in container: {e}")
            return False
        
        # Step 2: Restart the application process
        print("\n🔄 Step 2: Restarting application process...")
        
        restart_command = """
        # Find and kill the main application process
        pkill -f "uvicorn app.main:app" || true
        
        # Wait a moment
        sleep 2
        
        # The container should restart automatically due to health checks
        echo "✅ Application process restarted"
        """
        
        exec_restart = [
            'aws', 'ecs', 'execute-command',
            '--cluster', 'modernaction-staging',
            '--task', task_id,
            '--container', 'web',
            '--interactive',
            '--command', f'/bin/bash -c "{restart_command}"',
            '--region', 'us-east-1'
        ]
        
        try:
            result = subprocess.run(exec_restart, capture_output=True, text=True, timeout=30)
            print("✅ Application restart initiated")
        except Exception as e:
            print(f"⚠️  Restart command failed, but container should auto-restart: {e}")
        
        # Step 3: Wait and verify
        print("\n⏳ Step 3: Waiting for service to recover...")
        time.sleep(45)  # Give time for the service to restart
        
        # Step 4: Test the API
        print("\n🔍 Step 4: Testing the campaigns API...")
        
        import requests
        
        try:
            response = requests.get('https://staging.modernaction.io/api/v1/campaigns', timeout=30)
            
            if response.status_code == 200:
                campaigns = response.json()
                if isinstance(campaigns, list) and len(campaigns) > 0:
                    print(f"✅ SUCCESS! API returned {len(campaigns)} campaigns")
                    print("🎉 THE FINAL BUG IS FIXED!")
                    print("🚀 ModernAction.io is ready for launch!")
                    return True
                else:
                    print(f"⚠️  API returned empty list: {campaigns}")
                    return False
            else:
                print(f"❌ API returned status {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ API test failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Failed to get task information: {e}")
        return False

if __name__ == "__main__":
    success = fix_container_directly()
    sys.exit(0 if success else 1)
