#!/usr/bin/env python3
"""
Emergency script to fix staging database migration issues
"""
import os
import psycopg2
import subprocess

# Get database connection info from environment or use staging defaults
DB_HOST = "modernaction-staging-modernactiondatabasefdd241de-giohrnseld4l.csdoaiogadw5.us-east-1.rds.amazonaws.com"
DB_PORT = "5432"
DB_NAME = "modernaction"

def get_db_credentials():
    """Get database credentials from AWS Secrets Manager"""
    import json
    import boto3
    
    client = boto3.client('secretsmanager', region_name='us-east-1')
    
    try:
        response = client.get_secret_value(
            SecretId='DatabaseCredentials8547B3E7-7zaDeXDnFrx0'
        )
        credentials = json.loads(response['SecretString'])
        return credentials['username'], credentials['password']
    except Exception as e:
        print(f"Error getting credentials: {e}")
        return None, None

def fix_alembic_version(connection):
    """Fix the alembic_version table to match current migration"""
    cursor = connection.cursor()
    
    try:
        # Check if alembic_version table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'alembic_version'
            );
        """)
        
        if cursor.fetchone()[0]:
            print("Alembic version table exists, updating...")
            # Update to current revision
            cursor.execute("DELETE FROM alembic_version;")
            cursor.execute("INSERT INTO alembic_version (version_num) VALUES ('3d4ee03ec8da');")
        else:
            print("Creating alembic version table...")
            cursor.execute("CREATE TABLE alembic_version (version_num VARCHAR(32) NOT NULL, CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num));")
            cursor.execute("INSERT INTO alembic_version (version_num) VALUES ('3d4ee03ec8da');")
        
        connection.commit()
        print("✅ Alembic version table fixed!")
        
    except Exception as e:
        print(f"Error fixing alembic version: {e}")
        connection.rollback()
    finally:
        cursor.close()

def main():
    username, password = get_db_credentials()
    
    if not username or not password:
        print("❌ Could not get database credentials")
        return
    
    try:
        # Connect to database
        connection = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=username,
            password=password
        )
        
        print(f"✅ Connected to staging database at {DB_HOST}")
        
        # Fix alembic version table
        fix_alembic_version(connection)
        
        connection.close()
        print("✅ Database migration fix completed!")
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")

if __name__ == "__main__":
    main()