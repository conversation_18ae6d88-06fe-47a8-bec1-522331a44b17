#!/usr/bin/env python3
"""
Fix database data consistency issues for bills endpoint
"""
import sys
import os
sys.path.append('/Users/<USER>/modern-action-2.0/apps/api')

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import get_settings

def fix_bills_data():
    """Fix bills data by converting empty objects to empty arrays"""
    
    # Use the same database URL that the API is using (from logs)
    database_url = "postgresql://modernaction_user:modernaction_password@localhost:5432/modernaction"
    
    print(f"Connecting to PostgreSQL database...")
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    session = SessionLocal()
    
    try:
        # Check what tables exist (PostgreSQL syntax)
        print("Checking database tables...")
        result = session.execute(text("SELECT tablename FROM pg_tables WHERE schemaname = 'public';"))
        tables = result.fetchall()
        print(f"Available tables: {[table[0] for table in tables]}")
        
        if not any('bills' in str(table) for table in tables):
            print("❌ No bills table found in database")
            return
            
        print("Fixing bills data consistency issues...")
        
        # First, let's see what the problematic records look like
        result = session.execute(text("SELECT id, tags, categories, reasons_for_support, reasons_for_opposition FROM bills LIMIT 25;"))
        bills = result.fetchall()
        
        print(f"\nFound {len(bills)} bills, checking for problematic data...")
        
        problematic_count = 0
        for bill in bills:
            bill_id, tags, categories, reasons_for_support, reasons_for_opposition = bill
            if str(tags) == '{}' or str(categories) == '{}' or str(reasons_for_support) == '{}' or str(reasons_for_opposition) == '{}':
                problematic_count += 1
                print(f"Bill {bill_id}: tags={tags}, categories={categories}, reasons_for_support={reasons_for_support}, reasons_for_opposition={reasons_for_opposition}")
        
        print(f"Found {problematic_count} bills with problematic data")
        
        if problematic_count > 0:
            # Update empty objects to empty arrays for the problematic fields (PostgreSQL JSONB syntax)
            queries = [
                "UPDATE bills SET tags = '[]'::jsonb WHERE tags::text = '{}';",
                "UPDATE bills SET categories = '[]'::jsonb WHERE categories::text = '{}';", 
                "UPDATE bills SET reasons_for_support = '[]'::jsonb WHERE reasons_for_support::text = '{}';",
                "UPDATE bills SET reasons_for_opposition = '[]'::jsonb WHERE reasons_for_opposition::text = '{}';",
            ]
            
            for query in queries:
                print(f"Executing: {query}")
                result = session.execute(text(query))
                print(f"Updated {result.rowcount} rows")
            
            session.commit()
            print("✅ Database fixes applied successfully")
        else:
            print("✅ No problematic data found")
        
        # Test the bills endpoint
        print("\nTesting bills query...")
        result = session.execute(text("SELECT COUNT(*) FROM bills;"))
        count = result.scalar()
        print(f"Total bills in database: {count}")
        
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    fix_bills_data()