#!/usr/bin/env python3
"""
Emergency fix for staging database - add missing five_calls_id column
"""
import psycopg2

# Staging database credentials
host = 'modernaction-staging-modernactiondatabasefdd241de-giohrnseld4l.csdoaiogadw5.us-east-1.rds.amazonaws.com'
username = 'modernaction_admin'
password = '5WS4vmMmK-yMJnE9=_QOPlN-Ik5QEk'
database = 'modernaction'

print('🔧 Emergency fix: Adding missing five_calls_id column to staging database...')

try:
    conn = psycopg2.connect(
        host=host,
        user=username,
        password=password,
        database=database,
        port=5432
    )
    cur = conn.cursor()
    
    # Check if column already exists
    cur.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'officials' AND column_name = 'five_calls_id';
    """)
    
    if cur.fetchone():
        print('✅ Column five_calls_id already exists')
    else:
        # Add the missing column
        cur.execute('ALTER TABLE officials ADD COLUMN five_calls_id VARCHAR;')
        print('✅ Added five_calls_id column')
        conn.commit()
    
    print('🚀 Database schema fixed!')
    
except Exception as e:
    print(f'❌ Error: {e}')
    if 'conn' in locals():
        conn.rollback()
finally:
    if 'cur' in locals():
        cur.close()
    if 'conn' in locals():
        conn.close()
