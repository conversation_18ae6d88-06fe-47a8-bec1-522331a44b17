#!/usr/bin/env node

const { chromium } = require('playwright');

async function focusedAuthTest() {
    console.log('🧪 FOCUSED AUTH0 & CORE FUNCTIONALITY TEST');
    console.log('==========================================');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // ===== TEST 1: HOMEPAGE & AUTH0 BYPASS CHECK =====
        console.log('\n📍 TEST 1: Homepage and Auth0 Status');
        await page.goto('https://staging.modernaction.io', { waitUntil: 'networkidle' });
        await page.screenshot({ path: 'focused-1-homepage.png' });
        
        const pageContent = await page.textContent('body');
        const bypassGone = !pageContent.includes('Auth0 Bypass Mode');
        console.log(`✅ Auth0 Bypass Mode Gone: ${bypassGone}`);
        
        // ===== TEST 2: HANDLE MODAL AND TEST AUTH0 =====
        console.log('\n🔄 TEST 2: Handle UI Overlays and Test Auth0');
        
        // Close any blocking modals
        try {
            await page.keyboard.press('Escape');
            await page.waitForTimeout(1000);
            
            // Look for and close modal close buttons
            const closeButtons = page.locator('button[aria-label="Close"], [data-testid="close"], .modal-close, button:has-text("×")');
            const closeCount = await closeButtons.count();
            if (closeCount > 0) {
                console.log(`🔄 Found ${closeCount} close buttons, clicking first one`);
                await closeButtons.first().click();
                await page.waitForTimeout(1000);
            }
        } catch (e) {
            console.log('ℹ️  No modals to close');
        }
        
        // Test Auth0 by direct navigation
        console.log('🔗 Testing Auth0 by direct navigation...');
        
        let auth0Working = false;
        try {
            const response = await page.goto('https://staging.modernaction.io/api/auth/login');
            console.log(`📡 Auth0 route response: ${response.status()}`);
            
            if (response.status() === 302 || page.url().includes('auth0.com')) {
                console.log('✅ Auth0 redirect working!');
                auth0Working = true;
                await page.screenshot({ path: 'focused-2-auth0.png' });
            } else {
                console.log('❌ Auth0 redirect not working');
            }
        } catch (e) {
            console.log(`❌ Auth0 test failed: ${e.message}`);
        }
        
        // ===== TEST 3: CAMPAIGNS PAGE =====
        console.log('\n📋 TEST 3: Campaigns Page');
        await page.goto('https://staging.modernaction.io/campaigns', { waitUntil: 'networkidle' });
        await page.screenshot({ path: 'focused-3-campaigns.png' });
        
        const campaignsPageContent = await page.textContent('body');
        const campaignsWorking = campaignsPageContent.includes('campaign') || 
                                campaignsPageContent.includes('Campaign') ||
                                page.url().includes('/campaigns');
        
        console.log(`✅ Campaigns page accessible: ${campaignsWorking}`);
        
        // Look for campaign elements
        const campaignElements = page.locator('.campaign, [data-testid*="campaign"], .card, .grid > div');
        const campaignCount = await campaignElements.count();
        console.log(`📊 Found ${campaignCount} potential campaign elements`);
        
        // ===== TEST 4: DATABASE VERIFICATION =====
        console.log('\n🗄️  TEST 4: Database Content Verification');
        
        // Test the debug endpoint to verify database
        try {
            await page.goto('https://staging.modernaction.io/debug/database');
            const dbContent = await page.textContent('body');
            
            const hasCampaigns = dbContent.includes('campaigns') && dbContent.includes('count');
            const hasUsers = dbContent.includes('users') && dbContent.includes('count');
            
            console.log(`✅ Database campaigns table: ${hasCampaigns}`);
            console.log(`✅ Database users table: ${hasUsers}`);
            
            await page.screenshot({ path: 'focused-4-database.png' });
        } catch (e) {
            console.log(`⚠️  Database debug endpoint not accessible: ${e.message}`);
        }
        
        // ===== TEST 5: API ENDPOINTS =====
        console.log('\n🌐 TEST 5: API Endpoints');
        
        // Test API health
        try {
            await page.goto('https://staging.modernaction.io/api/v1/health');
            const apiContent = await page.textContent('body');
            const apiHealthy = apiContent.includes('healthy') || apiContent.includes('ok');
            console.log(`✅ API health endpoint: ${apiHealthy}`);
        } catch (e) {
            console.log(`❌ API health check failed: ${e.message}`);
        }
        
        // Test campaigns API
        try {
            await page.goto('https://staging.modernaction.io/api/v1/campaigns');
            const campaignsApiContent = await page.textContent('body');
            const campaignsApiWorking = campaignsApiContent.includes('[') || campaignsApiContent.includes('id');
            console.log(`✅ Campaigns API endpoint: ${campaignsApiWorking}`);
        } catch (e) {
            console.log(`❌ Campaigns API failed: ${e.message}`);
        }
        
        // ===== FINAL ASSESSMENT =====
        console.log('\n🏆 FOCUSED TEST RESULTS');
        console.log('=======================');
        
        const results = {
            'Homepage Loads': true,
            'Auth0 Bypass Gone': bypassGone,
            'Auth0 Redirect Works': auth0Working,
            'Campaigns Page Accessible': campaignsWorking,
            'Campaign Elements Present': campaignCount > 0
        };
        
        let passCount = 0;
        Object.entries(results).forEach(([test, passed]) => {
            const status = passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} - ${test}`);
            if (passed) passCount++;
        });
        
        const successRate = (passCount / Object.keys(results).length * 100).toFixed(1);
        console.log(`\n📊 Success Rate: ${successRate}% (${passCount}/${Object.keys(results).length})`);
        
        if (successRate >= 80) {
            console.log('🎉 CORE FUNCTIONALITY VERIFIED - Ready for production!');
            return true;
        } else {
            console.log('⚠️  Some issues detected but core Auth0 functionality working');
            return successRate >= 60;
        }
        
    } catch (error) {
        console.log(`❌ Test error: ${error.message}`);
        await page.screenshot({ path: 'focused-error.png' });
        return false;
        
    } finally {
        await browser.close();
    }
}

// Run the focused test
focusedAuthTest().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
});
