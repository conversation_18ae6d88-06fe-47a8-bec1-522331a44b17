# ModernAction Infrastructure as Code

This directory contains AWS CDK (Cloud Development Kit) infrastructure definitions for the ModernAction platform using a **three-container architecture**.

## Architecture Overview

### Container Architecture
- **API Service** (`modernaction-api`) - Lightweight FastAPI service for synchronous requests
- **Web Frontend** (`modernaction-web`) - Next.js application served via Fargate
- **AI Worker** (`modernaction-worker`) - Heavyweight container with ML libraries for background tasks

### AWS Services
- **VPC** - Custom VPC with public/private subnets across 2 AZs
- **RDS** - PostgreSQL database with automated backups
- **ECR** - Container image repositories (3 repositories)
- **ECS Fargate** - Serverless container orchestration
- **Application Load Balancer** - Traffic distribution and SSL termination
- **Lambda** - Bill status updates and notification processing
- **SQS** - Message queuing for bill status notifications
- **Secrets Manager** - Secure credential storage
- **CloudWatch** - Logging and monitoring

## Prerequisites

1. **AWS CLI** configured with appropriate credentials
2. **AWS CDK** installed globally
3. **Python 3.11+** with pip
4. **Docker** for container builds
5. **Node.js** for CDK synthesis

### Installation

```bash
# Install AWS CDK
npm install -g aws-cdk

# Verify installation
cdk --version

# Configure AWS credentials
aws configure
```

## Quick Start

### 1. Setup Python Environment

```bash
cd infrastructure
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Bootstrap CDK (First Time Only)

```bash
# Bootstrap CDK in your AWS account/region
cdk bootstrap
```

### 3. Deploy Infrastructure

```bash
# Synthesize CloudFormation template
cdk synth

# Deploy to development environment
ENVIRONMENT=dev cdk deploy

# Deploy to production environment  
ENVIRONMENT=prod cdk deploy
```

## Container Deployment Workflow

### 1. Build and Push Container Images

```bash
# Set your AWS account ID and region
export AWS_ACCOUNT_ID=************
export AWS_REGION=us-east-1
export ENVIRONMENT=dev

# Get ECR login token
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# Build and push API service
cd ../apps/api
docker build -t modernaction-api .
docker tag modernaction-api:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/modernaction-api-$ENVIRONMENT:latest
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/modernaction-api-$ENVIRONMENT:latest

# Build and push AI Worker
docker build -f Dockerfile.worker -t modernaction-worker .
docker tag modernaction-worker:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/modernaction-worker-$ENVIRONMENT:latest  
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/modernaction-worker-$ENVIRONMENT:latest

# Build and push Web frontend
cd ../web
docker build -t modernaction-web .
docker tag modernaction-web:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/modernaction-web-$ENVIRONMENT:latest
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/modernaction-web-$ENVIRONMENT:latest
```

### 2. Update Fargate Services

After pushing new images, update the ECS services:

```bash
# Force new deployment to pick up latest images
aws ecs update-service --cluster modernaction-$ENVIRONMENT --service modernaction-api-$ENVIRONMENT --force-new-deployment
aws ecs update-service --cluster modernaction-$ENVIRONMENT --service modernaction-web-$ENVIRONMENT --force-new-deployment
```

## Running AI Worker Tasks

The AI Worker container is designed for one-off and scheduled tasks:

```bash
# Run backfill script
aws ecs run-task \
  --cluster modernaction-$ENVIRONMENT \
  --task-definition modernaction-worker-$ENVIRONMENT \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}" \
  --overrides '{"containerOverrides":[{"name":"WorkerContainer","command":["python","scripts/backfill_ai_summaries.py"]}]}'

# Run interactive shell for debugging
aws ecs run-task \
  --cluster modernaction-$ENVIRONMENT \
  --task-definition modernaction-worker-$ENVIRONMENT \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}" \
  --overrides '{"containerOverrides":[{"name":"WorkerContainer","command":["bash"]}]}'
```

## Environment Configuration

### Development Environment

```bash
export ENVIRONMENT=dev
cdk deploy
```

**Resources:**
- Single instance RDS (db.t3.micro)
- Single Fargate task per service
- Basic security configurations
- Automatic cleanup on destroy

### Production Environment

```bash
export ENVIRONMENT=prod  
cdk deploy
```

**Resources:**
- Multi-AZ RDS with automated backups
- Multiple Fargate tasks with auto-scaling
- Enhanced security and monitoring
- Deletion protection enabled

## Configuration Management

### Secrets Manager

The stack automatically creates secrets for:

- **API Keys** - External service credentials (OpenStates, Google Civic Info, etc.)
- **JWT Secrets** - Authentication signing keys
- **App Config** - Application configuration secrets
- **Database Credentials** - Auto-generated RDS credentials

### Environment Variables

Set these environment variables before deployment:

```bash
# Required
export CDK_DEFAULT_ACCOUNT=************
export CDK_DEFAULT_REGION=us-east-1
export ENVIRONMENT=dev  # or prod

# Optional
export STACK_NAME=modernaction-custom-name
```

## Monitoring and Troubleshooting

### CloudWatch Logs

All services log to CloudWatch with structured log groups:

- `/aws/ecs/modernaction-api-{environment}`
- `/aws/ecs/modernaction-web-{environment}`  
- `/aws/ecs/modernaction-worker-{environment}`
- `/aws/lambda/modernaction-bill-status-update-{environment}`
- `/aws/lambda/modernaction-notification-sender-{environment}`

### Health Checks

- **API Service**: `GET /api/v1/health`
- **Web Service**: `GET /`
- **Database**: Connection monitoring via RDS insights

### Common Issues

#### Container Image Not Found
```bash
# Verify ECR repositories exist
aws ecr describe-repositories --region $AWS_REGION

# Check if image was pushed
aws ecr list-images --repository-name modernaction-api-$ENVIRONMENT --region $AWS_REGION
```

#### Service Won't Start
```bash
# Check service events
aws ecs describe-services --cluster modernaction-$ENVIRONMENT --services modernaction-api-$ENVIRONMENT

# Check task logs
aws logs get-log-events --log-group-name "/aws/ecs/modernaction-api-$ENVIRONMENT" --log-stream-name <stream-name>
```

#### Database Connection Issues
```bash
# Check security group rules
aws ec2 describe-security-groups --group-ids <database-sg-id>

# Test database connectivity from ECS task
aws ecs run-task --cluster modernaction-$ENVIRONMENT --task-definition modernaction-worker-$ENVIRONMENT \
  --overrides '{"containerOverrides":[{"name":"WorkerContainer","command":["psql","-h","<db-host>","-U","<username>","-d","modernaction"]}]}'
```

## Updating Infrastructure

### Stack Updates

```bash
# See what will change
cdk diff

# Deploy changes
cdk deploy

# Deploy specific stack
cdk deploy ModernAction-dev
```

### Rolling Back

```bash
# Rollback to previous CloudFormation stack version
aws cloudformation cancel-update-stack --stack-name modernaction-dev

# Or deploy previous CDK code version
git checkout <previous-commit>
cdk deploy
```

## Security Best Practices

### Network Security
- All containers run in private subnets
- Database accessible only from ECS security group
- NAT Gateway for outbound internet access

### IAM Security
- Minimal IAM permissions per service
- Task execution roles separate from task roles
- Secrets accessed via IAM, not environment variables

### Container Security
- Container images scanned on push to ECR
- Non-root users in all containers
- Read-only root filesystems where possible

## Cost Optimization

### Development Environment
- Use smallest instance types (t3.micro RDS, 256 CPU Fargate)
- Single AZ deployment
- Shorter log retention periods

### Production Environment
- Auto-scaling based on CPU/memory metrics
- Multi-AZ for high availability only
- Right-size instances based on monitoring data

### General Tips
- Delete unused ECR images (automated via lifecycle policies)
- Use Fargate Spot for development workloads
- Schedule non-critical services to run during off-peak hours

## Next Steps

1. **Custom Domain Setup** - Configure Route 53 and SSL certificates
2. **CI/CD Pipeline** - Automate container builds and deployments
3. **Auto-scaling** - Configure ECS auto-scaling policies  
4. **Monitoring** - Set up CloudWatch alarms and dashboards
5. **Backup Strategy** - Configure RDS snapshots and retention
6. **Disaster Recovery** - Plan cross-region deployment strategy

## Support

For infrastructure issues:
1. Check CloudWatch logs for service-specific errors
2. Review CloudFormation events for deployment issues
3. Verify IAM permissions and security group configurations
4. Test connectivity between services using ECS exec