{"family": "modernaction-api-dev", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::308755113449:role/ecsTaskExecutionRole", "containerDefinitions": [{"name": "modernaction-api", "image": "308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-api-dev:latest-amd64", "portMappings": [{"containerPort": 8000, "protocol": "tcp"}], "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/modernaction-api-dev", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "ENV", "value": "production"}]}]}