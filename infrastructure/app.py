#!/usr/bin/env python3
import os
import aws_cdk as cdk
from modernaction.modernaction_stack import ModernActionStack

app = cdk.App()

# Get environment variables with defaults
account = os.getenv('CDK_DEFAULT_ACCOUNT', '************')
region = os.getenv('CDK_DEFAULT_REGION', 'us-east-1')
environment = os.getenv('ENVIRONMENT', 'dev')

# Create the main stack
ModernActionStack(
    app, 
    f"ModernAction-{environment}",
    env=cdk.Environment(account=account, region=region),
    env_name=environment,
    stack_name=f"modernaction-{environment}"
)

app.synth()