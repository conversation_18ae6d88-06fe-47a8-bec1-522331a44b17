{"containerDefinitions": [{"name": "WebContainer", "image": "308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:final-launch-candidate", "cpu": 0, "links": [], "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "INTERNAL_API_URL", "value": "http://api.staging.local:8000/api/v1"}, {"name": "NEXT_PUBLIC_API_URL", "value": "https://staging.modernaction.io/api/v1"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "modernaction-staging-WebTaskDefinitionWebContainerLogGroup9BD817D4-g0P3dC8LggmF", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "web"}, "secretOptions": []}, "systemControls": [], "credentialSpecs": []}], "family": "ModernActionstagingWebTaskDefinition6EB55C12", "taskRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskRole30FC0FBB-YQ5wIqK35bv0", "executionRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8J2CBwsOvvxh", "networkMode": "awsvpc", "volumes": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048"}