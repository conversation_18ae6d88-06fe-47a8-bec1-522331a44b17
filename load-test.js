import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
export let errorRate = new Rate('errors');

// Load test configuration
export let options = {
  stages: [
    { duration: '30s', target: 20 },   // Ramp up to 20 users
    { duration: '1m', target: 50 },    // Stay at 50 users
    { duration: '1m', target: 100 },   // Ramp up to 100 users
    { duration: '2m', target: 100 },   // Stay at 100 users
    { duration: '30s', target: 0 },    // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests should be below 2s
    http_req_failed: ['rate<0.1'],     // Error rate should be below 10%
    errors: ['rate<0.1'],              // Custom error rate should be below 10%
  },
};

const BASE_URL = 'https://staging.modernaction.io';

// Simulated user behavior patterns
const USER_SCENARIOS = {
  BROWSER: 0.7,        // 70% just browse
  API_HEALTH: 0.2,     // 20% check API health
  FULL_JOURNEY: 0.1,   // 10% attempt full action workflow
};

export default function () {
  const scenario = Math.random();
  
  if (scenario < USER_SCENARIOS.BROWSER) {
    // Scenario 1: User browses the website
    browseCampaigns();
  } else if (scenario < USER_SCENARIOS.BROWSER + USER_SCENARIOS.API_HEALTH) {
    // Scenario 2: Health check requests
    checkAPIHealth();
  } else {
    // Scenario 3: Full user journey (if web app was working)
    attemptFullJourney();
  }
  
  // Random think time between 1-5 seconds
  sleep(Math.random() * 4 + 1);
}

function browseCampaigns() {
  // Test main pages that should be available
  let responses = http.batch([
    ['GET', `${BASE_URL}/`],
    ['GET', `${BASE_URL}/campaigns`],
    ['GET', `${BASE_URL}/about`],
  ]);
  
  responses.forEach((response, index) => {
    const urls = ['/', '/campaigns', '/about'];
    check(response, {
      [`${urls[index]} status is 200`]: (r) => r.status === 200,
      [`${urls[index]} response time < 2s`]: (r) => r.timings.duration < 2000,
    }) || errorRate.add(1);
  });
}

function checkAPIHealth() {
  // Test API endpoints directly
  let healthResponse = http.get(`${BASE_URL}/api/v1/health`);
  
  check(healthResponse, {
    'API health status is 200': (r) => r.status === 200,
    'API health returns correct response': (r) => {
      try {
        const json = JSON.parse(r.body);
        return json.status === 'ok';
      } catch (e) {
        return false;
      }
    },
    'API health response time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);
  
  // Test other API endpoints that should be available
  let apiResponses = http.batch([
    ['GET', `${BASE_URL}/api/v1/campaigns`],
    ['GET', `${BASE_URL}/api/v1/officials`],
  ]);
  
  apiResponses.forEach((response, index) => {
    const endpoints = ['/api/v1/campaigns', '/api/v1/officials'];
    // These might return errors if not properly configured, but should not timeout
    check(response, {
      [`${endpoints[index]} responds within timeout`]: (r) => r.timings.duration < 10000,
      [`${endpoints[index]} not a connection error`]: (r) => r.status !== 0,
    }) || errorRate.add(1);
  });
}

function attemptFullJourney() {
  // Simulate a user trying to complete an action workflow
  // This tests the full stack integration
  
  // 1. Load homepage
  let homepage = http.get(`${BASE_URL}/`);
  check(homepage, {
    'Homepage loads': (r) => r.status === 200,
  }) || errorRate.add(1);
  
  sleep(1);
  
  // 2. Try to load campaigns page
  let campaignsPage = http.get(`${BASE_URL}/campaigns`);
  check(campaignsPage, {
    'Campaigns page accessible': (r) => r.status === 200,
  }) || errorRate.add(1);
  
  sleep(2);
  
  // 3. Try to access a specific campaign (if routing works)
  let campaignDetail = http.get(`${BASE_URL}/campaigns/test-campaign`);
  // This might 404, but should not timeout
  check(campaignDetail, {
    'Campaign detail responds': (r) => r.status !== 0 && r.timings.duration < 5000,
  }) || errorRate.add(1);
  
  sleep(1);
  
  // 4. Test API health as part of the journey
  let apiHealth = http.get(`${BASE_URL}/api/v1/health`);
  check(apiHealth, {
    'API accessible during journey': (r) => r.status === 200,
  }) || errorRate.add(1);
}

// Setup and teardown functions
export function setup() {
  console.log('Starting load test against staging environment');
  console.log(`Target URL: ${BASE_URL}`);
  
  // Verify the target is accessible before starting the test
  let response = http.get(`${BASE_URL}/api/v1/health`);
  if (response.status !== 200) {
    console.warn('Warning: API health check failed during setup');
  }
  
  return { startTime: Date.now() };
}

export function teardown(data) {
  let duration = (Date.now() - data.startTime) / 1000;
  console.log(`Load test completed in ${duration} seconds`);
}

// Helper function to generate realistic user delay
export function handleSummary(data) {
  return {
    'load-test-results.json': JSON.stringify(data, null, 2),
    'load-test-summary.txt': textSummary(data, { indent: ' ', enableColors: false }),
  };
}