#!/usr/bin/env python3
"""
Local data seeder that connects to staging database.
This allows us to test data seeding locally while using the same database as staging.
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add the API app to Python path
api_path = Path(__file__).parent / "apps" / "api"
sys.path.insert(0, str(api_path))

# Set environment to use our local .env file
os.environ.setdefault("ENV_FILE", ".env.local")

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

# Import models after setting up the path
from app.models.bill import Bill, BillStatus, BillType
from app.models.campaign import Campaign, CampaignStatus, CampaignType
from app.models.official import Official
from app.core.config import settings

def create_bills_data():
    """Create sample bills for seeding"""
    return [
        {
            "title": "Climate Action Now Act",
            "description": "A comprehensive bill to address climate change through renewable energy investments, emissions reductions, and green jobs creation.",
            "bill_number": "HR-1",
            "bill_type": BillType.HOUSE_BILL,
            "status": BillStatus.COMMITTEE,
            "session_year": 2024,
            "chamber": "house",
            "state": "federal",
            "summary": "This landmark climate legislation establishes a national framework for achieving net-zero emissions by 2050 through investments in clean energy, transportation electrification, and climate resilience infrastructure.",
            "ai_summary": "🌍 This critical climate bill would transform America's energy future by investing $500B in clean energy infrastructure, creating 2M green jobs, and putting the US on track to meet Paris Agreement goals. The legislation includes tax credits for renewable energy, funding for electric vehicle charging networks, and support for communities transitioning from fossil fuel economies.",
            "openstates_id": "hr-1-2024",
            "introduced_date": datetime(2024, 1, 15),
            "sponsor_name": "Rep. Alexandria Ocasio-Cortez",
            "sponsor_party": "Democratic",
            "sponsor_state": "NY",
            "is_featured": True,
            "priority_score": 95,
            "reasons_for_support": [
                "Creates millions of good-paying green jobs across America",
                "Reduces harmful air pollution that disproportionately affects communities of color",
                "Positions the US as a global leader in clean energy technology and exports",
                "Saves families money on energy bills through efficiency improvements",
                "Protects children's health and future from climate change impacts"
            ],
            "reasons_for_opposition": [
                "Concerns about federal spending and national debt",
                "Potential impacts on traditional energy sector workers",
                "Questions about government intervention in energy markets",
                "Regional differences in energy infrastructure needs"
            ],
            "tags": ["climate", "environment", "energy", "jobs", "infrastructure"],
            "categories": ["Environmental Policy", "Economic Development", "Infrastructure"]
        },
        {
            "title": "Affordable Housing Act",
            "description": "Legislation to address the national housing crisis through increased funding for affordable housing construction and rental assistance programs.",
            "bill_number": "S-147",
            "bill_type": BillType.SENATE_BILL,
            "status": BillStatus.FLOOR,
            "session_year": 2024,
            "chamber": "senate",
            "state": "federal",
            "summary": "The Affordable Housing Act provides $100 billion over 10 years to build and preserve affordable housing, expand rental assistance, and combat housing discrimination.",
            "ai_summary": "🏠 This vital housing legislation would help 2M families access affordable homes by funding construction of 1M new affordable units, expanding Section 8 vouchers, and providing down payment assistance for first-time homebuyers. The bill prioritizes developments near transit and includes strong labor standards.",
            "openstates_id": "s-147-2024",
            "introduced_date": datetime(2024, 2, 20),
            "sponsor_name": "Sen. Elizabeth Warren",
            "sponsor_party": "Democratic",
            "sponsor_state": "MA",
            "is_featured": True,
            "priority_score": 88,
            "reasons_for_support": [
                "Addresses homelessness and housing insecurity affecting millions",
                "Stimulates economic growth through construction jobs",
                "Helps teachers, nurses, and essential workers live where they work",
                "Reduces racial and economic segregation in housing",
                "Provides stable housing for families and children"
            ],
            "reasons_for_opposition": [
                "Concerns about federal housing program costs",
                "Local zoning and development concerns",
                "Questions about federal vs. local housing policy",
                "Potential market intervention effects"
            ],
            "tags": ["housing", "affordability", "development", "homelessness"],
            "categories": ["Housing Policy", "Economic Development", "Social Services"]
        },
        {
            "title": "Healthcare Price Transparency Act",
            "description": "Bipartisan legislation requiring hospitals and insurance companies to provide upfront pricing information to patients and consumers.",
            "bill_number": "HR-892",
            "bill_type": BillType.HOUSE_BILL,
            "status": BillStatus.PASSED,
            "session_year": 2024,
            "chamber": "house",
            "state": "federal",
            "summary": "This bipartisan bill mandates that healthcare providers and insurers disclose pricing information before services are rendered, helping patients make informed decisions and compare costs.",
            "ai_summary": "🏥 This transparency reform would end surprise medical bills and help families budget for healthcare by requiring hospitals to publish prices online and provide cost estimates before treatment. Studies show this could reduce healthcare costs by 15-30% through increased competition.",
            "openstates_id": "hr-892-2024",
            "introduced_date": datetime(2024, 3, 10),
            "sponsor_name": "Rep. Michael Burgess",
            "sponsor_party": "Republican",
            "sponsor_state": "TX",
            "is_featured": True,
            "priority_score": 82,
            "reasons_for_support": [
                "Protects patients from surprise medical bills",
                "Enables price shopping to reduce healthcare costs",
                "Increases market competition among providers",
                "Helps families budget for medical expenses",
                "Bipartisan solution to healthcare affordability"
            ],
            "reasons_for_opposition": [
                "Administrative burden on healthcare providers",
                "Concerns about proprietary pricing information",
                "Implementation costs for smaller hospitals",
                "Questions about pricing standardization"
            ],
            "tags": ["healthcare", "transparency", "pricing", "bipartisan"],
            "categories": ["Healthcare Policy", "Consumer Protection", "Government Reform"]
        }
    ]

def create_officials_data():
    """Create sample officials for seeding"""
    return [
        {
            "name": "Nancy Pelosi",
            "title": "Representative",
            "party": "Democratic",
            "email": "<EMAIL>",
            "level": "federal",
            "chamber": "house",
            "state": "CA",
            "district": "11",
            "bioguide_id": "P000197",
            "twitter_handle": "SpeakerPelosi",
            "is_active": True,
            "office_address": "1236 Longworth House Office Building",
            "office_city": "Washington",
            "office_state": "DC",
            "office_zip": "20515",
            "term_start": "2023",
            "term_end": "2025"
        },
        {
            "name": "Sean Casten",
            "title": "Representative",
            "party": "Democratic",
            "email": "<EMAIL>",
            "level": "federal",
            "chamber": "house",
            "state": "IL",
            "district": "6",
            "bioguide_id": "C001117",
            "twitter_handle": "RepCasten",
            "is_active": True,
            "office_address": "429 Cannon House Office Building",
            "office_city": "Washington",
            "office_state": "DC",
            "office_zip": "20515",
            "term_start": "2023",
            "term_end": "2025"
        },
        {
            "name": "Eddie Bernice Johnson",
            "title": "Representative", 
            "party": "Democratic",
            "email": "<EMAIL>",
            "level": "federal",
            "chamber": "house",
            "state": "TX",
            "district": "30",
            "bioguide_id": "J000126",
            "twitter_handle": "RepEBJ",
            "is_active": True,
            "office_address": "2468 Rayburn House Office Building",
            "office_city": "Washington",
            "office_state": "DC",
            "office_zip": "20515",
            "term_start": "2023",
            "term_end": "2025"
        },
        {
            "name": "Chuck Schumer",
            "title": "Senator",
            "party": "Democratic",
            "email": "<EMAIL>",
            "level": "federal",
            "chamber": "senate",
            "state": "NY",
            "bioguide_id": "S000148",
            "twitter_handle": "SenSchumer",
            "is_active": True,
            "office_address": "322 Hart Senate Office Building",
            "office_city": "Washington",
            "office_state": "DC",
            "office_zip": "20510",
            "term_start": "2023",
            "term_end": "2029"
        },
        {
            "name": "Elizabeth Warren",
            "title": "Senator",
            "party": "Democratic",
            "email": "<EMAIL>",
            "level": "federal",
            "chamber": "senate",
            "state": "MA",
            "bioguide_id": "W000817",
            "twitter_handle": "SenWarren",
            "is_active": True,
            "office_address": "309 Hart Senate Office Building",
            "office_city": "Washington",
            "office_state": "DC",
            "office_zip": "20510",
            "term_start": "2019",
            "term_end": "2025"
        }
    ]

def create_campaigns_data(bills_dict):
    """Create sample campaigns linked to bills"""
    return [
        {
            "title": "Support Climate Action Now - Secure Our Future",
            "description": "Join millions of Americans demanding bold climate action. The Climate Action Now Act represents our best chance to create good-paying green jobs, clean up our air and water, and leave a livable planet for our children. Your representatives need to hear from you TODAY.",
            "short_description": "Tell Congress to pass the Climate Action Now Act",
            "campaign_type": CampaignType.SUPPORT,
            "status": CampaignStatus.ACTIVE,
            "call_to_action": "Climate change affects every American community - from extreme weather to rising energy costs. Tell your representatives to vote YES on the Climate Action Now Act and invest in America's clean energy future.",
            "talking_points": [
                "This bill would create over 2 million good-paying jobs in clean energy, manufacturing, and infrastructure",
                "Investment in renewable energy will reduce electricity bills for families and businesses",
                "Clean air and water protections will improve public health, especially for children and vulnerable communities",
                "America can lead the world in clean energy exports and technology innovation",
                "Climate action is economic action - clean energy is now the cheapest source of power"
            ],
            "target_audience": "climate-conscious citizens",
            "geographic_scope": ["national"],
            "start_date": datetime(2024, 1, 20),
            "end_date": datetime(2024, 12, 31),
            "is_featured": True,
            "is_public": True,
            "goal_actions": 25000,
            "actual_actions": 8341,
            "social_media_message": "🌍 Climate action = economic action. Tell Congress to pass the #ClimateActionNowAct and invest in America's clean energy future! #CleanEnergyJobs #ClimateAction",
            "hashtags": ["#ClimateActionNowAct", "#CleanEnergyJobs", "#ClimateAction", "#GreenNewDeal"],
            "bill_id": bills_dict["HR-1"].id
        },
        {
            "title": "Make Housing Affordable for Working Families",
            "description": "Teachers, nurses, firefighters, and essential workers can't afford to live in the communities they serve. The Affordable Housing Act would build 1 million new affordable homes and help families achieve the American dream of homeownership.",
            "short_description": "Support the Affordable Housing Act to address the housing crisis",
            "campaign_type": CampaignType.SUPPORT,
            "status": CampaignStatus.ACTIVE,
            "call_to_action": "Housing costs are crushing working families across America. Tell your senators to pass the Affordable Housing Act and invest in homes working people can afford.",
            "talking_points": [
                "Teachers, nurses, and firefighters deserve to live in the communities they serve",
                "This bill would create 500,000 construction jobs while building affordable homes",
                "Stable housing helps children succeed in school and improves community health",
                "Every $1 invested in affordable housing generates $7 in economic activity",
                "Housing assistance helps families build wealth and achieve economic mobility"
            ],
            "target_audience": "working families and housing advocates",
            "geographic_scope": ["national"],
            "start_date": datetime(2024, 2, 25),
            "end_date": datetime(2024, 11, 30),
            "is_featured": True,
            "is_public": True,
            "goal_actions": 15000,
            "actual_actions": 4892,
            "social_media_message": "🏠 Every American deserves a safe, affordable home. Tell your senators to pass the #AffordableHousingAct! #HousingForAll #AffordableHousing",
            "hashtags": ["#AffordableHousingAct", "#HousingForAll", "#AffordableHousing", "#WorkingFamilies"],
            "bill_id": bills_dict["S-147"].id
        },
        {
            "title": "End Surprise Medical Bills - Support Healthcare Transparency",
            "description": "Medical bills are the leading cause of bankruptcy in America. The Healthcare Price Transparency Act has passed the House with bipartisan support and would end surprise billing while helping families plan for medical costs.",
            "short_description": "Urge the Senate to pass healthcare price transparency legislation",
            "campaign_type": CampaignType.SUPPORT,
            "status": CampaignStatus.ACTIVE,
            "call_to_action": "Surprise medical bills shouldn't bankrupt families. Tell your senators to pass the Healthcare Price Transparency Act and give patients the right to know costs upfront.",
            "talking_points": [
                "Medical bills are the #1 cause of personal bankruptcy in America",
                "Price transparency allows patients to shop for affordable care and save money",
                "This bipartisan bill has already passed the House with strong support",
                "Transparency increases competition and drives down healthcare costs",
                "Patients deserve to know what they'll pay before receiving medical care"
            ],
            "target_audience": "healthcare consumers and patients",
            "geographic_scope": ["national"],
            "start_date": datetime(2024, 3, 15),
            "end_date": datetime(2024, 9, 30),
            "is_featured": True,
            "is_public": True,
            "goal_actions": 12000,
            "actual_actions": 3621,
            "social_media_message": "💊 End surprise medical bills! Tell your senators to pass the #HealthcareTransparencyAct and give patients the right to know costs upfront. #EndSurpriseBills #HealthcareReform",
            "hashtags": ["#HealthcareTransparencyAct", "#EndSurpriseBills", "#HealthcareReform", "#PatientRights"],
            "bill_id": bills_dict["HR-892"].id
        }
    ]

def seed_database():
    """Main seeding function"""
    print("🌱 Starting local data seeding to staging database...")
    
    # Create database engine
    engine = create_engine(settings.database_url, echo=True)
    SessionLocal = sessionmaker(bind=engine)
    
    session = SessionLocal()
    
    try:
        # Test connection
        print("🔗 Testing database connection...")
        result = session.execute(text("SELECT version()"))
        version = result.fetchone()[0]
        print(f"✅ Connected to: {version}")
        
        # Check current data
        bill_count = session.query(Bill).count()
        official_count = session.query(Official).count()
        campaign_count = session.query(Campaign).count()
        
        print(f"📊 Current database state:")
        print(f"   Bills: {bill_count}")
        print(f"   Officials: {official_count}")
        print(f"   Campaigns: {campaign_count}")
        
        if bill_count > 0 or official_count > 0 or campaign_count > 0:
            print("⚠️  Database contains existing data. Adding more data...")
            # Continue automatically - we want to add bills and campaigns even if officials exist
        
        # Create bills
        print("\n📜 Creating bills...")
        bills_data = create_bills_data()
        created_bills = {}
        
        for bill_data in bills_data:
            try:
                # Check if bill already exists
                existing = session.query(Bill).filter_by(openstates_id=bill_data["openstates_id"]).first()
                if existing:
                    print(f"⚠️  Bill {bill_data['bill_number']} already exists, skipping")
                    created_bills[bill_data["bill_number"]] = existing
                    continue
                
                bill = Bill(**bill_data)
                session.add(bill)
                session.flush()
                created_bills[bill_data["bill_number"]] = bill
                print(f"✅ Created bill: {bill.bill_number} - {bill.title}")
            except Exception as e:
                print(f"❌ Error creating bill {bill_data['bill_number']}: {e}")
        
        # Create officials
        print("\n👥 Creating officials...")
        officials_data = create_officials_data()
        
        for official_data in officials_data:
            try:
                # Check if official already exists
                existing = session.query(Official).filter_by(bioguide_id=official_data["bioguide_id"]).first()
                if existing:
                    print(f"⚠️  Official {official_data['name']} already exists, skipping")
                    continue
                
                official = Official(**official_data)
                session.add(official)
                session.flush()
                print(f"✅ Created official: {official.name} ({official.state} {official.title})")
            except Exception as e:
                print(f"❌ Error creating official {official_data['name']}: {e}")
        
        # Create campaigns
        print("\n🏛️ Creating campaigns...")
        campaigns_data = create_campaigns_data(created_bills)
        
        for campaign_data in campaigns_data:
            try:
                # Check if campaign already exists
                existing = session.query(Campaign).filter_by(title=campaign_data["title"]).first()
                if existing:
                    print(f"⚠️  Campaign '{campaign_data['title']}' already exists, skipping")
                    continue
                
                campaign = Campaign(**campaign_data)
                session.add(campaign)
                session.flush()
                print(f"✅ Created campaign: {campaign.title}")
            except Exception as e:
                print(f"❌ Error creating campaign '{campaign_data['title']}': {e}")
        
        # Commit all changes
        session.commit()
        
        # Final count
        final_bill_count = session.query(Bill).count()
        final_official_count = session.query(Official).count()
        final_campaign_count = session.query(Campaign).count()
        
        print(f"\n✅ Data seeding completed successfully!")
        print(f"📊 Final database state:")
        print(f"   Bills: {final_bill_count} (+{final_bill_count - bill_count})")
        print(f"   Officials: {final_official_count} (+{final_official_count - official_count})")
        print(f"   Campaigns: {final_campaign_count} (+{final_campaign_count - campaign_count})")
        
        print("\n🌐 Check the results:")
        print("   • staging.modernaction.io - should now show campaigns")
        print("   • Local API at http://localhost:8000/api/v1/campaigns/")
        print("   • Local frontend at http://localhost:3000/campaigns")
        
    except Exception as e:
        session.rollback()
        print(f"❌ Error during seeding: {e}")
        raise
    finally:
        session.close()

if __name__ == "__main__":
    print("🚀 ModernAction.io Local Data Seeding")
    print("=" * 42)
    print("This will seed the staging database with sample civic engagement content")
    print("")
    
    seed_database()