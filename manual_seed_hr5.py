#!/usr/bin/env python3
"""
Manual seeding script for HR5 (Equality Act) - Sprint A validation
This script can be copied into the staging API container to test the data pipeline
"""

import os
import sys
import json
import requests
from datetime import datetime
import re
from typing import Dict, Any, Optional, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CongressGovAPI:
    """Congress.gov API integration service"""
    
    def __init__(self):
        self.api_key = "T1RKX9f8kEspd5nynWFRGqg18S4wd1aH3TFPh3HW"
        self.base_url = "https://api.congress.gov/v3"
        
    def parse_bill_number(self, bill_number: str) -> Dict[str, Any]:
        """Parse bill number into components"""
        # Remove spaces and convert to uppercase
        bill_number = bill_number.replace(" ", "").upper()
        
        # Handle different formats: HR5, H.R.5, S1234, S.1234
        if bill_number.startswith("H.R."):
            bill_type = "hr"
            number = int(bill_number[4:])
        elif bill_number.startswith("HR"):
            bill_type = "hr"
            number = int(bill_number[2:])
        elif bill_number.startswith("S."):
            bill_type = "s"
            number = int(bill_number[2:])
        elif bill_number.startswith("S"):
            bill_type = "s"
            number = int(bill_number[1:])
        else:
            raise ValueError(f"Invalid bill number format: {bill_number}")
            
        return {
            "bill_type": bill_type,
            "number": number,
            "congress": 118  # Current congress
        }
    
    def get_bill_metadata(self, bill_type: str, number: int, congress: int = 118) -> Dict[str, Any]:
        """Get bill metadata from Congress.gov API"""
        url = f"{self.base_url}/bill/{congress}/{bill_type}/{number}"
        params = {"api_key": self.api_key, "format": "json"}
        
        logger.info(f"Fetching bill metadata from: {url}")
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        bill_data = data.get("bill", {})
        
        return {
            "title": bill_data.get("title", ""),
            "short_title": bill_data.get("shortTitle", ""),
            "summary": bill_data.get("summary", {}).get("text", ""),
            "introduced_date": bill_data.get("introducedDate", ""),
            "congress": congress,
            "bill_type": bill_type,
            "number": number,
            "url": bill_data.get("url", "")
        }

class BillTextScraper:
    """Service to scrape bill text from congress.gov"""
    
    def scrape_bill_text(self, congress: int, bill_type: str, number: int) -> str:
        """Scrape full bill text from congress.gov"""
        # Construct the congress.gov URL for the bill text
        url = f"https://www.congress.gov/{congress}/bills/{bill_type}{number}/BILLS-{congress}{bill_type}{number}ih.htm"
        
        logger.info(f"Scraping bill text from: {url}")
        
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Simple text extraction - in production we'd use BeautifulSoup
            html_content = response.text
            
            # Extract text between <body> tags and clean up
            if "<body>" in html_content and "</body>" in html_content:
                start = html_content.find("<body>") + 6
                end = html_content.find("</body>")
                text_content = html_content[start:end]
                
                # Remove HTML tags (simple approach)
                import re
                text_content = re.sub(r'<[^>]+>', ' ', text_content)
                text_content = re.sub(r'\s+', ' ', text_content).strip()
                
                return text_content[:10000]  # Limit to first 10k characters
            else:
                return "Bill text not available in expected format"
                
        except Exception as e:
            logger.error(f"Error scraping bill text: {e}")
            return f"Error retrieving bill text: {str(e)}"

class AIService:
    """AI service for bill analysis (simplified version)"""
    
    def analyze_bill(self, title: str, text: str) -> Dict[str, Any]:
        """Generate AI analysis of the bill"""
        # For Sprint A validation, we'll create realistic mock data
        # In production, this would use the transformers library
        
        logger.info("Generating AI analysis for bill")
        
        # Generate realistic analysis based on HR5 (Equality Act)
        analysis = {
            "ai_summary": "The Equality Act would provide comprehensive federal civil rights protections for LGBTQ+ individuals by prohibiting discrimination in employment, housing, public accommodations, education, federally funded programs, credit, and jury service. The bill would amend existing civil rights laws to explicitly include sexual orientation and gender identity as protected characteristics.",
            "reasons_for_support": [
                "Provides comprehensive federal protections against LGBTQ+ discrimination",
                "Ensures equal access to employment, housing, and public accommodations",
                "Strengthens existing civil rights framework",
                "Promotes workplace safety and economic security for LGBTQ+ individuals",
                "Aligns federal law with majority of state anti-discrimination laws"
            ],
            "reasons_for_opposition": [
                "Concerns about religious freedom and exemptions",
                "Potential impact on sex-segregated facilities and sports",
                "Questions about enforcement mechanisms and scope",
                "Debate over federal vs. state jurisdiction on civil rights",
                "Implementation costs for businesses and organizations"
            ]
        }
        
        return analysis

def manual_seed_hr5():
    """Manually seed HR5 data for Sprint A validation"""
    logger.info("Starting manual seeding of HR5 (Equality Act)")
    
    try:
        # Initialize services
        congress_api = CongressGovAPI()
        text_scraper = BillTextScraper()
        ai_service = AIService()
        
        # Parse bill number
        bill_info = congress_api.parse_bill_number("HR5")
        logger.info(f"Parsed bill info: {bill_info}")
        
        # Get bill metadata
        metadata = congress_api.get_bill_metadata(
            bill_info["bill_type"], 
            bill_info["number"], 
            bill_info["congress"]
        )
        logger.info(f"Retrieved metadata: {metadata['title']}")
        
        # Scrape bill text
        bill_text = text_scraper.scrape_bill_text(
            bill_info["congress"],
            bill_info["bill_type"],
            bill_info["number"]
        )
        logger.info(f"Scraped bill text: {len(bill_text)} characters")
        
        # Generate AI analysis
        ai_analysis = ai_service.analyze_bill(metadata["title"], bill_text)
        logger.info("Generated AI analysis")
        
        # Combine all data
        complete_bill_data = {
            "bill_number": "HR5",
            "title": metadata["title"],
            "short_title": metadata.get("short_title", ""),
            "summary": metadata.get("summary", ""),
            "full_text": bill_text,
            "introduced_date": metadata.get("introduced_date", ""),
            "congress": bill_info["congress"],
            "session_year": 2023,  # 118th Congress
            "bill_type": bill_info["bill_type"],
            "number": bill_info["number"],
            "url": metadata.get("url", ""),
            **ai_analysis
        }
        
        # Print results for validation
        print("\n" + "="*80)
        print("SPRINT A DATA ENGINE VALIDATION RESULTS")
        print("="*80)
        print(f"Bill Number: {complete_bill_data['bill_number']}")
        print(f"Title: {complete_bill_data['title']}")
        print(f"AI Summary: {complete_bill_data['ai_summary'][:200]}...")
        print(f"Reasons for Support: {len(complete_bill_data['reasons_for_support'])} items")
        print(f"Reasons for Opposition: {len(complete_bill_data['reasons_for_opposition'])} items")
        print(f"Full Text Length: {len(complete_bill_data['full_text'])} characters")
        print("="*80)
        
        # Save to JSON file for inspection
        with open("/tmp/hr5_validation_data.json", "w") as f:
            json.dump(complete_bill_data, f, indent=2)
        
        logger.info("Manual seeding completed successfully!")
        logger.info("Validation data saved to /tmp/hr5_validation_data.json")
        
        return complete_bill_data
        
    except Exception as e:
        logger.error(f"Manual seeding failed: {e}")
        raise

if __name__ == "__main__":
    manual_seed_hr5()
