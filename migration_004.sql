-- Auth0 integration migration
-- Add Auth0 integration columns to users table
ALTER TABLE users ADD COLUMN auth0_user_id VARCHAR;
ALTER TABLE users ADD COLUMN name VA<PERSON>HAR;
ALTER TABLE users ADD COLUMN picture_url VARCHAR;
ALTER TABLE users ADD COLUMN email_verified BOOLEAN NOT NULL DEFAULT false;

-- Create index on auth0_user_id for fast lookups
CREATE UNIQUE INDEX ix_users_auth0_user_id ON users (auth0_user_id);

-- Make legacy fields nullable since we're using Auth0
ALTER TABLE users ALTER COLUMN hashed_password DROP NOT NULL;
ALTER TABLE users ALTER COLUMN first_name DROP NOT NULL;
ALTER TABLE users ALTER COLUMN last_name DROP NOT NULL;

-- Update alembic version table
INSERT INTO alembic_version (version_num) VALUES ('004_add_auth0_integration')
ON CONFLICT (version_num) DO NOTHING;
