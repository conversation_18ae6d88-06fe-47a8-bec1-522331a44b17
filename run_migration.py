#!/usr/bin/env python3
"""
Script to run database migration on staging environment
"""
import os
import subprocess
import sys

def run_migration():
    """Run the Alembic migration"""
    try:
        # Set the database URL for staging
        os.environ['DATABASE_URL'] = 'postgresql://postgres:<EMAIL>:5432/modernaction_staging'
        
        # Change to the API directory
        os.chdir('/Users/<USER>/modern-action-2.0/apps/api')
        
        # Run the migration
        result = subprocess.run(['poetry', 'run', 'alembic', 'upgrade', 'head'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Migration completed successfully!")
            print(result.stdout)
        else:
            print("❌ Migration failed!")
            print(result.stderr)
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error running migration: {e}")
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
