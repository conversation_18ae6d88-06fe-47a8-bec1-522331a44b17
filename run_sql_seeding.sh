#!/bin/bash

# Run SQL seeding script against staging database
# This script connects to the staging PostgreSQL database and runs the seeding SQL

echo "🔐 Retrieving database credentials from AWS Secrets Manager..."

# Get the database credentials from AWS Secrets Manager
DB_CREDENTIALS=$(aws secretsmanager get-secret-value \
    --secret-id "DatabaseCredentials8547B3E7-7zaDeXDnFrx0" \
    --query 'SecretString' \
    --output text)

if [ -z "$DB_CREDENTIALS" ]; then
    echo "❌ Failed to retrieve database credentials from AWS Secrets Manager"
    exit 1
fi

# Parse the credentials JSON
DB_HOST=$(echo $DB_CREDENTIALS | jq -r '.host')
DB_PORT=$(echo $DB_CREDENTIALS | jq -r '.port')
DB_NAME=$(echo $DB_CREDENTIALS | jq -r '.dbname')
DB_USER=$(echo $DB_CREDENTIALS | jq -r '.username')
DB_PASSWORD=$(echo $DB_CREDENTIALS | jq -r '.password')

echo "✅ Database credentials retrieved successfully"
echo "🗄️ Connecting to database: $DB_HOST:$DB_PORT/$DB_NAME"

# Set the PostgreSQL password environment variable
export PGPASSWORD="$DB_PASSWORD"

echo "🌱 Running SQL seeding script..."

# Run the SQL script
psql -h "$DB_HOST" -p "$DB_PORT" -d "$DB_NAME" -U "$DB_USER" -f staging_data.sql

if [ $? -eq 0 ]; then
    echo "✅ SQL seeding completed successfully!"
    echo "🌐 Visit https://staging.modernaction.io to see the populated content"
else
    echo "❌ SQL seeding failed"
    exit 1
fi

# Clean up
unset PGPASSWORD

echo "🎉 ModernAction.io staging database seeded with sample civic engagement content!"