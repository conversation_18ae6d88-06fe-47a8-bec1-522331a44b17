#!/bin/bash

# Deploy Auth0 Fix Script
# This script deploys the Auth0 authentication fix to the staging environment

set -e  # Exit on any error

# Configuration
ENVIRONMENT="staging"
ECR_REPO_URI="308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging"
CLUSTER_NAME="modernaction-staging"
SERVICE_NAME="modernaction-web-staging"
REGION="us-east-1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command_exists aws; then
        print_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS credentials not configured or invalid"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Build Docker image
build_image() {
    print_status "Building Docker image..."
    
    cd apps/web
    
    # Clean previous builds
    print_status "Cleaning previous builds..."
    rm -rf .next
    
    # Build the image
    docker build --no-cache --platform linux/amd64 -t modernaction-web-staging .
    
    if [ $? -eq 0 ]; then
        print_success "Docker image built successfully"
    else
        print_error "Docker build failed"
        exit 1
    fi
    
    cd ../..
}

# Push to ECR
push_to_ecr() {
    print_status "Pushing image to ECR..."
    
    # Login to ECR
    aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ECR_REPO_URI
    
    # Tag the image
    IMAGE_TAG="auth0-fix-$(date +%Y%m%d-%H%M%S)"
    docker tag modernaction-web-staging:latest $ECR_REPO_URI:$IMAGE_TAG
    docker tag modernaction-web-staging:latest $ECR_REPO_URI:latest
    
    # Push the image
    docker push $ECR_REPO_URI:$IMAGE_TAG
    docker push $ECR_REPO_URI:latest
    
    print_success "Image pushed to ECR with tag: $IMAGE_TAG"
}

# Deploy to ECS
deploy_to_ecs() {
    print_status "Deploying to ECS..."
    
    # Force new deployment
    aws ecs update-service \
        --cluster $CLUSTER_NAME \
        --service $SERVICE_NAME \
        --force-new-deployment \
        --region $REGION
    
    print_success "ECS deployment initiated"
}

# Wait for deployment
wait_for_deployment() {
    print_status "Waiting for deployment to complete..."
    
    aws ecs wait services-stable \
        --cluster $CLUSTER_NAME \
        --services $SERVICE_NAME \
        --region $REGION
    
    if [ $? -eq 0 ]; then
        print_success "Deployment completed successfully"
    else
        print_error "Deployment failed or timed out"
        exit 1
    fi
}

# Test the deployment
test_deployment() {
    print_status "Testing deployment..."
    
    # Get ALB URL
    ALB_URL="http://modern-ApiSe-5h6OCm8WlBhL-318015605.us-east-1.elb.amazonaws.com"
    
    # Test health endpoint
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $ALB_URL/campaigns)
    
    if [ "$HTTP_STATUS" = "200" ]; then
        print_success "Application is responding correctly"
        print_status "You can now test Auth0 login at: $ALB_URL"
    else
        print_warning "Application returned HTTP $HTTP_STATUS - may need more time to start"
    fi
}

# Fix campaign action counts
fix_action_counts() {
    print_status "Fixing campaign action counts..."
    
    ALB_URL="http://modern-ApiSe-5h6OCm8WlBhL-318015605.us-east-1.elb.amazonaws.com"
    
    # Call the admin API to recalculate action counts
    RESPONSE=$(curl -s -X POST $ALB_URL/api/v1/campaigns/admin/recalculate-action-counts)
    
    if [ $? -eq 0 ]; then
        print_success "Action counts recalculated"
        echo "Response: $RESPONSE"
    else
        print_warning "Failed to recalculate action counts - you may need to do this manually"
    fi
}

# Main deployment function
main() {
    echo "=========================================="
    echo "ModernAction.io Auth0 Fix Deployment"
    echo "=========================================="
    echo ""
    
    check_prerequisites
    echo ""
    
    build_image
    echo ""
    
    push_to_ecr
    echo ""
    
    deploy_to_ecs
    echo ""
    
    wait_for_deployment
    echo ""
    
    test_deployment
    echo ""
    
    fix_action_counts
    echo ""
    
    print_success "Deployment completed!"
    echo ""
    echo "Next steps:"
    echo "1. Complete Auth0 setup following docs/AUTH0_SETUP_GUIDE.md"
    echo "2. Update AWS Secrets Manager with Auth0 credentials"
    echo "3. Test the complete authentication flow"
    echo "4. Verify campaign action counts are displaying correctly"
}

# Run main function
main
