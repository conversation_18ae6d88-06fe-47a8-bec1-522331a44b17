#!/bin/bash

# Final Launch Validation Script for ModernAction.io
# This script validates all critical systems before launch

set -e

# Configuration
ALB_URL="http://modern-ApiSe-5h6OCm8WlBhL-318015605.us-east-1.elb.amazonaws.com"
STAGING_URL="https://staging.modernaction.io"
API_URL="$ALB_URL/api/v1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test result tracking
FAILED_TEST_NAMES=()

print_header() {
    echo ""
    echo "=========================================="
    echo "$1"
    echo "=========================================="
}

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    PASSED_TESTS=$((PASSED_TESTS + 1))
}

print_failure() {
    echo -e "${RED}[FAIL]${NC} $1"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_NAMES+=("$1")
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test API Health
test_api_health() {
    print_test "API Health Check"
    
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/health")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        print_success "API is responding (HTTP 200)"
    else
        print_failure "API health check failed (HTTP $HTTP_STATUS)"
    fi
}

# Test Web Application
test_web_application() {
    print_test "Web Application Health"
    
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$ALB_URL/campaigns")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        print_success "Web application is responding (HTTP 200)"
    else
        print_failure "Web application failed (HTTP $HTTP_STATUS)"
    fi
}

# Test Campaign Data
test_campaign_data() {
    print_test "Campaign Data Integrity"
    
    RESPONSE=$(curl -s "$API_URL/campaigns")
    
    if echo "$RESPONSE" | jq -e '. | length > 0' >/dev/null 2>&1; then
        print_success "Campaigns are being returned"
        
        # Check if campaigns have non-zero action counts
        ZERO_ACTION_COUNT=$(echo "$RESPONSE" | jq '[.[] | select(.actual_actions == 0)] | length')
        TOTAL_CAMPAIGNS=$(echo "$RESPONSE" | jq 'length')
        
        if [ "$ZERO_ACTION_COUNT" -eq "$TOTAL_CAMPAIGNS" ]; then
            print_failure "All campaigns show 0 actions - data inconsistency issue"
        else
            print_success "Some campaigns show action counts > 0"
        fi
    else
        print_failure "No campaigns returned or invalid JSON response"
    fi
}

# Test Auth0 Configuration
test_auth0_config() {
    print_test "Auth0 Configuration"
    
    # Check if the login page loads without "Auth0 configuration error"
    RESPONSE=$(curl -s "$ALB_URL/campaigns")
    
    if echo "$RESPONSE" | grep -q "Auth0 configuration error"; then
        print_failure "Auth0 configuration error detected on page"
    else
        print_success "No Auth0 configuration errors detected"
    fi
}

# Test Database Connectivity
test_database_connectivity() {
    print_test "Database Connectivity"
    
    # Test through API endpoint that requires DB
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/campaigns")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        print_success "Database connectivity working (campaigns endpoint responds)"
    else
        print_failure "Database connectivity issue (campaigns endpoint failed)"
    fi
}

# Test ECS Services
test_ecs_services() {
    print_test "ECS Services Status"
    
    # Check API service
    API_SERVICE_STATUS=$(aws ecs describe-services \
        --cluster modernaction-staging \
        --services modernaction-api-staging \
        --query 'services[0].status' \
        --output text 2>/dev/null || echo "ERROR")
    
    if [ "$API_SERVICE_STATUS" = "ACTIVE" ]; then
        print_success "API ECS service is ACTIVE"
    else
        print_failure "API ECS service status: $API_SERVICE_STATUS"
    fi
    
    # Check Web service
    WEB_SERVICE_STATUS=$(aws ecs describe-services \
        --cluster modernaction-staging \
        --services modernaction-web-staging \
        --query 'services[0].status' \
        --output text 2>/dev/null || echo "ERROR")
    
    if [ "$WEB_SERVICE_STATUS" = "ACTIVE" ]; then
        print_success "Web ECS service is ACTIVE"
    else
        print_failure "Web ECS service status: $WEB_SERVICE_STATUS"
    fi
}

# Test Load Balancer Health
test_load_balancer_health() {
    print_test "Load Balancer Target Health"
    
    # This is a simplified check - in production you'd check target group health
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$ALB_URL")
    
    if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "302" ]; then
        print_success "Load balancer is routing traffic"
    else
        print_failure "Load balancer health issue (HTTP $HTTP_STATUS)"
    fi
}

# Test Action Count Fix
test_action_count_fix() {
    print_test "Action Count Recalculation"
    
    RESPONSE=$(curl -s -X POST "$API_URL/campaigns/admin/recalculate-action-counts")
    
    if echo "$RESPONSE" | jq -e '.updated_campaigns' >/dev/null 2>&1; then
        UPDATED_COUNT=$(echo "$RESPONSE" | jq -r '.updated_campaigns')
        print_success "Action count recalculation completed ($UPDATED_COUNT campaigns updated)"
    else
        print_failure "Action count recalculation failed"
    fi
}

# Test Package Installation (for CI/CD)
test_package_installation() {
    print_test "Package Installation Check"
    
    # Check if the correct Auth0 package is installed
    if [ -f "apps/web/package.json" ]; then
        if grep -q "@auth0/nextjs-auth0" apps/web/package.json; then
            print_success "Correct Auth0 package (@auth0/nextjs-auth0) is installed"
        else
            print_failure "Wrong Auth0 package - should be @auth0/nextjs-auth0"
        fi
    else
        print_warning "Cannot check package.json - file not found"
    fi
}

# Test Environment Variables
test_environment_variables() {
    print_test "Environment Variables Configuration"
    
    # Check if .env.example has Auth0 variables
    if [ -f "apps/web/.env.example" ]; then
        if grep -q "AUTH0_" apps/web/.env.example; then
            print_success "Auth0 environment variables documented in .env.example"
        else
            print_failure "Auth0 environment variables missing from .env.example"
        fi
    else
        print_failure ".env.example file not found"
    fi
}

# Generate Summary Report
generate_summary() {
    print_header "VALIDATION SUMMARY"
    
    echo "Total Tests: $TOTAL_TESTS"
    echo "Passed: $PASSED_TESTS"
    echo "Failed: $FAILED_TESTS"
    echo ""
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED! The application is ready for launch.${NC}"
        echo ""
        echo "Next steps:"
        echo "1. Complete Auth0 setup following docs/AUTH0_SETUP_GUIDE.md"
        echo "2. Set up CI/CD pipeline following docs/CICD_SETUP_GUIDE.md"
        echo "3. Perform user acceptance testing"
        echo "4. Plan production deployment"
    else
        echo -e "${RED}❌ $FAILED_TESTS TESTS FAILED. Address these issues before launch:${NC}"
        echo ""
        for test_name in "${FAILED_TEST_NAMES[@]}"; do
            echo "- $test_name"
        done
        echo ""
        echo "Refer to the troubleshooting guides in the docs/ directory."
    fi
}

# Main execution
main() {
    print_header "ModernAction.io Final Launch Validation"
    echo "Testing against: $ALB_URL"
    echo ""
    
    # Core Infrastructure Tests
    print_header "Infrastructure Tests"
    test_ecs_services
    test_load_balancer_health
    
    # Application Health Tests
    print_header "Application Health Tests"
    test_api_health
    test_web_application
    test_database_connectivity
    
    # Data Integrity Tests
    print_header "Data Integrity Tests"
    test_campaign_data
    test_action_count_fix
    
    # Security Tests
    print_header "Security Tests"
    test_auth0_config
    
    # Configuration Tests
    print_header "Configuration Tests"
    test_package_installation
    test_environment_variables
    
    # Generate final report
    generate_summary
    
    # Exit with appropriate code
    if [ $FAILED_TESTS -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Check prerequisites
if ! command -v curl >/dev/null 2>&1; then
    echo "Error: curl is required but not installed"
    exit 1
fi

if ! command -v jq >/dev/null 2>&1; then
    echo "Error: jq is required but not installed"
    exit 1
fi

if ! command -v aws >/dev/null 2>&1; then
    echo "Warning: AWS CLI not found - some tests will be skipped"
fi

# Run main function
main
