#!/bin/bash

# Auth0 Setup Script for ModernAction.io
# This script uses the Auth0 CLI to create the application and API automatically

set -e

# Configuration
ENVIRONMENT="staging"
APP_NAME="ModernAction.io"
API_NAME="ModernAction.io API"
API_IDENTIFIER="https://api.modernaction.io"

# URLs for different environments
STAGING_URL="https://staging.modernaction.io"
PROD_URL="https://modernaction.io"
LOCAL_URL="http://localhost:3000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Ensure Auth0 CLI is available
check_auth0_cli() {
    if ! command -v auth0 >/dev/null 2>&1; then
        print_error "Auth0 CLI not found. Please install it first."
        exit 1
    fi
    
    print_success "Auth0 CLI found: $(auth0 --version)"
}

# Login to Auth0
auth0_login() {
    print_status "Logging into Auth0..."
    
    # Check if already logged in
    if auth0 tenants list >/dev/null 2>&1; then
        print_success "Already logged into Auth0"
        return
    fi
    
    print_status "Please log in to Auth0 when prompted..."
    auth0 login
    
    if [ $? -eq 0 ]; then
        print_success "Successfully logged into Auth0"
    else
        print_error "Failed to log into Auth0"
        exit 1
    fi
}

# Create Auth0 API
create_auth0_api() {
    print_status "Creating Auth0 API..."
    
    # Check if API already exists
    if auth0 apis list --json | jq -e ".[] | select(.identifier == \"$API_IDENTIFIER\")" >/dev/null 2>&1; then
        print_warning "API already exists with identifier: $API_IDENTIFIER"
        API_ID=$(auth0 apis list --json | jq -r ".[] | select(.identifier == \"$API_IDENTIFIER\") | .id")
        print_status "Using existing API ID: $API_ID"
        return
    fi
    
    # Create the API
    auth0 apis create \
        --name "$API_NAME" \
        --identifier "$API_IDENTIFIER" \
        --signing-alg "RS256" \
        --json > /tmp/auth0_api.json
    
    if [ $? -eq 0 ]; then
        API_ID=$(jq -r '.id' /tmp/auth0_api.json)
        print_success "Created Auth0 API with ID: $API_ID"
    else
        print_error "Failed to create Auth0 API"
        exit 1
    fi
}

# Create Auth0 Application
create_auth0_app() {
    print_status "Creating Auth0 Application..."
    
    # Check if app already exists
    if auth0 apps list --json | jq -e ".[] | select(.name == \"$APP_NAME\")" >/dev/null 2>&1; then
        print_warning "Application already exists with name: $APP_NAME"
        APP_ID=$(auth0 apps list --json | jq -r ".[] | select(.name == \"$APP_NAME\") | .client_id")
        print_status "Using existing Application Client ID: $APP_ID"
        
        # Get the existing app details
        auth0 apps show "$APP_ID" --json > /tmp/auth0_app.json
        return
    fi
    
    # Create the application
    auth0 apps create \
        --name "$APP_NAME" \
        --type "regular" \
        --callbacks "$LOCAL_URL/api/auth/callback,$STAGING_URL/api/auth/callback,$PROD_URL/api/auth/callback" \
        --logout-urls "$LOCAL_URL,$STAGING_URL,$PROD_URL" \
        --origins "$LOCAL_URL,$STAGING_URL,$PROD_URL" \
        --web-origins "$LOCAL_URL,$STAGING_URL,$PROD_URL" \
        --json > /tmp/auth0_app.json
    
    if [ $? -eq 0 ]; then
        APP_ID=$(jq -r '.client_id' /tmp/auth0_app.json)
        print_success "Created Auth0 Application with Client ID: $APP_ID"
    else
        print_error "Failed to create Auth0 Application"
        exit 1
    fi
}

# Generate Auth0 secret
generate_auth0_secret() {
    print_status "Generating AUTH0_SECRET..."
    
    # Generate a 32-character random string
    AUTH0_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    
    if [ ${#AUTH0_SECRET} -eq 32 ]; then
        print_success "Generated AUTH0_SECRET: ${AUTH0_SECRET:0:8}..."
    else
        print_error "Failed to generate AUTH0_SECRET"
        exit 1
    fi
}

# Extract configuration values
extract_config() {
    print_status "Extracting configuration values..."
    
    # Get tenant domain
    TENANT_DOMAIN=$(auth0 tenants list --json | jq -r '.[0].domain')
    
    # Get app details
    CLIENT_ID=$(jq -r '.client_id' /tmp/auth0_app.json)
    CLIENT_SECRET=$(jq -r '.client_secret' /tmp/auth0_app.json)
    
    print_success "Configuration extracted:"
    echo "  Domain: $TENANT_DOMAIN"
    echo "  Client ID: $CLIENT_ID"
    echo "  Client Secret: ${CLIENT_SECRET:0:8}..."
    echo "  API Identifier: $API_IDENTIFIER"
    echo "  AUTH0_SECRET: ${AUTH0_SECRET:0:8}..."
}

# Update AWS Secrets Manager
update_aws_secrets() {
    print_status "Updating AWS Secrets Manager..."
    
    # Create the secrets JSON
    SECRETS_JSON=$(jq -n \
        --arg auth0_secret "$AUTH0_SECRET" \
        --arg client_secret "$CLIENT_SECRET" \
        '{
            "AUTH0_SECRET": $auth0_secret,
            "AUTH0_CLIENT_SECRET": $client_secret
        }')
    
    # Update the secret
    aws secretsmanager update-secret \
        --secret-id "ModernActionstagingAuth0Secret" \
        --secret-string "$SECRETS_JSON" \
        --region us-east-1
    
    if [ $? -eq 0 ]; then
        print_success "Updated AWS Secrets Manager"
    else
        print_error "Failed to update AWS Secrets Manager"
        exit 1
    fi
}

# Update CDK configuration
update_cdk_config() {
    print_status "Updating CDK configuration..."
    
    # Update the CDK stack with the correct values
    sed -i.bak \
        -e "s|https://modernaction.auth0.com|https://$TENANT_DOMAIN|g" \
        -e "s|YOUR_AUTH0_CLIENT_ID|$CLIENT_ID|g" \
        infrastructure/modernaction/modernaction_stack.py
    
    if [ $? -eq 0 ]; then
        print_success "Updated CDK configuration"
        print_status "Backup saved as modernaction_stack.py.bak"
    else
        print_error "Failed to update CDK configuration"
        exit 1
    fi
}

# Generate environment file for local development
generate_env_file() {
    print_status "Generating local environment file..."
    
    cat > apps/web/.env.local << EOF
# Auth0 Configuration - Generated by setup-auth0.sh
AUTH0_SECRET=$AUTH0_SECRET
AUTH0_BASE_URL=http://localhost:3000
AUTH0_ISSUER_BASE_URL=https://$TENANT_DOMAIN
AUTH0_CLIENT_ID=$CLIENT_ID
AUTH0_CLIENT_SECRET=$CLIENT_SECRET
AUTH0_AUDIENCE=$API_IDENTIFIER

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
INTERNAL_API_URL=http://localhost:8000/api/v1

# Environment
NODE_ENV=development
EOF
    
    print_success "Created apps/web/.env.local for local development"
}

# Main execution
main() {
    echo "=========================================="
    echo "Auth0 Setup for ModernAction.io"
    echo "=========================================="
    echo ""
    
    check_auth0_cli
    auth0_login
    echo ""
    
    create_auth0_api
    echo ""
    
    create_auth0_app
    echo ""
    
    generate_auth0_secret
    echo ""
    
    extract_config
    echo ""
    
    update_aws_secrets
    echo ""
    
    update_cdk_config
    echo ""
    
    generate_env_file
    echo ""
    
    print_success "Auth0 setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Deploy the updated CDK stack: cd infrastructure && cdk deploy"
    echo "2. Deploy the application with the new configuration"
    echo "3. Test the authentication flow"
    echo ""
    echo "Configuration summary:"
    echo "  Auth0 Domain: $TENANT_DOMAIN"
    echo "  Client ID: $CLIENT_ID"
    echo "  API Identifier: $API_IDENTIFIER"
    echo "  AWS Secrets: Updated"
    echo "  CDK Config: Updated"
    echo "  Local .env: Created"
}

# Run main function
main
