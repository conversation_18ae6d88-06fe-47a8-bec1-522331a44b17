#!/usr/bin/env python3
"""
Seed officials data for testing core functionality
"""

import requests
import json

def seed_officials():
    """Seed sample officials data via API"""
    
    base_url = "https://staging.modernaction.io/api/v1"
    
    # Sample officials data for testing
    officials_data = [
        {
            "name": "<PERSON>",
            "title": "Senator",
            "party": "Democratic",
            "email": "<EMAIL>",
            "phone": "(*************",
            "website": "https://www.schumer.senate.gov",
            "level": "federal",
            "chamber": "senate",
            "state": "NY",
            "district": None,
            "bioguide_id": "S000148",
            "is_active": True
        },
        {
            "name": "<PERSON><PERSON>",
            "title": "Senator",
            "party": "Democratic", 
            "email": "<EMAIL>",
            "phone": "(*************",
            "website": "https://www.gillibrand.senate.gov",
            "level": "federal",
            "chamber": "senate",
            "state": "NY",
            "district": None,
            "bioguide_id": "G000555",
            "is_active": True
        },
        {
            "name": "<PERSON>",
            "title": "Representative",
            "party": "Democratic",
            "email": "<EMAIL>", 
            "phone": "(*************",
            "website": "https://nadler.house.gov",
            "level": "federal",
            "chamber": "house",
            "state": "NY",
            "district": "12",
            "bioguide_id": "N000002",
            "is_active": True
        },
        {
            "name": "Alexandria Ocasio-Cortez",
            "title": "Representative",
            "party": "Democratic",
            "email": "<EMAIL>",
            "phone": "(*************", 
            "website": "https://ocasio-cortez.house.gov",
            "level": "federal",
            "chamber": "house",
            "state": "NY",
            "district": "14",
            "bioguide_id": "O000172",
            "is_active": True
        },
        {
            "name": "Kathy Hochul",
            "title": "Governor",
            "party": "Democratic",
            "email": "<EMAIL>",
            "phone": "(*************",
            "website": "https://www.governor.ny.gov",
            "level": "state",
            "chamber": "executive",
            "state": "NY",
            "district": None,
            "is_active": True
        }
    ]
    
    print("🏛️  Seeding Officials Data")
    print("=========================")
    
    created_count = 0
    
    for official_data in officials_data:
        try:
            print(f"Creating: {official_data['name']} ({official_data['title']})")
            
            response = requests.post(
                f"{base_url}/officials/",
                json=official_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                print(f"✅ Created: {official_data['name']}")
                created_count += 1
            elif response.status_code == 400 and "already exists" in response.text:
                print(f"⚠️  Already exists: {official_data['name']}")
            else:
                print(f"❌ Failed to create {official_data['name']}: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Error creating {official_data['name']}: {e}")
    
    print(f"\n📊 Summary: Created {created_count} new officials")
    
    # Verify the seeding worked
    try:
        print("\n🔍 Verifying officials data...")
        
        # Test general officials endpoint
        response = requests.get(f"{base_url}/officials/")
        if response.status_code == 200:
            officials = response.json()
            print(f"✅ Total officials in database: {len(officials)}")
        
        # Test ZIP code lookup
        response = requests.get(f"{base_url}/officials/by-zip/10001")
        if response.status_code == 200:
            ny_officials = response.json()
            print(f"✅ Officials found for ZIP 10001: {len(ny_officials)}")
            
            for official in ny_officials:
                print(f"  - {official['name']} ({official['title']})")
        else:
            print(f"❌ ZIP lookup failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")

if __name__ == "__main__":
    seed_officials()
