#\!/usr/bin/env python3
"""
Simple API-based seeding approach for ModernAction.io staging.
"""

import json
import requests

API_BASE = "https://api-dev.modernaction.io/api/v1"

def seed_data():
    print("🌱 Starting API-based data seeding...")
    
    # Test API connectivity first
    try:
        response = requests.get(f"{API_BASE}/bills/", timeout=10)
        print(f"✅ API connectivity test: {response.status_code}")
        
        if response.status_code == 200:
            bills = response.json()
            print(f"📊 Current database has {len(bills)} bills")
            
            if len(bills) > 0:
                print("✅ Database already contains data:")
                for bill in bills[:3]:  # Show first 3 bills
                    print(f"  • {bill.get('bill_number', 'N/A')}: {bill.get('title', 'N/A')}")
                print("🌐 Visit https://staging.modernaction.io to see the content")
                return
            else:
                print("📋 Database is empty - would need to implement data creation endpoints")
        
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        print("🔧 May need to check API deployment status")

if __name__ == "__main__":
    print("🚀 ModernAction.io API-Based Data Check")
    print("=" * 42)
    seed_data()
EOF < /dev/null