#!/bin/bash

# Setup local development environment to connect to staging database
# This allows us to test data seeding locally using the same database as staging

echo "🔧 Setting up local development environment with staging database..."

# Get AWS secrets and set environment variables
echo "🔐 Retrieving database credentials from AWS Secrets Manager..."

# Get database credentials
DB_CREDENTIALS=$(aws secretsmanager get-secret-value \
    --secret-id "DatabaseCredentials8547B3E7-7zaDeXDnFrx0" \
    --query 'SecretString' \
    --output text)

if [ -z "$DB_CREDENTIALS" ]; then
    echo "❌ Failed to retrieve database credentials"
    exit 1
fi

DB_PASSWORD=$(echo $DB_CREDENTIALS | jq -r '.password')

# Get API keys
echo "🔑 Retrieving API keys from AWS Secrets Manager..."
API_SECRETS=$(aws secretsmanager get-secret-value \
    --secret-id "ApiKeysSecret5A58C5CD-crnudnHX1i32" \
    --query 'SecretString' \
    --output text)

if [ -z "$API_SECRETS" ]; then
    echo "⚠️  Warning: Could not retrieve API keys, continuing without them"
    API_SECRETS="{}"
fi

# Get Auth0 config
echo "🔐 Retrieving Auth0 configuration..."
AUTH0_SECRETS=$(aws secretsmanager get-secret-value \
    --secret-id "AppConfigSecret251CAC1E-E3j7gpyGiRr3" \
    --query 'SecretString' \
    --output text 2>/dev/null || echo "{}")

# Create local .env file with all credentials
echo "📝 Creating .env file for local development..."

cat > .env.local << EOF
# Local development environment connected to staging database
# Generated on $(date)

# Environment
ENVIRONMENT=development
DEBUG=true

# Database - Staging database connection
DB_HOST=modernaction-staging-modernactiondatabasefdd241de-giohrnseld4l.csdoaiogadw5.us-east-1.rds.amazonaws.com
DB_PORT=5432
DB_NAME=modernaction
DB_USERNAME=modernaction_admin
DB_PASSWORD=$DB_PASSWORD

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=ModernAction API - Local Dev
VERSION=1.0.0

# Security (temporary local keys)
SECRET_KEY=local-dev-secret-key-$(date +%s)
JWT_SECRET=local-dev-jwt-secret-$(date +%s)
JWT_ALGORITHM=HS256
JWT_EXPIRATION_MINUTES=60

# Auth0
AUTH0_DOMAIN=$(echo $AUTH0_SECRETS | jq -r '.AUTH0_DOMAIN // "dev-modernaction.us.auth0.com"')
AUTH0_AUDIENCE=$(echo $AUTH0_SECRETS | jq -r '.AUTH0_AUDIENCE // "https://api.modernaction.io"')

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080

# External APIs
OPENSTATES_API_KEY=$(echo $API_SECRETS | jq -r '.OPENSTATES_API_KEY // ""')
GOOGLE_CIVIC_INFO_API_KEY=$(echo $API_SECRETS | jq -r '.GOOGLE_CIVIC_INFO_API_KEY // ""')
PROPUBLICA_CONGRESS_API_KEY=$(echo $API_SECRETS | jq -r '.PROPUBLICA_CONGRESS_API_KEY // ""')
CONGRESS_GOV_API_KEY=$(echo $API_SECRETS | jq -r '.CONGRESS_GOV_API_KEY // ""')
HUGGING_FACE_API_KEY=$(echo $API_SECRETS | jq -r '.HUGGING_FACE_API_KEY // ""')

# AWS
AWS_REGION=us-east-1

# SES
AWS_SES_FROM_EMAIL=<EMAIL>
AWS_SES_FROM_NAME=ModernAction Local Dev

# Redis - local for development
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=DEBUG

# Rate Limiting - disabled for local dev
RATE_LIMIT_ENABLED=false
RATE_LIMIT_REQUESTS_PER_MINUTE=1000

# Models
MODEL_CACHE_SIZE=1
SUMMARIZATION_MODEL=t5-small
TEXT_GENERATION_MODEL=t5-small
EOF

echo "✅ Local environment configured successfully!"
echo "🗄️  Database: Connected to staging PostgreSQL"
echo "🔑 API Keys: $(echo $API_SECRETS | jq -r 'keys | length') keys loaded"
echo "🔐 Auth0: Configured for local development"

echo ""
echo "🚀 Next steps:"
echo "1. Start Redis locally: redis-server"
echo "2. Install API dependencies: cd apps/api && poetry install"
echo "3. Run database migrations: cd apps/api && poetry run alembic upgrade head"
echo "4. Seed the database: python local_data_seeder.py"
echo "5. Start API server: cd apps/api && poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
echo "6. Start frontend: cd apps/web && npm run dev"
echo ""
echo "🌐 Frontend will be available at: http://localhost:3000"
echo "📡 API will be available at: http://localhost:8000"
echo "🗄️  Both will use the staging database for testing"