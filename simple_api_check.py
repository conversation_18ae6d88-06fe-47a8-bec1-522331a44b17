#!/usr/bin/env python3
"""
Simple API check for ModernAction.io staging.
"""

import requests

API_BASE = "https://api-dev.modernaction.io/api/v1"

def check_api():
    print("🌱 Checking API and current data...")
    
    # Test API connectivity
    try:
        response = requests.get(f"{API_BASE}/bills/", timeout=10)
        print(f"✅ API connectivity: {response.status_code}")
        
        if response.status_code == 200:
            bills = response.json()
            print(f"📊 Current database has {len(bills)} bills")
            
            if len(bills) > 0:
                print("✅ Database already contains data:")
                for bill in bills[:3]:  # Show first 3 bills
                    print(f"  • {bill.get('bill_number', 'N/A')}: {bill.get('title', 'N/A')}")
                print("🌐 Visit https://staging.modernaction.io to see the content")
                return True
            else:
                print("📋 Database is empty - would need data seeding")
                return False
        else:
            print(f"❌ API returned status {response.status_code}: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

if __name__ == "__main__":
    print("🚀 ModernAction.io API Status Check")
    print("=" * 38)
    check_api()