#!/usr/bin/env python3
"""
Simple migration script to add Auth0 fields to users table
"""
import psycopg2
import os

def apply_migration():
    """Apply the Auth0 migration to staging database"""
    
    # Database connection string for staging
    db_url = "postgresql://postgres:<EMAIL>:5432/modernaction_staging"
    
    try:
        # Connect to database
        conn = psycopg2.connect(db_url)
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("Connected to staging database")
        
        # Check if migration already applied
        cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'auth0_user_id'")
        if cursor.fetchone():
            print("Auth0 migration already applied")
            return True
        
        print("Applying Auth0 migration...")
        
        # Add Auth0 columns
        cursor.execute("ALTER TABLE users ADD COLUMN auth0_user_id VARCHAR")
        cursor.execute("ALTER TABLE users ADD COLUMN name VARCHAR")
        cursor.execute("ALTER TABLE users ADD COLUMN picture_url VARCHAR")
        cursor.execute("ALTER TABLE users ADD COLUMN email_verified BOOLEAN NOT NULL DEFAULT false")
        
        # Create index
        cursor.execute("CREATE UNIQUE INDEX ix_users_auth0_user_id ON users (auth0_user_id)")
        
        # Make legacy fields nullable
        cursor.execute("ALTER TABLE users ALTER COLUMN hashed_password DROP NOT NULL")
        cursor.execute("ALTER TABLE users ALTER COLUMN first_name DROP NOT NULL")
        cursor.execute("ALTER TABLE users ALTER COLUMN last_name DROP NOT NULL")
        
        # Update alembic version
        cursor.execute("UPDATE alembic_version SET version_num = '004_add_auth0_integration'")
        
        print("✅ Migration applied successfully!")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error applying migration: {e}")
        return False

if __name__ == "__main__":
    apply_migration()
