-- ModernAction.io Staging Data Seeding Script
-- This script creates sample bills, campaigns, and officials for demonstration

-- Clean up existing data (optional - remove if you want to keep existing data)
-- DELETE FROM actions;
-- DELETE FROM campaigns;
-- DELETE FROM bills;
-- DELETE FROM officials;

-- Insert sample bills
INSERT INTO bills (
    id, title, description, bill_number, bill_type, status, session_year,
    chamber, state, summary, ai_summary, openstates_id, introduced_date,
    sponsor_name, sponsor_party, sponsor_state, is_featured, priority_score,
    tags, categories, created_at, updated_at
) VALUES 
(
    gen_random_uuid(),
    'Climate Action Now Act',
    'A comprehensive bill to address climate change through renewable energy investments, emissions reductions, and green jobs creation.',
    'HR-1',
    'house_bill',
    'committee',
    2024,
    'house',
    'federal',
    'This landmark climate legislation establishes a national framework for achieving net-zero emissions by 2050 through investments in clean energy, transportation electrification, and climate resilience infrastructure.',
    '🌍 This critical climate bill would transform America''s energy future by investing $500B in clean energy infrastructure, creating 2M green jobs, and putting the US on track to meet Paris Agreement goals. The legislation includes tax credits for renewable energy, funding for electric vehicle charging networks, and support for communities transitioning from fossil fuel economies.',
    'hr-1-2024',
    '2024-01-15 00:00:00',
    'Rep. Alexandria Ocasio-Cortez',
    'Democratic',
    'NY',
    true,
    95,
    '["climate", "environment", "energy", "jobs", "infrastructure"]'::jsonb,
    '["Environmental Policy", "Economic Development", "Infrastructure"]'::jsonb,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'Affordable Housing Act',
    'Legislation to address the national housing crisis through increased funding for affordable housing construction and rental assistance programs.',
    'S-147',
    'senate_bill',
    'floor',
    2024,
    'senate',
    'federal',
    'The Affordable Housing Act provides $100 billion over 10 years to build and preserve affordable housing, expand rental assistance, and combat housing discrimination.',
    '🏠 This vital housing legislation would help 2M families access affordable homes by funding construction of 1M new affordable units, expanding Section 8 vouchers, and providing down payment assistance for first-time homebuyers. The bill prioritizes developments near transit and includes strong labor standards.',
    's-147-2024',
    '2024-02-20 00:00:00',
    'Sen. Elizabeth Warren',
    'Democratic',
    'MA',
    true,
    88,
    '["housing", "affordability", "development", "homelessness"]'::jsonb,
    '["Housing Policy", "Economic Development", "Social Services"]'::jsonb,
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'Healthcare Price Transparency Act',
    'Bipartisan legislation requiring hospitals and insurance companies to provide upfront pricing information to patients and consumers.',
    'HR-892',
    'house_bill',
    'passed',
    2024,
    'house',
    'federal',
    'This bipartisan bill mandates that healthcare providers and insurers disclose pricing information before services are rendered, helping patients make informed decisions and compare costs.',
    '🏥 This transparency reform would end surprise medical bills and help families budget for healthcare by requiring hospitals to publish prices online and provide cost estimates before treatment. Studies show this could reduce healthcare costs by 15-30% through increased competition.',
    'hr-892-2024',
    '2024-03-10 00:00:00',
    'Rep. Michael Burgess',
    'Republican',
    'TX',
    true,
    82,
    '["healthcare", "transparency", "pricing", "bipartisan"]'::jsonb,
    '["Healthcare Policy", "Consumer Protection", "Government Reform"]'::jsonb,
    NOW(),
    NOW()
);

-- Insert sample officials
INSERT INTO officials (
    id, name, title, party, email, level, chamber, state, district,
    bioguide_id, twitter_handle, is_active, office_address, office_city,
    office_state, office_zip, created_at, updated_at
) VALUES 
(
    gen_random_uuid(),
    'Nancy Pelosi',
    'Representative',
    'Democratic',
    '<EMAIL>',
    'federal',
    'house',
    'CA',
    '11',
    'P000197',
    'SpeakerPelosi',
    true,
    '1236 Longworth House Office Building',
    'Washington',
    'DC',
    '20515',
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'Sean Casten',
    'Representative',
    'Democratic',
    '<EMAIL>',
    'federal',
    'house',
    'IL',
    '6',
    'C001117',
    'RepCasten',
    true,
    '429 Cannon House Office Building',
    'Washington',
    'DC',
    '20515',
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'Eddie Bernice Johnson',
    'Representative',
    'Democratic',
    '<EMAIL>',
    'federal',
    'house',
    'TX',
    '30',
    'J000126',
    'RepEBJ',
    true,
    '2468 Rayburn House Office Building',
    'Washington',
    'DC',
    '20515',
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'Chuck Schumer',
    'Senator',
    'Democratic',
    '<EMAIL>',
    'federal',
    'senate',
    'NY',
    NULL,
    'S000148',
    'SenSchumer',
    true,
    '322 Hart Senate Office Building',
    'Washington',
    'DC',
    '20510',
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'Elizabeth Warren',
    'Senator',
    'Democratic',
    '<EMAIL>',
    'federal',
    'senate',
    'MA',
    NULL,
    'W000817',
    'SenWarren',
    true,
    '309 Hart Senate Office Building',
    'Washington',
    'DC',
    '20510',
    NOW(),
    NOW()
);

-- Insert sample campaigns (linked to bills)
INSERT INTO campaigns (
    id, title, description, short_description, campaign_type, status,
    call_to_action, talking_points, target_audience, geographic_scope,
    start_date, end_date, is_featured, is_public, goal_actions, actual_actions,
    social_media_message, hashtags, bill_id, created_at, updated_at
) VALUES 
(
    gen_random_uuid(),
    'Support Climate Action Now - Secure Our Future',
    'Join millions of Americans demanding bold climate action. The Climate Action Now Act represents our best chance to create good-paying green jobs, clean up our air and water, and leave a livable planet for our children. Your representatives need to hear from you TODAY.',
    'Tell Congress to pass the Climate Action Now Act',
    'support',
    'active',
    'Climate change affects every American community - from extreme weather to rising energy costs. Tell your representatives to vote YES on the Climate Action Now Act and invest in America''s clean energy future.',
    '["This bill would create over 2 million good-paying jobs in clean energy, manufacturing, and infrastructure", "Investment in renewable energy will reduce electricity bills for families and businesses", "Clean air and water protections will improve public health, especially for children and vulnerable communities", "America can lead the world in clean energy exports and technology innovation", "Climate action is economic action - clean energy is now the cheapest source of power"]'::jsonb,
    'climate-conscious citizens',
    '["national"]'::jsonb,
    '2024-01-20 00:00:00',
    '2024-12-31 23:59:59',
    true,
    true,
    25000,
    8341,
    '🌍 Climate action = economic action. Tell Congress to pass the #ClimateActionNowAct and invest in America''s clean energy future! #CleanEnergyJobs #ClimateAction',
    '["#ClimateActionNowAct", "#CleanEnergyJobs", "#ClimateAction", "#GreenNewDeal"]'::jsonb,
    (SELECT id FROM bills WHERE openstates_id = 'hr-1-2024' LIMIT 1),
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'Make Housing Affordable for Working Families',
    'Teachers, nurses, firefighters, and essential workers can''t afford to live in the communities they serve. The Affordable Housing Act would build 1 million new affordable homes and help families achieve the American dream of homeownership.',
    'Support the Affordable Housing Act to address the housing crisis',
    'support',
    'active',
    'Housing costs are crushing working families across America. Tell your senators to pass the Affordable Housing Act and invest in homes working people can afford.',
    '["Teachers, nurses, and firefighters deserve to live in the communities they serve", "This bill would create 500,000 construction jobs while building affordable homes", "Stable housing helps children succeed in school and improves community health", "Every $1 invested in affordable housing generates $7 in economic activity", "Housing assistance helps families build wealth and achieve economic mobility"]'::jsonb,
    'working families and housing advocates',
    '["national"]'::jsonb,
    '2024-02-25 00:00:00',
    '2024-11-30 23:59:59',
    true,
    true,
    15000,
    4892,
    '🏠 Every American deserves a safe, affordable home. Tell your senators to pass the #AffordableHousingAct! #HousingForAll #AffordableHousing',
    '["#AffordableHousingAct", "#HousingForAll", "#AffordableHousing", "#WorkingFamilies"]'::jsonb,
    (SELECT id FROM bills WHERE openstates_id = 's-147-2024' LIMIT 1),
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'End Surprise Medical Bills - Support Healthcare Transparency',
    'Medical bills are the leading cause of bankruptcy in America. The Healthcare Price Transparency Act has passed the House with bipartisan support and would end surprise billing while helping families plan for medical costs.',
    'Urge the Senate to pass healthcare price transparency legislation',
    'support',
    'active',
    'Surprise medical bills shouldn''t bankrupt families. Tell your senators to pass the Healthcare Price Transparency Act and give patients the right to know costs upfront.',
    '["Medical bills are the #1 cause of personal bankruptcy in America", "Price transparency allows patients to shop for affordable care and save money", "This bipartisan bill has already passed the House with strong support", "Transparency increases competition and drives down healthcare costs", "Patients deserve to know what they''ll pay before receiving medical care"]'::jsonb,
    'healthcare consumers and patients',
    '["national"]'::jsonb,
    '2024-03-15 00:00:00',
    '2024-09-30 23:59:59',
    true,
    true,
    12000,
    3621,
    '💊 End surprise medical bills! Tell your senators to pass the #HealthcareTransparencyAct and give patients the right to know costs upfront. #EndSurpriseBills #HealthcareReform',
    '["#HealthcareTransparencyAct", "#EndSurpriseBills", "#HealthcareReform", "#PatientRights"]'::jsonb,
    (SELECT id FROM bills WHERE openstates_id = 'hr-892-2024' LIMIT 1),
    NOW(),
    NOW()
);

-- Verify the data was inserted
SELECT 'Bills created:' as table_name, COUNT(*) as count FROM bills
UNION ALL
SELECT 'Officials created:', COUNT(*) FROM officials
UNION ALL
SELECT 'Campaigns created:', COUNT(*) FROM campaigns;

-- Show sample data
SELECT bill_number, title, status, priority_score FROM bills ORDER BY priority_score DESC;
SELECT name, party, state, title FROM officials ORDER BY state, name;
SELECT title, actual_actions, goal_actions FROM campaigns ORDER BY actual_actions DESC;

-- Success message
SELECT '✅ ModernAction.io staging data seeded successfully! Visit https://staging.modernaction.io to see the content' as status;