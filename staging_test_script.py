#!/usr/bin/env python3
"""
Live Staging Environment Test Script for Sprint 6 QA Audit
Tests the complete action flow on the staging environment.
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional

# Configuration
STAGING_URL = "https://staging.modernaction.io/api/v1"
TEST_ZIP_CODE = "90210"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_NAME = "QA Test User"

def test_staging_connectivity() -> bool:
    """Test basic connectivity to staging environment."""
    try:
        print("Testing staging environment connectivity...")
        health_response = requests.get(f"{STAGING_URL}/health", timeout=10)
        if health_response.status_code == 200:
            print("✅ PASS: Staging environment is accessible")
            return True
        else:
            print(f"❌ FAIL: Health check returned {health_response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ FAIL: Cannot connect to staging environment: {e}")
        return False

def get_test_campaign_id() -> Optional[str]:
    """Get a valid campaign ID from staging for testing."""
    try:
        print("Fetching available campaigns...")
        campaigns_response = requests.get(f"{STAGING_URL}/campaigns?limit=1", timeout=10)
        campaigns_response.raise_for_status()
        campaigns = campaigns_response.json()
        
        if campaigns and len(campaigns) > 0:
            campaign_id = campaigns[0]['id']
            print(f"✅ PASS: Found test campaign ID: {campaign_id}")
            return campaign_id
        else:
            print("⚠️ WARN: No campaigns found in staging environment")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ FAIL: Failed to fetch campaigns: {e}")
        return None

def test_officials_endpoint() -> bool:
    """Test the officials endpoint with a zip code."""
    try:
        print(f"Testing GET /officials with zip code {TEST_ZIP_CODE}...")
        officials_response = requests.get(
            f"{STAGING_URL}/officials", 
            params={"zip_code": TEST_ZIP_CODE},
            timeout=10
        )
        officials_response.raise_for_status()
        officials = officials_response.json()
        
        if officials and len(officials) > 0:
            print(f"✅ PASS: Successfully fetched {len(officials)} officials for zip {TEST_ZIP_CODE}")
            return True
        else:
            print(f"⚠️ WARN: No officials found for zip code {TEST_ZIP_CODE}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ FAIL: Officials endpoint failed: {e}")
        return False

def test_action_creation(campaign_id: str) -> bool:
    """Test creating an action via the POST /actions endpoint."""
    try:
        # First get officials for the zip code
        officials_response = requests.get(
            f"{STAGING_URL}/officials", 
            params={"zip_code": TEST_ZIP_CODE},
            timeout=10
        )
        officials_response.raise_for_status()
        officials = officials_response.json()
        
        if not officials:
            print("❌ FAIL: No officials available for action test")
            return False
        
        # Construct action payload
        action_payload = {
            "campaign_id": campaign_id,
            "user_email": TEST_USER_EMAIL,
            "user_name": TEST_USER_NAME,
            "zip_code": TEST_ZIP_CODE,
            "message": "This is an automated QA test message generated by the Sprint 6 audit script. Please disregard this message.",
            "action_types": ["EMAIL"],
            "official_ids": [officials[0]['id']]  # Use first official
        }
        
        print("Sending POST /actions request...")
        print(f"Payload: {json.dumps(action_payload, indent=2)}")
        
        actions_response = requests.post(
            f"{STAGING_URL}/actions", 
            json=action_payload,
            timeout=15
        )
        
        print(f"Response status: {actions_response.status_code}")
        print(f"Response body: {actions_response.text}")
        
        if actions_response.status_code == 202:
            print("✅ PASS: Received 202 Accepted status code from /actions endpoint")
            return True
        else:
            print(f"❌ FAIL: Expected status 202, but received {actions_response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ FAIL: Action creation failed: {e}")
        return False

def run_live_action_test() -> Dict[str, Any]:
    """
    Run the complete live staging environment test.
    Returns a summary of test results.
    """
    results = {
        "connectivity": False,
        "officials_fetch": False,
        "action_creation": False,
        "overall_success": False
    }
    
    print("=" * 60)
    print("SPRINT 6 QA AUDIT - LIVE STAGING ENVIRONMENT TEST")
    print("=" * 60)
    
    # Test 1: Basic connectivity
    results["connectivity"] = test_staging_connectivity()
    if not results["connectivity"]:
        print("\n❌ CRITICAL: Cannot proceed without staging connectivity")
        return results
    
    # Test 2: Get test campaign
    campaign_id = get_test_campaign_id()
    if not campaign_id:
        print("\n⚠️ WARNING: No campaign available for testing")
        # We can still test officials endpoint
    
    # Test 3: Officials endpoint
    results["officials_fetch"] = test_officials_endpoint()
    
    # Test 4: Action creation (only if we have a campaign)
    if campaign_id:
        results["action_creation"] = test_action_creation(campaign_id)
    else:
        print("\n⚠️ SKIP: Action creation test skipped (no campaign available)")
    
    # Overall assessment
    results["overall_success"] = (
        results["connectivity"] and 
        results["officials_fetch"] and 
        (results["action_creation"] or not campaign_id)
    )
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Connectivity Test: {'✅ PASS' if results['connectivity'] else '❌ FAIL'}")
    print(f"Officials Fetch: {'✅ PASS' if results['officials_fetch'] else '❌ FAIL'}")
    print(f"Action Creation: {'✅ PASS' if results['action_creation'] else '❌ FAIL' if campaign_id else '⚠️ SKIP'}")
    print(f"Overall Result: {'✅ SUCCESS' if results['overall_success'] else '❌ FAILURE'}")
    
    return results

if __name__ == "__main__":
    try:
        results = run_live_action_test()
        
        # Exit with appropriate code
        if results["overall_success"]:
            print("\n🎉 All staging tests completed successfully!")
            sys.exit(0)
        else:
            print("\n💥 Some staging tests failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Unexpected error during testing: {e}")
        sys.exit(1)
