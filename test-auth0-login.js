#!/usr/bin/env node

const { chromium } = require('playwright');

async function testAuth0Login() {
    console.log('🧪 Testing Auth0 Login Functionality');
    console.log('=' * 50);
    
    const browser = await chromium.launch({ 
        headless: false,  // Show browser for debugging
        slowMo: 1000     // Slow down actions for visibility
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // Step 1: Navigate to the application
        console.log('📍 Step 1: Navigating to https://staging.modernaction.io');
        await page.goto('https://staging.modernaction.io', { waitUntil: 'networkidle' });
        
        // Take screenshot of homepage
        await page.screenshot({ path: 'homepage.png' });
        console.log('📸 Homepage screenshot saved as homepage.png');
        
        // Step 2: Check for Auth0 bypass mode (should be gone)
        const bypassText = await page.textContent('body');
        if (bypassText.includes('Auth0 Bypass Mode')) {
            console.log('❌ ERROR: Auth0 Bypass Mode still detected!');
            return false;
        }
        console.log('✅ Auth0 Bypass Mode is gone');
        
        // Step 3: Look for login button
        console.log('🔍 Step 3: Looking for Login button');
        
        // Try multiple selectors for login button
        const loginSelectors = [
            'text=Login',
            'text=Log in',
            'text=Sign in',
            'text=Sign In',
            '[data-testid="login-button"]',
            'a[href*="login"]',
            'button:has-text("Login")',
            'a:has-text("Login")'
        ];
        
        let loginButton = null;
        for (const selector of loginSelectors) {
            try {
                loginButton = await page.locator(selector).first();
                if (await loginButton.isVisible()) {
                    console.log(`✅ Found login button with selector: ${selector}`);
                    break;
                }
            } catch (e) {
                // Continue to next selector
            }
        }
        
        if (!loginButton || !(await loginButton.isVisible())) {
            console.log('❌ ERROR: No login button found!');
            console.log('📋 Available navigation elements:');
            
            // Debug: Show all navigation elements
            const navElements = await page.locator('nav a, header a, .nav a').all();
            for (const element of navElements) {
                const text = await element.textContent();
                const href = await element.getAttribute('href');
                console.log(`  - Text: "${text}", Href: "${href}"`);
            }
            
            return false;
        }
        
        // Step 4: Click login button and handle Auth0 redirect
        console.log('🖱️  Step 4: Clicking login button');
        const loginHref = await loginButton.getAttribute('href');
        console.log(`🔗 Login button href: ${loginHref}`);

        // Set up navigation listener for Auth0 redirect
        let redirectUrl = null;
        page.on('response', response => {
            if (response.status() >= 300 && response.status() < 400) {
                console.log(`🔄 Redirect detected: ${response.status()} -> ${response.headers()['location']}`);
            }
        });

        // Click and wait for either navigation or timeout
        try {
            const [response] = await Promise.all([
                page.waitForResponse(response =>
                    response.url().includes('/api/auth/login') ||
                    response.url().includes('auth0.com'),
                    { timeout: 15000 }
                ),
                loginButton.click()
            ]);

            console.log(`📡 Auth0 response: ${response.status()} ${response.url()}`);

            // Check if we got redirected to Auth0
            if (response.url().includes('auth0.com')) {
                console.log('✅ SUCCESS: Successfully redirected to Auth0!');
                redirectUrl = response.url();
            } else if (response.status() >= 300 && response.status() < 400) {
                const location = response.headers()['location'];
                if (location && location.includes('auth0.com')) {
                    console.log('✅ SUCCESS: Auth0 redirect detected!');
                    redirectUrl = location;
                }
            }

        } catch (e) {
            console.log(`⚠️  Auth0 response timeout: ${e.message}`);
        }

        // Wait a moment for any redirects to complete
        await page.waitForTimeout(2000);

        const currentUrl = page.url();
        console.log(`📍 Current URL after click: ${currentUrl}`);

        // Take screenshot after click
        await page.screenshot({ path: 'after-login-click.png' });
        console.log('📸 After login click screenshot saved as after-login-click.png');

        // Step 5: Check for success conditions
        const pageContent = await page.textContent('body');
        console.log('📄 Page content preview:');
        console.log(pageContent.substring(0, 500) + '...');

        // Check for Auth0 success indicators
        if (currentUrl.includes('auth0.com') ||
            redirectUrl?.includes('auth0.com') ||
            pageContent.includes('auth0.com') ||
            pageContent.includes('Log in to dev-vvwd64m28nwqm871') ||
            pageContent.includes('Sign up') ||
            pageContent.includes('Continue with Google')) {
            console.log('✅ SUCCESS: Auth0 login page detected!');
            return true;
        }

        // Check for errors
        if (pageContent.includes('Not Found') || pageContent.includes('404')) {
            console.log('❌ ERROR: Still getting "Not Found" error!');
            return false;
        }

        if (pageContent.includes('Internal Server Error') || pageContent.includes('500')) {
            console.log('❌ ERROR: Internal Server Error detected!');
            return false;
        }

        console.log('⚠️  UNCLEAR: Auth0 redirect behavior unclear, but no obvious errors');
        return false;
        
    } catch (error) {
        console.log(`❌ ERROR: ${error.message}`);
        await page.screenshot({ path: 'error.png' });
        console.log('📸 Error screenshot saved as error.png');
        return false;
        
    } finally {
        await browser.close();
    }
}

// Run the test
testAuth0Login().then(success => {
    if (success) {
        console.log('🎉 Auth0 login test PASSED!');
        process.exit(0);
    } else {
        console.log('💥 Auth0 login test FAILED!');
        process.exit(1);
    }
}).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
});
