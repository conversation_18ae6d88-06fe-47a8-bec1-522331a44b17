const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('🚀 Navigating to bills page...');
    await page.goto('http://localhost:3000/bills');
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Take a screenshot
    await page.screenshot({ path: 'bills-page-screenshot.png', fullPage: true });
    console.log('📸 Screenshot saved as bills-page-screenshot.png');
    
    // Check if bills are loaded
    const billCards = await page.locator('[data-testid="bill-card"], .bill-card, .enhanced-bill-card').count();
    console.log(`📋 Found ${billCards} bill cards`);
    
    // Look for Take Action buttons
    const takeActionButtons = await page.locator('button:has-text("Take Action")').count();
    console.log(`🎯 Found ${takeActionButtons} "Take Action" buttons`);
    
    // Look for More Info buttons
    const moreInfoButtons = await page.locator('button:has-text("More Info")').count();
    console.log(`ℹ️ Found ${moreInfoButtons} "More Info" buttons`);
    
    // Test clicking a More Info button if available
    if (moreInfoButtons > 0) {
      console.log('🔍 Testing "More Info" button click...');
      await page.locator('button:has-text("More Info")').first().click();
      await page.waitForTimeout(1000);
      
      // Check if expanded content is visible
      const expandedContent = await page.locator('.comprehensive-details, [data-testid="expanded-details"]').count();
      console.log(`📖 Found ${expandedContent} expanded detail sections`);
      
      // Take another screenshot after expansion
      await page.screenshot({ path: 'bills-page-expanded.png', fullPage: true });
      console.log('📸 Expanded screenshot saved as bills-page-expanded.png');
    }
    
    // Check for any error messages
    const errorMessages = await page.locator('.error, [role="alert"], .alert-error').count();
    console.log(`❌ Found ${errorMessages} error messages`);
    
    // Check page title
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);
    
    // Get page content for debugging
    const bodyText = await page.locator('body').textContent();
    if (bodyText.includes('Error') || bodyText.includes('404') || bodyText.includes('500')) {
      console.log('⚠️ Page contains error text');
      console.log('First 500 characters of page:', bodyText.substring(0, 500));
    }
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
  } finally {
    await browser.close();
  }
})();
