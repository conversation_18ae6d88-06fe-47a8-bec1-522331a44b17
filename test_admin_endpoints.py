#!/usr/bin/env python3
"""
Test script for admin values analysis endpoints.

This script tests the admin API endpoints for the values analysis review system.
"""

import requests
import json
import sys

# Configuration
API_BASE = "http://localhost:8000/api/v1"
ADMIN_API_KEY = "admin-dev-key-2024"

def test_review_queue():
    """Test the review queue endpoint."""
    print("🧪 Testing Review Queue Endpoint")
    print("-" * 40)
    
    try:
        response = requests.get(
            f"{API_BASE}/admin/review-queue",
            headers={"X-Admin-API-Key": ADMIN_API_KEY}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                items = data.get("data", {}).get("items", [])
                print(f"✅ Success! Found {len(items)} bills in review queue")
                
                if items:
                    print("\nFirst bill in queue:")
                    first_bill = items[0]
                    print(f"  Bill: {first_bill.get('bill_number')} - {first_bill.get('bill_title', 'No title')[:50]}...")
                    print(f"  Flagged: {first_bill.get('flagged_reason')}")
                    print(f"  Confidence: {first_bill.get('analysis', {}).get('confidence_score')}")
                else:
                    print("  No bills currently require review")
            else:
                print(f"❌ API returned success=false: {data}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()


def test_review_stats():
    """Test the review stats endpoint."""
    print("📊 Testing Review Stats Endpoint")
    print("-" * 40)
    
    try:
        response = requests.get(
            f"{API_BASE}/admin/review-stats",
            headers={"X-Admin-API-Key": ADMIN_API_KEY}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                stats = data.get("data", {})
                print("✅ Success! Review statistics:")
                print(f"  Pending Reviews: {stats.get('pending_reviews', 0)}")
                print(f"  Completed Reviews: {stats.get('completed_reviews', 0)}")
                print(f"  Total Analyses: {stats.get('total_analyses', 0)}")
                print(f"  Completion Rate: {stats.get('review_completion_rate', 0)}%")
                print(f"  Flagged Bills: {stats.get('flagged_bills', 0)}")
                
                threat_dist = stats.get('threat_level_distribution', {})
                if threat_dist:
                    print("  Threat Level Distribution:")
                    for level, count in threat_dist.items():
                        print(f"    {level}: {count}")
            else:
                print(f"❌ API returned success=false: {data}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()


def test_values_analysis_endpoint():
    """Test the values analysis endpoint to see if we have any analyzed bills."""
    print("🔍 Testing Values Analysis Coverage")
    print("-" * 40)
    
    try:
        response = requests.get(f"{API_BASE}/values/stats")
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Values analysis stats:")
            print(f"  Total Bills: {data.get('total_bills', 0)}")
            print(f"  Analyzed Bills: {data.get('analyzed_bills', 0)}")
            print(f"  Coverage: {data.get('coverage_percentage', 0)}%")
            print(f"  Requiring Review: {data.get('requiring_review', 0)}")
            
            if data.get('analyzed_bills', 0) == 0:
                print("\n💡 No bills have been analyzed yet.")
                print("   You may need to:")
                print("   1. Create some bills first")
                print("   2. Run values analysis on existing bills")
                print("   3. Use the /values/analyze/all-missing endpoint")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()


def test_analyze_missing_bills():
    """Test analyzing bills that don't have values analysis."""
    print("🤖 Testing Analyze Missing Bills")
    print("-" * 40)
    
    try:
        response = requests.post(f"{API_BASE}/values/analyze/all-missing")
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Analysis started:")
            print(f"  Total Bills to Analyze: {data.get('total', 0)}")
            print(f"  Status: {data.get('errors', ['Analysis started in background'])[0]}")
            
            if data.get('total', 0) > 0:
                print("\n⏳ Analysis is running in background.")
                print("   Wait a few moments, then check the review queue again.")
            else:
                print("\n💡 No bills need analysis.")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()


def test_api_key_protection():
    """Test that endpoints are properly protected."""
    print("🔒 Testing API Key Protection")
    print("-" * 40)
    
    try:
        # Test without API key
        response = requests.get(f"{API_BASE}/admin/review-queue")
        print(f"Without API key - Status: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ Endpoint properly protected (401 Unauthorized)")
        else:
            print(f"⚠️  Expected 401, got {response.status_code}")
        
        # Test with wrong API key
        response = requests.get(
            f"{API_BASE}/admin/review-queue",
            headers={"X-Admin-API-Key": "wrong-key"}
        )
        print(f"With wrong API key - Status: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ Wrong API key properly rejected (401 Unauthorized)")
        else:
            print(f"⚠️  Expected 401, got {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()


def main():
    """Run all tests."""
    print("🎯 Admin Values Analysis Endpoints Test")
    print("=" * 60)
    print()
    
    # Test API protection first
    test_api_key_protection()
    
    # Test basic functionality
    test_values_analysis_endpoint()
    test_analyze_missing_bills()
    test_review_stats()
    test_review_queue()
    
    print("=" * 60)
    print("🎉 Testing complete!")
    print()
    print("💡 Next steps:")
    print("1. If no bills are analyzed, create some bills first")
    print("2. Run values analysis on existing bills")
    print("3. Check the admin review interface at http://localhost:3000/admin/review")
    print("4. Test the complete review workflow")


if __name__ == "__main__":
    main()
