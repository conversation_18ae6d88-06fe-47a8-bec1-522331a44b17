#!/usr/bin/env python3

import asyncio
from playwright.async_api import async_playwright

async def test_bills_page():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("🚀 Navigating to bills page...")
            await page.goto("http://localhost:3000/bills")
            
            # Wait for page to load
            await page.wait_for_timeout(3000)
            
            # Take a screenshot
            await page.screenshot(path="bills-page-screenshot.png", full_page=True)
            print("📸 Screenshot saved as bills-page-screenshot.png")
            
            # Check page title
            title = await page.title()
            print(f"📄 Page title: {title}")
            
            # Check if bills are loaded
            bill_cards = await page.locator('[data-testid="bill-card"], .bill-card, .enhanced-bill-card').count()
            print(f"📋 Found {bill_cards} bill cards")

            # Look for Take Action buttons
            take_action_buttons = await page.locator('button:has-text("Take Action")').count()
            print(f"🎯 Found {take_action_buttons} 'Take Action' buttons")

            # Look for More Info buttons
            more_info_buttons = await page.locator('button:has-text("More Info")').count()
            print(f"ℹ️ Found {more_info_buttons} 'More Info' buttons")

            # Check for the new UI elements we implemented
            refresh_button = await page.locator('button:has-text("Refresh")').count()
            print(f"🔄 Found {refresh_button} 'Refresh' buttons")

            # Check for error message (expected since backend is down)
            error_text = await page.locator('text="Failed to load bills"').count()
            print(f"⚠️ Found {error_text} 'Failed to load bills' messages")

            # Check for empty state message
            empty_message = await page.locator('text="No bills available"').count()
            print(f"📭 Found {empty_message} 'No bills available' messages")
            
            # Check for any error messages
            error_messages = await page.locator('.error, [role="alert"], .alert-error').count()
            print(f"❌ Found {error_messages} error messages")
            
            # Get page content for debugging
            body_text = await page.locator('body').text_content()
            if any(keyword in body_text for keyword in ['Error', '404', '500', 'Cannot']):
                print("⚠️ Page contains error text")
                print("First 500 characters of page:", body_text[:500])
            
            # Test clicking a More Info button if available
            if more_info_buttons > 0:
                print("🔍 Testing 'More Info' button click...")
                await page.locator('button:has-text("More Info")').first.click()
                await page.wait_for_timeout(1000)

                # Check if expanded content is visible
                expanded_content = await page.locator('.comprehensive-details, [data-testid="expanded-details"]').count()
                print(f"📖 Found {expanded_content} expanded detail sections")

                # Take another screenshot after expansion
                await page.screenshot(path="bills-page-expanded.png", full_page=True)
                print("📸 Expanded screenshot saved as bills-page-expanded.png")

            # Test the UI components that should be present
            print("\n🔍 Testing UI Components:")

            # Check for filter components
            status_filter = await page.locator('select, [data-testid="status-filter"]').count()
            print(f"📊 Found {status_filter} status filter components")

            urgency_filter = await page.locator('text="🔥 High", text="⚡ Medium", text="📋 Low"').count()
            print(f"🚨 Found {urgency_filter} urgency filter options")

            # Check for sort options
            sort_options = await page.locator('text="Priority Score", text="Date Introduced", text="Title"').count()
            print(f"🔄 Found {sort_options} sort options")

            # Check for quick filters
            quick_filters = await page.locator('text="🔥 Urgent Votes", text="📈 Active Bills"').count()
            print(f"⚡ Found {quick_filters} quick filter options")

            # Verify the page shows proper empty state
            empty_state_text = await page.locator('text="No bills available at the moment"').count()
            print(f"📭 Found {empty_state_text} empty state messages")

            # Check for the new refresh functionality
            refresh_functionality = await page.locator('button:has-text("Refresh")').count()
            print(f"🔄 Found {refresh_functionality} refresh buttons")
            
            # Check for loading states
            loading_elements = await page.locator('.loading, .spinner, [data-testid="loading"]').count()
            print(f"⏳ Found {loading_elements} loading elements")
            
            # Check for empty states
            empty_states = await page.locator('.empty, .no-data, [data-testid="empty-state"]').count()
            print(f"📭 Found {empty_states} empty state elements")
            
            print("✅ UI testing completed successfully!")
            
        except Exception as error:
            print(f"❌ Error during testing: {error}")
            await page.screenshot(path="error-screenshot.png", full_page=True)
            print("📸 Error screenshot saved as error-screenshot.png")
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_bills_page())
