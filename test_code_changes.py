#!/usr/bin/env python3
"""
Unit test to verify our personal stories code changes are correct
"""

import sys
import os
import re

def test_api_schema_changes():
    """Test that the API schemas include personal_stories field"""
    
    print("🧪 Testing API Schema Changes")
    print("=" * 40)
    
    # Read the actions.py file
    try:
        with open('apps/api/app/api/v1/endpoints/actions.py', 'r') as f:
            actions_content = f.read()
        
        # Check ActionSubmitRequest schema
        if 'personal_stories: Optional[str] = ""  # User\'s personal story for AI personalization' in actions_content:
            print("✅ ActionSubmitRequest schema includes personal_stories field")
        else:
            print("❌ ActionSubmitRequest schema missing personal_stories field")
            return False
        
        # Check MessagePreviewRequest schema  
        if 'class MessagePreviewRequest(BaseModel):' in actions_content:
            # Find the MessagePreviewRequest class and check for personal_stories
            preview_request_match = re.search(
                r'class MessagePreviewRequest\(BaseModel\):(.*?)(?=class|\Z)', 
                actions_content, 
                re.DOTALL
            )
            if preview_request_match and 'personal_stories: Optional[str] = ""' in preview_request_match.group(1):
                print("✅ MessagePreviewRequest schema includes personal_stories field")
            else:
                print("❌ MessagePreviewRequest schema missing personal_stories field")
                return False
        
        # Check that personal_stories is passed to the service
        if "'personal_stories': preview_request.personal_stories" in actions_content:
            print("✅ Preview endpoint passes personal_stories to service")
        else:
            print("❌ Preview endpoint not passing personal_stories to service")
            return False
            
        if "'personal_stories': action_request.personal_stories" in actions_content:
            print("✅ Submit endpoint passes personal_stories to service")
        else:
            print("❌ Submit endpoint not passing personal_stories to service")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading actions.py: {e}")
        return False

def test_service_changes():
    """Test that the message personalization service handles personal stories"""
    
    print("\n🧪 Testing Service Changes")
    print("=" * 40)
    
    try:
        with open('apps/api/app/services/message_personalization_service.py', 'r') as f:
            service_content = f.read()
        
        # Check that personal_stories is extracted from request_data
        if 'personal_stories = request_data.get(\'personal_stories\', \'\')' in service_content:
            print("✅ Service extracts personal_stories from request_data")
        else:
            print("❌ Service not extracting personal_stories from request_data")
            return False
        
        # Check that personal_stories is passed to _create_message_for_representative
        if 'personal_stories=personal_stories' in service_content:
            print("✅ Service passes personal_stories to message creation")
        else:
            print("❌ Service not passing personal_stories to message creation")
            return False
        
        # Check that _create_message_for_representative accepts personal_stories parameter
        if 'personal_stories: str = \'\') -> Dict[str, Any]:' in service_content:
            print("✅ Message creation method accepts personal_stories parameter")
        else:
            print("❌ Message creation method missing personal_stories parameter")
            return False
        
        # Check that _build_personalization_prompt accepts personal_stories
        if 'personal_stories: str = \'\') -> str:' in service_content:
            print("✅ Prompt building method accepts personal_stories parameter")
        else:
            print("❌ Prompt building method missing personal_stories parameter")
            return False
        
        # Check that personal stories are included in the prompt
        if 'USER\'S PERSONAL STORY TO INCORPORATE:' in service_content:
            print("✅ Personal stories are included in AI prompt")
        else:
            print("❌ Personal stories not included in AI prompt")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading message_personalization_service.py: {e}")
        return False

def test_frontend_integration():
    """Test that the frontend properly sends personal stories"""
    
    print("\n🧪 Testing Frontend Integration")
    print("=" * 40)
    
    try:
        with open('apps/web/src/services/apiClient.ts', 'r') as f:
            api_client_content = f.read()
        
        # Check that API interfaces include personal_stories
        if 'personal_stories?: string;' in api_client_content:
            print("✅ Frontend API interfaces include personal_stories field")
        else:
            print("❌ Frontend API interfaces missing personal_stories field")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading apiClient.ts: {e}")
        return False

def test_modal_component():
    """Test that the BillActionModal component handles personal stories"""
    
    print("\n🧪 Testing Modal Component")
    print("=" * 40)
    
    try:
        with open('apps/web/src/components/shared/BillActionModal.tsx', 'r') as f:
            modal_content = f.read()
        
        # Check that personal_stories is in the form data interface
        if 'personal_stories: string;' in modal_content:
            print("✅ Modal component includes personal_stories in form data")
        else:
            print("❌ Modal component missing personal_stories in form data")
            return False
        
        # Check that personal stories are sent in API calls
        if 'personal_stories: previewData.personal_stories' in modal_content:
            print("✅ Modal component sends personal_stories in API calls")
        else:
            print("❌ Modal component not sending personal_stories in API calls")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading BillActionModal.tsx: {e}")
        return False

def main():
    """Run all tests"""
    
    print("🧪 Personal Stories Integration Code Review")
    print("=" * 50)
    
    tests = [
        ("API Schema Changes", test_api_schema_changes),
        ("Service Changes", test_service_changes),
        ("Frontend Integration", test_frontend_integration),
        ("Modal Component", test_modal_component)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Personal stories integration is properly implemented.")
        return True
    else:
        print("❌ SOME TESTS FAILED. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
