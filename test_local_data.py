#!/usr/bin/env python3
"""
Quick test to verify our seeded data is accessible
"""

import os
import sys
from pathlib import Path

# Add the API app to Python path
api_path = Path(__file__).parent / "apps" / "api"
sys.path.insert(0, str(api_path))

# Set environment to use our local .env file
os.environ.setdefault("ENV_FILE", ".env.local")

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.models.bill import Bill
from app.models.campaign import Campaign
from app.models.official import Official
from app.core.config import settings

def test_data():
    """Test that we can read the seeded data"""
    print("🔍 Testing seeded data access...")
    
    # Create database engine
    engine = create_engine(settings.database_url, echo=False)
    SessionLocal = sessionmaker(bind=engine)
    
    session = SessionLocal()
    
    try:
        # Query bills
        bills = session.query(Bill).order_by(Bill.priority_score.desc()).all()
        print(f"\n📜 Bills ({len(bills)}):")
        for bill in bills:
            print(f"  • {bill.bill_number}: {bill.title} (Priority: {bill.priority_score})")
        
        # Query campaigns
        campaigns = session.query(Campaign).filter_by(status='ACTIVE').all()
        print(f"\n🏛️ Active Campaigns ({len(campaigns)}):")
        for campaign in campaigns:
            print(f"  • {campaign.title}")
            print(f"    Progress: {campaign.actual_actions:,}/{campaign.goal_actions:,} actions")
            print(f"    Bill: {campaign.bill.bill_number if campaign.bill else 'None'}")
        
        # Query officials
        officials = session.query(Official).filter_by(level='federal').limit(5).all()
        print(f"\n👥 Sample Federal Officials ({len(officials)}):")
        for official in officials[:5]:
            print(f"  • {official.name} ({official.party}) - {official.state} {official.title}")
        
        # Test API data format
        if campaigns:
            print(f"\n🔍 Testing API data format for first campaign...")
            campaign = campaigns[0]
            api_data = {
                "id": str(campaign.id),
                "title": campaign.title,
                "description": campaign.description,
                "short_description": campaign.short_description,
                "status": campaign.status.value,
                "actual_actions": campaign.actual_actions,
                "goal_actions": campaign.goal_actions,
                "is_featured": campaign.is_featured,
                "bill": {
                    "id": str(campaign.bill.id),
                    "title": campaign.bill.title,
                    "bill_number": campaign.bill.bill_number
                } if campaign.bill else None
            }
            print(f"  ✅ Sample API response: {api_data['title']}")
            print(f"     Status: {api_data['status']}")
            print(f"     Progress: {api_data['actual_actions']}/{api_data['goal_actions']}")
        
    finally:
        session.close()

if __name__ == "__main__":
    print("🚀 ModernAction.io Local Data Test")
    print("=" * 36)
    test_data()