#!/usr/bin/env python3
"""
Test script for validating the neutral language system.

This script ensures our language generation maintains strict political neutrality
while providing clear, informative content for users.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'apps', 'api'))

from app.services.neutral_language_service import NeutralLanguageService


def test_tag_generation():
    """Test neutral tag generation across all categories and severity levels."""
    print("🏷️  Testing Neutral Tag Generation\n")
    
    categories = ['democracy', 'human_rights', 'environment']
    score_types = ['threat', 'support', 'neutral']
    severity_levels = [2, 5, 7, 9]  # Representative of each impact level
    
    all_neutral = True
    
    for category in categories:
        print(f"📋 {category.title()} Category:")
        
        for score_type in score_types:
            print(f"  {score_type.title()} Tags:")
            
            for severity in severity_levels:
                tag = NeutralLanguageService.generate_neutral_tag(
                    category, score_type, severity
                )
                
                print(f"    Severity {severity}: '{tag.display_text}'")
                print(f"      Description: {tag.description}")
                print(f"      Theme: {tag.color_theme}, Icon: {tag.icon_name}")
                
                # Validate neutrality
                is_neutral, issues = NeutralLanguageService.validate_neutrality(
                    tag.display_text + " " + tag.description
                )
                
                if is_neutral:
                    print(f"      ✅ Language is neutral")
                else:
                    print(f"      ⚠️  Potential issues: {issues}")
                    all_neutral = False
                
                print()
        
        print()
    
    return all_neutral


def test_summary_language():
    """Test neutral summary language generation."""
    print("📝 Testing Summary Language Generation\n")
    
    test_cases = [
        {
            'name': 'High Democracy Support',
            'scores': ((2, 8), (1, 1), (0, 3)),
            'expected_keywords': ['enhance', 'democratic processes']
        },
        {
            'name': 'Environmental Threat',
            'scores': ((0, 0), (2, 1), (7, 2)),
            'expected_keywords': ['impact', 'environmental policies']
        },
        {
            'name': 'Mixed Impact Bill',
            'scores': ((3, 6), (5, 2), (1, 7)),
            'expected_keywords': ['enhance', 'impact', 'addresses']
        },
        {
            'name': 'Low Impact Bill',
            'scores': ((1, 2), (0, 1), (2, 1)),
            'expected_keywords': ['policy provisions']
        }
    ]
    
    all_neutral = True
    
    for case in test_cases:
        democracy_scores, human_rights_scores, environment_scores = case['scores']
        
        summary = NeutralLanguageService.generate_summary_language(
            democracy_scores, human_rights_scores, environment_scores
        )
        
        print(f"🏛️  {case['name']}:")
        print(f"   Scores: D{democracy_scores}, HR{human_rights_scores}, E{environment_scores}")
        print(f"   Summary: '{summary}'")
        
        # Validate neutrality
        is_neutral, issues = NeutralLanguageService.validate_neutrality(summary)
        
        if is_neutral:
            print(f"   ✅ Language is neutral")
        else:
            print(f"   ⚠️  Potential issues: {issues}")
            all_neutral = False
        
        # Check for expected keywords
        summary_lower = summary.lower()
        found_keywords = [kw for kw in case['expected_keywords'] if kw.lower() in summary_lower]
        
        if found_keywords:
            print(f"   ✅ Contains expected elements: {found_keywords}")
        else:
            print(f"   ⚠️  Missing expected elements: {case['expected_keywords']}")
        
        print()
    
    return all_neutral


def test_confidence_language():
    """Test confidence level language generation."""
    print("🎯 Testing Confidence Language\n")
    
    confidence_levels = [0.95, 0.8, 0.6, 0.3]
    
    all_neutral = True
    
    for confidence in confidence_levels:
        language = NeutralLanguageService.get_confidence_language(confidence)
        
        print(f"Confidence {confidence}: '{language}'")
        
        # Validate neutrality
        is_neutral, issues = NeutralLanguageService.validate_neutrality(language)
        
        if is_neutral:
            print(f"  ✅ Language is neutral")
        else:
            print(f"  ⚠️  Potential issues: {issues}")
            all_neutral = False
        
        print()
    
    return all_neutral


def test_neutrality_validation():
    """Test the neutrality validation system itself."""
    print("🔍 Testing Neutrality Validation System\n")
    
    test_texts = [
        {
            'text': 'This bill addresses environmental policies through various provisions.',
            'should_be_neutral': True
        },
        {
            'text': 'This terrible bill will destroy our democracy!',
            'should_be_neutral': False
        },
        {
            'text': 'This wonderful progressive legislation fights climate change.',
            'should_be_neutral': False
        },
        {
            'text': 'The bill modifies voting procedures and election administration.',
            'should_be_neutral': True
        },
        {
            'text': 'Obviously, this conservative attack on voting rights is ridiculous.',
            'should_be_neutral': False
        }
    ]
    
    all_correct = True
    
    for case in test_texts:
        is_neutral, issues = NeutralLanguageService.validate_neutrality(case['text'])
        
        print(f"Text: '{case['text']}'")
        print(f"Expected neutral: {case['should_be_neutral']}, Detected neutral: {is_neutral}")
        
        if is_neutral == case['should_be_neutral']:
            print(f"✅ Validation correct")
        else:
            print(f"❌ Validation incorrect")
            all_correct = False
        
        if issues:
            print(f"Issues found: {issues}")
        
        print()
    
    return all_correct


def test_real_world_scenarios():
    """Test with realistic bill scenarios."""
    print("🌍 Testing Real-World Scenarios\n")
    
    scenarios = [
        {
            'name': 'Voting Rights Bill',
            'category': 'democracy',
            'score_type': 'support',
            'severity': 7,
            'context': 'Expands early voting and automatic registration'
        },
        {
            'name': 'Environmental Deregulation',
            'category': 'environment',
            'score_type': 'threat',
            'severity': 6,
            'context': 'Reduces oversight of industrial emissions'
        },
        {
            'name': 'Healthcare Access Bill',
            'category': 'human_rights',
            'score_type': 'support',
            'severity': 5,
            'context': 'Expands coverage for preventive care'
        }
    ]
    
    all_neutral = True
    
    for scenario in scenarios:
        print(f"🏛️  {scenario['name']}:")
        
        tag = NeutralLanguageService.generate_neutral_tag(
            scenario['category'],
            scenario['score_type'],
            scenario['severity'],
            scenario['context']
        )
        
        print(f"   Tag: '{tag.display_text}'")
        print(f"   Description: {tag.description}")
        print(f"   Severity: {tag.severity_level}, Theme: {tag.color_theme}")
        
        # Validate neutrality
        full_text = f"{tag.display_text} {tag.description}"
        is_neutral, issues = NeutralLanguageService.validate_neutrality(full_text)
        
        if is_neutral:
            print(f"   ✅ Language is neutral")
        else:
            print(f"   ⚠️  Potential issues: {issues}")
            all_neutral = False
        
        print()
    
    return all_neutral


def main():
    """Run all neutral language tests."""
    print("🎯 Neutral Language System Validation\n")
    print("=" * 60)
    
    tests = [
        ("Tag Generation", test_tag_generation),
        ("Summary Language", test_summary_language),
        ("Confidence Language", test_confidence_language),
        ("Neutrality Validation", test_neutrality_validation),
        ("Real-World Scenarios", test_real_world_scenarios)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"⚠️  {test_name}: NEEDS ATTENTION")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name}: FAILED - {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 All neutral language tests passed!")
        print("\n📋 System Ready For:")
        print("✅ Generating neutral tags for frontend display")
        print("✅ Creating unbiased bill impact summaries")
        print("✅ Maintaining political neutrality across all content")
        print("✅ Providing clear, factual information to users")
    else:
        print("⚠️  Some neutral language issues found.")
        print("Please address the issues above before proceeding.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
