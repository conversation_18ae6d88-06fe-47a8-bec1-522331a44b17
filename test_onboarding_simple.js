#!/usr/bin/env node
/**
 * Simple Frontend Onboarding Flow Test
 * 
 * This script validates that the onboarding system is properly set up
 * and can run basic E2E tests without complex Playwright setup.
 */

const { chromium } = require('playwright');

async function testOnboardingSetup() {
  console.log('🚀 Starting Simple Onboarding Test...');
  console.log('=' * 50);
  
  let browser, page;
  
  try {
    // Launch browser
    browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    page = await context.newPage();
    
    // Clear localStorage first
    await page.goto('http://localhost:3000');
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    
    console.log('✅ Browser launched and localStorage cleared');
    
    // Reload page to trigger onboarding
    await page.reload();
    await page.waitForTimeout(2000); // Wait for React to load
    
    // Check if onboarding modal appears
    try {
      const modal = await page.waitForSelector('[data-testid="onboarding-modal"]', { timeout: 10000 });
      if (modal) {
        console.log('✅ Onboarding modal appeared for first-time user');
        
        // Check for welcome text
        const welcomeText = await page.textContent('h3');
        if (welcomeText && welcomeText.includes('Welcome to ModernAction')) {
          console.log('✅ Welcome message displayed correctly');
        }
        
        // Check for issue buttons
        const environmentButton = await page.$('[data-testid="issue-environment"]');
        const healthcareButton = await page.$('[data-testid="issue-healthcare"]');
        
        if (environmentButton && healthcareButton) {
          console.log('✅ Issue selection buttons are present');
          
          // Try selecting an issue
          await environmentButton.click();
          console.log('✅ Issue selection works');
          
          // Check for save button
          const saveButton = await page.$('[data-testid="onboarding-save-button"]');
          if (saveButton) {
            console.log('✅ Save button is present');
            
            // Click save
            await saveButton.click();
            await page.waitForTimeout(2000); // Wait longer for Zustand persist
            
            // Check if modal disappeared
            const modalAfterSave = await page.$('[data-testid="onboarding-modal"]');
            if (!modalAfterSave || !(await modalAfterSave.isVisible())) {
              console.log('✅ Modal disappeared after saving');
              
              // Wait a bit more for localStorage to be updated by Zustand
              await page.waitForTimeout(1000);
              
              // Check localStorage
              const localStorageData = await page.evaluate(() => {
                const data = localStorage.getItem('modernaction-onboarding');
                return data ? JSON.parse(data) : null;
              });
              
              console.log('🔍 localStorage content:', localStorageData);
              
              if (localStorageData && localStorageData.state && localStorageData.state.hasCompletedOnboarding) {
                console.log('✅ localStorage updated correctly:', localStorageData);
                
                // Test returning user
                await page.reload();
                await page.waitForTimeout(2000);
                
                const modalAfterReload = await page.$('[data-testid="onboarding-modal"]');
                if (!modalAfterReload || !(await modalAfterReload.isVisible())) {
                  console.log('✅ Modal correctly hidden for returning user');
                  return true;
                } else {
                  console.log('❌ Modal should not appear for returning user');
                }
              } else {
                console.log('❌ localStorage not updated correctly');
              }
            } else {
              console.log('❌ Modal did not disappear after saving');
            }
          } else {
            console.log('❌ Save button not found');
          }
        } else {
          console.log('❌ Issue selection buttons not found');
        }
      } else {
        console.log('❌ Onboarding modal did not appear');
      }
    } catch (error) {
      console.log('❌ Onboarding modal did not appear within timeout');
      console.log('Error:', error.message);
    }
    
  } catch (error) {
    console.log('❌ Test failed with error:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
  
  return false;
}

async function checkFrontendSetup() {
  console.log('🧪 Checking Frontend Setup...');
  
  // Check if Next.js dev server is running
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('✅ Next.js development server is running');
      return true;
    } else {
      console.log('❌ Next.js development server responded with error:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Next.js development server is not running');
    console.log('💡 Please run "npm run dev" in the apps/web directory first');
    return false;
  }
}

async function main() {
  console.log('🚀 Onboarding Flow E2E Test');
  console.log('=' * 50);
  
  // Check if frontend is running
  const frontendRunning = await checkFrontendSetup();
  if (!frontendRunning) {
    console.log('\n❌ Frontend setup test failed');
    process.exit(1);
  }
  
  // Run onboarding test
  const testPassed = await testOnboardingSetup();
  
  console.log('\n' + '=' * 50);
  console.log('📊 TEST RESULTS');
  console.log('=' * 50);
  
  if (testPassed) {
    console.log('🎉 ALL ONBOARDING E2E TESTS PASSED!');
    console.log('✅ Modal appears for first-time users');
    console.log('✅ Issue selection works correctly');
    console.log('✅ Preferences saved to localStorage');
    console.log('✅ Modal hidden for returning users');
    console.log('\n🚀 Onboarding flow ready for production!');
    process.exit(0);
  } else {
    console.log('❌ ONBOARDING E2E TESTS FAILED');
    console.log('Please check the implementation and try again.');
    process.exit(1);
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.log('❌ Unhandled promise rejection:', error);
  process.exit(1);
});

// Run the test
main();