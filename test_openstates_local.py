#!/usr/bin/env python3
"""
Test OpenStates API integration locally without database dependency
"""
import asyncio
import sys
import os

# Add the apps/api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'apps', 'api'))

from app.services.openstates_officials_api import OpenStatesOfficialsAPI
from app.core.config import settings

def test_openstates_api():
    """Test OpenStates API integration"""
    print("🔍 Testing OpenStates API Integration")
    print("=" * 50)
    print(f"🔑 API Key: {settings.OPENSTATES_API_KEY[:10]}..." if settings.OPENSTATES_API_KEY else "❌ No API Key")
    print(f"🌍 Environment: {settings.ENVIRONMENT}")
    print()
    
    if not settings.OPENSTATES_API_KEY:
        print("❌ ERROR: OPENSTATES_API_KEY not found in environment")
        return
    
    # Initialize the API service
    api = OpenStatesOfficialsAPI()
    
    # Test 1: Chicago area (60302 - Oak Park, IL)
    print("🏛️ Test 1: Chicago Area (60302 - Oak Park, IL)")
    print("-" * 40)
    try:
        officials = api.get_officials_by_zip("60302")
        print(f"✅ Found {len(officials)} officials:")

        for i, official in enumerate(officials, 1):
            print(f"  {i}. {official.name}")
            print(f"     Title: {official.title}")
            print(f"     Party: {official.party}")
            print(f"     Level: {official.level}")
            print(f"     State: {official.state}")
            if official.district:
                print(f"     District: {official.district}")
            if official.chamber:
                print(f"     Chamber: {official.chamber}")
            if official.email:
                print(f"     Email: {official.email}")
            if official.phone:
                print(f"     Phone: {official.phone}")
            print()

    except Exception as e:
        print(f"❌ Error: {e}")

    print()

    # Test 2: Dallas area (75201 - Dallas, TX)
    print("🏛️ Test 2: Dallas Area (75201 - Dallas, TX)")
    print("-" * 40)
    try:
        officials = api.get_officials_by_zip("75201")
        print(f"✅ Found {len(officials)} officials:")

        for i, official in enumerate(officials, 1):
            print(f"  {i}. {official.name}")
            print(f"     Title: {official.title}")
            print(f"     Party: {official.party}")
            print(f"     Level: {official.level}")
            print(f"     State: {official.state}")
            if official.district:
                print(f"     District: {official.district}")
            if official.chamber:
                print(f"     Chamber: {official.chamber}")
            if official.email:
                print(f"     Email: {official.email}")
            if official.phone:
                print(f"     Phone: {official.phone}")
            print()

    except Exception as e:
        print(f"❌ Error: {e}")

    print()

    # Test 3: Test geocoding directly
    print("🌍 Test 3: Direct Geocoding Test")
    print("-" * 40)
    try:
        coords = api._zip_to_coordinates("60302")
        if coords:
            lat, lng = coords
            print(f"✅ Geocoding 60302: ({lat}, {lng})")
        else:
            print("❌ No coordinates returned for 60302")

        coords = api._zip_to_coordinates("75201")
        if coords:
            lat, lng = coords
            print(f"✅ Geocoding 75201: ({lat}, {lng})")
        else:
            print("❌ No coordinates returned for 75201")

    except Exception as e:
        print(f"❌ Geocoding Error: {e}")
    
    print()
    print("🎯 Summary")
    print("=" * 50)
    print("This test verifies that:")
    print("✅ OpenStates API key is configured")
    print("✅ Geocoding service (Nominatim) works")
    print("✅ OpenStates API returns officials data")
    print("✅ Data parsing and model creation works")
    print()
    print("If all tests pass, the issue is likely in deployment/container configuration,")
    print("not in the OpenStates integration code itself.")

if __name__ == "__main__":
    test_openstates_api()
