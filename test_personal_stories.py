#!/usr/bin/env python3
"""
Test script to verify personal stories integration in the API
"""

import json
import requests
import sys

def test_personal_stories_integration():
    """Test that personal stories are properly integrated into the API"""

    # First, let's check what bills are available
    print("🔍 Checking available bills...")
    try:
        bills_response = requests.get("http://localhost:8000/api/v1/bills?limit=1", timeout=10)
        if bills_response.status_code == 200:
            bills = bills_response.json()
            if bills and len(bills) > 0:
                bill_id = bills[0]['id']
                print(f"✅ Found bill: {bills[0].get('bill_number', 'N/A')} - {bills[0].get('title', 'N/A')}")
            else:
                print("⚠️  No bills found in database, using mock bill ID")
                bill_id = "test-bill-123"
        else:
            print("⚠️  Bills endpoint not accessible, using mock bill ID")
            bill_id = "test-bill-123"
    except Exception as e:
        print(f"⚠️  Error checking bills: {e}, using mock bill ID")
        bill_id = "test-bill-123"

    # Test data
    test_payload = {
        "bill_id": bill_id,
        "stance": "support",
        "selected_reasons": ["This bill improves safety standards", "Economic benefits"],
        "custom_reasons": ["My custom reason for supporting this"],
        "personal_stories": "As a parent of two young children, I am deeply concerned about the climate crisis and its impact on their future. We need immediate action to transition to clean energy.",
        "zip_code": "60302"
    }
    
    print("Testing personal stories integration...")
    print(f"Test payload: {json.dumps(test_payload, indent=2)}")
    
    try:
        # Test the preview-message endpoint
        url = "http://localhost:8000/api/v1/actions/preview-message"
        headers = {"Content-Type": "application/json"}
        
        print(f"\nSending POST request to: {url}")
        response = requests.post(url, json=test_payload, headers=headers, timeout=30)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"Response data: {json.dumps(response_data, indent=2)}")
            
            # Check if personal stories are included in the response
            if 'personalized_messages' in response_data:
                for message in response_data['personalized_messages']:
                    if 'body' in message:
                        body = message['body']
                        personal_story_keywords = ['parent', 'children', 'climate crisis', 'future']
                        found_keywords = [keyword for keyword in personal_story_keywords if keyword.lower() in body.lower()]
                        
                        print(f"\nMessage body contains personal story keywords: {found_keywords}")
                        if found_keywords:
                            print("✅ SUCCESS: Personal stories appear to be integrated into the message!")
                            return True
                        else:
                            print("❌ FAILURE: Personal stories do not appear in the message body")
                            print(f"Message body: {body}")
                            return False
            else:
                print("❌ FAILURE: No personalized_messages in response")
                return False
        else:
            print(f"❌ FAILURE: API returned status {response.status_code}")
            print(f"Response text: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ FAILURE: Request failed with error: {e}")
        return False
    except Exception as e:
        print(f"❌ FAILURE: Unexpected error: {e}")
        return False

def test_without_personal_stories():
    """Test that the API still works without personal stories"""
    
    test_payload = {
        "bill_id": "test-bill-123",
        "stance": "support",
        "selected_reasons": ["This bill improves safety standards"],
        "zip_code": "60302"
    }
    
    print("\n" + "="*50)
    print("Testing API without personal stories...")
    
    try:
        url = "http://localhost:8000/api/v1/actions/preview-message"
        headers = {"Content-Type": "application/json"}
        
        response = requests.post(url, json=test_payload, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print("✅ SUCCESS: API works without personal stories")
            return True
        else:
            print(f"❌ FAILURE: API returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ FAILURE: Error testing without personal stories: {e}")
        return False

if __name__ == "__main__":
    print("Personal Stories Integration Test")
    print("=" * 50)
    
    # Test with personal stories
    success1 = test_personal_stories_integration()
    
    # Test without personal stories (backward compatibility)
    success2 = test_without_personal_stories()
    
    print("\n" + "="*50)
    print("SUMMARY:")
    print(f"✅ With personal stories: {'PASS' if success1 else 'FAIL'}")
    print(f"✅ Without personal stories: {'PASS' if success2 else 'FAIL'}")
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED! Personal stories integration is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED. Please check the implementation.")
        sys.exit(1)
