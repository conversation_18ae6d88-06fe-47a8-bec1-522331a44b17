#!/usr/bin/env python3
"""
Test script to validate TL;DR functionality for bills.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'apps', 'api'))

import asyncio
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.models.bill import Bill, BillStatus, BillType
from app.services.ai_service import AIService
from app.services.enhanced_bill_service import EnhancedBillService

# Test database connection
DATABASE_URL = "postgresql://postgres:password@localhost:5432/modernaction"

def test_database_connection():
    """Test that we can connect to the database."""
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful")
            return engine
    except Exception as e:
        print(f"⚠️  Database connection failed (expected in test environment): {e}")
        print("✅ Skipping database tests")
        return None

def test_tldr_field_exists(engine):
    """Test that the TL;DR field exists in the database."""
    try:
        with engine.connect() as conn:
            # Check bills table
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'bills' AND column_name = 'tldr';
            """))
            bills_has_tldr = result.fetchone() is not None
            
            # Check bill_summary_versions table
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'bill_summary_versions' AND column_name = 'tldr';
            """))
            versions_has_tldr = result.fetchone() is not None
            
            if bills_has_tldr:
                print("✅ bills table has tldr column")
            else:
                print("❌ bills table missing tldr column")
            
            if versions_has_tldr:
                print("✅ bill_summary_versions table has tldr column")
            else:
                print("❌ bill_summary_versions table missing tldr column")
            
            return bills_has_tldr and versions_has_tldr
            
    except Exception as e:
        print(f"❌ Error checking TL;DR field: {e}")
        return False

def test_model_imports():
    """Test that models include TL;DR field."""
    try:
        # Test Bill model has tldr attribute
        if hasattr(Bill, 'tldr'):
            print("✅ Bill model has tldr attribute")
        else:
            print("❌ Bill model missing tldr attribute")
            return False
        
        # Test BillSummaryVersion model has tldr attribute
        from app.models.bill import BillSummaryVersion
        if hasattr(BillSummaryVersion, 'tldr'):
            print("✅ BillSummaryVersion model has tldr attribute")
        else:
            print("❌ BillSummaryVersion model missing tldr attribute")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Model import test failed: {e}")
        return False

async def test_ai_service_tldr():
    """Test that AI service can generate TL;DR."""
    try:
        # Mock AI service (without actual OpenAI calls)
        ai_service = AIService()
        
        # Check if the _generate_tldr method exists
        if hasattr(ai_service, '_generate_tldr'):
            print("✅ AIService has _generate_tldr method")
        else:
            print("❌ AIService missing _generate_tldr method")
            return False
        
        # Test fallback data includes TL;DR
        fallback_data = ai_service._get_fallback_ai_data({'title': 'Test Bill'})
        if 'tldr' in fallback_data:
            print("✅ Fallback data includes TL;DR")
            print(f"   Sample TL;DR: {fallback_data['tldr']}")
        else:
            print("❌ Fallback data missing TL;DR")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ AI service test failed: {e}")
        return False

def test_enhanced_bill_service():
    """Test that enhanced bill service processes TL;DR."""
    try:
        # Check if the service file includes TL;DR processing
        service_file = 'apps/api/app/services/enhanced_bill_service.py'
        with open(service_file, 'r') as f:
            content = f.read()
        
        if 'tldr' in content:
            print("✅ Enhanced bill service includes TL;DR processing")
        else:
            print("❌ Enhanced bill service missing TL;DR processing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced bill service test failed: {e}")
        return False

def test_frontend_types():
    """Test that frontend types include TL;DR."""
    try:
        types_file = 'apps/web/src/types/index.ts'
        with open(types_file, 'r') as f:
            content = f.read()
        
        if 'tldr?' in content:
            print("✅ Frontend types include TL;DR field")
        else:
            print("❌ Frontend types missing TL;DR field")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Frontend types test failed: {e}")
        return False

def test_frontend_components():
    """Test that frontend components display TL;DR."""
    try:
        # Check EnhancedBillCard
        card_file = 'apps/web/src/components/bills/EnhancedBillCard.tsx'
        with open(card_file, 'r') as f:
            card_content = f.read()
        
        if 'bill.tldr' in card_content:
            print("✅ EnhancedBillCard displays TL;DR")
        else:
            print("❌ EnhancedBillCard missing TL;DR display")
            return False
        
        # Check action page
        action_file = 'apps/web/src/app/bills/[id]/action/page.tsx'
        with open(action_file, 'r') as f:
            action_content = f.read()
        
        if 'bill.tldr' in action_content:
            print("✅ Bills action page displays TL;DR")
        else:
            print("❌ Bills action page missing TL;DR display")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Frontend components test failed: {e}")
        return False

def validate_tldr_quality(tldr_text):
    """Validate that TL;DR meets quality criteria."""
    if not tldr_text:
        return False, "TL;DR is empty"
    
    word_count = len(tldr_text.split())
    if word_count > 50:
        return False, f"TL;DR too long ({word_count} words, should be ≤50)"
    
    if not (tldr_text.startswith("This bill") or tldr_text.startswith("This law")):
        return False, "TL;DR should start with 'This bill' or 'This law'"
    
    # Check for complex words (basic check)
    complex_words = ['legislation', 'implementation', 'authorization', 'appropriation']
    for word in complex_words:
        if word.lower() in tldr_text.lower():
            return False, f"TL;DR contains complex word: {word}"
    
    return True, "TL;DR meets quality criteria"

async def main():
    """Run all tests."""
    print("🧪 Testing TL;DR Functionality Implementation\n")
    
    all_tests_passed = True
    
    # Test model imports
    print("📦 Testing Model Imports:")
    if not test_model_imports():
        all_tests_passed = False
    
    # Test database connection and schema
    print("\n🗄️  Testing Database Schema:")
    engine = test_database_connection()
    if engine:
        if not test_tldr_field_exists(engine):
            all_tests_passed = False
    
    # Test AI service
    print("\n🤖 Testing AI Service:")
    if not await test_ai_service_tldr():
        all_tests_passed = False
    
    # Test enhanced bill service
    print("\n⚙️  Testing Enhanced Bill Service:")
    if not test_enhanced_bill_service():
        all_tests_passed = False
    
    # Test frontend types
    print("\n📝 Testing Frontend Types:")
    if not test_frontend_types():
        all_tests_passed = False
    
    # Test frontend components
    print("\n🎨 Testing Frontend Components:")
    if not test_frontend_components():
        all_tests_passed = False
    
    # Test TL;DR quality validation
    print("\n✅ Testing TL;DR Quality Validation:")
    test_cases = [
        ("This bill gives money to schools for new computers.", True),
        ("This legislation implements comprehensive educational technology authorization.", False),
        ("This bill is about education and stuff and things and more things and even more things and lots of other things too.", False),
        ("", False)
    ]
    
    for tldr, should_pass in test_cases:
        is_valid, message = validate_tldr_quality(tldr)
        if is_valid == should_pass:
            print(f"✅ Quality test passed: {message}")
        else:
            print(f"❌ Quality test failed: {message}")
            all_tests_passed = False
    
    if all_tests_passed:
        print("\n🎉 All TL;DR functionality tests passed!")
        return True
    else:
        print("\n❌ Some TL;DR functionality tests failed.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
