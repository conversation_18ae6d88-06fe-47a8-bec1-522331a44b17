#!/usr/bin/env python3
"""
Test script for validating the values scoring system with real-world examples.

This script helps refine our scoring criteria by applying the rubric to actual bills
and identifying any ambiguities or inconsistencies in our methodology.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'apps', 'api'))

from app.services.values_scoring_criteria import ValuesScoring


def test_scoring_criteria_structure():
    """Test that our scoring criteria are properly structured."""
    print("🧪 Testing Scoring Criteria Structure\n")
    
    categories = ['democracy', 'human_rights', 'environment']
    score_types = ['threat', 'support']
    
    all_valid = True
    
    for category in categories:
        criteria_map = {
            'democracy': ValuesScoring.DEMOCRACY_CRITERIA,
            'human_rights': ValuesScoring.HUMAN_RIGHTS_CRITERIA,
            'environment': ValuesScoring.ENVIRONMENTAL_CRITERIA
        }
        
        category_criteria = criteria_map[category]
        
        for score_type in score_types:
            type_criteria = category_criteria[score_type]
            
            print(f"📋 {category.title()} - {score_type.title()}:")
            
            # Check that we have criteria for all score ranges
            expected_levels = [1, 4, 7, 9]  # Representative scores for each range
            
            for level in expected_levels:
                if level in type_criteria:
                    criteria = type_criteria[level]
                    score_range = criteria.score_range
                    examples_count = len(criteria.examples)
                    
                    print(f"  ✅ Level {level} (scores {score_range[0]}-{score_range[1]}): {examples_count} examples")
                    
                    # Validate that examples exist
                    if examples_count == 0:
                        print(f"    ⚠️  No examples provided for this level")
                        all_valid = False
                else:
                    print(f"  ❌ Missing criteria for level {level}")
                    all_valid = False
            
            print()
    
    return all_valid


def test_neutral_language():
    """Test that our language is appropriately neutral."""
    print("🎯 Testing Neutral Language Framework\n")
    
    # Test some descriptions
    test_cases = [
        ("democracy", "threat", 7),
        ("democracy", "support", 6),
        ("human_rights", "threat", 5),
        ("human_rights", "support", 8),
        ("environment", "threat", 4),
        ("environment", "support", 9)
    ]
    
    partisan_words = [
        'fight', 'battle', 'attack', 'defend', 'protect', 'threaten',
        'liberal', 'conservative', 'progressive', 'right-wing', 'left-wing',
        'socialist', 'capitalist', 'radical', 'extreme'
    ]
    
    all_neutral = True
    
    for category, score_type, score in test_cases:
        description = ValuesScoring.get_score_description(category, score_type, score)
        print(f"📝 {category} {score_type} (score {score}): '{description}'")
        
        # Check for partisan language
        description_lower = description.lower()
        found_partisan = [word for word in partisan_words if word in description_lower]
        
        if found_partisan:
            print(f"  ⚠️  Potentially partisan words found: {found_partisan}")
            all_neutral = False
        else:
            print(f"  ✅ Language appears neutral")
        
        print()
    
    return all_neutral


def manual_scoring_exercise():
    """Provide examples for manual scoring validation."""
    print("📊 Manual Scoring Exercise\n")
    print("Use these real-world examples to validate our scoring rubric:")
    print("Rate each on a 1-10 scale and compare with our criteria.\n")
    
    # Real-world bill examples for manual scoring
    test_bills = [
        {
            'title': 'Voting Rights Advancement Act',
            'description': 'Restores and strengthens voting rights protections, requires preclearance for voting changes',
            'category': 'democracy',
            'expected_type': 'support',
            'expected_score_range': (7, 9)
        },
        {
            'title': 'Secure Elections Act',
            'description': 'Requires voter ID, limits mail-in voting, reduces early voting periods',
            'category': 'democracy',
            'expected_type': 'threat',
            'expected_score_range': (5, 7)
        },
        {
            'title': 'Equality Act',
            'description': 'Prohibits discrimination based on sexual orientation and gender identity',
            'category': 'human_rights',
            'expected_type': 'support',
            'expected_score_range': (6, 8)
        },
        {
            'title': 'Religious Freedom Restoration Act',
            'description': 'Allows religious exemptions from anti-discrimination laws',
            'category': 'human_rights',
            'expected_type': 'threat',
            'expected_score_range': (4, 6)
        },
        {
            'title': 'Green New Deal',
            'description': 'Comprehensive climate action and clean energy transition plan',
            'category': 'environment',
            'expected_type': 'support',
            'expected_score_range': (8, 10)
        },
        {
            'title': 'Energy Independence Act',
            'description': 'Expands oil drilling, reduces environmental regulations',
            'category': 'environment',
            'expected_type': 'threat',
            'expected_score_range': (6, 8)
        }
    ]
    
    for i, bill in enumerate(test_bills, 1):
        print(f"🏛️  Example {i}: {bill['title']}")
        print(f"   Description: {bill['description']}")
        print(f"   Category: {bill['category']}")
        print(f"   Expected: {bill['expected_type']} (score {bill['expected_score_range'][0]}-{bill['expected_score_range'][1]})")
        print(f"   Manual Score: ___/10 ({bill['expected_type']})")
        print()
    
    print("💡 Scoring Guidelines:")
    print("   1-3: Low impact")
    print("   4-6: Medium impact") 
    print("   7-8: High impact")
    print("   9-10: Critical/transformative impact")
    print()
    
    return True


def test_examples_coverage():
    """Test that we have good coverage of examples across score ranges."""
    print("📈 Testing Example Coverage\n")
    
    try:
        examples = ValuesScoring.get_examples_for_testing()
    except Exception as e:
        print(f"❌ Error getting examples: {e}")
        return False

    all_covered = True

    for category_type, example_list in examples.items():
        print(f"📋 {category_type.replace('_', ' ').title()}: {len(example_list)} examples")
        
        if len(example_list) < 3:
            print(f"  ⚠️  Needs more examples (minimum 3 recommended)")
            all_covered = False
        else:
            print(f"  ✅ Good example coverage")
        
        # Show first few examples
        for i, example in enumerate(example_list[:3]):
            print(f"    {i+1}. {example}")
        
        if len(example_list) > 3:
            print(f"    ... and {len(example_list) - 3} more")
        
        print()
    
    return all_covered


def main():
    """Run all validation tests."""
    print("🎯 Values Scoring System Validation\n")
    print("=" * 60)
    
    tests = [
        ("Scoring Criteria Structure", test_scoring_criteria_structure),
        ("Neutral Language", test_neutral_language),
        ("Example Coverage", test_examples_coverage),
        ("Manual Scoring Exercise", manual_scoring_exercise)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"⚠️  {test_name}: NEEDS ATTENTION")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name}: FAILED - {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 All validation tests passed!")
        print("\n📋 Next Steps:")
        print("1. Complete the manual scoring exercise above")
        print("2. Refine criteria based on manual scoring results")
        print("3. Add more real-world examples to improve coverage")
        print("4. Implement the actual AI analysis using these criteria")
    else:
        print("⚠️  Some validation issues found.")
        print("Please address the issues above before proceeding.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
