# apps/api/tests/test_enhanced_actions.py
"""
Test suite for enhanced actions functionality (Part 2: Action Network Integration)

This test suite verifies the core functionality of the enhanced action system
including representative lookup, message personalization, and Action Network integration.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from app.services.officials_service import OfficialsService
from app.services.message_personalization_service import MessagePersonalizationService
from app.services.action_network_service import ActionNetworkService


class TestOfficialsService:
    """Test the Officials Lookup Service"""
    
    @pytest.fixture
    def officials_service(self):
        """Create a mock officials service for testing"""
        service = OfficialsService()
        return service
    
    @pytest.mark.asyncio
    async def test_zip_code_validation(self, officials_service):
        """Test ZIP code validation"""
        # Test valid ZIP codes
        valid_zips = ["90210", "10001", "60302"]
        for zip_code in valid_zips:
            assert len(zip_code) == 5
            assert zip_code.isdigit()
    
    @pytest.mark.asyncio
    async def test_mock_representative_lookup(self, officials_service):
        """Test representative lookup with mock data"""
        # Mock the API response
        mock_response = {
            'status': 'success',
            'state': 'CA',
            'district': '30',
            'senators': [
                {
                    'full_name': '<PERSON> <PERSON>di<PERSON>',
                    'title': 'Senator',
                    'party': 'Democratic',
                    'contact_email': '<EMAIL>'
                },
                {
                    'full_name': 'Laphonza <PERSON>',
                    'title': 'Senator', 
                    'party': 'Democratic',
                    'contact_email': '<EMAIL>'
                }
            ],
            'representative': {
                'full_name': 'Adam Schiff',
                'title': 'Representative',
                'party': 'Democratic',
                'district': '30',
                'contact_email': '<EMAIL>'
            },
            'total_representatives': 3
        }
        
        # Mock the service method
        with patch.object(officials_service, 'lookup_representatives_by_zip', 
                         return_value=mock_response) as mock_lookup:
            result = await officials_service.lookup_representatives_by_zip("90210")
            
            assert result['status'] == 'success'
            assert result['state'] == 'CA'
            assert len(result['senators']) == 2
            assert result['representative']['full_name'] == 'Adam Schiff'
            assert result['total_representatives'] == 3
            mock_lookup.assert_called_once_with("90210")


class TestMessagePersonalizationService:
    """Test the Message Personalization Service"""
    
    @pytest.fixture
    def message_service(self):
        """Create a mock message personalization service"""
        service = MessagePersonalizationService()
        return service
    
    @pytest.mark.asyncio
    async def test_mock_message_generation(self, message_service):
        """Test message generation with mock data"""
        # Mock bill data
        mock_bill = Mock()
        mock_bill.bill_number = "HR5"
        mock_bill.title = "Student Loan Forgiveness Act"
        mock_bill.ai_summary = "This bill provides student loan forgiveness..."
        
        # Mock representative data
        mock_representatives = [
            {
                'full_name': 'Alex Padilla',
                'title': 'Senator',
                'party': 'Democratic'
            }
        ]
        
        # Mock user info
        mock_user_info = {
            'first_name': 'John',
            'last_name': 'Doe',
            'zip_code': '90210'
        }
        
        # Mock response
        mock_response = {
            'status': 'success',
            'messages': [
                {
                    'representative': mock_representatives[0],
                    'subject': 'Support for HR5 - Student Loan Forgiveness Act',
                    'body': 'Dear Senator Padilla,\n\nAs your constituent from 90210, I urge you to support HR5...',
                    'talking_points': [
                        'Student debt relief is crucial for economic recovery',
                        'This bill will help millions of Americans'
                    ]
                }
            ]
        }
        
        # Mock the service method
        with patch.object(message_service, 'create_personalized_messages',
                         return_value=mock_response) as mock_create:
            
            personalization_data = {
                'bill': mock_bill,
                'position': 'support',
                'user_info': mock_user_info,
                'representatives': mock_representatives,
                'custom_message': ''
            }
            
            result = await message_service.create_personalized_messages(personalization_data)
            
            assert result['status'] == 'success'
            assert len(result['messages']) == 1
            assert 'HR5' in result['messages'][0]['subject']
            assert 'Senator Padilla' in result['messages'][0]['body']
            assert len(result['messages'][0]['talking_points']) == 2
            mock_create.assert_called_once_with(personalization_data)


class TestActionNetworkService:
    """Test the Action Network Service"""
    
    @pytest.fixture
    def action_network_service(self):
        """Create a mock Action Network service"""
        service = ActionNetworkService()
        return service
    
    @pytest.mark.asyncio
    async def test_mock_message_submission(self, action_network_service):
        """Test message submission with mock data"""
        # Mock message data
        mock_message_data = {
            'person': {
                'first_name': 'John',
                'last_name': 'Doe',
                'email': '<EMAIL>',
                'zip_code': '90210'
            },
            'targets': [
                {
                    'full_name': 'Alex Padilla',
                    'title': 'Senator',
                    'contact_email': '<EMAIL>'
                }
            ],
            'subject': 'Support for HR5',
            'body': 'Dear Senator, I urge you to support HR5...',
            'bill_number': 'HR5',
            'position': 'support'
        }
        
        # Mock response
        mock_response = {
            'status': 'success',
            'person_id': 'person-123',
            'message_results': [
                {
                    'target': 'Alex Padilla',
                    'status': 'sent',
                    'message_id': 'msg-456',
                    'timestamp': '2024-01-15T10:30:00Z'
                }
            ],
            'total_sent': 1
        }
        
        # Mock the service method
        with patch.object(action_network_service, 'submit_message',
                         return_value=mock_response) as mock_submit:
            
            result = await action_network_service.submit_message(mock_message_data)
            
            assert result['status'] == 'success'
            assert result['person_id'] == 'person-123'
            assert result['total_sent'] == 1
            assert len(result['message_results']) == 1
            assert result['message_results'][0]['status'] == 'sent'
            mock_submit.assert_called_once_with(mock_message_data)


class TestIntegratedWorkflow:
    """Test the integrated action workflow"""
    
    @pytest.mark.asyncio
    async def test_complete_action_workflow_mock(self):
        """Test the complete action workflow with mocked services"""
        
        # This test simulates the complete workflow:
        # 1. Look up representatives
        # 2. Generate personalized messages  
        # 3. Submit via Action Network
        
        # Step 1: Mock representative lookup
        mock_rep_result = {
            'status': 'success',
            'senators': [{'full_name': 'Alex Padilla', 'title': 'Senator'}],
            'representative': {'full_name': 'Adam Schiff', 'title': 'Representative'},
            'total_representatives': 3
        }
        
        # Step 2: Mock message generation
        mock_message_result = {
            'status': 'success',
            'messages': [
                {
                    'representative': {'full_name': 'Alex Padilla'},
                    'subject': 'Support for HR5',
                    'body': 'Dear Senator...',
                    'talking_points': ['Point 1', 'Point 2']
                }
            ]
        }
        
        # Step 3: Mock Action Network submission
        mock_action_result = {
            'status': 'success',
            'total_sent': 1,
            'message_results': [{'status': 'sent', 'message_id': 'msg-123'}]
        }
        
        # Simulate the workflow
        zip_code = "90210"
        position = "support"
        bill_id = 1
        
        # In a real test, these would be actual service calls
        # For now, we're just verifying the data flow structure
        
        assert mock_rep_result['status'] == 'success'
        assert len(mock_rep_result['senators']) > 0
        
        assert mock_message_result['status'] == 'success'
        assert len(mock_message_result['messages']) > 0
        
        assert mock_action_result['status'] == 'success'
        assert mock_action_result['total_sent'] > 0
        
        print("✅ Complete action workflow structure validated")


def test_environment_validation():
    """Test that required environment variables are properly validated"""
    required_vars = [
        'CONGRESS_GOV_API_KEY',
        'OPENAI_API_KEY',
        'OPENSTATES_API_KEY', 
        'ACTION_NETWORK_API_KEY'
    ]
    
    # This test just validates the structure
    # In production, these would be checked against actual environment
    for var in required_vars:
        assert isinstance(var, str)
        assert len(var) > 0
        print(f"✅ Environment variable {var} structure validated")


if __name__ == "__main__":
    # Run basic tests
    print("🧪 Running Enhanced Actions Test Suite")
    print("=" * 50)
    
    # Test environment validation
    test_environment_validation()
    
    # Run async tests
    async def run_async_tests():
        # Test workflow structure
        test_instance = TestIntegratedWorkflow()
        await test_instance.test_complete_action_workflow_mock()
    
    # Run the async tests
    asyncio.run(run_async_tests())
    
    print("=" * 50)
    print("✅ All basic tests passed!")
    print("\nNote: These are structural tests with mocked data.")
    print("For full integration testing, configure environment variables:")
    print("  • CONGRESS_GOV_API_KEY")
    print("  • OPENAI_API_KEY") 
    print("  • OPENSTATES_API_KEY")
    print("  • ACTION_NETWORK_API_KEY")
