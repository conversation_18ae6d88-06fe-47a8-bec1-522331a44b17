#!/usr/bin/env python3
"""
<PERSON>ript to update ECS task definition with startup delay to fix Auth0 secrets timing issue
"""

import json
import subprocess
import sys

def main():
    # Read the current task definition
    with open('current-task-def-v12.json', 'r') as f:
        task_def = json.load(f)
    
    # Remove fields that shouldn't be in the registration request
    fields_to_remove = [
        'taskDefinitionArn', 'revision', 'status', 'requiresAttributes',
        'placementConstraints', 'compatibilities', 'registeredAt',
        'registeredBy'
    ]
    
    for field in fields_to_remove:
        task_def.pop(field, None)
    
    # Keep the execution role ARN for secrets access
    execution_role_arn = "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8J2CBwsOvvxh"
    task_def['executionRoleArn'] = execution_role_arn
    
    # Update the container command to include a startup delay and secret check
    container = task_def['containerDefinitions'][0]
    
    # Change the command to include a startup script that waits for secrets
    container['command'] = [
        "/bin/sh", 
        "-c", 
        """
        echo "Waiting for Auth0 secrets to be injected..."
        while [ -z "$AUTH0_SECRET" ] || [ -z "$AUTH0_CLIENT_SECRET" ]; do
          echo "Secrets not found, sleeping for 2 seconds..."
          sleep 2
        done
        echo "✅ Auth0 secrets found!"
        echo "🚀 Starting ModernAction web application..."
        exec node server.js
        """
    ]
    
    # Write the updated task definition
    with open('updated-task-def-v13.json', 'w') as f:
        json.dump(task_def, f, indent=2)
    
    print("✅ Updated task definition with startup delay for Auth0 secrets")
    print("📝 Saved to updated-task-def-v13.json")
    
    # Register the new task definition
    print("🚀 Registering new task definition...")
    result = subprocess.run([
        'aws', 'ecs', 'register-task-definition',
        '--cli-input-json', 'file://updated-task-def-v13.json'
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        response = json.loads(result.stdout)
        new_task_def_arn = response['taskDefinition']['taskDefinitionArn']
        print(f"✅ New task definition registered: {new_task_def_arn}")
        
        # Update the web service to use the new task definition
        print("🔄 Updating web service...")
        update_result = subprocess.run([
            'aws', 'ecs', 'update-service',
            '--cluster', 'modernaction-staging',
            '--service', 'modernaction-web-staging',
            '--task-definition', new_task_def_arn
        ], capture_output=True, text=True)
        
        if update_result.returncode == 0:
            print("✅ Web service updated successfully!")
            print("🎉 Auth0 timing fix deployed!")
            print("⏳ Waiting for deployment to complete...")
            
            # Wait for the service to stabilize
            wait_result = subprocess.run([
                'aws', 'ecs', 'wait', 'services-stable',
                '--cluster', 'modernaction-staging',
                '--services', 'modernaction-web-staging'
            ], capture_output=True, text=True)
            
            if wait_result.returncode == 0:
                print("✅ Service deployment completed successfully!")
            else:
                print("⚠️  Service deployment may still be in progress")
                
        else:
            print(f"❌ Failed to update service: {update_result.stderr}")
            return 1
    else:
        print(f"❌ Failed to register task definition: {result.stderr}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
