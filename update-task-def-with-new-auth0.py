#!/usr/bin/env python3
"""
Script to update ECS task definition with new Auth0 client ID
"""

import json
import subprocess
import sys

def main():
    # Read the current task definition
    with open('updated-task-def-v13.json', 'r') as f:
        task_def = json.load(f)
    
    # Update the AUTH0_CLIENT_ID environment variable
    container = task_def['containerDefinitions'][0]
    
    # Find and update the AUTH0_CLIENT_ID environment variable
    for env_var in container['environment']:
        if env_var['name'] == 'AUTH0_CLIENT_ID':
            env_var['value'] = 'Z5044niYKdAyiwjBYgPwYu3ogxCeEL44'
            print(f"✅ Updated AUTH0_CLIENT_ID to: {env_var['value']}")
            break
    
    # Write the updated task definition
    with open('updated-task-def-v14.json', 'w') as f:
        json.dump(task_def, f, indent=2)
    
    print("✅ Updated task definition with new Auth0 client ID")
    print("📝 Saved to updated-task-def-v14.json")
    
    # Register the new task definition
    print("🚀 Registering new task definition...")
    result = subprocess.run([
        'aws', 'ecs', 'register-task-definition',
        '--cli-input-json', 'file://updated-task-def-v14.json'
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        response = json.loads(result.stdout)
        new_task_def_arn = response['taskDefinition']['taskDefinitionArn']
        print(f"✅ New task definition registered: {new_task_def_arn}")
        
        # Update the web service to use the new task definition
        print("🔄 Updating web service...")
        update_result = subprocess.run([
            'aws', 'ecs', 'update-service',
            '--cluster', 'modernaction-staging',
            '--service', 'modernaction-web-staging',
            '--task-definition', new_task_def_arn
        ], capture_output=True, text=True)
        
        if update_result.returncode == 0:
            print("✅ Web service updated successfully!")
            print("🎉 New Auth0 configuration deployed!")
            print("⏳ Waiting for deployment to complete...")
            
            # Wait for the service to stabilize
            wait_result = subprocess.run([
                'aws', 'ecs', 'wait', 'services-stable',
                '--cluster', 'modernaction-staging',
                '--services', 'modernaction-web-staging'
            ], capture_output=True, text=True)
            
            if wait_result.returncode == 0:
                print("✅ Service deployment completed successfully!")
                print("🔥 FINAL AUTH0 FIX DEPLOYED!")
            else:
                print("⚠️  Service deployment may still be in progress")
                
        else:
            print(f"❌ Failed to update service: {update_result.stderr}")
            return 1
    else:
        print(f"❌ Failed to register task definition: {result.stderr}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
