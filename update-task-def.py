#!/usr/bin/env python3
"""
<PERSON>ript to update ECS task definition with Auth0 secrets
"""

import json
import subprocess
import sys

def main():
    # Read the current task definition
    with open('current-task-def.json', 'r') as f:
        task_def = json.load(f)
    
    # Remove fields that shouldn't be in the registration request
    fields_to_remove = [
        'taskDefinitionArn', 'revision', 'status', 'requiresAttributes',
        'placementConstraints', 'compatibilities', 'registeredAt',
        'registeredBy'
    ]

    # Keep the execution role ARN for secrets access
    execution_role_arn = "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8J2CBwsOvvxh"
    task_def['executionRoleArn'] = execution_role_arn
    
    for field in fields_to_remove:
        task_def.pop(field, None)
    
    # Add Auth0 secrets to the web container
    container = task_def['containerDefinitions'][0]
    
    # Add Auth0 environment variables
    auth0_env_vars = [
        {
            "name": "AUTH0_BASE_URL",
            "value": "https://staging.modernaction.io"
        },
        {
            "name": "AUTH0_ISSUER_BASE_URL",
            "value": "https://dev-modernaction.us.auth0.com"
        },
        {
            "name": "AUTH0_CLIENT_ID",
            "value": "Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC"
        },
        {
            "name": "AUTH0_AUDIENCE",
            "value": "https://api.modernaction.io"
        }
    ]
    
    # Add Auth0 secrets from AWS Secrets Manager
    auth0_secrets = [
        {
            "name": "AUTH0_SECRET",
            "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0ConfigSecret5DCB30FC-unX0jBWDsgB7-I7b74d:AUTH0_SECRET::"
        },
        {
            "name": "AUTH0_CLIENT_SECRET", 
            "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0ConfigSecret5DCB30FC-unX0jBWDsgB7-I7b74d:AUTH0_CLIENT_SECRET::"
        }
    ]
    
    # Add to environment and secrets
    container['environment'].extend(auth0_env_vars)
    container['secrets'].extend(auth0_secrets)
    
    # Write the updated task definition
    with open('updated-task-def.json', 'w') as f:
        json.dump(task_def, f, indent=2)
    
    print("✅ Updated task definition with Auth0 configuration")
    print("📝 Saved to updated-task-def.json")
    
    # Register the new task definition
    print("🚀 Registering new task definition...")
    result = subprocess.run([
        'aws', 'ecs', 'register-task-definition',
        '--cli-input-json', 'file://updated-task-def.json'
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        response = json.loads(result.stdout)
        new_task_def_arn = response['taskDefinition']['taskDefinitionArn']
        print(f"✅ New task definition registered: {new_task_def_arn}")
        
        # Update the web service to use the new task definition
        print("🔄 Updating web service...")
        update_result = subprocess.run([
            'aws', 'ecs', 'update-service',
            '--cluster', 'modernaction-staging',
            '--service', 'modernaction-web-staging',
            '--task-definition', new_task_def_arn
        ], capture_output=True, text=True)
        
        if update_result.returncode == 0:
            print("✅ Web service updated successfully!")
            print("🎉 Auth0 configuration complete!")
        else:
            print(f"❌ Failed to update service: {update_result.stderr}")
            return 1
    else:
        print(f"❌ Failed to register task definition: {result.stderr}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
