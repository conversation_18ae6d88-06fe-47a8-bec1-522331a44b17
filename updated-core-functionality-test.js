#!/usr/bin/env node

const { chromium } = require('playwright');

async function updatedCoreFunctionalityTest() {
    console.log('🧪 UPDATED CORE FUNCTIONALITY TEST');
    console.log('==================================');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    const testResults = {
        billsAPI: false,
        billSummaries: false,
        campaignsAPI: false,
        campaignsPage: false,
        campaignModal: false,
        emailTemplate: false,
        socialMessage: false,
        actionFlow: false,
        officialsAPI: false
    };
    
    try {
        // ===== TEST 1: BILLS API =====
        console.log('\n📋 TEST 1: Bills API Verification');
        
        await page.goto('https://staging.modernaction.io/api/v1/bills');
        const billsContent = await page.textContent('body');
        
        try {
            const billsData = JSON.parse(billsContent);
            testResults.billsAPI = Array.isArray(billsData) && billsData.length > 0;
            
            if (testResults.billsAPI) {
                console.log(`✅ Bills API working - Found ${billsData.length} bills`);
                
                const billsWithSummaries = billsData.filter(bill => 
                    bill.summary && bill.summary.length > 10
                );
                testResults.billSummaries = billsWithSummaries.length > 0;
                console.log(`✅ Bill summaries: ${billsWithSummaries.length}/${billsData.length} bills have summaries`);
            }
        } catch (e) {
            console.log(`❌ Bills API failed: ${e.message}`);
        }
        
        // ===== TEST 2: CAMPAIGNS API =====
        console.log('\n🎯 TEST 2: Campaigns API Verification');
        
        await page.goto('https://staging.modernaction.io/api/v1/campaigns');
        const campaignsContent = await page.textContent('body');
        
        try {
            const campaignsData = JSON.parse(campaignsContent);
            testResults.campaignsAPI = Array.isArray(campaignsData) && campaignsData.length > 0;
            
            if (testResults.campaignsAPI) {
                console.log(`✅ Campaigns API working - Found ${campaignsData.length} campaigns`);
                
                // Check for email templates and social messages
                const campaignsWithEmail = campaignsData.filter(c => c.email_template && c.email_template.length > 10);
                const campaignsWithSocial = campaignsData.filter(c => c.social_media_message && c.social_media_message.length > 10);
                
                testResults.emailTemplate = campaignsWithEmail.length > 0;
                testResults.socialMessage = campaignsWithSocial.length > 0;
                
                console.log(`✅ Email templates: ${campaignsWithEmail.length}/${campaignsData.length} campaigns`);
                console.log(`✅ Social messages: ${campaignsWithSocial.length}/${campaignsData.length} campaigns`);
                
                // Log sample data
                if (campaignsData.length > 0) {
                    const sample = campaignsData[0];
                    console.log(`📋 Sample campaign: "${sample.title}"`);
                    if (sample.bill) {
                        console.log(`📄 Associated bill: ${sample.bill.title} (${sample.bill.bill_number})`);
                    }
                }
            }
        } catch (e) {
            console.log(`❌ Campaigns API failed: ${e.message}`);
        }
        
        // ===== TEST 3: OFFICIALS API (EXPECTED TO BE EMPTY) =====
        console.log('\n🏛️  TEST 3: Officials API Check');
        
        await page.goto('https://staging.modernaction.io/api/v1/officials');
        const officialsContent = await page.textContent('body');
        
        try {
            const officialsData = JSON.parse(officialsContent);
            testResults.officialsAPI = Array.isArray(officialsData); // Just check it returns an array
            
            console.log(`📊 Officials API status: ${testResults.officialsAPI ? 'Working' : 'Failed'}`);
            console.log(`📊 Officials count: ${officialsData.length} (expected to be 0 for now)`);
            
            if (officialsData.length === 0) {
                console.log('ℹ️  Officials table is empty - this is expected and needs to be seeded');
            }
        } catch (e) {
            console.log(`❌ Officials API failed: ${e.message}`);
        }
        
        // ===== TEST 4: FRONTEND CAMPAIGNS PAGE =====
        console.log('\n🖥️  TEST 4: Frontend Campaigns Page');
        
        await page.goto('https://staging.modernaction.io/campaigns', { waitUntil: 'networkidle' });
        await page.screenshot({ path: 'updated-test-campaigns-page.png' });
        
        // Check if campaigns page loads
        const pageTitle = await page.title();
        const pageContent = await page.textContent('body');
        
        testResults.campaignsPage = pageTitle.includes('Campaigns') || pageContent.includes('Active Campaigns');
        console.log(`✅ Campaigns page loads: ${testResults.campaignsPage}`);
        
        // Look for campaign links (the actual HTML structure)
        const campaignLinks = page.locator('a[href*="/campaigns/"]');
        const campaignCount = await campaignLinks.count();
        
        console.log(`📊 Campaign links found: ${campaignCount}`);
        
        if (campaignCount > 0) {
            console.log('✅ Campaign elements are visible on the page');
            
            try {
                // Click on the first campaign link
                console.log('🖱️  Clicking on first campaign...');
                await campaignLinks.first().click();
                await page.waitForTimeout(3000);
                
                // Check if we're on a campaign detail page
                const currentUrl = page.url();
                const isOnCampaignPage = currentUrl.includes('/campaigns/') && currentUrl.length > 50;
                
                if (isOnCampaignPage) {
                    console.log('✅ Successfully navigated to campaign detail page');
                    testResults.campaignModal = true;
                    
                    await page.screenshot({ path: 'updated-test-campaign-detail.png' });
                    
                    // Look for action elements on the campaign page
                    const actionButtons = page.locator('button:has-text("Take Action"), button:has-text("Send"), button:has-text("Submit")');
                    const actionButtonCount = await actionButtons.count();
                    
                    console.log(`📤 Action buttons found: ${actionButtonCount}`);
                    
                    if (actionButtonCount > 0) {
                        testResults.actionFlow = true;
                        console.log('✅ Action flow elements are present');
                    }
                    
                    // Look for email/social toggles or forms
                    const emailElements = page.locator('input[type="checkbox"], input[type="email"], textarea');
                    const emailElementCount = await emailElements.count();
                    
                    console.log(`📧 Form elements found: ${emailElementCount}`);
                    
                } else {
                    console.log('⚠️  Campaign click did not navigate to detail page');
                }
                
            } catch (e) {
                console.log(`⚠️  Campaign interaction failed: ${e.message}`);
            }
        } else {
            console.log('❌ No campaign links found on page');
        }
        
    } catch (error) {
        console.log(`❌ Test error: ${error.message}`);
        await page.screenshot({ path: 'updated-test-error.png' });
        
    } finally {
        await browser.close();
    }
    
    // ===== RESULTS SUMMARY =====
    console.log('\n🏆 UPDATED CORE FUNCTIONALITY RESULTS');
    console.log('=====================================');
    
    const tests = [
        { name: 'Bills API Working', result: testResults.billsAPI, critical: true },
        { name: 'Bill Summaries Present', result: testResults.billSummaries, critical: true },
        { name: 'Campaigns API Working', result: testResults.campaignsAPI, critical: true },
        { name: 'Email Templates Available', result: testResults.emailTemplate, critical: true },
        { name: 'Social Messages Available', result: testResults.socialMessage, critical: true },
        { name: 'Campaigns Page Loads', result: testResults.campaignsPage, critical: true },
        { name: 'Campaign Detail Navigation', result: testResults.campaignModal, critical: true },
        { name: 'Action Flow Present', result: testResults.actionFlow, critical: true },
        { name: 'Officials API Functional', result: testResults.officialsAPI, critical: false }
    ];
    
    let passedTests = 0;
    let criticalPassed = 0;
    let criticalTotal = 0;
    
    tests.forEach(test => {
        const status = test.result ? '✅ PASS' : '❌ FAIL';
        const priority = test.critical ? '[CRITICAL]' : '[OPTIONAL]';
        console.log(`${status} ${priority} - ${test.name}`);
        
        if (test.result) passedTests++;
        if (test.critical) {
            criticalTotal++;
            if (test.result) criticalPassed++;
        }
    });
    
    const overallSuccess = (passedTests / tests.length * 100).toFixed(1);
    const criticalSuccess = (criticalPassed / criticalTotal * 100).toFixed(1);
    
    console.log(`\n📊 Overall Success Rate: ${overallSuccess}% (${passedTests}/${tests.length})`);
    console.log(`🎯 Critical Features Success: ${criticalSuccess}% (${criticalPassed}/${criticalTotal})`);
    
    // Assessment
    if (criticalSuccess >= 100) {
        console.log('\n🎉 🚀 CORE FUNCTIONALITY FULLY OPERATIONAL! 🚀 🎉');
        console.log('All critical features are working correctly.');
        console.log('📝 Note: Officials data needs to be seeded for representative lookup functionality.');
        return true;
    } else if (criticalSuccess >= 75) {
        console.log('\n⚠️  MOSTLY FUNCTIONAL - Some critical issues detected');
        console.log('Most core features working but some improvements needed.');
        return true;
    } else {
        console.log('\n❌ CRITICAL FUNCTIONALITY ISSUES');
        console.log('Major core features are not working properly.');
        return false;
    }
}

// Execute the updated test
updatedCoreFunctionalityTest().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('💥 Updated core functionality test crashed:', error);
    process.exit(1);
});
