{"containerDefinitions": [{"name": "WebContainer", "image": "308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:final-launch-candidate", "cpu": 0, "links": [], "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": ["/bin/sh", "-c", "\n        echo \"Waiting for Auth0 secrets to be injected...\"\n        while [ -z \"$AUTH0_SECRET\" ] || [ -z \"$AUTH0_CLIENT_SECRET\" ]; do\n          echo \"Secrets not found, sleeping for 2 seconds...\"\n          sleep 2\n        done\n        echo \"✅ Auth0 secrets found!\"\n        echo \"🚀 Starting ModernAction web application...\"\n        exec node server.js\n        "], "environment": [{"name": "AUTH0_CLIENT_ID", "value": "Z5044niYKdAyiwjBYgPwYu3ogxCeEL44"}, {"name": "INTERNAL_API_URL", "value": "http://api.staging.local:8000/api/v1"}, {"name": "NODE_ENV", "value": "production"}, {"name": "AUTH0_ISSUER_BASE_URL", "value": "https://dev-modernaction.us.auth0.com"}, {"name": "AUTH0_BASE_URL", "value": "https://staging.modernaction.io"}, {"name": "AUTH0_AUDIENCE", "value": "https://api.modernaction.io"}, {"name": "NEXT_PUBLIC_API_URL", "value": "https://staging.modernaction.io/api/v1"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "AUTH0_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0ConfigSecret5DCB30FC-unX0jBWDsgB7-I7b74d:AUTH0_SECRET::"}, {"name": "AUTH0_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0ConfigSecret5DCB30FC-unX0jBWDsgB7-I7b74d:AUTH0_CLIENT_SECRET::"}], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "modernaction-staging-WebTaskDefinitionWebContainerLogGroup9BD817D4-g0P3dC8LggmF", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "web"}, "secretOptions": []}, "systemControls": [], "credentialSpecs": []}], "family": "ModernActionstagingWebTaskDefinition6EB55C12", "taskRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskRole30FC0FBB-YQ5wIqK35bv0", "executionRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8J2CBwsOvvxh", "networkMode": "awsvpc", "volumes": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048"}