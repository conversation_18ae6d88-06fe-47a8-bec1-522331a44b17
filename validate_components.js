#!/usr/bin/env node
/**
 * Validate React components for syntax and import errors
 */

const fs = require('fs');
const path = require('path');

function validateTypeScriptFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Basic syntax validation
        const issues = [];
        
        // Check for common TypeScript/React issues
        if (!content.includes('import React')) {
            issues.push('Missing React import');
        }
        
        // Check for proper export
        if (!content.includes('export')) {
            issues.push('No export statement found');
        }
        
        // Check for proper interface definitions
        const interfaceMatches = content.match(/interface\s+\w+/g);
        if (interfaceMatches) {
            console.log(`✅ Found ${interfaceMatches.length} interface(s) in ${path.basename(filePath)}`);
        }
        
        // Check for proper component definition
        const componentMatches = content.match(/export\s+const\s+\w+.*=.*React\.FC/g);
        if (componentMatches) {
            console.log(`✅ Found React functional component in ${path.basename(filePath)}`);
        }
        
        if (issues.length === 0) {
            console.log(`✅ ${path.basename(filePath)} appears to be valid`);
            return true;
        } else {
            console.log(`❌ Issues in ${path.basename(filePath)}:`);
            issues.forEach(issue => console.log(`   - ${issue}`));
            return false;
        }
        
    } catch (error) {
        console.log(`❌ Error reading ${filePath}: ${error.message}`);
        return false;
    }
}

function validateApiClient(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for our new API functions
        const expectedFunctions = [
            'getBillSummaryVersions',
            'getBillStatusHistory',
            'getBillTimeline'
        ];
        
        let allFound = true;
        expectedFunctions.forEach(func => {
            if (content.includes(func)) {
                console.log(`✅ API function ${func} found`);
            } else {
                console.log(`❌ API function ${func} missing`);
                allFound = false;
            }
        });
        
        return allFound;
        
    } catch (error) {
        console.log(`❌ Error reading ${filePath}: ${error.message}`);
        return false;
    }
}

function main() {
    console.log('🧪 Validating React Components and API Client\n');
    
    const componentsToValidate = [
        'apps/web/src/components/bills/BillLifecycleTracker.tsx',
        'apps/web/src/components/bills/SummaryVersionTracker.tsx'
    ];
    
    const apiClientPath = 'apps/web/src/services/apiClient.ts';
    const actionPagePath = 'apps/web/src/app/bills/[id]/action/page.tsx';
    
    let allValid = true;
    
    // Validate components
    console.log('📦 Validating React Components:');
    componentsToValidate.forEach(componentPath => {
        if (!validateTypeScriptFile(componentPath)) {
            allValid = false;
        }
    });
    
    console.log('\n🔌 Validating API Client:');
    if (!validateApiClient(apiClientPath)) {
        allValid = false;
    }
    
    console.log('\n📄 Validating Action Page Integration:');
    if (!validateTypeScriptFile(actionPagePath)) {
        allValid = false;
    }
    
    // Check if action page imports our components
    try {
        const actionPageContent = fs.readFileSync(actionPagePath, 'utf8');
        
        if (actionPageContent.includes('BillLifecycleTracker')) {
            console.log('✅ Action page imports BillLifecycleTracker');
        } else {
            console.log('❌ Action page missing BillLifecycleTracker import');
            allValid = false;
        }
        
        if (actionPageContent.includes('SummaryVersionTracker')) {
            console.log('✅ Action page imports SummaryVersionTracker');
        } else {
            console.log('❌ Action page missing SummaryVersionTracker import');
            allValid = false;
        }
        
    } catch (error) {
        console.log(`❌ Error checking action page: ${error.message}`);
        allValid = false;
    }
    
    if (allValid) {
        console.log('\n🎉 All components and integrations are valid!');
        process.exit(0);
    } else {
        console.log('\n❌ Some validation issues found.');
        process.exit(1);
    }
}

main();
