#!/usr/bin/env python3
"""
Validate React components for syntax and import errors
"""

import os
import re

def validate_typescript_file(file_path):
    """Validate a TypeScript/React file for basic syntax and structure."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # Check for React import
        if 'import React' not in content:
            issues.append('Missing React import')
        
        # Check for proper export
        if 'export' not in content:
            issues.append('No export statement found')
        
        # Check for interface definitions
        interface_matches = re.findall(r'interface\s+\w+', content)
        if interface_matches:
            print(f"✅ Found {len(interface_matches)} interface(s) in {os.path.basename(file_path)}")
        
        # Check for React functional component
        component_matches = re.findall(r'export\s+const\s+\w+.*=.*React\.FC', content)
        if component_matches:
            print(f"✅ Found React functional component in {os.path.basename(file_path)}")
        
        if len(issues) == 0:
            print(f"✅ {os.path.basename(file_path)} appears to be valid")
            return True
        else:
            print(f"❌ Issues in {os.path.basename(file_path)}:")
            for issue in issues:
                print(f"   - {issue}")
            return False
            
    except Exception as error:
        print(f"❌ Error reading {file_path}: {error}")
        return False

def validate_api_client(file_path):
    """Validate that API client has our new functions."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        expected_functions = [
            'getBillSummaryVersions',
            'getBillStatusHistory',
            'getBillTimeline'
        ]
        
        all_found = True
        for func in expected_functions:
            if func in content:
                print(f"✅ API function {func} found")
            else:
                print(f"❌ API function {func} missing")
                all_found = False
        
        return all_found
        
    except Exception as error:
        print(f"❌ Error reading {file_path}: {error}")
        return False

def main():
    """Run all validations."""
    print('🧪 Validating React Components and API Client\n')
    
    components_to_validate = [
        'apps/web/src/components/bills/BillLifecycleTracker.tsx',
        'apps/web/src/components/bills/SummaryVersionTracker.tsx'
    ]
    
    api_client_path = 'apps/web/src/services/apiClient.ts'
    action_page_path = 'apps/web/src/app/bills/[id]/action/page.tsx'
    
    all_valid = True
    
    # Validate components
    print('📦 Validating React Components:')
    for component_path in components_to_validate:
        if not validate_typescript_file(component_path):
            all_valid = False
    
    print('\n🔌 Validating API Client:')
    if not validate_api_client(api_client_path):
        all_valid = False
    
    print('\n📄 Validating Action Page Integration:')
    if not validate_typescript_file(action_page_path):
        all_valid = False
    
    # Check if action page imports our components
    try:
        with open(action_page_path, 'r', encoding='utf-8') as f:
            action_page_content = f.read()
        
        if 'BillLifecycleTracker' in action_page_content:
            print('✅ Action page imports BillLifecycleTracker')
        else:
            print('❌ Action page missing BillLifecycleTracker import')
            all_valid = False
        
        if 'SummaryVersionTracker' in action_page_content:
            print('✅ Action page imports SummaryVersionTracker')
        else:
            print('❌ Action page missing SummaryVersionTracker import')
            all_valid = False
            
    except Exception as error:
        print(f"❌ Error checking action page: {error}")
        all_valid = False
    
    if all_valid:
        print('\n🎉 All components and integrations are valid!')
        return True
    else:
        print('\n❌ Some validation issues found.')
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
