#!/bin/bash

# ModernAction.io Launch Verification Script
# This script verifies that all key endpoints are working correctly

BASE_URL="http://modern-ApiSe-5h6OCm8WlBhL-318015605.us-east-1.elb.amazonaws.com"

echo "🚀 ModernAction.io Launch Verification"
echo "======================================"
echo "Base URL: $BASE_URL"
echo ""

# Test API Health
echo "🔍 Testing API Health..."
HEALTH_RESPONSE=$(curl -s "$BASE_URL/api/v1/health")
if [[ $HEALTH_RESPONSE == *"ok"* ]]; then
    echo "✅ API Health: PASS"
else
    echo "❌ API Health: FAIL"
    echo "Response: $HEALTH_RESPONSE"
fi
echo ""

# Test Campaigns Endpoint
echo "🔍 Testing Campaigns Endpoint..."
CAMPAIGNS_COUNT=$(curl -s "$BASE_URL/api/v1/campaigns/" | jq length)
if [[ $CAMPAIGNS_COUNT -eq 5 ]]; then
    echo "✅ Campaigns: PASS ($CAMPAIGNS_COUNT campaigns found)"
else
    echo "❌ Campaigns: FAIL (Expected 5, found $CAMPAIGNS_COUNT)"
fi
echo ""

# Test Featured Campaigns
echo "🔍 Testing Featured Campaigns..."
FEATURED_COUNT=$(curl -s "$BASE_URL/api/v1/campaigns/featured" | jq length)
if [[ $FEATURED_COUNT -eq 5 ]]; then
    echo "✅ Featured Campaigns: PASS ($FEATURED_COUNT featured campaigns)"
else
    echo "❌ Featured Campaigns: FAIL (Expected 5, found $FEATURED_COUNT)"
fi
echo ""

# Test Bills Endpoint
echo "🔍 Testing Bills Endpoint..."
BILLS_COUNT=$(curl -s "$BASE_URL/api/v1/bills/" | jq length)
if [[ $BILLS_COUNT -eq 5 ]]; then
    echo "✅ Bills: PASS ($BILLS_COUNT bills found)"
else
    echo "❌ Bills: FAIL (Expected 5, found $BILLS_COUNT)"
fi
echo ""

# Test Frontend
echo "🔍 Testing Frontend..."
FRONTEND_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/")
if [[ $FRONTEND_RESPONSE -eq 200 ]]; then
    echo "✅ Frontend: PASS (HTTP $FRONTEND_RESPONSE)"
else
    echo "❌ Frontend: FAIL (HTTP $FRONTEND_RESPONSE)"
fi
echo ""

# List Campaign Titles
echo "📋 Campaign Titles:"
curl -s "$BASE_URL/api/v1/campaigns/" | jq -r '.[] | "• \(.title) (\(.bill.bill_number))"'
echo ""

echo "🎉 Launch verification complete!"
echo "ModernAction.io is ready for public launch! 🚀"
